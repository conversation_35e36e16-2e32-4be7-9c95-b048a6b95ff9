import time
import traceback

from temporalio import activity

from nebula.services.search.common import get_recurrence, typesense_index_meeting, TypesenseSessionRecurrenceData
from nebula.shared.transcriptions import get_all_transcriptions_paragraphs_firstname
from nebula.temporal.types import (
    TypesenseRecurrenceData,
    TypesenseProcessedTranscripts,
    TypesenseIndexingResult,
    GetRecurrenceDataInput,
    ProcessTranscriptsForTypesenseInput,
    IndexMeetingInTypesenseInput,
)


class TypesenseActivities:
    @activity.defn
    async def get_recurrence_data(
        self, input_data: GetRecurrenceDataInput
    ) -> TypesenseRecurrenceData:
        """
        Activity to get recurrence data for typesense indexing.

        Args:
            input_data: GetRecurrenceDataInput containing session and recurrence IDs

        Returns:
            TypesenseRecurrenceData containing recurrence information
        """
        try:
            # Get mars DB pool from activity context
            from nebula.db.pool import get_mars_pool
            mars_pool = await get_mars_pool()
            
            # Use the general get_recurrence function which has all fields
            full_recurrence = await get_recurrence(int(input_data.recurrence_id), mars_pool)
            
            # Extract only the fields needed for typesense
            typesense_recurrence: TypesenseSessionRecurrenceData = {
                "recurrenceID": full_recurrence["recurrenceID"],
                "sessionID": full_recurrence["sessionID"], 
                "title": full_recurrence["title"],
                "about": full_recurrence["about"],
                "dataVisibility": full_recurrence["dataVisibility"],
                "creatorID": full_recurrence.get("creatorID"),
                "creatorFirstName": full_recurrence.get("creatorFirstName"),
                "creatorLastName": full_recurrence.get("creatorLastName"),
                "creatorEmail": full_recurrence.get("creatorEmail"),
                "creatorAvatar": full_recurrence.get("creatorAvatar"),
                "createdAt": full_recurrence["createdAt"],
            }

            activity.logger.info(
                f"Retrieved recurrence data for typesense indexing - session {input_data.session_id}"
            )

            return TypesenseRecurrenceData(
                recurrence_id=int(input_data.recurrence_id),
                session_id=int(input_data.session_id),
                recurrence_data=typesense_recurrence,
            )

        except Exception as e:
            activity.logger.error(
                f"Error retrieving recurrence data for typesense: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def process_transcripts_for_typesense(
        self, input_data: ProcessTranscriptsForTypesenseInput
    ) -> TypesenseProcessedTranscripts:
        """
        Activity to process transcripts for typesense indexing.

        Args:
            input_data: ProcessTranscriptsForTypesenseInput containing transcripts

        Returns:
            TypesenseProcessedTranscripts containing processed transcript text
        """
        try:
            # Now input_data.transcripts should be proper DTOs thanks to consistent converter
            transcript_fulltext = get_all_transcriptions_paragraphs_firstname(
                input_data.transcripts
            )

            activity.logger.info(
                f"Processed {len(input_data.transcripts)} transcripts for typesense: "
                f"{len(transcript_fulltext)} characters"
            )

            return TypesenseProcessedTranscripts(
                transcript_fulltext=transcript_fulltext,
                processed_count=len(input_data.transcripts),
            )

        except Exception as e:
            activity.logger.error(
                f"Error processing transcripts for typesense: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def index_meeting_in_typesense(
        self, input_data: IndexMeetingInTypesenseInput
    ) -> TypesenseIndexingResult:
        """
        Activity to index the meeting in Typesense.

        Args:
            input_data: IndexMeetingInTypesenseInput containing recurrence data and processed transcripts

        Returns:
            TypesenseIndexingResult containing indexing results
        """
        try:
            start_time = time.time()
            
            # Get mars DB pool
            from nebula.db.pool import get_mars_pool
            mars_pool = await get_mars_pool()

            # Index the meeting in Typesense
            await typesense_index_meeting(
                input_data.recurrence_data.recurrence_data,
                idx=None,
                transcript_fulltext=input_data.processed_transcripts.transcript_fulltext,
                mars_pool=mars_pool,
            )

            processing_time = time.time() - start_time

            activity.logger.info(
                f"Indexed meeting in typesense for recurrence {input_data.recurrence_data.recurrence_id} "
                f"in {processing_time:.2f} seconds"
            )

            return TypesenseIndexingResult(
                indexed_documents=1,  # Each meeting is one document
                processing_time_seconds=processing_time,
            )

        except Exception as e:
            activity.logger.error(
                f"Error indexing meeting in typesense: {str(e)}\n{traceback.format_exc()}"
            )
            raise 