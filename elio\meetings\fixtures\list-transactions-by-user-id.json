{"success": true, "message": "Data fetched successfully", "data": [{"transaction": {"address": {}, "addressID": "add_01hq87vc8f264gqv1hm36xzrrs", "adjustmentsTotals": {"breakdown": {}}, "billedAt": "2025-05-22T11:06:08.854426Z", "billingPeriod": {"endsAt": "2025-06-22T11:05:31.148223Z", "startsAt": "2025-05-22T11:05:31.148223Z"}, "business": {}, "checkout": {"url": "https://staging.rumi.ai/pricing/checkout?_ptxn=txn_01jvvtr22p66z9e2nfxrspfbp8"}, "collectionMode": "automatic", "createdAt": "2025-05-22T11:06:08.908902Z", "currencyCode": "USD", "customData": {"userID": "806428820482557594"}, "customer": {}, "customerID": "ctm_01hq87v6jn14vn84yzkhma2yf1", "details": {"adjustedPayoutTotals": {"chargebackFee": {"amount": "0"}, "currencyCode": "USD", "earnings": "712", "fee": "100", "subtotal": "812", "tax": "187", "total": "999"}, "adjustedTotals": {"currencyCode": "USD", "earnings": "712", "fee": "100", "grandTotal": "999", "subtotal": "812", "tax": "187", "total": "999"}, "lineItems": [{"id": "txnitm_01jvvtr23h9j32egjya3hnnw2d", "price_id": "pri_01hn32p3k1rcgzevmqjz1t9nej", "quantity": 1, "tax_rate": "0.23", "unit_totals": {"subtotal": "812", "discount": "0", "tax": "187", "total": "999"}, "totals": {"subtotal": "812", "discount": "0", "tax": "187", "total": "999"}, "product": {"id": "pro_01hn31q5grky5xcyp58401kp1v", "name": "Premium", "description": "Premium ", "type": "standard", "tax_category": "standard", "image_url": "", "status": "active", "created_at": "2024-01-26T14:10:29.528Z", "updated_at": "2024-01-26T14:10:29.528Z"}}], "payoutTotals": {"balance": "0", "credit": "0", "creditToBalance": "0", "currencyCode": "USD", "discount": "0", "earnings": "712", "fee": "100", "grandTotal": "999", "subtotal": "812", "tax": "187", "total": "999"}, "taxRatesUsed": [{"tax_rate": "0.23", "totals": {"subtotal": "812", "discount": "0", "tax": "187", "total": "999"}}], "totals": {"balance": "0", "credit": "0", "creditToBalance": "0", "currencyCode": "USD", "discount": "0", "earnings": "712", "fee": "100", "grandTotal": "999", "subtotal": "812", "tax": "187", "total": "999"}}, "discount": {}, "id": "txn_01jvvtr22p66z9e2nfxrspfbp8", "invoiceID": "inv_01jvvtr4ykc9z4qfedpdh0tv0a", "invoiceNumber": "5993-10323", "items": [{"price": {"billingCycle": {"frequency": 1, "interval": "month"}, "createdAt": "2024-01-26T14:27:23.361285Z", "description": "Monthly subscription", "id": "pri_01hn32p3k1rcgzevmqjz1t9nej", "name": "Monthly subscription", "product": {}, "productID": "pro_01hn31q5grky5xcyp58401kp1v", "quantity": {"maximum": 1, "minimum": 1}, "status": "active", "taxMode": "account_setting", "type": "standard", "unitPrice": {"amount": "999", "currencyCode": "USD"}, "updatedAt": "2024-01-26T14:27:23.361285Z"}, "quantity": 1}], "origin": "subscription_recurring", "payments": [{"amount": "999", "capturedAt": "2025-05-22T11:06:10.833986Z", "createdAt": "2025-05-22T11:06:08.996707Z", "methodDetails": {"card": {"cardholderName": "man", "expiryMonth": 1, "expiryYear": 2027, "last4": "5556", "type": "visa"}, "type": "card"}, "paymentAttemptID": "452c1358-331e-47f5-a7e3-668ee5b34458", "paymentMethodID": "paymtd_01hq87vs6cs2cc89c0c2nv0nyr", "status": "captured", "storedPaymentMethodID": "7a11f0ed-f80c-4fd1-bc9e-d103a3cbef20"}], "status": "completed", "subscriptionID": "sub_01hq87vxrvcsqg7gxc930ps4yt", "updatedAt": "2025-05-22T11:06:13.910239Z"}, "invoiceUrl": "https://paddle-sandbox-invoice-service-pdfs.s3.amazonaws.com/invoices/17288/88b95f6d-9a79-4e5d-a99f-143e7db62e58/invoice_5993-10255_Rumi-ai-Inc.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=ASIAZWZYLIX3EKNOYDMK%2F20250610%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250610T210552Z&X-Amz-Expires=3600&X-Amz-Security-Token=IQoJb3JpZ2luX2VjEO3%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEaCXVzLWVhc3QtMSJIMEYCIQCB%2Fp%2FZCiuxErf5Jq97IByTCXG7beoSq2OVilBqp1rr3wIhAJD%2BnbE4hedKPaG4uPZIGDfzkfxdM3XO09y707nnBiNYKowECMb%2F%2F%2F%2F%2F%2F%2F%2F%2F%2FwEQAxoMNjY3NDQ4NzIyOTM0Igzf9j2pAkLrudMbnt4q4ANDDcICZf5JlDuCwHGGIb%2B%2BgT%2Beps6XK21RWCp2d%2FMRFoVbxji68vB8qviq8b40cSbITQsOjRjsRV6XzyEAV71UNCjj1biAXHm%2FeZRN%2Fb71eatgYS2zrIwWUOXEiIQbIOG4CRG3iZN%2FyiK6H9usloUTqTvzTd%2Fa5%2FA7ocjPFHcWPrUA7mHnl%2FouzuDkca3P3D1pk8Ezl0cbyNrTuoW6zywWuWpI%2B1ZOGsltiGGERFcdmC8d0Z1ACV4Kv3Xqv2D2H9kEvRLm4mWCP46bS489j4O5EI9SHOHom2RFilTUgN6rPD7S07rN8UVH0D5taPipvWZzwT2LSh%2FahwU1qtFtEN0fyopZWDX0Q079KBtwaXksCCE0fvXADgOOGF8xnHjpulWSG%2F61nbeSoexIGt2VklOJOHN5Sr8uOOe4%2BcL6a1o9q70Sq1fzFnmD3abmvmq24hBVJFFaBhL51YvE%2F4IOKRTMHEee%2F8ghsoXFuhuh7jdR1NXRqtryCludrZUJyxuJcBsj6z30D6%2BZv%2FjoHw6IUbiQPqN3sfUPlTf6BC7IFPj1wURK8O0Ff%2FwiEP3gkarFCuriTdxQNlWDoMBAmD1jDWRAkxQMq8hjyeu0hlHYHwx0uxQbTUhGAmGSsGu%2F75S6xF0wm7WiwgY6pAGPu%2Bdv8lxwLkJ%2Bz1k7xcjx2D0SBgfylGWh0QLlqhqdtyUjIEvzrPd0Rxqv2a3oBORzO4086ZAkusaZWB5P1KMGnk7VCgPplt5gaWd6PRJThEclqMnYPqJppnYr6KMWs2okNQcJCdFmR4r6urYZjoYk%2Bdbv2DAF4%2BHETEFP6kfn6SOFgqyVD5cCZJK90P%2BDQEi9n%2FNpRvQ3mWJNnvYzS1rW3jUdTg%3D%3D&X-Amz-SignedHeaders=host&response-content-disposition=attachment&response-content-type=application%2Fpdf&X-Amz-Signature=d5c9ca5f637d46e966061a4df353b2dfb434ae22b97bf7fd8e4e654320d50065"}]}