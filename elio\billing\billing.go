package billing

import (
	"context"
	api "encore.app/billing/api"
	config2 "encore.app/billing/config"
	"encore.app/billing/database"
	"encore.app/shared"
	"encore.dev/config"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"fmt"
	paddleSDK "github.com/PaddleHQ/paddle-go-sdk/v3"
	"github.com/jmoiron/sqlx"
	"github.com/samber/lo"
	"net/http"
)

var secrets struct {
	PaddleAPIKey string
}

//encore:service
type Billing struct {
	paddleClient *paddleSDK.SDK
}

var namedMarsDB = sqldb.Named("mars")
var MarsDB = sqlx.NewDb(namedMarsDB.Stdlib(), "pgx")

var Config = config.Load[*config2.BillingConfig]()

func initBilling() (*Billing, error) {
	billing := &Billing{}

	var err error

	HasOverrideConfig := Config.MarsPostgresOverride() != ""
	if HasOverrideConfig {
		if MarsDB, err = sqlx.Connect("pgx", Config.MarsPostgresOverride()); err != nil {
			return nil, err
		}
	}

	ctx := context.Background()
	if err := database.Statements.ValidateAll(ctx, MarsDB, "mars"); err != nil {
		return nil, err
	}

	paddleClient, err := paddleSDK.New(
		secrets.PaddleAPIKey,
		paddleSDK.WithBaseURL(Config.PaddleAPIBaseURL()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create paddle client: %w", err)
	}

	billing.paddleClient = paddleClient

	return billing, nil
}

//encore:api public method=GET path=/v2.0/billing/plans
func (b *Billing) ListPlans(ctx context.Context) (*api.ListPlansResponse, error) {
	listPaddleProductsRes, err := ListProducts(ctx, api.ListProductsRequest{
		IncludePrices: true,
	})
	if err != nil {
		err = fmt.Errorf("error listing paddle products: %w", err)
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	paddleProducts := listPaddleProductsRes.Products

	paddleProductIDs := lo.Map(paddleProducts, func(paddleProduct shared.BillingProductDTO, _ int) string {
		return paddleProduct.ID
	})

	subscriptionPlanConfigRows, err := database.ListSubscriptionPlanConfigsByPaddleProductIDs(ctx, MarsDB, paddleProductIDs)
	if err != nil {
		err = fmt.Errorf("error listing subscription plan configs by paddle product ids: %w", err)
		return nil, shared.HttpResponseError(err.Error(), http.StatusInternalServerError, http.StatusInternalServerError)
	}

	subscriptionPlanConfigs := lo.Map(*subscriptionPlanConfigRows, func(spcr shared.SubscriptionPlanConfigRow, _ int) shared.SubscriptionPlanConfig {
		spc, err2 := spcr.ToSubscriptionPlanConfig()
		if err2 != nil {
			err = err2
			return shared.SubscriptionPlanConfig{}
		}

		return *spc
	})
	if err != nil {
		return nil, err
	}

	mergedSubscriptionPlanConfigsAndPaddleProducts := lo.Map(subscriptionPlanConfigs, func(spc shared.SubscriptionPlanConfig, _ int) api.SubscriptionPlanConfigDTOAndPaddleProductDTO {
		mergedDTOs := api.SubscriptionPlanConfigDTOAndPaddleProductDTO{
			Config: spc.ToDTO(),
		}

		paddleProduct, ok := lo.Find(paddleProducts, func(pp shared.BillingProductDTO) bool {
			return pp.ID == spc.PaddleProductID
		})

		if ok {
			mergedDTOs.BillingProductDTO = paddleProduct
		} else {
			rlog.Warn("paddle product not found in Billing.ListPlans", "paddleProductID", spc.PaddleProductID)
		}

		return mergedDTOs
	})

	return &api.ListPlansResponse{
		Success: true,
		Message: "Data fetched successfully",
		Data:    mergedSubscriptionPlanConfigsAndPaddleProducts,
	}, nil
}
