import asyncio
import time
import traceback
from typing import Optional, List, Any

from temporalio import activity

from nebula.services.elio.service import (
    get_concentrated_transcripts,
    get_session_by_id,
    get_session_participants,
    get_session_speakers_as_guests,
)
from nebula.services.draconids.session_email_service import draconids_summary_ready
from nebula.db.models.post_session_summary import PostSessionSummary
from nebula.shared.transcriptions import get_all_transcriptions_with_timestamps
from nebula.services.post_session_summaries.post_session_summaries_langchain_service import generate_pms
from nebula.services.ai_feed.ai_feed_service import get_action_items_for_session
from elio_client import ApiSessionUserDTO
from piccolo.columns.combination import And
from nebula.temporal.types import (
    PostSessionSummaryWorkflowParams,
    TranscriptionBatchesResult,
    ProcessedTranscriptResult,
    PostSessionDataModel,
    PostSessionSummaryGenerationResult,
    PostSessionSummaryRecordResult,
    GeneratePMSInput,
    CreateOrUpdatePostSessionSummaryInput,
    GetActionItemsInput,
    NotifyDraconidsInput,
)


class PostSessionSummaryActivities:
    @activity.defn
    async def get_transcription_batches_for_pss(
        self, session_info: PostSessionSummaryWorkflowParams
    ) -> TranscriptionBatchesResult:
        """
        Activity to retrieve transcription batches for post session summary.

        Args:
            session_info: Session identification information

        Returns:
            TranscriptionBatchesResult containing transcription batches
        """
        try:
            transcripts = await asyncio.to_thread(
                get_concentrated_transcripts,
                session_info.session_id, session_info.recurrence_id
            )

            activity.logger.info(
                f"Retrieved transcript batches for PSS - session {session_info.session_id}"
            )

            return TranscriptionBatchesResult(
                transcription_batches=list(transcripts) if transcripts else []
            )

        except Exception as e:
            activity.logger.error(
                f"Error retrieving transcription batches for PSS: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def get_session_data_for_pss(
        self, session_info: PostSessionSummaryWorkflowParams
    ) -> PostSessionDataModel:
        """
        Activity to retrieve session data for post session summary.

        Args:
            session_info: Session identification information

        Returns:
            PostSessionDataModel containing meeting information and participants
        """
        try:
            session = await asyncio.to_thread(
                get_session_by_id,
                session_info.session_id, session_info.recurrence_id
            )
            participants = await asyncio.to_thread(
                get_session_participants,
                session_info.session_id, session_info.recurrence_id
            )

            # Handle recall-bot sessions with guest speakers
            if session.meeting_type == "recall-bot":
                guests = await asyncio.to_thread(
                    get_session_speakers_as_guests,
                    session_info.session_id, session_info.recurrence_id
                )
                for guest in guests:
                    session_user = ApiSessionUserDTO.from_dict({
                        "user": None,
                        "guest": None,
                        "joined": True,
                        "sessionUserID": "0",  # Guests don't seem to have a valid ID
                        "roleIDs": ["guest", "viewer"],
                        "isViewerAccessRevoked": False,
                    })
                    if session_user is not None:
                        session_user.guest = guest
                        participants.append(session_user)

            # Process participants
            participant_list = []
            for participant in participants:
                if participant.joined is False:
                    continue
                
                # Generate fullname
                fullname = None
                if participant.user is not None and participant.user.first_name and participant.user.last_name:
                    fullname = participant.user.first_name + " " + participant.user.last_name
                elif participant.guest is not None and participant.guest.full_name:
                    fullname = participant.guest.full_name
                
                if fullname:
                    participant_list.append({"name": fullname})

            meeting_type = (
                session.user_meeting_type.title
                if session.user_meeting_type
                else None
            )

            session_data = PostSessionDataModel(
                session_id=session_info.session_id,
                recurrence_id=session_info.recurrence_id,
                participants=participant_list,
                meeting_type=meeting_type,
            )

            activity.logger.info(
                f"Retrieved session data for PSS {session_info.session_id}: "
                f"meeting_type='{meeting_type}', "
                f"participants={len(participant_list)}"
            )

            return session_data

        except Exception as e:
            activity.logger.error(
                f"Error retrieving session data for PSS: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def process_transcript_for_pss(
        self, transcript_batches: TranscriptionBatchesResult
    ) -> ProcessedTranscriptResult:
        """
        Activity to process transcript batches into a single transcript for PSS.

        Args:
            transcript_batches: Transcription batches to process

        Returns:
            ProcessedTranscriptResult containing the processed transcript
        """
        try:
            # Get full transcript text
            transcripts, locale = get_all_transcriptions_with_timestamps(
                transcript_batches.transcription_batches
            )
            transcript = "\n".join(transcripts)

            activity.logger.info(
                f"Processed transcript for PSS: {len(transcript)} characters, locale={locale}"
            )

            return ProcessedTranscriptResult(
                transcript=transcript, locale=locale or "en-US"
            )

        except Exception as e:
            activity.logger.error(
                f"Error processing transcript for PSS: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def create_or_update_post_session_summary(
        self, input_data: CreateOrUpdatePostSessionSummaryInput
    ) -> PostSessionSummaryRecordResult:
        """
        Activity to create or update post session summary records.
        Handles both create and update operations in one activity.

        Args:
            input_data: CreateOrUpdatePostSessionSummaryInput containing session info and content

        Returns:
            PostSessionSummaryRecordResult containing the summary ID
        """
        try:
            # Get existing summary
            summary = await PostSessionSummary.objects().get(
                And(
                    PostSessionSummary.session_id == input_data.session_id,
                    PostSessionSummary.session_recurrence_id == input_data.recurrence_id,
                )
            )
            
            if summary is None:
                # Create new summary
                summary = PostSessionSummary(
                    session_id=input_data.session_id,
                    session_recurrence_id=input_data.recurrence_id,
                    content=input_data.content,
                    tldr=input_data.tldr,
                )
            else:
                # Update existing summary
                if input_data.content is not None:
                    summary.content = input_data.content
                if input_data.tldr is not None:
                    summary.tldr = input_data.tldr

            await summary.save()

            activity.logger.info(
                f"[create_or_update_pms_with content] done. recurrence_id: {input_data.recurrence_id}, session_id: {input_data.session_id}"
            )

            return PostSessionSummaryRecordResult(summary_id=summary.id)

        except Exception as e:
            activity.logger.error(
                f"Error creating/updating post session summary: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def generate_post_meeting_summary(
        self, input: GeneratePMSInput
    ) -> PostSessionSummaryGenerationResult:
        """
        Activity to generate the main post meeting summary using LLM.

        Args:
            input: GeneratePMSInput containing transcript and session data

        Returns:
            PostSessionSummaryGenerationResult containing the generated summary
        """
        try:
            start_time = time.time()
            
            summary = await generate_pms(
                transcript=input.transcript,
                recurrence_id=input.recurrence_id,
                locale=input.locale,
                meeting_type=input.meeting_type,
            )

            processing_time = time.time() - start_time

            activity.logger.info(
                f"Generated PMS for recurrence {input.recurrence_id} "
                f"in {processing_time:.2f} seconds"
            )

            return PostSessionSummaryGenerationResult(
                summary=summary,
                processing_time_seconds=processing_time,
            )

        except Exception as e:
            activity.logger.error(
                f"Error generating post meeting summary: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def get_action_items_for_pss(
        self, input_data: GetActionItemsInput
    ) -> List[dict]:
        """
        Activity to retrieve action items for the session.

        Args:
            input_data: GetActionItemsInput containing session and recurrence IDs

        Returns:
            List[dict] containing action items
        """
        try:
            action_items = await get_action_items_for_session(input_data.session_id, input_data.recurrence_id)

            activity.logger.info(
                f"Retrieved {len(action_items)} action items for session {input_data.session_id}"
            )

            return action_items

        except Exception as e:
            activity.logger.error(
                f"Error retrieving action items: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def notify_draconids_summary_ready(
        self, input_data: NotifyDraconidsInput
    ) -> None:
        """
        Activity to notify Draconids that the summary is ready.

        Args:
            input_data: NotifyDraconidsInput containing session and recurrence IDs
        """
        try:
            await draconids_summary_ready(input_data.session_id, input_data.recurrence_id)

            activity.logger.info(
                f"Notified Draconids that summary is ready for session {input_data.session_id}"
            )

        except Exception as e:
            activity.logger.error(
                f"Error notifying Draconids: {str(e)}\n{traceback.format_exc()}"
            )
            raise 