package pulsar

import (
	pulsarConfig "encore.app/pulsar/config"
	"encore.app/pulsar/storage"
	"encore.dev/config"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
)

var secrets struct {
	AWSAccessKey string
	AWSSecretKey string
}

var Config = config.Load[pulsarConfig.PulsarConfig]()

//encore:service
type Pulsar struct{}

var S3Client storage.S3Client

func initPulsar() (*Pulsar, error) {
	pulsar := &Pulsar{}
	// Create a session using your AWS credentials
	sess, err := session.NewSession(&aws.Config{
		Region:      aws.String(Config.AWSRegion()),
		Credentials: credentials.NewStaticCredentials(secrets.AWSAccessKey, secrets.AWSSecretKey, ""),
	})
	if err != nil {
		panic("Failed to create AWS session")
	}
	if S3Client == nil {
		S3Client = &storage.AWSS3Client{Client: s3.New(sess), SourceBucket: Config.RecordingBucket()}
	}

	return pulsar, err
}
