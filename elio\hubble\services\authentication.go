package services

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"encore.app/hubble/database"
	"encore.app/pkg/preview"
	encore "encore.dev"
	"errors"
	"fmt"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/samber/lo"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"encore.app/hubble/api"
	"encore.app/hubble/config"
	"encore.app/hubble/models"
	"encore.app/hubble/repositories"
	"encore.app/hubble/utils"
	"encore.app/shared"
	"encore.dev/rlog"
	"encore.dev/storage/cache"
	"github.com/jinzhu/copier"
)

type GenerateSignUpEmailVerificationOTPParams struct {
	Email          string  `json:"email"`
	FirstName      string  `json:"firstName"`
	LastName       string  `json:"lastName"`
	MarketingOptIn *bool   `json:"marketingOptIn,omitempty"`
	Timezone       *string `json:"timezone"`
	Fingerprint    *string `json:"fingerprint,omitempty"`
}

type AuthenticationService struct {
	Repo              *repositories.RedisRepository
	securityBasicAuth string // securityBasicAuth is the encoded base64 string that must be used to pass authentication
	jwtSecret         string
	jwtRefreshSecret  string
}

func NewAuthenticationService(repo *repositories.RedisRepository, user, password, secret, refreshSecret string) AuthenticationService {
	securityBasicAuth := base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", user, password)))
	return AuthenticationService{
		Repo:              repo,
		securityBasicAuth: securityBasicAuth,
		jwtSecret:         secret,
		jwtRefreshSecret:  refreshSecret,
	}
}

func (service AuthenticationService) GenerateLoginEmailVerificationOTP(ctx context.Context, email, clientID, redirectUri string) (string, error) {
	otp, err := utils.GenerateOTP()
	if err != nil {
		return otp, err
	}
	if clientID != "" {
		err = service.Repo.OAuthVerifyEmailOTP.Set(ctx, repositories.OAuthEmailOTPKey{Email: email, ClientID: clientID}, repositories.SignUpEmailVerificationOTP{
			IsSignUp:    false,
			OTP:         otp,
			RedirectUri: redirectUri,
		})
		return otp, err
	}

	err = service.Repo.VerifyEmailOTP.Set(ctx, repositories.EmailOTPKey{Email: email}, repositories.SignUpEmailVerificationOTP{
		IsSignUp: false,
		OTP:      otp,
	})

	return otp, err
}

func (service AuthenticationService) GenerateSignupEmailVerificationOTP(ctx context.Context, input GenerateSignUpEmailVerificationOTPParams, clientID, redirectUri string) (string, error) {
	otp, err := utils.GenerateOTP()
	if err != nil {
		return otp, err
	}
	marketingOptIn := true

	if input.MarketingOptIn != nil {
		marketingOptIn = *input.MarketingOptIn
	}

	if clientID != "" {
		err = service.Repo.OAuthVerifyEmailOTP.Set(ctx, repositories.OAuthEmailOTPKey{Email: input.Email, ClientID: clientID}, repositories.SignUpEmailVerificationOTP{
			IsSignUp:       true,
			OTP:            otp,
			FirstName:      input.FirstName,
			LastName:       input.LastName,
			MarketingOptIn: &marketingOptIn,
			Timezone:       input.Timezone,
			Fingerprint:    input.Fingerprint,
			RedirectUri:    redirectUri,
		})
		return otp, err
	}
	err = service.Repo.VerifyEmailOTP.Set(ctx, repositories.EmailOTPKey{Email: input.Email}, repositories.SignUpEmailVerificationOTP{
		IsSignUp:       true,
		OTP:            otp,
		FirstName:      input.FirstName,
		LastName:       input.LastName,
		MarketingOptIn: &marketingOptIn,
		Timezone:       input.Timezone,
		Fingerprint:    input.Fingerprint,
	})

	return otp, err
}

func (service AuthenticationService) VerifyOTPForEmailVerification(ctx context.Context, email, clientOTP, clientID string) (*repositories.SignUpEmailVerificationOTP, error) {
	result, err := func() (repositories.SignUpEmailVerificationOTP, error) {
		if clientID != "" {
			return service.Repo.OAuthVerifyEmailOTP.Get(ctx, repositories.OAuthEmailOTPKey{Email: email, ClientID: clientID})
		}
		return service.Repo.VerifyEmailOTP.Get(ctx, repositories.EmailOTPKey{Email: email})
	}()

	// Verify OTP
	if errors.Is(err, cache.Miss) {
		return nil, errors.New("invalid OTP")
	}
	if err != nil {
		return nil, err
	}

	// Local/testing override
	if result.OTP != clientOTP {
		if encore.Meta().Environment.Cloud == encore.CloudLocal && encore.Meta().Environment.Type == encore.EnvDevelopment {
			if clientOTP != "0000" {
				return &result, fmt.Errorf("invalid OTP")
			}
		} else {
			return nil, fmt.Errorf("invalid OTP")
		}
	}

	// Delete OTP
	delCount, err := func() (int, error) {
		if clientID != "" {
			return service.Repo.OAuthVerifyEmailOTP.Delete(ctx, repositories.OAuthEmailOTPKey{Email: email, ClientID: clientID})
		}
		return service.Repo.VerifyEmailOTP.Delete(ctx, repositories.EmailOTPKey{Email: email})
	}()
	if err != nil {
		rlog.Warn("Error deleting OTP", "err", err)
	}
	if delCount == 0 {
		rlog.Warn("OTP not found for deletion", "email", email)
	}

	return &result, nil
}

func (service AuthenticationService) VerifyAuthToken(ctx context.Context, token string) (*shared.DecodedToken, error) {
	claims, err := utils.DecodeToken(shared.TokenTypeAuth, service.jwtSecret, service.jwtRefreshSecret, token)
	if err != nil {
		return nil, err
	}

	_, err = service.Repo.InvalidatedRefreshTokens.Get(ctx, repositories.InvalidatedTokenKey{TokenID: claims.ID})
	if errors.Is(err, cache.Miss) {
		return claims, nil
	}
	if err != nil {
		return nil, err
	}

	return nil, errors.New("invalid token")
}

func (service AuthenticationService) VerifyTrxitaBasicAuth(basic string) error {
	if service.securityBasicAuth != basic {
		return errors.New("invalid basic auth")
	}

	return nil
}

var InvalidatedTokenErr = errors.New("invalidated token")

func (service AuthenticationService) VerifyRefreshToken(ctx context.Context, token string) (*shared.DecodedToken, error) {
	claims, err := utils.DecodeToken(shared.TokenTypeRefresh, service.jwtSecret, service.jwtRefreshSecret, token)
	if err != nil {
		return nil, err
	}

	_, err = service.Repo.InvalidatedRefreshTokens.Get(ctx, repositories.InvalidatedTokenKey{TokenID: claims.ID})
	if errors.Is(err, cache.Miss) {
		return claims, nil
	}
	if err != nil {
		return nil, err
	}

	return claims, InvalidatedTokenErr
}

func (service AuthenticationService) InvalidateTokenPair(ctx context.Context, tokenID string) error {
	key := repositories.InvalidatedTokenKey{TokenID: tokenID}
	tokenErr := service.Repo.InvalidatedTokens.Set(ctx, key, 1)
	refreshErr := service.Repo.InvalidatedRefreshTokens.Set(ctx, key, 1)
	if tokenErr != nil {
		return tokenErr
	}
	return refreshErr
}

func (service AuthenticationService) InvalidateRefreshToken(ctx context.Context, tokenID string) error {
	key := repositories.InvalidatedTokenKey{TokenID: tokenID}
	return service.Repo.InvalidatedRefreshTokens.Set(ctx, key, 1)
}

func (service AuthenticationService) InvalidateAllTokensByUserID(userID string) (int, error) {
	ctx := context.Background()
	tokenIDs, err := service.Repo.UserIssuedTokens.Items(ctx, repositories.UserIDKey{UserID: userID})
	if err != nil {
		return 0, err
	}

	_, err = service.Repo.UserIssuedTokens.Delete(context.Background(), repositories.UserIDKey{
		UserID: userID,
	})
	if err != nil {
		return 0, err
	}

	deletedCount := 0

	for tokenID := range tokenIDs {
		err = service.InvalidateTokenPair(ctx, strconv.Itoa(tokenID))
		if err != nil {
			return deletedCount, err
		}
		deletedCount += 1
	}

	return deletedCount, nil
}

func (service AuthenticationService) GenerateAuthRefreshPair(authUser *models.AuthUser) (string, string, error) {
	builder := utils.NewJWTTokenBuilder(service.jwtSecret, service.jwtRefreshSecret).
		NewTokenID().
		UserToken(shared.FormatInt(authUser.ID), authUser.Email, authUser.RoleIDs, authUser.TeamID, authUser.TeamRoleIDs)
	authenticationToken, refreshToken, err := builder.Build()
	if err != nil {
		return "", "", err
	}

	decoded := builder.Decoded()
	service.Repo.UserIssuedTokens.Add(context.Background(), repositories.UserIDKey{UserID: decoded.UserID}, decoded.ID)

	return authenticationToken, refreshToken, nil
}

type socialLoginTokenVerificationApiResponseDecodedTokenData struct {
	Email     string             `json:"email"`
	FirstName string             `json:"firstName"`
	LastName  string             `json:"lastName"`
	RoleIDs   []models.USER_ROLE `json:"roleIDs"`
	GoogleID  *string            `json:"googleID,omitempty"`
	AppleID   *string            `json:"appleID,omitempty"`
	Avatar    *string            `json:"avatar,omitempty"`
}

type socialLoginVerificationApiResponseData struct {
	DecodedTokenData socialLoginTokenVerificationApiResponseDecodedTokenData `json:"decodedTokenData"`
	User             *models.User                                            `json:"user"`
}
type httpErrorResp struct {
	Name       string `json:"name"`
	Message    string `json:"message"`
	StatusCode int    `json:"statusCode,omitempty"`
	ErrCode    int    `json:"errCode,omitempty"`
}

type SocialLoginVerificationApiResponse struct {
	Success bool                                    `json:"success"`
	Error   *httpErrorResp                          `json:"error"`
	Data    *socialLoginVerificationApiResponseData `json:"data"`
}

func SocialLoginTokenVerification(ctx context.Context, config config.Mars, marsUsername, marsPassword string,
	input *api.SocialLoginRequest) (*SocialLoginVerificationApiResponse, error) {
	newReq := &api.SocialLoginRequest{}
	copier.CopyWithOption(newReq, input, copier.Option{IgnoreEmpty: true, DeepCopy: true})
	requestBody, err := json.Marshal(newReq)
	if err != nil {
		return nil, err
	}

	var apiPath strings.Builder
	auroraHostUrl := config.URL()
	prid := shared.FormatInt(preview.ExtractPRFromURL(encore.Meta().APIBaseURL.Host))
	auroraHostUrl = strings.ReplaceAll(auroraHostUrl, ":prid", prid)
	apiPath.WriteString(auroraHostUrl)
	apiPath.WriteString("/auth/social/token/verify")

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		apiPath.String(),
		bytes.NewBuffer(requestBody),
	)
	if err != nil {
		return nil, err
	}
	// Set basic auth in headers
	req.SetBasicAuth(marsUsername, marsPassword)
	req.Header.Set("Content-Type", "application/json")

	resp, err := utils.HttpClient.Do(req)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var socialLoginResponse SocialLoginVerificationApiResponse
	err = json.Unmarshal(body, &socialLoginResponse)
	if err != nil {
		return nil, err
	}

	return &socialLoginResponse, nil
}

func SocialLoginCodeVerification(ctx context.Context, config config.Mars, marsUsername, marsPassword string, input api.SocialLoginViaCodeRequest) (*SocialLoginVerificationApiResponse, error) {
	newReq := &api.SocialLoginViaCodeRequest{}
	copier.CopyWithOption(newReq, input, copier.Option{IgnoreEmpty: true, DeepCopy: true})
	requestBody, err := json.Marshal(newReq)
	if err != nil {
		return nil, err
	}

	var apiPath strings.Builder
	marsHostUrl := config.URL()
	prid := shared.FormatInt(preview.ExtractPRFromURL(encore.Meta().APIBaseURL.Host))
	marsHostUrl = strings.ReplaceAll(marsHostUrl, ":prid", prid)
	apiPath.WriteString(marsHostUrl)
	apiPath.WriteString("/auth/social/code/verify")

	ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		apiPath.String(),
		bytes.NewBuffer(requestBody),
	)
	if err != nil {
		return nil, err
	}
	// Set basic auth in headers
	req.SetBasicAuth(marsUsername, marsPassword)
	req.Header.Set("Content-Type", "application/json")

	resp, err := utils.HttpClient.Do(req)
	if resp != nil {
		defer resp.Body.Close()
	}
	if err != nil {
		return nil, err
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var socialLoginResponse SocialLoginVerificationApiResponse
	err = json.Unmarshal([]byte(body), &socialLoginResponse)
	if err != nil {
		return nil, err
	}

	return &socialLoginResponse, nil
}

type CreateOrUpdateUserFromSocialLoginReturn struct {
	AuthUser               *models.AuthUser
	SocialAvatar           *string
	SubscribeUserOnMailjet bool
}

func CreateOrUpdateUserFromSocialLogin(ctx context.Context, marsService *MarsService, repo *repositories.AuthUserRepository,
	input *socialLoginVerificationApiResponseData) (*CreateOrUpdateUserFromSocialLoginReturn, error) {
	decodedTokenData := input.DecodedTokenData
	user := input.User

	if user == nil {
		// When user sign's up via social login then they are marked to opt in by default

		newUserIDPtr, err := database.InsertUser(ctx, database.InsertUserRequest{
			Email:          decodedTokenData.Email,
			FirstName:      decodedTokenData.FirstName,
			LastName:       lo.ToPtr(decodedTokenData.LastName),
			RoleIDs:        decodedTokenData.RoleIDs,
			MarketingOptIn: true,
		})
		if err != nil {
			var dbErr *pgconn.PgError
			if errors.As(err, &dbErr) {
				if dbErr.Code == "23505" && "USER_EMAIL_INDEX" == dbErr.ConstraintName {
					return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserAlreadyExists], shared.UserAlreadyExists, http.StatusConflict)
				}
			}
			return nil, err
		}
		newUserID := *newUserIDPtr

		authUserEntity := &models.User{
			ID:    newUserID,
			Email: decodedTokenData.Email,
			RoleIDs: lo.Map(decodedTokenData.RoleIDs, func(roleID models.USER_ROLE, _ int) string {
				return string(roleID)
			}),
		}
		if decodedTokenData.AppleID != nil {
			authUserEntity.AppleID = decodedTokenData.AppleID
		}
		if decodedTokenData.GoogleID != nil {
			authUserEntity.GoogleID = decodedTokenData.GoogleID
		}
		authUser, err := repo.CreateAuthUser(ctx, authUserEntity)
		if err != nil {
			return nil, err
		}

		return &CreateOrUpdateUserFromSocialLoginReturn{
			AuthUser:               authUser,
			SocialAvatar:           decodedTokenData.Avatar,
			SubscribeUserOnMailjet: true,
		}, nil
	} else {
		// Update mars user
		updateMarsUser := false
		updateMarsUserInput := UpdateMarsUserThirdPartyAuthInput{
			UserID: strconv.FormatInt(user.ID, 10),
		}
		if user.AppleID == nil && decodedTokenData.AppleID != nil {
			updateMarsUser = true
			updateMarsUserInput.AuthType = APPLE_THIRD_PARTY_AUTH
			updateMarsUserInput.AuthID = *decodedTokenData.AppleID
		} else if user.GoogleID == nil && decodedTokenData.GoogleID != nil {
			updateMarsUser = true
			updateMarsUserInput.AuthType = GOOGLE_THIRD_PARTY_AUTH
			updateMarsUserInput.AuthID = *decodedTokenData.GoogleID
		}

		if updateMarsUser {
			marsUserApiResp, err := marsService.UpdateMarsUserThirdPartyAuth(ctx, updateMarsUserInput)
			if err != nil {
				return nil, err
			}
			if !marsUserApiResp.Success {
				rlog.Error("failed to update user on mars", "error", marsUserApiResp.Error.Message)
				return nil, errors.New("failed to update user on mars")
			}
		}

		// Create/Update auth user
		authUser, err := repo.GetAuthUserByID(ctx, user.ID)
		if err != nil {
			return nil, err
		}

		if authUser == nil {
			authUserEntity := &models.User{
				ID:          user.ID,
				Email:       user.Email,
				RoleIDs:     user.RoleIDs,
				TeamID:      nil,
				TeamRoleIDs: []string{},
			}
			if decodedTokenData.AppleID != nil {
				authUserEntity.AppleID = decodedTokenData.AppleID
			}
			if decodedTokenData.GoogleID != nil {
				authUserEntity.GoogleID = decodedTokenData.GoogleID
			}
			authUser, err = repo.CreateAuthUser(ctx, authUserEntity)
			if err != nil {
				return nil, err
			}
		} else {
			updateAuthUser := false
			dataToUpdate := models.AuthUser{}
			if user.AppleID == nil && decodedTokenData.AppleID != nil {
				updateAuthUser = true
				dataToUpdate.AppleID = decodedTokenData.AppleID
			} else if user.GoogleID == nil && decodedTokenData.GoogleID != nil {
				updateAuthUser = true
				dataToUpdate.GoogleID = decodedTokenData.GoogleID
			}

			if updateAuthUser {
				_, err = repo.UpdateAuthUserByID(ctx, user.ID, dataToUpdate)
				if err != nil {
					return nil, err
				}

				authUser, err = repo.GetAuthUserByID(ctx, user.ID)
				if err != nil {
					return nil, err
				}
			}
		}

		return &CreateOrUpdateUserFromSocialLoginReturn{
			AuthUser:               authUser,
			SocialAvatar:           decodedTokenData.Avatar,
			SubscribeUserOnMailjet: false,
		}, nil
	}
}
