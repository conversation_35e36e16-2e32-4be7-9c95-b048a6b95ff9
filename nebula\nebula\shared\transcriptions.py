from typing import Optional, List

from nebula.services.elio.service import get_concentrated_transcripts
from elio_client.models.shared_transcription_dto import SharedTranscriptionDTO
import datetime

from loguru import logger
from collections import Counter

"""
    Get all transcriptions for a batch of session IDs and recurrence IDs
    :param session_ids: The session IDs
    :param recurrence_ids: The recurrence IDs
    :return: A tuple containing the transcriptions and the most common locale
"""


def get_all_transcriptions_batch_with_timestamps(
    session_ids, recurrence_ids
) -> tuple[list[str], str]:
    locales = Counter()
    combined_transcript: list[str] = list()
    for session_id, recurrence_id in zip(session_ids, recurrence_ids):
        transcriptions = get_concentrated_transcripts(session_id, recurrence_id)
        formatted_transcript, locale_resp = get_all_transcriptions_with_timestamps(
            transcriptions
        )
        combined_transcript.extend(formatted_transcript)
        locales.update(locale_resp)
    return combined_transcript, locales.most_common(1)[0][0]


def _get_all_transcript_impl(
    transcriptions: list[SharedTranscriptionDTO], include_timestamp: bool
) -> tuple[list[str], str]:
    locales = Counter()
    combined_transcript: list[str] = list()
    combined_offset = 0
    current_speaker = ""
    for transcript in transcriptions:
        if transcript.speaker_uid == current_speaker:
            combined_transcript[combined_offset] += " " + transcript.text
        else:
            combined_offset = len(combined_transcript)
            locales[transcript.language_locale] += 1
            ts = datetime.datetime
            time = (
                "["
                + ts.fromtimestamp((transcript.time_unix or 0) / 1000).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                + "] "
                if include_timestamp is True and transcript.time_unix is not None
                else ""
            )
            combined_transcript.append(
                f"{time}{transcript.speaker_full_name}: {transcript.text.strip()}"
            )
            current_speaker = transcript.speaker_uid

    return combined_transcript, locales.most_common(1)[0][0]


def get_all_transcriptions_with_user_ids(
    transcriptions: list[SharedTranscriptionDTO],
) -> tuple[list[str], str, set[tuple[str, str]]]:
    """
    Get all transcriptions with user IDs included for multi-user processing.
    
    Returns:
        tuple: (formatted_transcripts, locale, unique_users)
        - formatted_transcripts: List of transcript lines with user IDs
        - locale: Most common locale
        - unique_users: Set of (user_id, full_name) tuples
    """
    locales = Counter()
    combined_transcript: list[str] = list()
    unique_users: set[tuple[str, str]] = set()
    combined_offset = 0
    current_speaker = ""
    
    for transcript in transcriptions:
        # Track unique users - use speaker_user_id if available, otherwise fall back to user_id
        user_id = transcript.speaker_user_id or getattr(transcript, 'user_id', None)
        if user_id:
            unique_users.add((user_id, transcript.speaker_full_name))
        
        if transcript.speaker_uid == current_speaker:
            combined_transcript[combined_offset] += " " + transcript.text
        else:
            combined_offset = len(combined_transcript)
            locales[transcript.language_locale] += 1
            
            # Format timestamp
            ts = datetime.datetime
            time = (
                "["
                + ts.fromtimestamp((transcript.time_unix or 0) / 1000).strftime(
                    "%Y-%m-%d %H:%M:%S"
                )
                + "] "
                if transcript.time_unix is not None
                else ""
            )
            
            # Include user ID in transcript format
            user_id_info = f" (userID: {user_id})" if user_id else ""
            
            combined_transcript.append(
                f"{time}{transcript.speaker_full_name}{user_id_info}: {transcript.text.strip()}"
            )
            current_speaker = transcript.speaker_uid

    return combined_transcript, locales.most_common(1)[0][0], unique_users


"""
    Get all transcriptions with timestamps speaker name, switching speaker by speaker change
    :param session_id: The session ID
    :param recurrence_id: The recurrence ID
    :return: A tuple containing the transcriptions and the most common locale
"""


def get_all_transcriptions_with_timestamps(
    transcriptions: list[SharedTranscriptionDTO],
) -> tuple[list[str], str]:
    return _get_all_transcript_impl(transcriptions, include_timestamp=True)


"""
    Like get_all_transcriptions_with_timestamps excluding the timestamp on each transcript line
    :param session_id: The session ID
    :param recurrence_id: The recurrence ID
    :return: A tuple containing the transcriptions and the most common locale
"""


def get_all_transcriptions_with_without_timestamps(
    transcriptions: list[SharedTranscriptionDTO],
) -> tuple[list[str], str]:
    return _get_all_transcript_impl(transcriptions, include_timestamp=False)


"""
    Get all transcriptions as paragraphs, using the first name of the speaker
"""


def get_all_transcriptions_paragraphs_firstname(
    transcriptions: List[SharedTranscriptionDTO],
) -> str:
    combined_transcript = ""
    current_speaker = ""
    for transcript in transcriptions:
        if transcript.speaker_uid == current_speaker:
            combined_transcript += " " + transcript.text
        else:
            fname = (
                transcript.speaker_first_name
                if transcript.speaker_first_name is not None
                else transcript.speaker_full_name
            )
            combined_transcript += f"\n\n{fname}: {transcript.text.strip()}"
            current_speaker = transcript.speaker_uid

    return combined_transcript


"""
    Method to calculate the duration between two Unix timestamps
    Returns the duration as an integer
"""


def calculate_duration(first_time_unix, last_time_unix) -> Optional[int]:
    try:
        if isinstance(first_time_unix, int) and isinstance(last_time_unix, int):
            duration = last_time_unix - first_time_unix
            return duration
        else:
            logger.error("Provided timestamps are not in Unix format.")
            return None
    except Exception as e:
        logger.error(f"Error in calculate_duration: {str(e)}")
        return None


# Method to find the language name by code
def find_language_name_by_code(code):
    ##language support and filter
    supported_languages = {
        "zh-HK": "Chinese (Cantonese, Traditional)",
        "zh-CN": "Chinese (Mandarin, Simplified)",
        "zh-TW": "Chinese (Taiwanese Putonghua)",
        "en-IN": "English (India)",
        "en-US": "English (US)",
        "fr-FR": "French (French)",
        "de-DE": "German (Germany)",
        "th-TH": "Hai (Thailand)",
        "hi-IN": "Hindi (India)",
        "id-ID": "Indonesian (Indonesia)",
        "it-IT": "Italian (Italy)",
        "ja-JP": "Japanese (Japan)",
        "ko-KR": "Korean (South Korea)",
        "ms-MY": "Malay (Malaysia)",
        "fa-IR": "Persian (Iran)",
        "pl-PL": "Polish (Polski)",
        "pt-PT": "Portuguese (Portugal)",
        "ru-RU": "Russian (Russia)",
        "es-ES": "Spanish (Spain)",
        "tr-TR": "Turkish (Turkey)",
        "vi-VN": "Vietnamese (Vietnam)",
    }
    return supported_languages.get(code)
