from typing import Any, Dict, List, Optional
from nebula.services.search.common import (
    Participant,
    StateUpdatedAtItem,
    TypesenseSessionRecurrenceData,
    SessionRecurrence as SearchSessionRecurrenceType,
)

from nebula.services.x_ray.types import XRayModel
from nebula.web.api.x_ray.schema import XRayResponse
from pydantic import BaseModel

from elio_client.models.shared_transcription_dto import SharedTranscriptionDTO
from nebula.services.meeting_metadata.models import MeetingMetadataExtraction
from nebula.services.meeting_suggestions.models import (
    AllUsersMeetingSuggestionsExtraction,
    ParticipantInfo,
)


class MetadataWorkflowParams(BaseModel):
    """Session identification information"""

    session_id: str
    recurrence_id: str


class BackFillMetadataWorkflowParams(BaseModel):
    """Params for backfill metadata workflow"""

    interval: str


class AiEndMeetingWorkflowParams(BaseModel):
    """Params for ai end meeting workflow"""

    session_id: str
    recurrence_id: str


class PostSessionSummaryWorkflowParams(BaseModel):
    """Params for post session summary workflow"""

    session_id: str
    recurrence_id: str


class CreateMetadataInput(BaseModel):
    """Input model for create_metadata_record activity"""

    session_info: MetadataWorkflowParams
    metadata: MeetingMetadataExtraction


class CreatePostSessionSummaryInput(BaseModel):
    """Input model for create_post_session_summary_record activity"""

    session_id: str
    recurrence_id: str


class TranscriptionBatchesResult(BaseModel):
    """Result model for get_transcription_batches activity"""

    transcription_batches: List[SharedTranscriptionDTO]


class SessionDataModel(BaseModel):
    """Session data model for metadata activities"""

    meeting_id: str
    title: str
    participants: List[str]
    type: str


class PostSessionDataModel(BaseModel):
    """Session data model for post session summary activities"""

    session_id: str
    recurrence_id: str
    participants: List[Any]
    meeting_type: Optional[str] = None


class MetadataRecordResult(BaseModel):
    """Result model for create_metadata_record activity"""

    metadata_id: int


class PostSessionSummaryRecordResult(BaseModel):
    """Result model for create_post_session_summary_record activity"""

    summary_id: int


class ExtractMetadataInput(BaseModel):
    """Input model for extract_metadata activity"""

    transcript: str
    session_data: SessionDataModel


class GeneratePMSInput(BaseModel):
    """Input model for generate_post_meeting_summary activity"""

    transcript: str
    recurrence_id: str
    locale: str
    meeting_type: Optional[str] = None


class ProcessedTranscriptResult(BaseModel):
    """Result model for process_transcript activity"""

    transcript: str
    locale: str


class PostSessionSummaryGenerationResult(BaseModel):
    """Result model for post meeting summary generation"""

    summary: Dict[str, Any]
    processing_time_seconds: float


class MetadataGenerationResultData(BaseModel):
    """Data model for metadata generation result"""

    metadata_id: int
    metadata: MeetingMetadataExtraction
    processing_time_seconds: float


class MetadataGenerationResult(BaseModel):
    """Final result of metadata generation workflow"""

    done: bool
    message: str
    data: Optional[MetadataGenerationResultData] = None


class PostSessionSummaryWorkflowResult(BaseModel):
    """Final result of post session summary workflow"""

    done: bool
    message: str
    summary_id: Optional[int] = None
    processing_time_seconds: Optional[float] = None


class CreateElioUpdateInput(BaseModel):
    """Input model for create_elio_update activity"""

    session_info: MetadataWorkflowParams
    metadata: MeetingMetadataExtraction


# TODO: find a better place for this
class SessionRecurrence(BaseModel):
    recurrenceID: int
    sessionID: int
    title: str
    about: Optional[str] = None
    startTimestamp: int
    endTimestamp: int
    createdAt: int
    creatorID: Optional[int] = None
    creatorFirstName: Optional[str] = None
    creatorEmail: Optional[str] = None
    creatorLastName: Optional[str] = None
    creatorAvatar: Optional[str] = None
    stateUpdatedAt: List[StateUpdatedAtItem]
    dataVisibility: str
    participants: Optional[List[Participant]] = None


class TypesenseIndexingWorkflowParams(BaseModel):
    """Params for typesense indexing workflow"""

    session_id: str
    recurrence_id: str


class TypesenseIndexingWorkflowResult(BaseModel):
    """Final result of typesense indexing workflow"""

    done: bool
    message: str
    indexed_documents: Optional[int] = None
    processing_time_seconds: Optional[float] = None


class TypesenseRecurrenceData(BaseModel):
    """Recurrence data for typesense indexing"""

    recurrence_id: int
    session_id: int
    recurrence_data: TypesenseSessionRecurrenceData


class TypesenseProcessedTranscripts(BaseModel):
    """Processed transcripts for typesense indexing"""

    transcript_fulltext: str
    processed_count: int


class TypesenseIndexingResult(BaseModel):
    """Result of typesense indexing operation"""

    indexed_documents: int
    processing_time_seconds: float


class GetRecurrenceDataInput(BaseModel):
    """Input model for get_recurrence_data activity"""

    session_id: str
    recurrence_id: str


class ProcessTranscriptsForTypesenseInput(BaseModel):
    """Input model for process_transcripts_for_typesense activity"""

    transcripts: List[SharedTranscriptionDTO]


class IndexMeetingInTypesenseInput(BaseModel):
    """Input model for index_meeting_in_typesense activity"""

    recurrence_data: TypesenseRecurrenceData
    processed_transcripts: TypesenseProcessedTranscripts


class TLDRGenerationWorkflowParams(BaseModel):
    """Params for TLDR generation workflow"""

    session_id: str
    recurrence_id: str


class TLDRGenerationWorkflowResult(BaseModel):
    """Final result of TLDR generation workflow"""

    done: bool
    message: str
    tldr_content: Optional[str] = None
    summary_id: Optional[str] = None
    processing_time_seconds: Optional[float] = None


class TLDRGenerationResult(BaseModel):
    """Result of TLDR generation activity"""

    tldr_content: str
    processing_time_seconds: float
    summary_id: Optional[str] = None


class GenerateTLDRInput(BaseModel):
    """Input model for generate_tldr_summary activity"""

    transcript: str
    locale: str
    meeting_type: Optional[str] = None


class UpdatePostSessionSummaryWithTLDRInput(BaseModel):
    """Input model for update_post_session_summary_with_tldr activity"""

    session_id: str
    recurrence_id: str
    tldr_content: str


class NotifyLuxorTLDRReadyInput(BaseModel):
    """Input model for notify_luxor_tldr_ready activity"""

    summary_id: str
    session_id: str
    recurrence_id: str


class CreateOrUpdatePostSessionSummaryInput(BaseModel):
    """Input model for create_or_update_post_session_summary activity"""

    session_id: str
    recurrence_id: str
    content: Optional[str] = None
    tldr: Optional[str] = None


class GetActionItemsInput(BaseModel):
    """Input model for get_action_items_for_pss activity"""

    session_id: str
    recurrence_id: str


class NotifyDraconidsInput(BaseModel):
    """Input model for notify_draconids_summary_ready activity"""

    session_id: str
    recurrence_id: str


class MeetingSuggestionsWorkflowParams(BaseModel):
    """Params for meeting suggestions workflow"""

    session_id: str
    recurrence_id: str


class MeetingSuggestionsWorkflowResult(BaseModel):
    """Final result of meeting suggestions workflow"""

    done: bool
    message: str
    processing_time_seconds: Optional[float] = None


class AllUsersMeetingSuggestionsRecordResult(BaseModel):
    """Result model for create_all_users_meeting_suggestions_record activity"""

    total_records_created: int
    participant_count: int


class ParticipantsResult(BaseModel):
    """Result model for get_all_participants activity"""

    participants: List[ParticipantInfo]


class ExtractAllUsersMeetingSuggestionsInput(BaseModel):
    """Input model for extract_all_users_meeting_suggestions activity"""

    title: str
    date: str
    transcript: str
    participants: List[ParticipantInfo]


class CreateAllUsersMeetingSuggestionsInput(BaseModel):
    """Input model for create_all_users_meeting_suggestions_record activity"""

    session_info: MeetingSuggestionsWorkflowParams
    suggestions: AllUsersMeetingSuggestionsExtraction


class ProcessedTranscriptWithUsersResult(BaseModel):
    """Result model for process_transcript_with_user_ids activity"""

    transcript: str
    locale: str
    participants: List[ParticipantInfo]


class ProcessTranscriptsForSuggestionsInput(BaseModel):
    """Input model for process_transcripts_for_suggestions activity"""

    transcripts: List[SharedTranscriptionDTO]


class GenerateSuggestionsInput(BaseModel):
    """Input model for generate_suggestions activity"""

    recurrence_data: SearchSessionRecurrenceType
    transcript_fulltext: str


# X-Ray related types


class XRayScanWorkflowParams(BaseModel):
    """Params for X-Ray scan workflow"""

    user_id: int
    recurrence_id: int
    team_id: Optional[int] = None


class XRayBackfillScanWorkflowParams(BaseModel):
    """Params for X-Ray backfill scan workflow"""

    xray_id: int
    user_id: int
    tz: str


class XRayBackfillScanWorkflowResult(BaseModel):
    """Result for X-Ray backfill scan workflow"""

    success: bool
    duration: float
    initial_len: int
    processed_len: int


class XRayQuickScanActivityParams(BaseModel):
    """Input params for X-Ray quick scan activity"""

    recurrence_id: int
    transcript: str
    xrays: List[XRayResponse.XRay]


class XRayQuickScanResult(BaseModel):
    """Result of X-Ray quick scan - used as LLM response format"""

    xray_ids: List[int]


class XRayDeepScanParams(BaseModel):
    """Input params for X-Ray deep scan activity"""

    xray: XRayModel
    recurrence_id: int
    transcript: str
    quick_result_scan: XRayQuickScanResult


class XRayDeepScanLLMResult(BaseModel):
    """LLM response format for X-Ray deep scan"""

    new_doc_commit_content: str


class XRayDeepScanActivityResult(BaseModel):
    """Result of X-Ray deep scan activity"""

    success: bool
    changed: bool
    message: str
    prev_commit_content: Optional[str]
    new_commit_content: Optional[str]


class XRayScanWorkflowResult(BaseModel):
    """Result of X-Ray scan workflow"""

    success: bool
    message: str
    xrays_processed: int
    processing_time_seconds: Optional[float] = None


class GetUserIdsForXRayScansActivityInput(BaseModel):
    """Input model for get_user_ids_for_xray_scans activity"""

    session_id: int
    recurrence_id: int


# X-Ray Notification Models
class XRayNotificationItem(BaseModel):
    """Individual notification item generated by LLM"""

    title: str  # "Deal closed — Dennis Drake, Abe Roder"
    content: str  # "• SmartTax will onboard two users\n• **Impact:** Won new business"


class XRayNotificationsResult(BaseModel):
    """LLM response format for generating multiple notifications"""

    notifications: List[XRayNotificationItem]  # 0-3 notifications
    reasoning: Optional[str] = None  # For debugging/understanding AI decisions


class GenerateScheduledDigestContentInput(BaseModel):
    xray_id: int
    team_id: Optional[int] = None


# X-Ray Digest Workflow Types
class XRayDigestWorkflowParams(BaseModel):
    """Params for X-Ray digest workflow"""

    xray_id: int


class XRayDigestWorkflowResult(BaseModel):
    """Result of X-Ray digest workflow"""

    success: bool
    message: str
    digest_content: Optional[str] = None
    commit_id: Optional[int] = None
    processing_time_seconds: Optional[float] = None


class CreateDigestCommitInput(BaseModel):
    """Input model for create_digest_commit activity"""

    xray_id: int
    digest_content: str
    author_id: int
