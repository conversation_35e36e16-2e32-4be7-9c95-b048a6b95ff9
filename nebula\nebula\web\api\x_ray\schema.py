from typing import Any, List, Optional, Dict
from nebula.db.models.xray_doc_commit import XRayDocumentCommit
from nebula.db.models.xray_template import XRayTemplate
from nebula.utils import to_camel
from pydantic import BaseModel
from pydantic import ConfigDict
from nebula.db.models.xray import XRay
import json


class XRayCreateStep1Body(BaseModel):
    """Request model for X-Ray description generation"""

    description: str


class XRayCreateStep1Response(BaseModel):
    """Response model for X-Ray xrayType and prompt generation"""

    success: bool
    message: str
    data: Optional["XRayCreateStep1Response.Data"] = None

    class Data(BaseModel):
        xrayType: str
        prompt: str


class XRayCreateStep2Body(BaseModel):
    """Request model for X-Ray title, emoji, and short summary generation"""

    xrayType: str
    prompt: str


class XRayCreateStep2Response(BaseModel):
    """Response model for X-Ray title, emoji, and short summary generation"""

    success: bool
    message: str
    data: Optional["XRayCreateStep2Response.Data"] = None

    class Data(BaseModel):
        title: str
        emoji: str
        shortSummary: str


class XRayCreateStep3Body(BaseModel):
    """Request model for creating an X-Ray with all generated data"""

    description: str
    xrayType: str
    prompt: str
    title: str
    emoji: str
    shortSummary: str
    alertChannels: Optional[Dict[str, bool]] = None
    # Digest-specific fields (optional for non-digest types)
    frequency: Optional[str] = None  # Cron expression for digest scheduling
    timezone: Optional[str] = (
        None  # Timezone identifier (e.g., 'America/New_York', 'UTC')
    )


class XRayUpdateRequestBody(BaseModel):
    """Request model for updating an X-Ray"""

    title: Optional[str] = None
    alertChannels: Optional[Dict[str, bool]] = None
    isActive: Optional[bool] = None
    # Digest-specific fields for updates
    frequency: Optional[str] = None  # Cron expression for digest scheduling
    timezone: Optional[str] = None  # Timezone identifier


class XRayDocCommit(BaseModel):
    """Model for an X-Ray document commit"""

    id: int
    xrayId: int
    content: str
    authorId: int
    createdAt: int
    updatedAt: int


class XRayResponse(BaseModel):
    """Used across all x-ray endpoints where output is a single x-ray"""

    success: bool
    message: str
    data: Optional["XRayResponse.Data"] = None

    class XRay(BaseModel):
        model_config = ConfigDict(
            from_attributes=True, populate_by_name=True, alias_generator=to_camel
        )
        id: int
        owner_id: int
        title: str
        description: str
        prompt: str
        icon: str
        short_summary: str
        current_commit_id: Optional[int] = None
        current_commit: Optional[XRayDocCommit] = None
        alert_channels: Dict[str, bool]
        is_active: bool
        visibility: str
        xray_type: str
        scope: str
        unread_notifications_count: Optional[int] = None
        # Digest-specific fields
        frequency: Optional[str] = None  # Cron expression for digest scheduling
        timezone: Optional[str] = None  # Timezone identifier
        last_digest_at: Optional[int] = None  # Timestamp when digest was last generated
        created_at: int
        updated_at: int

    class Data(BaseModel):
        xray: "XRayResponse.XRay"


class XRayTemplateResponse(BaseModel):
    """Used across all x-ray endpoints where output is a list of x-ray templates"""

    success: bool
    message: str
    data: Optional["XRayTemplateResponse.Data"] = None

    class XRayTemplate(BaseModel):
        model_config = ConfigDict(
            from_attributes=True, populate_by_name=True, alias_generator=to_camel
        )
        id: int
        owner_id: Optional[int]
        title: str
        description: str
        prompt: str
        short_summary: str
        xray_type: str
        icon: str
        created_at: int
        updated_at: int

    class Data(BaseModel):
        model_config = ConfigDict(
            from_attributes=True, populate_by_name=True, alias_generator=to_camel
        )
        template: "XRayTemplateResponse.XRayTemplate"


class XRayListResponse(BaseModel):
    """Response model for listing X-Rays"""

    success: bool
    message: str
    data: Optional["XRayListResponse.Data"] = None

    class Data(BaseModel):
        xrays: List["XRayResponse.XRay"]
        total: int
        hasMore: bool


class XRayTemplateListResponse(BaseModel):
    """Response model for listing X-Ray templates"""

    success: bool
    message: str
    data: Optional["XRayTemplateListResponse.Data"] = None

    class Data(BaseModel):
        templates: List["XRayTemplateResponse.XRayTemplate"]
        total: int
        hasMore: bool


class XRayNotificationResponse(BaseModel):
    """Response model for X-Ray notifications"""

    success: bool
    message: str
    data: Optional["XRayNotificationResponse.Data"] = None

    class Notification(BaseModel):
        model_config = ConfigDict(
            from_attributes=True, populate_by_name=True, alias_generator=to_camel
        )
        id: int
        xray_doc_commit_id: int
        user_id: int
        title: str
        seen: bool
        content: str
        source: Dict[str, Any]
        created_at: int
        updated_at: int

    class Data(BaseModel):
        notifications: List["XRayNotificationResponse.Notification"]
        total: int
        hasMore: bool


class MarkNotificationsSeenResponse(BaseModel):
    """Response model for marking notifications as seen"""

    success: bool
    message: str
    data: Optional["MarkNotificationsSeenResponse.Data"] = None

    class Data(BaseModel):
        markedCount: int


# TODO: XRay that lives inside the XRayResponse.XRay class is probably not a good idea
def xray_model_from_db_model(
    xray: XRay, current_commit: Optional[XRayDocumentCommit]
) -> XRayResponse.XRay:
    return XRayResponse.XRay(
        id=xray.id,
        owner_id=xray.owner_id,
        title=xray.title,
        description=xray.description,
        prompt=xray.prompt,
        icon=xray.icon,
        short_summary=xray.short_summary,
        current_commit_id=xray.current_commit_id,
        current_commit=XRayDocCommit(
            id=current_commit.id,
            xrayId=current_commit.xray_id,
            content=current_commit.content,
            authorId=current_commit.author_id,
            createdAt=int(current_commit.created_at.timestamp()),
            updatedAt=int(current_commit.updated_at.timestamp()),
        )
        if current_commit
        else None,
        alert_channels=json.loads(xray.alert_channels) if xray.alert_channels else {},
        is_active=xray.is_active,
        visibility=xray.visibility,
        xray_type=xray.xray_type,
        scope=xray.scope,
        # Digest-specific fields
        frequency=xray.frequency,
        timezone=xray.timezone,
        last_digest_at=int(xray.last_digest_at.timestamp())
        if xray.last_digest_at
        else None,
        created_at=int(xray.created_at.timestamp()),
        updated_at=int(xray.updated_at.timestamp()),
    )


def xray_model_from_xray_row_dict(xray: Dict[str, Any]) -> XRayResponse.XRay:
    return XRayResponse.XRay(
        id=xray["id"],
        owner_id=xray["owner_id"],
        title=xray["title"],
        description=xray["description"],
        prompt=xray["prompt"],
        icon=xray["icon"],
        short_summary=xray["short_summary"],
        alert_channels=json.loads(xray["alert_channels"]),
        current_commit_id=xray["current_commit_id"],
        current_commit=XRayDocCommit(
            id=xray["current_commit_id"],
            xrayId=xray["current_commit_id"],
            content=xray["current_commit_content"],
            authorId=xray["current_commit_author_id"],
            createdAt=int(xray["current_commit_created_at"].timestamp()),
            updatedAt=int(xray["current_commit_updated_at"].timestamp()),
        )
        if xray.get(
            "current_commit_content"
        )  # getting by this key because current_commit_id is present even when there's no sql join
        else None,
        is_active=xray["is_active"],
        visibility=xray["visibility"],
        xray_type=xray["xray_type"],
        scope=xray["scope"],
        unread_notifications_count=xray.get("unread_notifications_count", 0),
        # Digest-specific fields
        frequency=xray.get("frequency"),
        timezone=xray.get("timezone"),
        last_digest_at=int(xray["last_digest_at"].timestamp())
        if xray.get("last_digest_at")
        else None,
        created_at=int(xray["created_at"].timestamp()),
        updated_at=int(xray["updated_at"].timestamp()),
    )


def xray_template_model_from_db_model(
    template: XRayTemplate,
) -> XRayTemplateResponse.XRayTemplate:
    return XRayTemplateResponse.XRayTemplate(
        id=template.id,
        owner_id=template.owner_id,
        title=template.title,
        xray_type=template.xray_type,
        short_summary=template.short_summary,
        description=template.description,
        prompt=template.prompt,
        icon=template.icon,
        created_at=int(template.created_at.timestamp()),
        updated_at=int(template.updated_at.timestamp()),
    )
