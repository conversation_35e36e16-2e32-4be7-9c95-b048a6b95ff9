# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from elio_client.models.shared_billing_cycle_dto import SharedBillingCycleDTO
from elio_client.models.shared_price_dto_trial_period import SharedPriceDTOTrialPeriod
from elio_client.models.shared_price_quantity_dto import SharedPriceQuantityDTO
from elio_client.models.shared_unit_price_dto import SharedUnitPriceDTO
from elio_client.models.shared_unit_price_override_dto import SharedUnitPriceOverrideDTO
from typing import Optional, Set
from typing_extensions import Self

class SharedPriceDTO(BaseModel):
    """
    SharedPriceDTO
    """ # noqa: E501
    billing_cycle: Optional[SharedBillingCycleDTO] = Field(default=None, alias="billingCycle")
    id: Optional[StrictStr] = None
    product_id: Optional[StrictStr] = Field(default=None, alias="productID")
    quantity: Optional[SharedPriceQuantityDTO] = None
    status: Optional[StrictStr] = None
    tax_mode: Optional[StrictStr] = Field(default=None, alias="taxMode")
    trial_period: Optional[SharedPriceDTOTrialPeriod] = Field(default=None, alias="trialPeriod")
    unit_price: Optional[SharedUnitPriceDTO] = Field(default=None, alias="unitPrice")
    unit_price_overrides: Optional[List[SharedUnitPriceOverrideDTO]] = Field(default=None, alias="unitPriceOverrides")
    __properties: ClassVar[List[str]] = ["billingCycle", "id", "productID", "quantity", "status", "taxMode", "trialPeriod", "unitPrice", "unitPriceOverrides"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SharedPriceDTO from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of billing_cycle
        if self.billing_cycle:
            _dict['billingCycle'] = self.billing_cycle.to_dict()
        # override the default output from pydantic by calling `to_dict()` of quantity
        if self.quantity:
            _dict['quantity'] = self.quantity.to_dict()
        # override the default output from pydantic by calling `to_dict()` of trial_period
        if self.trial_period:
            _dict['trialPeriod'] = self.trial_period.to_dict()
        # override the default output from pydantic by calling `to_dict()` of unit_price
        if self.unit_price:
            _dict['unitPrice'] = self.unit_price.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in unit_price_overrides (list)
        _items = []
        if self.unit_price_overrides:
            for _item_unit_price_overrides in self.unit_price_overrides:
                if _item_unit_price_overrides:
                    _items.append(_item_unit_price_overrides.to_dict())
            _dict['unitPriceOverrides'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SharedPriceDTO from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "billingCycle": SharedBillingCycleDTO.from_dict(obj["billingCycle"]) if obj.get("billingCycle") is not None else None,
            "id": obj.get("id"),
            "productID": obj.get("productID"),
            "quantity": SharedPriceQuantityDTO.from_dict(obj["quantity"]) if obj.get("quantity") is not None else None,
            "status": obj.get("status"),
            "taxMode": obj.get("taxMode"),
            "trialPeriod": SharedPriceDTOTrialPeriod.from_dict(obj["trialPeriod"]) if obj.get("trialPeriod") is not None else None,
            "unitPrice": SharedUnitPriceDTO.from_dict(obj["unitPrice"]) if obj.get("unitPrice") is not None else None,
            "unitPriceOverrides": [SharedUnitPriceOverrideDTO.from_dict(_item) for _item in obj["unitPriceOverrides"]] if obj.get("unitPriceOverrides") is not None else None
        })
        return _obj


