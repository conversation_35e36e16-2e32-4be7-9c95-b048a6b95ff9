# PUTMeetingsUpdateUserByIDRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**user_data** | [**ApiUpdateUserRequestData**](ApiUpdateUserRequestData.md) |  | 

## Example

```python
from elio_client.models.put_meetings_update_user_by_id_request import PUTMeetingsUpdateUserByIDRequest

# TODO update the JSON string below
json = "{}"
# create an instance of PUTMeetingsUpdateUserByIDRequest from a JSON string
put_meetings_update_user_by_id_request_instance = PUTMeetingsUpdateUserByIDRequest.from_json(json)
# print the JSON string representation of the object
print(PUTMeetingsUpdateUserByIDRequest.to_json())

# convert the object into a dict
put_meetings_update_user_by_id_request_dict = put_meetings_update_user_by_id_request_instance.to_dict()
# create an instance of PUTMeetingsUpdateUserByIDRequest from a dict
put_meetings_update_user_by_id_request_from_dict = PUTMeetingsUpdateUserByIDRequest.from_dict(put_meetings_update_user_by_id_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


