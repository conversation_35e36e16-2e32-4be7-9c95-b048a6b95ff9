from typing import TypedDict
from nebula.db.models.post_session_summary import PostSessionSummary
from piccolo.apps.migrations.auto.migration_manager import MigrationManager
from nebula.settings import settings


ID = "2025-03-10T12:50:13:081571"
VERSION = "1.23.0"
DESCRIPTION = "Adds a unique constraint so that no two rows in post_session_summary table can have the same combination of session_id and recurrence_id"


class PmsRow(TypedDict):
    session_recurrence_id: str
    count: int


async def get_all_recurrences_with_pms_duplicates() -> list[PmsRow]:
    q = f"""SELECT session_recurrence_id, COUNT(*) as count
    FROM "{settings.postgres_schema}".post_session_summary
    GROUP BY session_recurrence_id
    HAVING COUNT(*) > 1
    ORDER BY count DESC;
    """
    rows = await PostSessionSummary.raw(q)
    return rows


async def cleanup_pms_duplicates():
    recs = await get_all_recurrences_with_pms_duplicates()

    # Loop over recurrences with duplicate pms
    for rec in recs:
        # PMS content and tldr are spread across different pms rows, we're gonna store them here as we're looping over every pms row
        content = None
        tldr = None

        # PMS rows
        pms_rows = await PostSessionSummary.objects().where(
            PostSessionSummary.session_recurrence_id == rec["session_recurrence_id"]
        )

        # Finding content and tldr across all recurrence summaries rows (duplicates)
        for pms in pms_rows:
            if pms.content and pms.content != "":
                content = pms.content
            if pms.tldr and pms.tldr != "":
                tldr = pms.tldr

        designated_pms = None
        # Either content or tldr has to exist in order for us to save one PMS
        # there are duplicate PMS rows that are simply empty - no content and no tldr. We simply want to delete all of them.
        if content or tldr:
            # remove the designated pms from pms_rows which will be deleted
            designated_pms = pms_rows.pop()
            if content:
                designated_pms.content = content

            if tldr:
                designated_pms.tldr = tldr
            await designated_pms.save()

        deleted_pms_ids = [pms.id for pms in pms_rows]
        print(f"deleting pms with ids: {deleted_pms_ids}")

        del_res = await PostSessionSummary.delete().where(
            PostSessionSummary.id.is_in(deleted_pms_ids)
        )
        print(
            f"recurrence data: {rec['session_recurrence_id']} -> content_len: {len(content) if content else 0}, tldr_len: {len(tldr) if tldr else 0}"
        )
        print("deleted rows result ->", del_res)


async def forwards():
    manager = MigrationManager(migration_id=ID, app_name="", description=DESCRIPTION)

    if settings.environment == "test" or settings.environment == "local":
        print(f"skipping forwards {ID} -> Cleaning up pms duplicates")
        return manager

    async def run_forwards():
        print(f"running forwards {ID} -> Adding unique constraint")
        await PostSessionSummary.raw(f'''ALTER TABLE "{settings.postgres_schema}".post_session_summary
        ADD CONSTRAINT unique_session_composite
        UNIQUE (session_id, session_recurrence_id);
        ''')

    async def run_backwards():
        if settings.environment == "test":
            print(f"skipping backwards {ID}")
            return

        await PostSessionSummary.raw(f'''ALTER TABLE "{settings.postgres_schema}".post_session_summary
        DROP CONSTRAINT unique_session_composite;
        ''')

    manager.add_raw(run_forwards)
    manager.add_raw_backwards(run_backwards)

    return manager
