package database

import "encore.app/pkg/database"

var GetUserSubscriptionPlan = database.NewStatement(`
	SELECT
		usp.id as "planID",
        usp."paddleSubscriptionID",
        usp."userID",
        usp."planConfigID",
        usp."planConfigOverrides",
        usp."trialEndsAt",
        usp.status,
        usp."customerID",
        usp."addressID",
        usp."businessID",
        usp."currencyCode",
        usp."startedAt",
        usp."firstBilledAt",
        usp."nextBilledAt",
        usp."pausedAt",
        usp."canceledAt",
        usp."collectionMode",
        usp."billingDetails",
        usp."currentBillingPeriod",
        usp."billingCycle",
        usp."scheduledChange",
        usp.items,
        usp."customData",
        usp."createdAt",
        usp."updatedAt"
	FROM "user_subscription_plans" usp
	WHERE id = :planID
`)

var SubscriptionPlanConfigByUserID = database.NewStatement(`
	SELECT
		sp."aiFeed", sp."customFeedItems", sp."customIntegrations", sp."integrations",
		sp."meetingMemory", sp."meetingSummary", sp."meetingTemplates", sp."meetingWorkflows", sp."meetings", sp."modelSegregation",
		sp."offTheRecord", sp."paddleProductID", sp."paddleProductName", sp."queueMode", sp."recording", sp."stream", sp."support", sp."crm", sp."bots",
sp."timeLimit", sp."createdAt", sp."updatedAt",
        usp."planConfigID", usp."planConfigOverrides", usp.id as "planID"
	FROM "user_subscription_plans" usp
	JOIN "subscription_plan_configs" sp ON usp."planConfigID" = sp.id
	WHERE usp."userID" = :userID
	LIMIT 1
`)

var SubscriptionPlanConfigByCustomerID = database.NewStatement(`
	SELECT
		sp."aiFeed", sp."customFeedItems", sp."customIntegrations", sp."integrations",
		sp."meetingMemory", sp."meetingSummary", sp."meetingTemplates", sp."meetingWorkflows", sp."meetings", sp."modelSegregation",
		sp."offTheRecord", sp."paddleProductID", sp."paddleProductName", sp."queueMode", sp."recording", sp."stream", sp."support", sp."crm", sp."bots",
sp."timeLimit", sp."createdAt", sp."updatedAt",
        usp."planConfigID", usp."planConfigOverrides", usp.id as "planID"
	FROM "user_subscription_plans" usp
	JOIN "subscription_plan_configs" sp ON usp."planConfigID" = sp.id
	WHERE usp."customerID" = :customerID
	LIMIT 1
`)

var SubscriptionPlanConfigByPaddleProductName = database.NewStatement(`
	SELECT
		sp."aiFeed", sp."customFeedItems", sp."customIntegrations", sp."integrations",
		sp."meetingMemory", sp."meetingSummary", sp."meetingTemplates", sp."meetingWorkflows", sp."meetings", sp."modelSegregation",
		sp."offTheRecord", sp."paddleProductID", sp."paddleProductName", sp."queueMode", sp."recording", sp."stream", sp."support", sp."crm", sp."bots",
		sp."id" as "planConfigID"
	FROM "subscription_plan_configs" sp
	WHERE sp."paddleProductName" = :paddleProductName
	LIMIT 1
`)

func init() {
	Statements = append(Statements, database.Statements{
		GetUserSubscriptionPlan,
		SubscriptionPlanConfigByUserID,
		SubscriptionPlanConfigByCustomerID,
		SubscriptionPlanConfigByPaddleProductName,
	}...)
}
