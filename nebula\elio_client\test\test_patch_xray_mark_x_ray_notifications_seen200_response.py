# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.patch_xray_mark_x_ray_notifications_seen200_response import PATCHXrayMarkXRayNotificationsSeen200Response

class TestPATCHXrayMarkXRayNotificationsSeen200Response(unittest.TestCase):
    """PATCHXrayMarkXRayNotificationsSeen200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> PATCHXrayMarkXRayNotificationsSeen200Response:
        """Test PATCHXrayMarkXRayNotificationsSeen200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `PATCHXrayMarkXRayNotificationsSeen200Response`
        """
        model = PATCHXrayMarkXRayNotificationsSeen200Response()
        if include_optional:
            return PATCHXrayMarkXRayNotificationsSeen200Response(
                data = elio_client.models.api/mark_x_ray_notifications_seen_response_data.api.MarkXRayNotificationsSeenResponseData(
                    marked_count = 56, ),
                message = '',
                success = True
            )
        else:
            return PATCHXrayMarkXRayNotificationsSeen200Response(
                data = elio_client.models.api/mark_x_ray_notifications_seen_response_data.api.MarkXRayNotificationsSeenResponseData(
                    marked_count = 56, ),
                message = '',
                success = True,
        )
        """

    def testPATCHXrayMarkXRayNotificationsSeen200Response(self):
        """Test PATCHXrayMarkXRayNotificationsSeen200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
