import aiopg
import datetime

from nebula.utils import relative_tz_dt_from_unix
from nebula.services.x_ray.xray_service import XRayService


class BaseActivity:
    """
    Base class for all activities.
    """

    def __init__(
        self,
        mars_db: aiopg.Pool,
        luxor_db: aiopg.Pool = None,
    ) -> None:
        self.mars_db = mars_db
        self.luxor_db = luxor_db
        self.xray_service = XRayService()

    def get_current_time_prompt(self, iana: str = "Europe/Belgrade") -> str:
        """Generate current time prompt for LLM context."""
        unix_now = int(datetime.datetime.now().timestamp())
        return f"Current time: {relative_tz_dt_from_unix(unix_now, iana)}"
