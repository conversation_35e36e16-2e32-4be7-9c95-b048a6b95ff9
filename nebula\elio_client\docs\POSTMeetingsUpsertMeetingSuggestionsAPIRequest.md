# POSTMeetingsUpsertMeetingSuggestionsAPIRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**recurrence_id** | **str** |  | 
**session_id** | **str** |  | 
**suggestions** | [**List[ApiMeetingSuggestionInput]**](ApiMeetingSuggestionInput.md) |  | 

## Example

```python
from elio_client.models.post_meetings_upsert_meeting_suggestions_api_request import POSTMeetingsUpsertMeetingSuggestionsAPIRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTMeetingsUpsertMeetingSuggestionsAPIRequest from a JSON string
post_meetings_upsert_meeting_suggestions_api_request_instance = POSTMeetingsUpsertMeetingSuggestionsAPIRequest.from_json(json)
# print the JSON string representation of the object
print(POSTMeetingsUpsertMeetingSuggestionsAPIRequest.to_json())

# convert the object into a dict
post_meetings_upsert_meeting_suggestions_api_request_dict = post_meetings_upsert_meeting_suggestions_api_request_instance.to_dict()
# create an instance of POSTMeetingsUpsertMeetingSuggestionsAPIRequest from a dict
post_meetings_upsert_meeting_suggestions_api_request_from_dict = POSTMeetingsUpsertMeetingSuggestionsAPIRequest.from_dict(post_meetings_upsert_meeting_suggestions_api_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


