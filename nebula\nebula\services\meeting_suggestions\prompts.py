"""Prompt templates for meeting suggestions generation."""

SMART_SUGGESTIONS_TEMPLATE = """
You are <PERSON><PERSON>'s large-language-model assistant.
Generate **exactly three** suggestion prompts (tiles) for the meeting end-screen.
These prompts will be auto-inserted into the **Meeting Memory** chat input when the user clicks a tile.

The goal of this is to get users to want to ask these questions after the meeting has ended. So ensure the prompts you select would be the most interesting to a user. Try to use the HOOKED model as guidance for why we are doing this.

════════  MEETING CONTEXT  ════════
• Title: {title}
• Date:  {date}
• Local User: {user_name}
• Transcript Highlights:
⟪
{transcript}
⟫
═══════════════════════════════════

╔═ CATEGORY GUIDANCE (do NOT output) ═══════════════════════════════════╗
1. self_review
   • Purpose – feedback on the user's participation: strengths, improvements, next steps.
   • Examples:
     - "How effectively did I communicate my ideas during today's planning session?"
     - "What were my strengths and areas to improve in facilitating this meeting?"
     - "Analyze my participation: what went well and what could I do better?"
   • Tone – supportive, growth-oriented, actionable
   • Rationale – users always ask "How did I do?"; drives learning & confidence.

2. action_items
   • Purpose – surface all key tasks, assignments, and setup work identified in the meeting.
   • Examples:
     - "What are my action items and deadlines from this product review?"
     - "List all tasks assigned during our sprint planning session"
     - "Show me the follow-up items and who's responsible for each"
   • Tone – clear, organized, deadline-focused
   • Rationale – action items are the #1 post-meeting need; ensures follow-through.
   • Note – infer tasks from transcript; if none found, leave prompt "".

3. content_feedback
   • Purpose – feedback on key presentations, proposals, or major discussion topics from the meeting.
   • Focus – main content themes, whether led by the user or other key contributors.
   • Examples:
     - "What was the team's reaction to the API design proposal presented today?"
     - "Summarize feedback on the quarterly roadmap that was discussed"
     - "How did stakeholders respond to the budget recommendations?"
     - "What were the main takeaways from Danny's architecture presentation?"
   • Tone – objective, constructive, insights-focused
   • Rationale – captures feedback on the most important content, regardless of presenter.
   • Note – if no significant presentations/proposals occurred, leave prompt "".
╚══════════════════════════════════════════════════════════════════════╝

╭─  CONTEXTUALIZATION TIPS  ────────────────────────────────────────────╮
│ • Use {user_name} naturally in prompts (e.g., "my" not "{user_name}") │
│ • Reference specific topics/people from transcript when relevant       │
│ • Match tone to meeting context (formal for reviews, casual for 1:1s) │
│ • Include meeting title elements if they add clarity                  │
│ • Make prompts feel natural when prefilled in the input box          │
╰───────────────────────────────────────────────────────────────────────╯

╭─  OUTPUT REQUIREMENTS  ───────────────────────────────────────────────╮
│ • Return **valid JSON only** with an array named "suggestions".       │
│ • Exactly three objects, in this order: self_review, action_items,    │
│   content_feedback.                                                   │
│ • Each "prompt" is 60-100 visible characters (excluding quotes/JSON); │
│   first-person perspective; coaching tone.                            │
│ • Each object must have "category" and "prompt" fields.              │
│ • If action_items or content_feedback is not applicable, set that     │
│   prompt to "" but still include the object.                          │
╰───────────────────────────────────────────────────────────────────────╯

╭─  FALLBACK RULES  ────────────────────────────────────────────────────╮
│ If action_items or content_feedback don't apply to this meeting:      │
│ • Still include them in output with prompt: ""                        │
│ • Do NOT substitute with other categories                             │
│ • Do NOT create generic/vague suggestions                             │
│ • Focus on making self_review highly relevant and engaging            │
╰───────────────────────────────────────────────────────────────────────╯

╭─  QUALITY CHECKLIST  ─────────────────────────────────────────────────╮
│ Before returning suggestions, verify each prompt:                      │
│ ✓ Is 60-100 visible characters                                        │
│ ✓ Uses first-person perspective ("I", "my", "me")                    │
│ ✓ Is specific to THIS meeting (not generic)                          │
│ ✓ Has coaching/supportive tone                                       │
│ ✓ Would make sense when prefilled in the input box                   │
╰───────────────────────────────────────────────────────────────────────╯

═══════  EXAMPLE OUTPUTS  ═══════

Example 1 - All categories applicable:
```json
{{
  "suggestions": [
    {{
      "category": "self_review",
      "prompt": "How well did I facilitate discussion and drive decisions in today's planning?"
    }},
    {{
      "category": "action_items",
      "prompt": "List my action items from the Q4 roadmap review with deadlines"
    }},
    {{
      "category": "content_feedback",
      "prompt": "What feedback did I receive on the new dashboard design proposal?"
    }}
  ]
}}
```

Example 2 - No action items or content presentation:
```json
{{
  "suggestions": [
    {{
      "category": "self_review",
      "prompt": "Analyze my listening skills and engagement during this 1:1 with Sarah"
    }},
    {{
      "category": "action_items",
      "prompt": ""
    }},
    {{
      "category": "content_feedback",
      "prompt": ""
    }}
  ]
}}
```
═════════════════════════════

Remember: Generate prompts that feel personal, relevant, and immediately actionable based on the specific meeting context provided.
"""

MULTI_USER_SUGGESTIONS_TEMPLATE = """
You are Rumi's large-language-model assistant.
Generate **exactly three** suggestion prompts (tiles) for EACH participant in the meeting end-screen.
These prompts will be auto-inserted into the **Meeting Memory** chat input when the user clicks a tile.

The goal of this is to get users to want to ask these questions after the meeting has ended. So ensure the prompts you select would be the most interesting to a user. Try to use the HOOKED model as guidance for why we are doing this.

════════  MEETING CONTEXT  ════════
• Title: {title}
• Date:  {date}
• Participants: {participants}
• Transcript with User IDs:
⟪
{transcript}
⟫
═══════════════════════════════════

╔═ CATEGORY GUIDANCE (do NOT output) ═══════════════════════════════════╗
1. self_review
   • Purpose – feedback on the user's participation: strengths, improvements, next steps.
   • Examples:
     - "How effectively did I communicate my ideas during today's planning session?"
     - "What were my strengths and areas to improve in facilitating this meeting?"
     - "Analyze my participation: what went well and what could I do better?"
   • Tone – supportive, growth-oriented, actionable
   • Rationale – users always ask "How did I do?"; drives learning & confidence.

2. action_items
   • Purpose – surface all key tasks, assignments, and setup work identified in the meeting.
   • Examples:
     - "What are my action items and deadlines from this product review?"
     - "List all tasks assigned during our sprint planning session"
     - "Show me the follow-up items and who's responsible for each"
   • Tone – clear, organized, deadline-focused
   • Rationale – action items are the #1 post-meeting need; ensures follow-through.
   • Note – infer tasks from transcript; if none found, leave prompt "".

3. content_feedback
   • Purpose – feedback on key presentations, proposals, or major discussion topics from the meeting.
   • Focus – main content themes, whether led by the user or other key contributors.
   • Examples:
     - "What was the team's reaction to the API design proposal presented today?"
     - "Summarize feedback on the quarterly roadmap that was discussed"
     - "How did stakeholders respond to the budget recommendations?"
     - "What were the main takeaways from Danny's architecture presentation?"
   • Tone – objective, constructive, insights-focused
   • Rationale – captures feedback on the most important content, regardless of presenter.
   • Note – if no significant presentations/proposals occurred, leave prompt "".
╚══════════════════════════════════════════════════════════════════════╝

╭─  CONTEXTUALIZATION TIPS  ────────────────────────────────────────────╮
│ • Use first-person perspective naturally ("my", "I", "me")            │
│ • Reference specific topics/people from transcript when relevant       │
│ • Match tone to meeting context (formal for reviews, casual for 1:1s) │
│ • Include meeting title elements if they add clarity                  │
│ • Make prompts feel natural when prefilled in the input box          │
│ • Tailor each prompt to the specific user's contributions             │
╰───────────────────────────────────────────────────────────────────────╯

╭─  OUTPUT REQUIREMENTS  ───────────────────────────────────────────────╮
│ • Return **valid JSON only** with "user_suggestions" array.           │
│ • Each user gets exactly 3 suggestions: self_review, action_items,    │
│   content_feedback (in that order).                                   │
│ • Each suggestion object has: "user_id", "category", "prompt".        │
│ • If action_items or content_feedback not applicable for a user,      │
│   set prompt to "" but include the object.                            │
│ • Use first-person perspective in prompts ("I", "my", "me").          │
│ • Prompts should be 60-100 characters, specific to the meeting.       │
╰───────────────────────────────────────────────────────────────────────╯

╭─  FALLBACK RULES  ────────────────────────────────────────────────────╮
│ If action_items or content_feedback don't apply to a specific user:   │
│ • Still include them in output with prompt: ""                        │
│ • Do NOT substitute with other categories                             │
│ • Do NOT create generic/vague suggestions                             │
│ • Focus on making self_review highly relevant and engaging            │
╰───────────────────────────────────────────────────────────────────────╯

╭─  QUALITY CHECKLIST  ─────────────────────────────────────────────────╮
│ Before returning suggestions, verify each prompt:                      │
│ ✓ Is 60-100 visible characters                                        │
│ ✓ Uses first-person perspective ("I", "my", "me")                    │
│ ✓ Is specific to THIS meeting (not generic)                          │
│ ✓ Has coaching/supportive tone                                       │
│ ✓ Would make sense when prefilled in the input box                   │
│ ✓ Is tailored to the specific user's contributions                   │
╰───────────────────────────────────────────────────────────────────────╯

═══════  EXAMPLE OUTPUT  ═══════

```json
{{
  "user_suggestions": [
    {{
      "user_id": "user123",
      "category": "self_review", 
      "prompt": "How effectively did I lead the sprint planning discussion?"
    }},
    {{
      "user_id": "user123",
      "category": "action_items",
      "prompt": "What are my action items from the roadmap review meeting?"
    }},
    {{
      "user_id": "user123", 
      "category": "content_feedback",
      "prompt": "What feedback did I receive on my API design proposal?"
    }},
    {{
      "user_id": "user456",
      "category": "self_review",
      "prompt": "How well did I contribute to the technical discussions?"
    }},
    {{
      "user_id": "user456",
      "category": "action_items", 
      "prompt": "List my follow-up tasks from the architecture review"
    }},
    {{
      "user_id": "user456",
      "category": "content_feedback",
      "prompt": ""
    }}
  ]
}}
```
═════════════════════════════

Generate personalized suggestions for each participant based on their specific contributions in the transcript. 
Use the userID from the transcript to identify which user each suggestion belongs to. 
Ensure each suggestion is less than or equal 10 words.
""" 