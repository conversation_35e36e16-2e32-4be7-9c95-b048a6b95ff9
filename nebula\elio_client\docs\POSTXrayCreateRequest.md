# POSTXrayCreateRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**alert_channels** | **Dict[str, bool]** |  | 
**description** | **str** |  | 
**frequency** | **str** |  | 
**icon** | **str** |  | 
**prompt** | **str** |  | 
**short_summary** | **str** |  | 
**title** | **str** |  | 
**type** | **str** |  | 

## Example

```python
from elio_client.models.post_xray_create_request import POSTXrayCreateRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTXrayCreateRequest from a JSON string
post_xray_create_request_instance = POSTXrayCreateRequest.from_json(json)
# print the JSON string representation of the object
print(POSTXrayCreateRequest.to_json())

# convert the object into a dict
post_xray_create_request_dict = post_xray_create_request_instance.to_dict()
# create an instance of POSTXrayCreateRequest from a dict
post_xray_create_request_from_dict = POSTXrayCreateRequest.from_dict(post_xray_create_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


