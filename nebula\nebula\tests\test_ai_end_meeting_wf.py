import pytest
from datetime import timedelta
from temporalio.testing import WorkflowEnvironment
from temporalio.api.enums.v1 import EventType
from temporalio.worker import Worker
from temporalio import activity
from temporalio.exceptions import ApplicationError

from nebula.temporal.types import AiEndMeetingWorkflowParams, MetadataWorkflowParams, PostSessionSummaryWorkflowParams
from nebula.tests.helpers.temporal import (
    create_ai_end_meet_worker_for_tests,
)
from nebula.temporal.workflows.ai_end_meeting_wf import AiEndMeetingWorkflow
from nebula.temporal.workflows.metadata_wf import MetadataWorkflow
from nebula.temporal.workflows.post_session_summary_wf import PostSessionSummaryWorkflow
from nebula.temporal.workflows.typesense_indexing_wf import TypesenseIndexingWorkflow
from nebula.temporal.workflows.tldr_generation_wf import TLDRGenerationWorkflow
from nebula.temporal.workflows.meeting_suggestions_wf import MeetingSuggestionsWorkflow
from nebula.temporal.workflows.metadata_backfill_wf import BackfillMetadataWorkflow
from nebula.temporal.converter import pydantic_data_converter
from nebula.settings import settings

@pytest.mark.asyncio
async def test_ai_end_meeting_workflow():
    """Test the AI end meeting workflow executes successfully."""
    # Create a workflow environment for testing
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        # Get client from the test environment
        client = env.client

        # Use the standard task queue from settings
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # Execute the AI end meeting workflow
            await client.execute_workflow(
                AiEndMeetingWorkflow.run,
                arg=AiEndMeetingWorkflowParams(session_id="1", recurrence_id="1"),
                id="test-ai-end-meeting-workflow",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # The workflow completes without raising an exception
            # which means the test passes


@pytest.mark.asyncio
async def test_ai_end_meeting_child_workflow():
    """Test that the AI end meeting workflow correctly executes its child workflows."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # Execute the AI end meeting workflow
            await client.execute_workflow(
                AiEndMeetingWorkflow.run,
                arg=AiEndMeetingWorkflowParams(session_id="1", recurrence_id="1"),
                id="test-ai-end-meeting-child-workflow",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Get workflow history
            history = await client.get_workflow_handle(
                "test-ai-end-meeting-child-workflow"
            ).fetch_history()

            # Check for child workflow executions
            child_workflow_events = [
                event
                for event in history.events
                if event.event_type
                == EventType.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED
            ]

            # The workflow should start exactly four child workflows (MetadataWorkflow, PostSessionSummaryWorkflow, TypesenseIndexingWorkflow, TLDRGenerationWorkflow)
            # Note: MeetingSuggestionsWorkflow is now a child of PostSessionSummaryWorkflow
            assert len(child_workflow_events) == 4


@pytest.mark.asyncio
async def test_combined_workflows():
    """Test that both AI end meeting and metadata workflows can run on the same task queue."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # 1. Execute the metadata workflow directly first
            metadata_result = await client.execute_workflow(
                MetadataWorkflow.run,
                arg=MetadataWorkflowParams(session_id="1", recurrence_id="1"),
                id="test-combined-metadata-workflow",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify metadata workflow succeeded
            assert metadata_result.done is True
            assert "Successfully generated metadata" in metadata_result.message
            assert metadata_result.data is not None
            assert metadata_result.data.metadata_id == 1

            # 2. Execute the post session summary workflow directly
            pss_result = await client.execute_workflow(
                PostSessionSummaryWorkflow.run,
                arg=PostSessionSummaryWorkflowParams(session_id="2", recurrence_id="2"),
                id="test-combined-post-session-summary-workflow",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify post session summary workflow succeeded
            assert pss_result.done is True
            assert "Successfully generated post session summary" in pss_result.message
            assert pss_result.summary_id is not None
            assert pss_result.summary_id == 1

            # 3. Now execute the AI end meeting workflow which calls both workflows as children
            await client.execute_workflow(
                AiEndMeetingWorkflow.run,
                arg=AiEndMeetingWorkflowParams(session_id="3", recurrence_id="3"),
                id="test-combined-ai-end-meeting-workflow",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Get workflow history to verify child workflow execution
            history = await client.get_workflow_handle(
                "test-combined-ai-end-meeting-workflow"
            ).fetch_history()

            # Check for child workflow executions
            child_workflow_events = [
                event
                for event in history.events
                if event.event_type
                == EventType.EVENT_TYPE_CHILD_WORKFLOW_EXECUTION_STARTED
            ]

            # The workflow should start exactly four child workflows (MetadataWorkflow, PostSessionSummaryWorkflow, TypesenseIndexingWorkflow, TLDRGenerationWorkflow)
            # Note: MeetingSuggestionsWorkflow is now a child of PostSessionSummaryWorkflow
            assert len(child_workflow_events) == 4


@pytest.mark.asyncio
async def test_ai_end_meeting_workflow_with_shared_activities():
    """Test the AI end meeting workflow with shared activities approach."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # Execute the AI end meeting workflow - this should succeed with shared mock activities
            await client.execute_workflow(
                AiEndMeetingWorkflow.run,
                arg=AiEndMeetingWorkflowParams(session_id="1", recurrence_id="1"),
                id="test-ai-end-meeting-workflow-with-shared-activities",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )
            
            # If we reach here, the workflow completed successfully
            # This verifies that the shared activities approach works correctly with all child workflows


@pytest.mark.asyncio
async def test_ai_end_meeting_workflow_with_child_failure():
    """Test the AI end meeting workflow handles child workflow failures correctly."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Import the required classes locally to avoid circular imports
        from nebula.tests.mocks.mock_shared_activities import MockSharedActivities
        from nebula.tests.mocks.mock_metadata_activities import MockMetadataActivities
        from nebula.tests.mocks.mock_recurrence_activities import MockRecurrenceActivities
        from nebula.tests.mocks.mock_post_session_summary_activities import MockPostSessionSummaryActivities
        from nebula.tests.mocks.mock_typesense_activities import MockTypesenseActivities
        from nebula.tests.mocks.mock_tldr_activities import MockTLDRActivities
        from nebula.tests.mocks.mock_meeting_suggestions_activities import MockMeetingSuggestionsActivities

        # Create a custom failing mock for MetadataActivities
        class FailingMockMetadataActivities(MockMetadataActivities):
            @activity.defn
            async def extract_metadata(self, input):
                # Use ApplicationError with non_retryable=True for immediate failure
                raise ApplicationError(
                    "Simulated failure in metadata extraction",
                    non_retryable=True
                )

        # Setup worker with failing mock
        workflows = [MetadataWorkflow, BackfillMetadataWorkflow, AiEndMeetingWorkflow, 
                    PostSessionSummaryWorkflow, TypesenseIndexingWorkflow, TLDRGenerationWorkflow, MeetingSuggestionsWorkflow]

        # Setup mock activities with one failing activity
        mock_shared_activities = MockSharedActivities()
        failing_metadata_activities = FailingMockMetadataActivities()
        mock_recurrence_activities = MockRecurrenceActivities()
        mock_post_session_summary_activities = MockPostSessionSummaryActivities()
        mock_typesense_activities = MockTypesenseActivities()
        mock_tldr_activities = MockTLDRActivities()
        mock_meeting_suggestions_activities = MockMeetingSuggestionsActivities()

        activities = [
            # Shared activities (used by multiple workflows)
            mock_shared_activities.get_transcription_batches,
            mock_shared_activities.get_session_data,
            mock_shared_activities.process_transcript,
            
            # Metadata-specific activities (with failing extract_metadata)
            failing_metadata_activities.create_metadata_record,
            failing_metadata_activities.extract_metadata,  # This will fail
            failing_metadata_activities.get_metadata_record,
            failing_metadata_activities.update_elio_about_metadata_creation,
            
            # Recurrence activities
            mock_recurrence_activities.get_past_ai_enabled_recurrences,
            
            # Post session summary activities
            mock_post_session_summary_activities.get_transcription_batches_for_pss,
            mock_post_session_summary_activities.get_session_data_for_pss,
            mock_post_session_summary_activities.process_transcript_for_pss,
            mock_post_session_summary_activities.create_or_update_post_session_summary,
            mock_post_session_summary_activities.generate_post_meeting_summary,
            mock_post_session_summary_activities.get_action_items_for_pss,
            mock_post_session_summary_activities.notify_draconids_summary_ready,
            
            # Typesense activities
            mock_typesense_activities.get_recurrence_data,
            mock_typesense_activities.process_transcripts_for_typesense,
            mock_typesense_activities.index_meeting_in_typesense,
            
            # TLDR activities
            mock_tldr_activities.generate_tldr_summary,
            mock_tldr_activities.update_post_session_summary_with_tldr,
            mock_tldr_activities.notify_luxor_tldr_ready,

            # Meeting suggestions specific activities
            mock_meeting_suggestions_activities.process_transcript_with_user_ids,
            mock_meeting_suggestions_activities.extract_all_users_meeting_suggestions,
            mock_meeting_suggestions_activities.create_all_users_meeting_suggestions_record,
        ]

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=workflows,
            activities=activities,
        ) as worker:
            # Execute the AI end meeting workflow - it should fail due to metadata workflow failure
            with pytest.raises(Exception) as exc_info:
                await client.execute_workflow(
                    AiEndMeetingWorkflow.run,
                    arg=AiEndMeetingWorkflowParams(session_id="1", recurrence_id="1"),
                    id="test-ai-end-meeting-workflow-failure",
                    task_queue=task_queue,
                    execution_timeout=timedelta(minutes=5),
                )
            
            # Verify that the workflow failed as expected
            # The exception type and message indicate a workflow failure due to child workflow issues
            assert exc_info.value is not None
            assert "failed" in str(exc_info.value).lower()
