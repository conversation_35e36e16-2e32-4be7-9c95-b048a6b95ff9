# POSTXrayGenerateXRayPrompt200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**ApiGenerateXRayPromptResponseData**](ApiGenerateXRayPromptResponseData.md) |  | 
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.post_xray_generate_x_ray_prompt200_response import POSTXrayGenerateXRayPrompt200Response

# TODO update the JSON string below
json = "{}"
# create an instance of POSTXrayGenerateXRayPrompt200Response from a JSON string
post_xray_generate_x_ray_prompt200_response_instance = POSTXrayGenerateXRayPrompt200Response.from_json(json)
# print the JSON string representation of the object
print(POSTXrayGenerateXRayPrompt200Response.to_json())

# convert the object into a dict
post_xray_generate_x_ray_prompt200_response_dict = post_xray_generate_x_ray_prompt200_response_instance.to_dict()
# create an instance of POSTXrayGenerateXRayPrompt200Response from a dict
post_xray_generate_x_ray_prompt200_response_from_dict = POSTXrayGenerateXRayPrompt200Response.from_dict(post_xray_generate_x_ray_prompt200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


