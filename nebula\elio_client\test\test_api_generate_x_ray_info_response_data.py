# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_generate_x_ray_info_response_data import ApiGenerateXRayInfoResponseData

class TestApiGenerateXRayInfoResponseData(unittest.TestCase):
    """ApiGenerateXRayInfoResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiGenerateXRayInfoResponseData:
        """Test ApiGenerateXRayInfoResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiGenerateXRayInfoResponseData`
        """
        model = ApiGenerateXRayInfoResponseData()
        if include_optional:
            return ApiGenerateXRayInfoResponseData(
                icon = '',
                short_summary = '',
                title = ''
            )
        else:
            return ApiGenerateXRayInfoResponseData(
                icon = '',
                short_summary = '',
                title = '',
        )
        """

    def testApiGenerateXRayInfoResponseData(self):
        """Test ApiGenerateXRayInfoResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
