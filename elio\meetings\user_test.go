package meetings_test

import (
	"context"
	"encore.app/billing"
	billing_api "encore.app/billing/api"
	"encore.app/billing/mocks"
	"encore.app/transcriptions"
	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"errors"
	"fmt"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/mock"
	"os"
	"path"
	"testing"

	"encore.app/hubble"
	hubble_api "encore.app/hubble/api"
	"encore.dev/et"
	"github.com/samber/lo"

	"encore.app/meetings"
	"encore.app/meetings/api"
	meetingsDatabase "encore.app/meetings/database"
	"encore.app/pkg/database"
	shared "encore.app/shared"
	"github.com/stretchr/testify/assert"
)

type MockHubbleService struct {
	mock.Mock
}

func (m *MockHubbleService) IsAuthorized(ctx context.Context, params *hubble_api.IsAuthorizedRequest) error {
	args := m.Called(params)
	return args.Error(0)
}

func setUpUserTest() {
	et.EnableServiceInstanceIsolation()
	testDB, err := et.NewTestDatabase(context.Background(), "mars")
	if err != nil {
		panic(err)
	}

	testDBX := sqlx.NewDb(testDB.Stdlib(), "pgx")
	meetings.MarsDB = testDBX
	transcriptions.MarsDB = testDBX

	// Order matters here, seed_users.sql must be seeded first
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("seed_users.sql"))
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("user_subscription_plans.sql"))
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("seed_categories.sql"))
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("seed_basic_session.sql"))
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("teams.sql"))
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("seed_sessions.sql"))

	et.MockEndpoint(hubble.IsAuthorized, func(ctx context.Context, p *hubble_api.IsAuthorizedRequest) error {
		return nil
	})
}

func TestGetUserByID(t *testing.T) {
	setUpUserTest()
	t.Run("Valid user ID returns user data", func(t *testing.T) {
		req := api.GetUserByIDRequest{
			UserID: "1",
		}
		resp, err := meetings.GetUserByID(context.Background(), &req)

		if assert.NoError(t, err) {
			assert.IsType(t, &shared.UserDTO{}, resp)
		}
		database.AssertXNoOpenConns(t, meetings.MarsDB)
	})
}

func TestGetUserByIDWithRelations(t *testing.T) {
	setUpUserTest()
	meetings.MarsDB.Exec("UPDATE users SET lobbies = CAST('[{\"lobbyID\":\"john-doe\",\"slug\":\"john-doe\",\"isActive\":true}]' as jsonb) WHERE id = 1")
	ctx := context.Background()

	t.Run("Valid user returns complete user data", func(t *testing.T) {
		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "1"})

		if assert.NoError(t, err) {
			if assert.IsType(t, &api.GetMyUserResponse{}, resp) {
				if assert.NotNil(t, resp.Data.User.SubscriptionPlan) {
					assert.NotNil(t, resp.Data.User.SubscriptionPlan.Id)
				}
				assert.NotNil(t, resp.Data.User.Onboarding)
				if assert.NotNil(t, resp.Data.User.Lobbies) {
					assert.Equal(t, "john-doe", (*resp.Data.User.Lobbies)[0].LobbyID)
					assert.Equal(t, "john-doe", (*resp.Data.User.Lobbies)[0].Slug)
					assert.Equal(t, true, (*resp.Data.User.Lobbies)[0].IsActive)
				}
			}
		}
		database.AssertXNoOpenConns(t, meetings.MarsDB)
	})

	t.Run("Non-existent user returns error", func(t *testing.T) {
		_, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "1212"})

		assert.Error(t, err)
		database.AssertXNoOpenConns(t, meetings.MarsDB)
	})

	t.Run("User without subscription plan has appropriate values", func(t *testing.T) {
		t.Skip("Test is failing it's assertions")
		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "1"})

		database.AssertXNoOpenConns(t, meetings.MarsDB)
		if !assert.NoError(t, err) {
			return
		}
		if !assert.IsType(t, &api.GetMyUserResponse{}, resp) {
			return
		}
		assert.Nil(t, resp.Data.User.SubscriptionPlan)
		assert.Nil(t, resp.Data.User.Team)
		if !assert.NotNil(t, resp.Data.User.Onboarding) {
			return
		}
		ob := *resp.Data.User.Onboarding
		assert.True(t, *ob.AddPeopleToSession)
		assert.True(t, *ob.ConnectCalendar)
		assert.False(t, *ob.WatchDemo)
		assert.False(t, *ob.TryMeetingMemory)
		assert.Equal(t, *ob.Features, "!TODO: this is a ISO timestamp")
	})

	t.Run("Update user onboarding flags", func(t *testing.T) {
		err := meetingsDatabase.UpdateUserOnboardingFlag(ctx, "1", &shared.OnboardingFlags{
			Intro: lo.ToPtr(true),
		})

		if !assert.NoError(t, err) {
			return
		}

		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "1"})

		if assert.NoError(t, err) {
			if assert.IsType(t, &api.GetMyUserResponse{}, resp) {
				if assert.NotNil(t, resp.Data.User.Onboarding) {
					assert.True(t, *resp.Data.User.Onboarding.Intro)
				}
			}
		}
		database.AssertXNoOpenConns(t, meetings.MarsDB)
	})

	t.Run("Update user onboarding flags again expect same response", func(t *testing.T) {
		err := meetingsDatabase.UpdateUserOnboardingFlag(ctx, "1", &shared.OnboardingFlags{
			Intro:    lo.ToPtr(true),
			Features: lo.ToPtr("bots,welcome"),
		})

		if !assert.NoError(t, err) {
			return
		}

		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "1"})

		if assert.NoError(t, err) {
			if assert.IsType(t, &api.GetMyUserResponse{}, resp) {
				if assert.NotNil(t, resp.Data.User.Onboarding) {
					assert.True(t, *resp.Data.User.Onboarding.Intro)
					assert.Equal(t, *resp.Data.User.Onboarding.Features, "bots,welcome")
				}
			}
		}
		database.AssertXNoOpenConns(t, meetings.MarsDB)
	})

	t.Run("Team members get premium subscription plan", func(t *testing.T) {
		// Arrange
		// User 2 has no user_subscription_plan, but is part of Test Team so should get premium plan as subscription_plan_config

		// Act
		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "2"})

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "943369326642071161", resp.Data.User.SubscriptionPlan.PlanConfig.Id) // Premium plan
	})

	t.Run("Non team members return self subscription plan", func(t *testing.T) {
		// Arrange
		// User 3 is not part of a team, but has a user_subscription_plan

		// Act
		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "3"})

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "943369326642071161", resp.Data.User.SubscriptionPlan.PlanConfig.Id)
		assert.Equal(t, "sub_01hnfk4wkv9gbmrv72fhsq1mgt2", *resp.Data.User.SubscriptionPlan.PaddleSubscriptionID)
	})

	t.Run("Team members return the team", func(t *testing.T) {
		// Arrange
		// User 1 is part of Test Team

		// Act
		resp, err := meetings.GetUserByIDWithRelations(ctx, &api.GetUserByIDRequest{UserID: "1"})
		shared.PP(resp.Data.User.Team)

		// Assert
		assert.NoError(t, err)
		assert.Equal(t, shared.TeamWithInlineRelationsDTO{
			ID:           "1",
			Name:         "Test Team",
			Domains:      []string{"test.com"},
			CreatedAt:    1653528800,
			UpdatedAt:    1653528800,
			MembersCount: lo.ToPtr(2),
			Role:         lo.ToPtr("owner"),
		}, *resp.Data.User.Team)
	})
}

func TestCreateUser(t *testing.T) {
	setUpUserTest()

	t.Run("Creates user and auth user", func(t *testing.T) {
		// Arrange
		var mockCreateAuthUserRequest hubble_api.CreateAuthUsersRequest
		et.MockEndpoint(hubble.UpsertAuthUsers, func(ctx context.Context, req *hubble_api.CreateAuthUsersRequest) (*hubble_api.CreateAuthUsersResponse, error) {
			mockCreateAuthUserRequest = *req
			return &hubble_api.CreateAuthUsersResponse{}, nil
		})

		createUserRequest := api.CreateUserRequest{
			UserEmail:        "<EMAIL>",
			UserFirstName:    "John",
			UserLastName:     "Doe",
			RoleIDs:          []string{"session_owner"},
			MarketingOptIn:   lo.ToPtr(true),
			PaddleCustomerID: lo.ToPtr("test-paddle-customer-id"),
			Timezone:         lo.ToPtr("America/New_York"),
		}

		// Act
		createUserResponse, err := meetings.CreateUser(context.Background(), &createUserRequest)
		assert.NoError(t, err)

		// Assert

		// Create user in db
		userRow, err := meetingsDatabase.GetUserByID(context.Background(), createUserResponse.UserID)
		assert.NoError(t, err)

		assert.Equal(t, createUserRequest.UserEmail, userRow.Email.String)
		assert.Equal(t, createUserRequest.UserFirstName, userRow.FirstName.String)
		assert.Equal(t, createUserRequest.UserLastName, userRow.LastName.String)
		assert.Equal(t, *createUserRequest.MarketingOptIn, userRow.MarketingOptIn)
		assert.Equal(t, *createUserRequest.PaddleCustomerID, userRow.PaddleCustomerID.String)
		assert.Equal(t, *createUserRequest.Timezone, userRow.Timezone.String)
		assert.ElementsMatch(t, createUserRequest.RoleIDs, userRow.RoleIDs)

		// Calls hubble to create auth user
		assert.Equal(t, hubble_api.CreateAuthUsersRequest{Users: []hubble_api.CreateAuthUserInput{
			{
				ID:      createUserResponse.UserID,
				Email:   createUserRequest.UserEmail,
				RoleIDs: createUserRequest.RoleIDs,
			},
		}}, mockCreateAuthUserRequest)
	})

	t.Run("Errors when creating user with invalid email", func(t *testing.T) {
		// Arrange
		createUserRequest := api.CreateUserRequest{
			UserEmail:     "not-a-valid-email",
			UserFirstName: "John",
			UserLastName:  "Doe",
			RoleIDs:       []string{"session_owner"},
		}

		// Act
		_, err := meetings.CreateUser(context.Background(), &createUserRequest)

		// Assert
		assert.Error(t, err)
	})
}

func TestListUsersByEmail(t *testing.T) {
	setUpUserTest()

	t.Run("List by email", func(t *testing.T) {
		// Arrange
		listUsersRequest := api.ListUsersRequest{
			Email: "<EMAIL>", // Matches user with id 1 in seed_users.sql
		}

		// Act
		listUsersRes, err := meetings.ListUsers(context.Background(), &listUsersRequest)
		assert.NoError(t, err)

		// Assert
		userIDs := lo.Map(listUsersRes.Users, func(user shared.UserDTO, _ int) string {
			return *user.Id
		})
		assert.Contains(t, userIDs, "1")
	})

	t.Run("Search by email", func(t *testing.T) {
		// Arrange
		listUsersRequest := api.ListUsersRequest{
			Email: "%@rumi.ai", // Matches users with id 2,3,4 in seed_users.sql
		}

		// Act
		listUsersRes, err := meetings.ListUsers(context.Background(), &listUsersRequest)
		assert.NoError(t, err)

		// Assert
		userIDs := lo.Map(listUsersRes.Users, func(user shared.UserDTO, _ int) string {
			return *user.Id
		})
		assert.Contains(t, userIDs, "2")
		assert.Contains(t, userIDs, "3")
		assert.Contains(t, userIDs, "4")
	})

	t.Run("Search by email with offset and limit", func(t *testing.T) {
		// Arrange
		listUsersRequest := api.ListUsersRequest{
			Email:          "%@rumi.ai", // Matches users with id 2,3,4 in seed_users.sql
			Limit:          1,
			Offset:         1, // will match user with id 3 in seed_users.sql
			OrderBy:        "id",
			OrderDirection: "ASC",
		}

		// Act
		listUsersRes, err := meetings.ListUsers(context.Background(), &listUsersRequest)
		assert.NoError(t, err)

		// Assert
		userIDs := lo.Map(listUsersRes.Users, func(user shared.UserDTO, _ int) string {
			return *user.Id
		})
		assert.Contains(t, userIDs, "3")
		assert.Equal(t, 1, len(userIDs))
		assert.Equal(t, 3, listUsersRes.OffsetPaginationResponse.TotalCount)
	})
}

var listTransactionsMock = (func() []shared.BillingTransactionDTO {
	listTransactionsMock, err := mocks.GetListTransactionsMock("..")
	if err != nil {
		panic(err)
	}

	transactions := listTransactionsMock.Transactions

	return transactions
})()

var getSubscriptionUpdatePaymentMethodTransactionMock = (func() billing_api.GetSubscriptionUpdatePaymentMethodTransactionResponse {
	getSubscriptionUpdatePaymentMethodTransactionMock, err := mocks.GetGetSubscriptionUpdatePaymentMethodTransactionMock("..")
	if err != nil {
		panic(err)
	}

	return *getSubscriptionUpdatePaymentMethodTransactionMock
})()

func TestGetUserPaymentMethodDetails(t *testing.T) {
	setUpUserTest()

	t.Run("Errors when userID is not the same as the userID in the JWT", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		et.OverrideAuthInfo(auth.UID("2"), &shared.DecodedToken{
			UserID: "2",
		})

		// Act
		resp, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(16), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.Unauthorized, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.Unauthorized], errDetails.Message)
	})

	t.Run("Errors when user not found", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "999999"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserNotFound], errDetails.Message)
	})

	t.Run("Errors when user has no paddle customer id", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(13), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.PaddleCustomerDoesNotExist, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.PaddleCustomerDoesNotExist], errDetails.Message)
	})

	t.Run("Returns payment method details", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "3"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		et.MockEndpoint(billing.ListTransactionsByCustomerID, func(ctx context.Context, req billing_api.ListTransactionsByCustomerIDRequest) (*billing_api.
		ListTransactionsByCustomerIDResponse, error) {
			return &billing_api.ListTransactionsByCustomerIDResponse{
				Transactions: listTransactionsMock,
			}, nil
		})

		// Act
		res, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)
		assert.NoError(t, err)

		// Assert
		resJSON := shared.MarshalResponse(t, res)
		expectedResJSON := readFixture(t, "get-user-payment-method-details.json")
		shared.AssertEqualJSON(t, expectedResJSON, resJSON)
	})
}

var getInvoiceByTransactionIDMock = (func() billing_api.GetInvoiceByTransactionIDResponse {
	getInvoiceByTransactionIDMock, err := mocks.GetGetInvoiceByTransactionIDMock("..")
	if err != nil {
		panic(err)
	}

	return *getInvoiceByTransactionIDMock
})()

func TestListUserTransactions(t *testing.T) {
	setUpUserTest()

	t.Run("Errors when userID is not the same as the userID in the JWT", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		et.OverrideAuthInfo(auth.UID("2"), &shared.DecodedToken{
			UserID: "2",
		})

		// Act
		resp, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(16), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.Unauthorized, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.Unauthorized], errDetails.Message)
	})

	t.Run("Errors when user not found", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "999999"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.ListUserTransactions(context.Background(), uid, &api.ListUserTransactionsRequest{})

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserNotFound], errDetails.Message)
	})

	t.Run("Returns empty list when user has no paddle customer id", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.ListUserTransactions(context.Background(), uid, &api.ListUserTransactionsRequest{})
		assert.NoError(t, err)

		// Assert
		assert.Equal(t, &api.ListUserTransactionsResponse{
			Data: []api.ListUserTransactionsResponseData{},
		}, resp)
	})

	t.Run("Lists transactions including invoice url", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "3"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		et.MockEndpoint(billing.ListTransactionsByCustomerID, func(ctx context.Context, req billing_api.ListTransactionsByCustomerIDRequest) (*billing_api.
		ListTransactionsByCustomerIDResponse, error) {
			return &billing_api.ListTransactionsByCustomerIDResponse{
				Transactions: listTransactionsMock,
			}, nil
		})

		et.MockEndpoint(billing.GetInvoiceByTransactionID, func(ctx context.Context, req billing_api.GetInvoiceByTransactionIDRequest) (*billing_api.
		GetInvoiceByTransactionIDResponse, error) {
			return &getInvoiceByTransactionIDMock, nil
		})

		// Act
		resp, err := meetings.ListUserTransactions(context.Background(), uid, &api.ListUserTransactionsRequest{})
		assert.NoError(t, err)

		// Assert
		resJSON := shared.MarshalResponse(t, resp)
		expectedResJSON := readFixture(t, "list-transactions-by-user-id.json")
		shared.AssertEqualJSON(t, expectedResJSON, resJSON)
	})
}

func TestGetUserPlanByUserID(t *testing.T) {
	setUpUserTest()

	t.Run("Errors when userID is not the same as the userID in the JWT", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		et.OverrideAuthInfo(auth.UID("2"), &shared.DecodedToken{
			UserID: "2",
		})

		// Act
		resp, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(16), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.Unauthorized, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.Unauthorized], errDetails.Message)
	})

	t.Run("Errors when user not found", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "999999"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.GetUserPlanByUserID(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserNotFound], errDetails.Message)
	})

	t.Run("Errors when user has no subscription plan", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "4"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.GetUserPlanByUserID(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserSubscriptionPlanNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserSubscriptionPlanNotFound], errDetails.Message)
	})

	t.Run("Gets user subscription plan", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "3"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		res, err := meetings.GetUserPlanByUserID(context.Background(), uid)
		assert.NoError(t, err)

		// Assert
		resJSON := shared.MarshalResponse(t, res)
		expectedResJSON := readFixture(t, "get-user-plan-by-user-id.json")
		shared.AssertEqualJSON(t, expectedResJSON, resJSON)
	})
}

func TestGetUpdateUserPaymentMethodTransaction(t *testing.T) {
	setUpUserTest()

	t.Run("Errors when userID is not the same as the userID in the JWT", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		et.OverrideAuthInfo(auth.UID("2"), &shared.DecodedToken{
			UserID: "2",
		})

		// Act
		resp, err := meetings.GetUserPaymentMethodDetails(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(16), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.Unauthorized, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.Unauthorized], errDetails.Message)
	})

	t.Run("Errors when user not found", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "999999"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.GetUpdateUserPaymentMethodTransaction(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserNotFound], errDetails.Message)
	})

	t.Run("Errors when user has no subscription plan", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "4"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		// Act
		resp, err := meetings.GetUpdateUserPaymentMethodTransaction(context.Background(), uid)

		// Assert
		assert.Error(t, err)
		assert.Nil(t, resp)

		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserSubscriptionPlanNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserSubscriptionPlanNotFound], errDetails.Message)
	})

	t.Run("Returns transaction with status ready", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "3"
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})

		et.MockEndpoint(billing.GetSubscriptionUpdatePaymentMethodTransaction, func(ctx context.Context,
			req billing_api.GetSubscriptionUpdatePaymentMethodTransactionRequest) (*billing_api.GetSubscriptionUpdatePaymentMethodTransactionResponse, error) {
			return &getSubscriptionUpdatePaymentMethodTransactionMock, nil
		})

		// Act
		resp, err := meetings.GetUpdateUserPaymentMethodTransaction(context.Background(), uid)
		assert.NoError(t, err)

		// Assert
		resJSON := shared.MarshalResponse(t, resp)
		expectedResJSON := readFixture(t, "get-update-user-payment-method-transaction.json")
		shared.AssertEqualJSON(t, expectedResJSON, resJSON)
	})
}

func TestUpdateUserByID(t *testing.T) {
	setUp()

	setUpHubbleMock := func(uid string, err error) *MockHubbleService {
		mockHubbleService := MockHubbleService{}
		expectedIsAuthorizedReq := &hubble_api.IsAuthorizedRequest{
			Sub: shared.GetUserSubjectWithRole(uid), Obj: shared.GetUserObjectByID(uid), Act: shared.AuthPolicyActionUpdate,
		}
		mockHubbleService.On("IsAuthorized", expectedIsAuthorizedReq).Return(
			err,
		)

		et.MockEndpoint(hubble.IsAuthorized, mockHubbleService.IsAuthorized)

		return &mockHubbleService
	}

	overrideAuthInfo := func(uid string) {
		et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
			UserID: uid,
		})
	}

	t.Run("Calls hubble.IsAuthorized", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		mockHubbleService := setUpHubbleMock(uid, nil)

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{})

		// Assert
		mockHubbleService.AssertExpectations(t)
	})

	t.Run("Errors when hubble.IsAuthorized errors", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, shared.B().WithCode(shared.Unauthorized))

		// Act
		_, err := meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{})

		// Assert
		assert.Error(t, err)
		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(7), httpError.Code)
		errDetails := httpError.Details.(shared.CodeDetails)
		assert.Equal(t, shared.Unauthorized, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.Unauthorized], errDetails.Message)
	})

	t.Run("Errors when user not found", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "0"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		// Act
		_, err := meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{})

		// Assert
		assert.Error(t, err)
		var httpError *errs.Error
		errors.As(err, &httpError)
		assert.Equal(t, errs.ErrCode(3), httpError.Code)
		errDetails := httpError.Details.(shared.HttpErrorDetail)
		assert.Equal(t, shared.UserNotFound, errDetails.ErrCode)
		assert.Equal(t, shared.ErrorMessages[shared.UserNotFound], errDetails.Message)
	})

	t.Run("Updates user avatar to nil when empty string is passed", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{
				Avatar: lo.ToPtr(""),
			},
		})

		// Assert
		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		user := *userRow
		assert.NoError(t, err)
		assert.Nil(t, user.Avatar)
	})

	t.Run("Updates user avatar to an URL", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		nextAvatarURL := "https://example.com/avatar.png"

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{
				Avatar: lo.ToPtr(nextAvatarURL),
			},
		})

		// Assert
		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		user := *userRow
		assert.NoError(t, err)
		assert.Equal(t, nextAvatarURL, *user.Avatar)
	})

	t.Run("Updates user's onboarding flags", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		nextOnboardingFlags := shared.OnboardingFlags{
			Intro:              lo.ToPtr(true),
			AddPeopleToSession: lo.ToPtr(true),
			ConnectCalendar:    lo.ToPtr(true),
			WatchDemo:          lo.ToPtr(true),
			TryMeetingMemory:   lo.ToPtr(true),
			Notetaker:          lo.ToPtr(true),
			Features:           lo.ToPtr("something"),
		}

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{
				Onboarding: lo.ToPtr(nextOnboardingFlags),
			},
		})

		// Assert
		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		user := *userRow
		assert.NoError(t, err)
		assert.Equal(t, nextOnboardingFlags, *user.Onboarding)
	})

	t.Run("Updates user's firstName, lastName, and about", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		nextFirstName := "Manuel"
		nextLastName := "Silva"
		nextAbout := "About Manny"

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{
				FirstName: lo.ToPtr(nextFirstName),
				LastName:  lo.ToPtr(nextLastName),
				About:     lo.ToPtr(nextAbout),
			},
		})

		// Assert
		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		user := *userRow
		assert.NoError(t, err)
		assert.Equal(t, nextFirstName, *user.FirstName)
		assert.Equal(t, nextLastName, *user.LastName)
		assert.Equal(t, nextAbout, *user.About)
	})

	t.Run("Updates user's social", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		nextSocials := shared.Socials{
			{
				Platform: lo.ToPtr("twitter"),
				Url:      "https://x.com/mansilva",
			},
			{
				Platform: lo.ToPtr("instagram"),
				Url:      "https://insta.com/mansilva",
			},
		}

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{
				Social: lo.ToPtr(nextSocials),
			},
		})

		// Assert
		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		user := *userRow
		assert.NoError(t, err)
		assert.Equal(t, nextSocials, *user.Social)
	})

	t.Run("Leaves user unchanged if nothing is passed", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		userBeforeUpdate := *userRow

		// Act
		_, _ = meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{},
		})

		// Assert
		userRow, err = meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		userAfterUpdate := *userRow
		assert.NoError(t, err)
		assert.Equal(t, *userBeforeUpdate.Avatar, *userAfterUpdate.Avatar)
		assert.Equal(t, *userBeforeUpdate.FirstName, *userAfterUpdate.FirstName)
		assert.Equal(t, *userBeforeUpdate.LastName, *userAfterUpdate.LastName)
		assert.Equal(t, *userBeforeUpdate.About, *userAfterUpdate.About)
		assert.Equal(t, *userBeforeUpdate.Social, *userAfterUpdate.Social)
	})

	t.Run("Returns user DTO after update", func(t *testing.T) {
		// t.Skip()

		// Arrange
		uid := "1"
		overrideAuthInfo(uid)
		setUpHubbleMock(uid, nil)

		userRow, err := meetings.GetUserByID(context.Background(), &api.GetUserByIDRequest{UserID: uid})
		userBeforeUpdate := *userRow

		// Act
		res, err := meetings.UpdateUserByID(context.Background(), uid, &api.UpdateUserRequest{
			UserData: api.UpdateUserRequestData{},
		})

		// Assert
		assert.NoError(t, err)
		user := res.Data.User

		assert.Equal(t, *userBeforeUpdate.Id, *user.Id)
	})
}

func readFixture(t *testing.T, fixtureName string) []byte {
	expectedJson, err := os.ReadFile(path.Join("./", "fixtures", fixtureName))
	if err != nil {
		err = fmt.Errorf("error reading file: %w", err)
		t.Fatal(err)
	}

	return expectedJson
}
