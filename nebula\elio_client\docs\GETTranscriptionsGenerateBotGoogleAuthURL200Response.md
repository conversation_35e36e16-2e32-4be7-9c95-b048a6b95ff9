# GETTranscriptionsGenerateBotGoogleAuthURL200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**auth_url** | **str** |  | 

## Example

```python
from elio_client.models.get_transcriptions_generate_bot_google_auth_url200_response import GETTranscriptionsGenerateBotGoogleAuthURL200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETTranscriptionsGenerateBotGoogleAuthURL200Response from a JSON string
get_transcriptions_generate_bot_google_auth_url200_response_instance = GETTranscriptionsGenerateBotGoogleAuthURL200Response.from_json(json)
# print the JSON string representation of the object
print(GETTranscriptionsGenerateBotGoogleAuthURL200Response.to_json())

# convert the object into a dict
get_transcriptions_generate_bot_google_auth_url200_response_dict = get_transcriptions_generate_bot_google_auth_url200_response_instance.to_dict()
# create an instance of GETTranscriptionsGenerateBotGoogleAuthURL200Response from a dict
get_transcriptions_generate_bot_google_auth_url200_response_from_dict = GETTranscriptionsGenerateBotGoogleAuthURL200Response.from_dict(get_transcriptions_generate_bot_google_auth_url200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


