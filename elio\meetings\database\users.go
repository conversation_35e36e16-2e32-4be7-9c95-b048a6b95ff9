package database

import (
	"context"
	"encoding/json"
	"encore.app/pkg/database"
	"encore.app/shared"
	"fmt"
	"github.com/jmoiron/sqlx"
	"github.com/lib/pq"
	"github.com/samber/lo"
	"time"
)

var createUserWithIDs = database.NewStatement(`
	INSERT INTO users (
	  id, email, "firstName", "lastName", fingerprint,
		"roleIDs", about, avatar, timezone, "marketingOptIn",
		"notificationSettings", "isTestUser", "paddleCustomerID", "signUpTimestamp", "createdAt", "updatedAt")
	 VALUES (
		:id, :email, :firstName, :lastName, :fingerprint,
		:roleIDs, :about, :avatar, :timezone, :marketingOptIn,
		:notificationSettings, :isTestUser, :paddleCustomerID, :signUpTimestamp, :createdAt, :updatedAt)
	RETURNING id
`)

var getUserByID = database.NewStatement(`
	SELECT
		id, email, "firstName", "lastName", "signUpTimestamp", fingerprint, "googleOauthMetadata", "appleID", "googleID",
		"roleIDs", about, avatar, social, timezone, "marketingOptIn", "createdAt", "updatedAt",
		"notificationSettings", "isTestUser", "paddleCustomerID", "onboarding", lobbies
	FROM users WHERE id = :id
`)

var usersTableOrderByKeyFromColumnName = map[string]int{
	"id":              1,
	"email":           2,
	"firstName":       3,
	"lastName":        4,
	"signUpTimestamp": 5,
	"createdAt":       17,
	"updatedAt":       18,
}

var listUsersByEmailASCQry = database.NewStatement(`
	SELECT
		id, email, "firstName", "lastName", "signUpTimestamp", fingerprint, "googleOauthMetadata", "appleID", "googleID",
		"roleIDs", about, avatar, social, timezone, "marketingOptIn", "createdAt", "updatedAt",
		"notificationSettings", "isTestUser", "paddleCustomerID", "onboarding", lobbies
	FROM users
	WHERE email LIKE :email
	ORDER BY (CASE CAST(:orderBy AS INTEGER)
			WHEN 1 THEN CAST("id" AS TEXT)
			WHEN 2 THEN CAST("email" AS TEXT)
			WHEN 3 THEN CAST("firstName" AS TEXT)
			WHEN 4 THEN CAST("lastName" AS TEXT)
			WHEN 5 THEN CAST("signUpTimestamp" AS TEXT)
			WHEN 17 THEN CAST("createdAt" AS TEXT)
			WHEN 18 THEN CAST("updatedAt" AS TEXT)
        END) ASC
	LIMIT :limit
	OFFSET :offset
`)

var listUsersByEmailDESCQry = database.NewStatement(`
	SELECT
		id, email, "firstName", "lastName", "signUpTimestamp", fingerprint, "googleOauthMetadata", "appleID", "googleID",
		"roleIDs", about, avatar, social, timezone, "marketingOptIn", "createdAt", "updatedAt",
		"notificationSettings", "isTestUser", "paddleCustomerID", "onboarding", lobbies
	FROM users
	WHERE email LIKE :email
	ORDER BY (CASE CAST(:orderBy AS INTEGER)
		WHEN 1 THEN CAST("id" AS TEXT)
		WHEN 2 THEN CAST("email" AS TEXT)
		WHEN 3 THEN CAST("firstName" AS TEXT)
		WHEN 4 THEN CAST("lastName" AS TEXT)
		WHEN 5 THEN CAST("signUpTimestamp" AS TEXT)
		WHEN 17 THEN CAST("createdAt" AS TEXT)
		WHEN 18 THEN CAST("updatedAt" AS TEXT)
			END) DESC
	LIMIT :limit
	OFFSET :offset
`)

var listUsersASCQry = database.NewStatement(`
	SELECT
		id, email, "firstName", "lastName", "signUpTimestamp", fingerprint, "googleOauthMetadata", "appleID", "googleID",
		"roleIDs", about, avatar, social, timezone, "marketingOptIn", "createdAt", "updatedAt",
		"notificationSettings", "isTestUser", "paddleCustomerID", "onboarding", lobbies
	FROM users
	ORDER BY (CASE CAST(:orderBy AS INTEGER)
		WHEN 1 THEN CAST("id" AS TEXT)
		WHEN 2 THEN CAST("email" AS TEXT)
		WHEN 3 THEN CAST("firstName" AS TEXT)
		WHEN 4 THEN CAST("lastName" AS TEXT)
		WHEN 5 THEN CAST("signUpTimestamp" AS TEXT)
		WHEN 17 THEN CAST("createdAt" AS TEXT)
		WHEN 18 THEN CAST("updatedAt" AS TEXT)
			END) ASC
	LIMIT :limit
	OFFSET :offset
`)

var listUsersDESCQry = database.NewStatement(`
	SELECT
		id, email, "firstName", "lastName", "signUpTimestamp", fingerprint, "googleOauthMetadata", "appleID", "googleID",
		"roleIDs", about, avatar, social, timezone, "marketingOptIn", "createdAt", "updatedAt",
		"notificationSettings", "isTestUser", "paddleCustomerID", "onboarding", lobbies
	FROM users
		ORDER BY (CASE CAST(:orderBy AS INTEGER)
			WHEN 1 THEN CAST("id" AS TEXT)
			WHEN 2 THEN CAST("email" AS TEXT)
			WHEN 3 THEN CAST("firstName" AS TEXT)
			WHEN 4 THEN CAST("lastName" AS TEXT)
			WHEN 5 THEN CAST("signUpTimestamp" AS TEXT)
			WHEN 17 THEN CAST("createdAt" AS TEXT)
			WHEN 18 THEN CAST("updatedAt" AS TEXT)
        END) DESC
	LIMIT :limit
	OFFSET :offset
`)

var countUsersByEmailQry = database.NewStatement(`
	SELECT COUNT(*) FROM users
	WHERE email LIKE :email
`)

var countUsersQry = database.NewStatement(`
	SELECT COUNT(*) FROM users
`)

var GetPaddleCustomerByUserID = database.NewStatement(`
	SELECT "paddleCustomerID" FROM users WHERE id = :userID
`)

var assignUserOnboarding = database.NewStatement(`
	UPDATE users
	SET onboarding = CASE
	WHEN onboarding IS NULL THEN
		CAST(:onboarding AS jsonb)
	ELSE
		onboarding || CAST(:onboarding AS jsonb)
	END
	WHERE id = :id
`)

var updateUserCmd = database.NewStatement(`
	UPDATE users
	SET "avatar" = :avatar, "firstName" = :firstName, "lastName" = :lastName, "about" = :about, "social" = :social, "updatedAt" = :updatedAt
	WHERE id = :id
`)

func init() {
	Statements = append(Statements, database.Statements{createUserWithIDs, getUserByID, GetPaddleCustomerByUserID, assignUserOnboarding,
		listUsersByEmailASCQry,
		listUsersByEmailDESCQry,
		countUsersByEmailQry,
		listUsersASCQry,
		listUsersDESCQry,
		countUsersQry,
		updateUserCmd,
	}...)
}

type CreateUserWithIDsRequest struct {
	UserEmail        string
	UserFirstName    string
	UserLastName     string
	RoleIDs          []string
	IsTestUser       *bool
	Fingerprint      *string
	Timezone         *string
	Avatar           *string
	About            *string
	MarketingOptIn   *bool
	PaddleCustomerID *string
	SignUpTimestamp  *int64
	CreatedAt        *int64
	UpdatedAt        *int64
}

func InsertUser(ctx context.Context, req CreateUserWithIDsRequest) (*string, error) {
	nowTimestamp := time.Now().Unix()

	if req.SignUpTimestamp == nil {
		req.SignUpTimestamp = lo.ToPtr(nowTimestamp)
	}

	if req.CreatedAt == nil {
		req.CreatedAt = lo.ToPtr(nowTimestamp)
	}

	if req.UpdatedAt == nil {
		req.UpdatedAt = lo.ToPtr(nowTimestamp)
	}

	if req.IsTestUser == nil {
		req.IsTestUser = lo.ToPtr(false)
	}

	if req.MarketingOptIn == nil {
		req.MarketingOptIn = lo.ToPtr(false)
	}

	if req.PaddleCustomerID == nil {
		req.PaddleCustomerID = lo.ToPtr("")
	}

	id := ""
	if err := NewSnowflakeID.GetValues(ctx, database.Args{}, &id); err != nil {
		return nil, err
	}

	_, err := createUserWithIDs.Execx(ctx, map[string]interface{}{
		"id":                   id,
		"email":                req.UserEmail,
		"firstName":            req.UserFirstName,
		"lastName":             req.UserLastName,
		"roleIDs":              pq.Array(req.RoleIDs),
		"fingerprint":          req.Fingerprint,
		"timezone":             req.Timezone,
		"avatar":               req.Avatar,
		"about":                req.About,
		"marketingOptIn":       req.MarketingOptIn,
		"paddleCustomerID":     req.PaddleCustomerID,
		"notificationSettings": "{}",
		"isTestUser":           req.IsTestUser,
		"signUpTimestamp":      req.SignUpTimestamp,
		"createdAt":            req.CreatedAt,
		"updatedAt":            req.UpdatedAt,
	})
	if err != nil {
		return nil, err
	}

	return &id, nil
}

func GetUserByID(ctx context.Context, id string) (*UserRow, error) {
	row := UserRow{}
	if err := getUserByID.GetStruct(ctx, database.Args{"id": id}, &row); err != nil {
		return nil, err
	}

	return &row, nil
}

type ListUsersRequest struct {
	Limit          int
	Offset         int
	OrderBy        string
	OrderDirection string
}

func ListUsers(ctx context.Context, req *ListUsersRequest) ([]UserRow, error) {
	var err error

	userRows := []UserRow{}

	var query *database.ValidatedStmt
	switch req.OrderDirection {
	case "ASC":
		query = listUsersASCQry
		break
	case "DESC":
		query = listUsersDESCQry
		break
	default:
		query = listUsersASCQry
		break
	}

	orderBy, ok := usersTableOrderByKeyFromColumnName[req.OrderBy]
	if !ok {
		return nil, fmt.Errorf("invalid orderBy: %s", req.OrderBy)
	}

	query.Queryx(ctx, database.Args{
		"orderBy":        orderBy,
		"limit":          req.Limit,
		"offset":         req.Offset,
		"orderDirection": req.OrderDirection,
	}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var userRow UserRow
		if scanErr := r.StructScan(&userRow); scanErr != nil {
			err = scanErr
			return false
		}

		userRows = append(userRows, userRow)
		return true
	})
	if err != nil {
		return nil, err
	}

	return userRows, nil
}

func CountUsers(ctx context.Context) (int, error) {
	var count int
	err := countUsersQry.GetValues(ctx, database.Args{}, &count)
	return count, err
}

type ListUsersByEmailRequest struct {
	Limit          int
	Offset         int
	OrderBy        string
	OrderDirection string
	Email          string
}

func ListUsersByEmail(ctx context.Context, req *ListUsersByEmailRequest) ([]UserRow, error) {
	var err error
	userRows := []UserRow{}

	var query *database.ValidatedStmt
	switch req.OrderDirection {
	case "ASC":
		query = listUsersByEmailASCQry
		break
	case "DESC":
		query = listUsersByEmailDESCQry
		break
	default:
		query = listUsersByEmailASCQry
		break
	}

	orderBy, ok := usersTableOrderByKeyFromColumnName[req.OrderBy]
	if !ok {
		return nil, fmt.Errorf("invalid orderBy: %s", req.OrderBy)
	}

	query.Queryx(ctx, database.Args{
		"orderBy": orderBy,
		"limit":   req.Limit,
		"offset":  req.Offset,
		"email":   req.Email,
	}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var userRow UserRow
		if scanErr := r.StructScan(&userRow); scanErr != nil {
			err = scanErr
			return false
		}

		userRows = append(userRows, userRow)
		return true
	})

	return userRows, err
}

type CountUsersByEmailRequest struct {
	Email string
}

func CountUsersByEmail(ctx context.Context, req *CountUsersByEmailRequest) (int, error) {
	var count int
	err := countUsersByEmailQry.GetValues(ctx, map[string]interface{}{
		"email": req.Email,
	}, &count)
	return count, err
}

func GetUsersMapByID(ctx context.Context, db *sqlx.DB, ids []string) (map[int64]UserRow, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	query, args, err := sqlx.In(`SELECT * FROM users WHERE "id" IN (?)`, ids)
	if err != nil {
		return nil, err
	}

	result := make(map[int64]UserRow)
	query = db.Rebind(query)
	rows, err := db.Unsafe().QueryxContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}

	defer rows.Close()
	for rows.Next() {
		row := UserRow{}
		if err = rows.StructScan(&row); err != nil {
			return nil, err
		}
		result[row.ID] = row
	}
	return result, nil
}

func GetUserSummariesByID(ctx context.Context, db *sqlx.DB, yield func(UserSummaryRow, error) bool, ids ...string) { // func(yield func(UserSummaryRow, error) bool) {
	// return func(yield func(UserSummaryRow, error) bool) {
	if len(ids) == 0 {
		return
	}
	query, args, err := sqlx.In(`SELECT * FROM users WHERE "id" IN (?)`, ids)
	if err != nil {
		yield(UserSummaryRow{}, err)
		return
	}

	query = db.Rebind(query)
	rows, err := db.Unsafe().QueryxContext(ctx, query, args...)
	if err != nil {
		yield(UserSummaryRow{}, err)
		return
	}

	defer rows.Close()
	for rows.Next() {
		row := UserSummaryRow{}
		if err = rows.StructScan(&row); err != nil {
			yield(UserSummaryRow{}, err)
			return
		}
		if !yield(row, nil) {
			break
		}
	}
	// }
}

func GetUserSummariesByEmail(ctx context.Context, db *sqlx.DB, yield func(UserSummaryRow, error) bool, emails ...string) { // func(yield func(UserSummaryRow, error) bool) {
	// return func(yield func(UserSummaryRow, error) bool) {
	if len(emails) == 0 {
		return
	}
	query, args, err := sqlx.In(`SELECT * FROM users WHERE "email" IN(?)`, emails)
	if err != nil {
		yield(UserSummaryRow{}, err)
		return
	}

	query = db.Rebind(query)
	rows, err := db.Unsafe().QueryxContext(ctx, query, args...)
	if err != nil {
		yield(UserSummaryRow{}, err)
		return
	}

	defer rows.Close()
	for rows.Next() {
		row := UserSummaryRow{}
		if err = rows.StructScan(&row); err != nil {
			yield(UserSummaryRow{}, err)
			return
		}
		if !yield(row, nil) {
			break
		}
	}
	// }
}

func UpdateUserOnboardingFlag(ctx context.Context, userID string, onboarding *shared.OnboardingFlags) error {
	onboardingJson, err := json.Marshal(onboarding)
	if err != nil {
		return err
	}
	_, err = assignUserOnboarding.Execx(ctx,
		database.Args{
			"id":         shared.ParseInt(userID),
			"onboarding": onboardingJson,
		},
	)
	return err
}

type UpdateUserRequest struct {
	UserID    string
	Avatar    *string
	FirstName *string
	LastName  *string
	About     *string
	Social    *shared.Socials
}

func UpdateUser(ctx context.Context, updateUserRequest UpdateUserRequest) error {
	var err error

	var nextSocialsJSON []byte
	if updateUserRequest.Social != nil {
		nextSocialsJSON, err = json.Marshal(updateUserRequest.Social)
		if err != nil {
			return err
		}
	}

	_, err = updateUserCmd.Execx(ctx, database.Args{
		"id":        shared.ParseInt(updateUserRequest.UserID),
		"avatar":    updateUserRequest.Avatar,
		"firstName": updateUserRequest.FirstName,
		"lastName":  updateUserRequest.LastName,
		"about":     updateUserRequest.About,
		"social":    nextSocialsJSON,
		"updatedAt": time.Now().Unix(),
	})

	return err
}
