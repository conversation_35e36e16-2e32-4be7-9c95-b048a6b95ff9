{"transactions": [{"address": {}, "addressID": "add_01hq87vc8f264gqv1hm36xzrrs", "adjustmentsTotals": {"breakdown": {}}, "billedAt": "2025-05-22T11:06:08.854426Z", "billingPeriod": {"endsAt": "2025-06-22T11:05:31.148223Z", "startsAt": "2025-05-22T11:05:31.148223Z"}, "business": {}, "checkout": {"url": "https://staging.rumi.ai/pricing/checkout?_ptxn=txn_01jvvtr22p66z9e2nfxrspfbp8"}, "collectionMode": "automatic", "createdAt": "2025-05-22T11:06:08.908902Z", "currencyCode": "USD", "customData": {"userID": "806428820482557594"}, "customer": {}, "customerID": "ctm_01hq87v6jn14vn84yzkhma2yf1", "details": {"adjustedPayoutTotals": {"chargebackFee": {"amount": "0"}, "currencyCode": "USD", "earnings": "712", "fee": "100", "subtotal": "812", "tax": "187", "total": "999"}, "adjustedTotals": {"currencyCode": "USD", "earnings": "712", "fee": "100", "grandTotal": "999", "subtotal": "812", "tax": "187", "total": "999"}, "lineItems": [{"id": "txnitm_01jvvtr23h9j32egjya3hnnw2d", "price_id": "pri_01hn32p3k1rcgzevmqjz1t9nej", "quantity": 1, "tax_rate": "0.23", "unit_totals": {"subtotal": "812", "discount": "0", "tax": "187", "total": "999"}, "totals": {"subtotal": "812", "discount": "0", "tax": "187", "total": "999"}, "product": {"id": "pro_01hn31q5grky5xcyp58401kp1v", "name": "Premium", "description": "Premium ", "type": "standard", "tax_category": "standard", "image_url": "", "status": "active", "created_at": "2024-01-26T14:10:29.528Z", "updated_at": "2024-01-26T14:10:29.528Z"}}], "payoutTotals": {"balance": "0", "credit": "0", "creditToBalance": "0", "currencyCode": "USD", "discount": "0", "earnings": "712", "fee": "100", "grandTotal": "999", "subtotal": "812", "tax": "187", "total": "999"}, "taxRatesUsed": [{"tax_rate": "0.23", "totals": {"subtotal": "812", "discount": "0", "tax": "187", "total": "999"}}], "totals": {"balance": "0", "credit": "0", "creditToBalance": "0", "currencyCode": "USD", "discount": "0", "earnings": "712", "fee": "100", "grandTotal": "999", "subtotal": "812", "tax": "187", "total": "999"}}, "discount": {}, "id": "txn_01jvvtr22p66z9e2nfxrspfbp8", "invoiceID": "inv_01jvvtr4ykc9z4qfedpdh0tv0a", "invoiceNumber": "5993-10323", "items": [{"price": {"billingCycle": {"frequency": 1, "interval": "month"}, "createdAt": "2024-01-26T14:27:23.361285Z", "description": "Monthly subscription", "id": "pri_01hn32p3k1rcgzevmqjz1t9nej", "name": "Monthly subscription", "product": {}, "productID": "pro_01hn31q5grky5xcyp58401kp1v", "quantity": {"maximum": 1, "minimum": 1}, "status": "active", "taxMode": "account_setting", "type": "standard", "unitPrice": {"amount": "999", "currencyCode": "USD"}, "updatedAt": "2024-01-26T14:27:23.361285Z"}, "quantity": 1}], "origin": "subscription_recurring", "payments": [{"amount": "999", "capturedAt": "2025-05-22T11:06:10.833986Z", "createdAt": "2025-05-22T11:06:08.996707Z", "methodDetails": {"card": {"cardholderName": "man", "expiryMonth": 1, "expiryYear": 2027, "last4": "5556", "type": "visa"}, "type": "card"}, "paymentAttemptID": "452c1358-331e-47f5-a7e3-668ee5b34458", "paymentMethodID": "paymtd_01hq87vs6cs2cc89c0c2nv0nyr", "status": "captured", "storedPaymentMethodID": "7a11f0ed-f80c-4fd1-bc9e-d103a3cbef20"}], "status": "completed", "subscriptionID": "sub_01hq87vxrvcsqg7gxc930ps4yt", "updatedAt": "2025-05-22T11:06:13.910239Z"}]}