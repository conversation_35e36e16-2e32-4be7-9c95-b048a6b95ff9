name: nebula feature deployments
on:
  pull_request:
    types: [opened, synchronize]
    paths-ignore:
      - "README.md"
      - "docs/**"
  workflow_dispatch:
    inputs:
      pr_number:
        description: "PR number to deploy"
        required: true
        type: number

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.event.inputs.pr_number || github.ref }}
  cancel-in-progress: true

jobs:
  check_pr_author:
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft != true
    outputs:
      should_run: ${{ steps.check_author.outputs.should_run }}
      pr_number: ${{ steps.set_pr_number.outputs.pr_number }}
    steps:
      - name: Check PR author
        id: check_author
        if: github.event_name == 'pull_request'
        run: |
          if [[ "${{ github.event.pull_request.user.login }}" == "dependabot[bot]" || "${{ github.event.pull_request.user.login }}" == "dependabot-preview[bot]" ]]; then
            echo "should_run=false" >> $GITHUB_OUTPUT
            echo "Skipping workflow for Dependabot PR"
          else
            echo "should_run=true" >> $GITHUB_OUTPUT
          fi

      - name: Set PR number
        id: set_pr_number
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "pr_number=${{ github.event.pull_request.number }}" >> $GITHUB_OUTPUT
          else
            echo "pr_number=${{ github.event.inputs.pr_number }}" >> $GITHUB_OUTPUT
          fi
          echo "should_run=true" >> $GITHUB_OUTPUT

  build_and_push:
    name: build_and_push
    needs: check_pr_author
    if: needs.check_pr_author.outputs.should_run == 'true'
    runs-on: ubuntu-latest
    env:
      APP_NAME: nebula
    # Add outputs section here to make variables available to other jobs
    outputs:
      branch: ${{ steps.extract_branch.outputs.branch }}
      pr_number: ${{ steps.extract_branch.outputs.pr_number }}
      env_name: ${{ steps.set_env_vars.outputs.env_name }}
      namespace: ${{ steps.set_env_vars.outputs.namespace }}
      image: ${{ steps.build-image.outputs.image }}

    steps:
      ## Checkout luxor
      - name: Checkout
        uses: actions/checkout@v2.4.0

      ## extract branch name and PR number for variable injection
      - name: Extract branch name and PR number
        shell: bash
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "branch=$(echo ${GITHUB_HEAD_REF})" >> $GITHUB_OUTPUT
            echo "pr_number=${{ github.event.pull_request.number }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            # For workflow_dispatch, get PR details using GitHub API
            PR_DATA=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              "https://api.github.com/repos/${{ github.repository }}/pulls/${{ needs.check_pr_author.outputs.pr_number }}")
            BRANCH=$(echo "$PR_DATA" | jq -r .head.ref)
            echo "branch=${BRANCH}" >> $GITHUB_OUTPUT
            echo "pr_number=${{ needs.check_pr_author.outputs.pr_number }}" >> $GITHUB_OUTPUT
          else
            echo "branch=$(echo ${GITHUB_REF#refs/heads/})" >> $GITHUB_OUTPUT
            echo "pr_number=0" >> $GITHUB_OUTPUT
          fi
        id: extract_branch

      ## Set environment variables based on event type
      - name: Set environment variables
        id: set_env_vars
        shell: bash
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ] || [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "ENV_NAME=pr-${{ steps.extract_branch.outputs.pr_number }}" >> $GITHUB_ENV
            echo "NAMESPACE=pr-${{ steps.extract_branch.outputs.pr_number }}" >> $GITHUB_ENV
            echo "env_name=pr-${{ steps.extract_branch.outputs.pr_number }}" >> $GITHUB_OUTPUT
            echo "namespace=pr-${{ steps.extract_branch.outputs.pr_number }}" >> $GITHUB_OUTPUT
          else
            echo "ENV_NAME=${{ steps.extract_branch.outputs.branch }}" >> $GITHUB_ENV
            echo "NAMESPACE=${{ steps.extract_branch.outputs.branch }}" >> $GITHUB_ENV
            echo "env_name=${{ steps.extract_branch.outputs.branch }}" >> $GITHUB_OUTPUT
            echo "namespace=${{ steps.extract_branch.outputs.branch }}" >> $GITHUB_OUTPUT
          fi

      ## Notify Slack of Build Status
      - name: Build notification
        uses: edge/simple-slack-notify@master
        with:
          text: "${{ env.APP_NAME }} ${{ steps.extract_branch.outputs.branch }}
            Build Starting"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      ## Configure aws credentials (Use AWS_GITLAB_ECR user)
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      ## Log into AWS so the image can be pushed
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      ## Build the docker image and Puch it into the AWS Elastic Container Registery
      - name: Build, tag, and push the image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ env.APP_NAME }}
          ENV_NAME: ${{ env.ENV_NAME }}
        run: |
          # Build a docker image and push it to ECR 
          cd nebula
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$ENV_NAME-$GITHUB_RUN_NUMBER .
          echo "Pushing image to ECR..."
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$ENV_NAME-$GITHUB_RUN_NUMBER
          echo "image=$ECR_REGISTRY/$ECR_REPOSITORY:$ENV_NAME-$GITHUB_RUN_NUMBER" >> $GITHUB_OUTPUT

      ## Notify Slack of the docker build status
      - name: Build notification
        uses: edge/simple-slack-notify@master
        with:
          text: "${{ env.APP_NAME }} ${{ steps.extract_branch.outputs.branch }}
            Build Has Completed"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  #Deploy the latest image to helm
  helm_deploy:
    needs: [check_pr_author, build_and_push]
    if: needs.check_pr_author.outputs.should_run == 'true'
    name: helm_deploy
    runs-on: ubuntu-latest
    env:
      APP_NAME: nebula
      ENV_NAME: ${{ needs.build_and_push.outputs.env_name }}
      NAMESPACE: ${{ needs.build_and_push.outputs.namespace }}
    steps:
      ## Checkout luxor
      - name: Checkout
        uses: actions/checkout@v2.4.0

      ## Checkout generic-app
      - name: Checkout
        uses: actions/checkout@v2.4.0
        with:
          token: ${{ secrets.HELM_GH_TOKEN }}
          repository: Waitroom/generic-app
          ref: main
          path: ./app

      ## Notify Slack that the deployment is starting
      - name: Deploy notification
        uses: edge/simple-slack-notify@master
        with:
          text: "${{ env.APP_NAME }} ${{ needs.build_and_push.outputs.branch }}
            Deployment is Starting"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
      ## Create Secrets file and Update Values file
      - name: Set up Python 3.10
        uses: actions/setup-python@v3
        with:
          python-version: "3.10"

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install requests pyyaml neon-api

      # In the helm_deploy job, replace the "Create Secrets & Update Values" step with this:
      - name: Create Secrets & Update Values
        env:
          OP_TOKEN: ${{ secrets.OP_TOKEN }}
          VAULT_URL: ${{ secrets.VAULT_URL }}
          NEON_API_KEY: ${{ secrets.NEON_API_KEY }}
          NEON_PROJECT_ID: ${{ secrets.NEON_PROJECT_ID }}
          DB_OWNER_PASSWORD: ${{ secrets.DB_OWNER_PASSWORD }}
        run: |
          # Get database connection string for PR
          if [ "${{ github.event_name }}" = "pull_request" ] || [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            PR_NUMBER=$(echo $ENV_NAME | cut -d'-' -f2)

            # Create directory for connection files
            mkdir -p temp_connections
            
            # Values file path
            VALUES_FILE=".github/values/nebula/nebula-feature-values.yaml"
            
            # Get connection strings for all needed services
            for SERVICE in nebula mars luxor; do
              # Get connection string and save to a temporary file
              python deployments/scripts/neon_service_connections.py --project-id $NEON_PROJECT_ID --pr-number $PR_NUMBER --service $SERVICE --db-owner-password $DB_OWNER_PASSWORD --output-file temp_connections/${SERVICE}_connection.txt
              
              if [ -f "temp_connections/${SERVICE}_connection.txt" ]; then
                # Read the connection string from the file
                CONNECTION_STRING=$(cat temp_connections/${SERVICE}_connection.txt)
                
                # Extract components using sed
                USERNAME=$(echo $CONNECTION_STRING | sed -n 's/postgresql:\/\/\([^:]*\):.*/\1/p')
                PASSWORD=$(echo $CONNECTION_STRING | sed -n 's/postgresql:\/\/[^:]*:\([^@]*\)@.*/\1/p')
                HOST=$(echo $CONNECTION_STRING | sed -n 's/postgresql:\/\/[^@]*@\([^:]*\):.*/\1/p')
                PORT=$(echo $CONNECTION_STRING | sed -n 's/postgresql:\/\/[^@]*@[^:]*:\([^/]*\)\/.*/\1/p')
                DB_NAME=$(echo $CONNECTION_STRING | sed -n 's/postgresql:\/\/[^@]*@[^/]*\/\(.*\)/\1/p')
                
                # Update values based on which service we're processing
                if [ "$SERVICE" = "nebula" ]; then
                  # Update nebula database settings - find and replace the value in the correct format
                  sed -i "/name: POSTGRES_DB/{ n; s|value:.*|value: \"$DB_NAME\"|; }" $VALUES_FILE
                  sed -i "/name: POSTGRES_USER/{ n; s|value:.*|value: \"$USERNAME\"|; }" $VALUES_FILE
                  sed -i "/name: POSTGRES_PASS/{ n; s|value:.*|value: \"$PASSWORD\"|; }" $VALUES_FILE
                  sed -i "/name: POSTGRES_HOST/{ n; s|value:.*|value: \"$HOST\"|; }" $VALUES_FILE
                  sed -i "/name: POSTGRES_PORT/{ n; s|value:.*|value: \"$PORT\"|; }" $VALUES_FILE
                elif [ "$SERVICE" = "mars" ]; then
                  # Update mars database settings
                  sed -i "/name: MARS_DB_DATABASE/{ n; s|value:.*|value: \"$DB_NAME\"|; }" $VALUES_FILE
                  sed -i "/name: MARS_DB_USER/{ n; s|value:.*|value: \"$USERNAME\"|; }" $VALUES_FILE
                  sed -i "/name: MARS_DB_PASSWORD/{ n; s|value:.*|value: \"$PASSWORD\"|; }" $VALUES_FILE
                  sed -i "/name: MARS_DB_HOST/{ n; s|value:.*|value: \"$HOST\"|; }" $VALUES_FILE
                  sed -i "/name: MARS_DB_PORT/{ n; s|value:.*|value: \"$PORT\"|; }" $VALUES_FILE
                  sed -i "/name: LUXOR_DB_DATABASE/{ n; s|value:.*|value: \"$DB_NAME\"|; }" $VALUES_FILE
                  sed -i "/name: LUXOR_DB_USER/{ n; s|value:.*|value: \"$USERNAME\"|; }" $VALUES_FILE
                  sed -i "/name: LUXOR_DB_PASSWORD/{ n; s|value:.*|value: \"$PASSWORD\"|; }" $VALUES_FILE
                  sed -i "/name: LUXOR_DB_HOST/{ n; s|value:.*|value: \"$HOST\"|; }" $VALUES_FILE
                  sed -i "/name: LUXOR_DB_PORT/{ n; s|value:.*|value: \"$PORT\"|; }" $VALUES_FILE
                elif [ "$SERVICE" = "luxor" ]; then
                  # Update luxor database settings
                  echo "The Luxor database is now using the same database as mars"
                fi
              else
                echo "Failed to get connection string for $SERVICE from Neon API"
                exit 1
              fi
            done
            
            # Clean up temporary files
            rm -rf temp_connections
            
            # Update Kafka topics with PR prefix - replace existing values
            sed -i "/name: KAFKA_TOPIC_SESSIONS_UPDATES/{ n; s|value:.*|value: \"pr-$PR_NUMBER-sessions-updates\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_SESSIONS_EVENTS/{ n; s|value:.*|value: \"pr-$PR_NUMBER-sessions-events\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_USER_UPDATES/{ n; s|value:.*|value: \"pr-$PR_NUMBER-user-updates\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_TEAM_UPDATES/{ n; s|value:.*|value: \"pr-$PR_NUMBER-team-updates\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_USER_LEADS_UPDATES/{ n; s|value:.*|value: \"pr-$PR_NUMBER-user-leads-updates\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_SESSION_RECORDING_UPDATES/{ n; s|value:.*|value: \"pr-$PR_NUMBER-session-recording-updates\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_LIVE_TRANSCRIPTS/{ n; s|value:.*|value: \"pr-$PR_NUMBER-sessions-live-transcriptions\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_BATCH_TRANSCRIPTIONS/{ n; s|value:.*|value: \"pr-$PR_NUMBER-sessions-live-transcriptions-batch\"|; }" $VALUES_FILE
            sed -i "/name: KAFKA_TOPIC_IMAGE_UPLOADS/{ n; s|value:.*|value: \"pr-$PR_NUMBER-image-upload\"|; }" $VALUES_FILE
            
            # Update temporal queue prefix
            sed -i "/name: TEMPORAL_QUEUE_PREFIX/{ n; s|value:.*|value: \"pr-$PR_NUMBER\"|; }" $VALUES_FILE
            
            # Update the internal service URLs to point to K8s service names
            sed -i "/name: NEBULA_BASE_URL/{ n; s|value:.*|value: \"http://nebula-feature-pr-$PR_NUMBER:8000\"|; }" $VALUES_FILE
            sed -i "/name: DRACONIDS_BASE_URL/{ n; s|value:.*|value: \"http://draconids-feature-pr-$PR_NUMBER:3000\"|; }" $VALUES_FILE
            sed -i "/name: AURORA_BASE_URL/{ n; s|value:.*|value: \"http://aurora-feature-pr-$PR_NUMBER:3000\"|; }" $VALUES_FILE
            sed -i "/name: MARS_BASE_URL/{ n; s|value:.*|value: \"http://mars-feature-pr-$PR_NUMBER:3000\"|; }" $VALUES_FILE
            sed -i "/name: IO_SERVER_BASE_URL/{ n; s|value:.*|value: \"http://io-server-feature-pr-$PR_NUMBER:3000\"|; }" $VALUES_FILE
            sed -i "/name: LUXOR_BASE_URL/{ n; s|value:.*|value: \"http://luxor-feature-pr-$PR_NUMBER:3000\"|; }" $VALUES_FILE

            # Set the wormhole URL to the required format
            sed -i "/name: WORMHOLE_BASE_URL/{ n; s|value:.*|value: \"https://pr$PR_NUMBER-elio-bzw2.encr.app\"|; }" $VALUES_FILE
            sed -i "/name: ELIO_BASE_URL/{ n; s|value:.*|value: \"http://pr$PR_NUMBER-elio-bzw2.encr.app\"|; }" $VALUES_FILE
            
            # Update temporal queue prefix
            sed -i "/name: TEMPORAL_QUEUE_PREFIX/{ n; s|value:.*|value: \"pr-$PR_NUMBER\"|; }" $VALUES_FILE
            # Update ingress subdomain with PR number
            sed -i "s/\$FEATBRANCH/pr-$PR_NUMBER/g" $VALUES_FILE
          fi

          # Generate secrets
          python deployments/scripts/helmOpInjector/generateSecrets.py -n feature-branch-defaults -p .github/values/nebula/nebula-feature-values.yaml
                  
          echo "After script execution:"
          ls -la .github/values/nebula/

      ## Check if this is the first build for the app, if so include the create namespace command
      - name: Prepare Helm command
        id: helmCMD
        shell: bash
        run: |
          echo "value=helm upgrade ${{ env.APP_NAME }}-${{ env.ENV_NAME }} ./app --force -i --wait --namespace=${{ env.NAMESPACE }} --create-namespace --set=deployment.image.tag=${{ env.ENV_NAME }}-${{ github.run_number }} --set=app.kubernetes.io/managed-by=Helm --set=meta.helm.sh/release-name=${{ env.APP_NAME }}-${{ env.ENV_NAME }} --set=meta.helm.sh/release-namespace=${{ env.NAMESPACE }} --values=.github/values/nebula/nebula-feature-values.yaml" >> $GITHUB_OUTPUT

      ## Configure AWS credentials
      - name: AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.EKS_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.EKS_AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1 # change as needed

      - name: Configure kubectl
        run: |
          aws eks update-kubeconfig --name ${{ secrets.WORKING_CLUSTER }} --region us-east-1
          echo 'KUBE_CONFIG_DATA<<EOF' >> $GITHUB_ENV
          echo $(cat ~/.kube/config | base64) >> $GITHUB_ENV
          echo 'EOF' >> $GITHUB_ENV

      - name: Create namespace if it doesn't exist
        run: |
          if ! kubectl get namespace ${{ env.NAMESPACE }} &> /dev/null; then
            echo "Namespace ${{ env.NAMESPACE }} doesn't exist. Creating..."
            kubectl create namespace ${{ env.NAMESPACE }}
          else
            echo "Namespace ${{ env.NAMESPACE }} already exists. Skipping creation."
          fi

      - name: Create secrets
        uses: koslibpro/helm-eks-action@master
        env:
          KUBE_CONFIG_DATA: "${{ env.KUBE_CONFIG_DATA }}"
        with:
          command: kubectl apply -f .github/values/${{ env.APP_NAME }}/nebula-feature-values-secrets.yaml -n ${{ env.NAMESPACE }}

      - name: Deploy PR
        id: Deploy_PR
        uses: koslibpro/helm-eks-action@master
        env:
          KUBE_CONFIG_DATA: ${{ env.KUBE_CONFIG_DATA }}
        with:
          command: ${{ steps.helmCMD.outputs.value }}

      ## Notify Slack that the deployment is complete
      - name: Deploy notification
        uses: edge/simple-slack-notify@master
        with:
          text: "${{ env.APP_NAME }} ${{ needs.build_and_push.outputs.branch }}
            Deployment Has Completed"
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
