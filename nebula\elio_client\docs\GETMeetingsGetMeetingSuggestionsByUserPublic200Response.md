# GETMeetingsGetMeetingSuggestionsByUserPublic200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**suggestions** | **List[str]** |  | 

## Example

```python
from elio_client.models.get_meetings_get_meeting_suggestions_by_user_public200_response import GETMeetingsGetMeetingSuggestionsByUserPublic200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETMeetingsGetMeetingSuggestionsByUserPublic200Response from a JSON string
get_meetings_get_meeting_suggestions_by_user_public200_response_instance = GETMeetingsGetMeetingSuggestionsByUserPublic200Response.from_json(json)
# print the JSON string representation of the object
print(GETMeetingsGetMeetingSuggestionsByUserPublic200Response.to_json())

# convert the object into a dict
get_meetings_get_meeting_suggestions_by_user_public200_response_dict = get_meetings_get_meeting_suggestions_by_user_public200_response_instance.to_dict()
# create an instance of GETMeetingsGetMeetingSuggestionsByUserPublic200Response from a dict
get_meetings_get_meeting_suggestions_by_user_public200_response_from_dict = GETMeetingsGetMeetingSuggestionsByUserPublic200Response.from_dict(get_meetings_get_meeting_suggestions_by_user_public200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


