-- subscription_plan_configs
INSERT INTO
  subscription_plan_configs (
    id,
    "paddleProductID",
    "paddleProductName",
    meetings,
    "queueMode",
    integrations,
    stream,
    "aiFeed",
    "meetingSummary",
    support,
    "offTheRecord",
    recording,
    "customFeedItems",
    "meetingTemplates",
    "meetingWorkflows",
    "meetingMemory",
    "customIntegrations",
    "modelSegregation",
    "createdAt",
    "updatedAt",
    "timeLimit",
    "crm",
    "bots"
  )
VALUES
  (
    943368825976391288,
    'pro_01hdd98axk4eqszvr6fkbm7z0c',
    'enterprise',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"apps": null, "enabled": true}',
    '{"enabled": true, "quality": 1080}',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"type": "premium", "enabled": true}',
    '{"enabled": true}',
    '{"local": true, "enabled": true}',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"enabled": true}',
    1706691327,
    1706691327,
    '{"max": 9999999, "enabled": true}',
    '{}',
    '{}'
  ),
  (
    943369722081052282,
    'pro_01hnastrkep1fd3f9y08cym4r6',
    'free',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"apps": ["gcal", "outlook"], "enabled": true}',
    '{"enabled": true, "quality": 720}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"type": "basic", "enabled": true}',
    '{"enabled": false}',
    '{"local": false, "enabled": false}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"enabled": false}',
    1706691434,
    1706691434,
    '{"max": 9999999, "enabled": true}',
    '{}',
    '{}'
  ),
  (
    943369326642071161,
    'pro_01hn31q5grky5xcyp58401kp1v',
    'premium',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"apps": null, "enabled": true}',
    '{"enabled": true, "quality": 1080}',
    '{"enabled": true}',
    '{"enabled": true}',
    '{"type": "premium", "enabled": true}',
    '{"enabled": true}',
    '{"local": true, "enabled": true}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"enabled": false}',
    '{"enabled": true}',
    '{"enabled": false}',
    '{"enabled": false}',
    1706691387,
    1706691387,
    '{"max": 9999999, "enabled": true}',
    '{}',
    '{}'
  );

-- user_subscription_plans
INSERT INTO
  user_subscription_plans (
    "id",
    "paddleSubscriptionID",
    "userID",
    "planConfigID",
    "planConfigOverrides",
    "trialEndsAt",
    status,
    "customerID",
    "addressID",
    "businessID",
    "currencyCode",
    "startedAt",
    "firstBilledAt",
    "nextBilledAt",
    "pausedAt",
    "canceledAt",
    "collectionMode",
    "billingDetails",
    "currentBillingPeriod",
    "billingCycle",
    "scheduledChange",
    items,
    "customData",
    "createdAt",
    "updatedAt"
  )
VALUES
  (
   	1,
    'sub_01hnfk4wkv9gbmrv72fhsq1mgt1',
    1,
    943369326642071161,
    NULL,
    **********,
    'active',
    'ctm_01hnfk339kxyjc6504m6m6yp9s',
    'add_01hnfk409pz4hc9ve4v9a2wvjn',
    NULL,
    'USD',
    '2024-01-31T11:05:58.139Z',
    NULL,
    NULL,
    NULL,
    '2024-02-14T11:06:00.679Z',
    'automatic',
    NULL,
    NULL,
    '{"interval": "month", "frequency": 1}',
    NULL,
    '[{"price": {"id": "pri_01hkpwf6jrrs65n0rk1wtktvqy", "taxMode": "account_setting", "productID": "pro_01hdd98039vq8qsd44rx9gvybh", "unitPrice": {"amount": "999", "currencyCode": "USD"}, "description": "Monthly with trial subscription", "trialPeriod": {"interval": "day", "frequency": 14}, "billingCycle": {"interval": "month", "frequency": 1}}, "status": "trialing", "quantity": 1, "createdAt": "2024-01-31T11:05:58.139Z", "recurring": true, "updatedAt": "2024-02-14T11:06:00.686Z", "trialDates": {"endsAt": "2024-02-14T11:05:07Z", "startsAt": "2024-01-31T11:05:58.139Z"}, "nextBilledAt": null, "previouslyBilledAt": null}]',
    NULL,
    **********,
    **********
  ),
  (
   2,
    'sub_01hnfk4wkv9gbmrv72fhsq1mgt2',
    3,
    943369326642071161,
    '{"foobar": "test"}',
    **********,
    'active',
    'ctm_01hnfk339kxyjc6504m6m6yp9s',
    'add_01hnfk409pz4hc9ve4v9a2wvjn',
    NULL,
    'USD',
    '2024-01-31T11:05:58.139Z',
    NULL,
    NULL,
    NULL,
    '2024-02-14T11:06:00.679Z',
    'automatic',
    NULL,
    NULL,
    '{"interval": "month", "frequency": 1}',
    NULL,
    '[{"price": {"id": "pri_01hkpwf6jrrs65n0rk1wtktvqz", "taxMode": "account_setting", "productID": "pro_01hdd98039vq8qsd44rx9gvybh", "unitPrice": {"amount": "999", "currencyCode": "USD"}, "description": "Monthly with trial subscription", "trialPeriod": {"interval": "day", "frequency": 14}, "billingCycle": {"interval": "month", "frequency": 1}}, "status": "trialing", "quantity": 1, "createdAt": "2024-01-31T11:05:58.139Z", "recurring": true, "updatedAt": "2024-02-14T11:06:00.686Z", "trialDates": {"endsAt": "2024-02-14T11:05:07Z", "startsAt": "2024-01-31T11:05:58.139Z"}, "nextBilledAt": null, "previouslyBilledAt": null}]',
    NULL,
    **********,
    **********
  );
