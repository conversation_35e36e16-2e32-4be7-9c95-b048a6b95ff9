package wormhole

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"time"

	"encore.app/shared"
	"encore.app/wormhole/api"
	nebula_api "encore.app/wormhole/services/nebula-api"
	"encore.dev/rlog"
)

// PostSessionSummary Request the PSS payload from nebula.
//
//encore:api private tag:trixta
func (w *Wormhole) PostSessionSummary(ctx context.Context, req *api.PostSessionSummaryRequest) (*api.PostSessionSummaryResponse, error) {
	nebulaReq := &nebula_api.GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams{SessionRecurrenceID: req.SessionRecurrenceID}
	body := make([]byte, 0)
	status := 0
	// Attempt to fetch the PSS 10 times with max timeout of 5 seconds so the total time spent is 50 seconds, being under the 1-minute maximum
	if err := shared.Retry(10, 100*time.Millisecond, func(attempt int) error {
		timedCtx, cancelFn := context.WithTimeout(ctx, 5*time.Second)
		resp, httpErr := w.NebulaClient.GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet(timedCtx, req.SessionID, nebulaReq)
		if httpErr == nil {
			status = resp.StatusCode
			body, _ = io.ReadAll(resp.Body)
		}
		cancelFn()
		return httpErr
	}); err != nil {
		return nil, err
	}

	if status == http.StatusNotFound {
		return api.NewPostSessionSummaryResponse(shared.PostSessionSummaryNotFound), nil
	}

	if status == http.StatusAccepted {
		return api.NewPostSessionSummaryResponse(shared.PostSessionSummaryPending), nil
	}

	parsedResponse := api.NewPostSessionSummaryResponse(shared.HttpSuccessCode)
	err := json.Unmarshal(body, parsedResponse)
	if err != nil {
		rlog.Error("error unmarshalling PostSessionSummary response", "error", err, "body", string(body))
		return nil, err
	}

	return parsedResponse, nil
}
