from nebula.db.models.xray_doc_commit import XRayDocumentCommit
from piccolo.columns import BigSerial, Text, Timestamp, Boolean, BigInt, JSONB
from piccolo.columns.column_types import ForeignKey
from piccolo.columns.defaults.timestamp import TimestampNow
from piccolo.table import Table


class XRayDocumentCommitNotification(Table, tablename="xray_doc_commit_notifications"):
    """
    Represents a notification for a commit to an XRay document.
    It's generated for all three xray types and is surfaced both in the notifications bell like UI, as well as on the X-Ray page (Notifications tab).
    """

    id = BigSerial(primary_key=True)
    user_id = BigInt(null=False)

    xray_doc_commit_id = ForeignKey(references=XRayDocumentCommit, null=False)

    title = Text(null=False)
    content = Text(null=False)

    seen = Boolean(null=False, default=False)
    source = JSONB(
        null=False
    )  # For recurrences, e.g. "{ "type": "recurrence", "id": <bigint> }", where "id" is "recurrenceID" in marsDB
    created_at = Timestamp(default=TimestampNow())
    updated_at = Timestamp(default=TimestampNow())
