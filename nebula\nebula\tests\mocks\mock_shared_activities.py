from temporalio import activity
from typing import Union

from nebula.temporal.types import (
    MetadataWorkflowParams,
    MeetingSuggestionsWorkflowParams,
    TranscriptionBatchesResult,
    SessionDataModel,
    ProcessedTranscriptResult,
    SharedTranscriptionDTO,
)


class MockSharedActivities:
    """Shared mock activities that are used by multiple workflows"""
    
    @activity.defn
    async def get_transcription_batches(
        self, session_info: Union[MetadataWorkflowParams, MeetingSuggestionsWorkflowParams]
    ) -> TranscriptionBatchesResult:
        """Shared activity to get transcription batches for any workflow"""
        # Create properly formed SharedTranscriptionDTO objects with comprehensive content
        transcript_content = (
            "This is a comprehensive mock meeting transcript with substantial content to process. "
            "The team gathered today to discuss our Q4 planning and strategic initiatives. "
            "<PERSON> opened the meeting by presenting the quarterly roadmap, highlighting key milestones and deliverables. "
            "He emphasized the importance of staying aligned with our company objectives while maintaining high quality standards. "
            "Sarah raised important concerns about the proposed timeline, particularly regarding the engineering resources needed for the new features. "
            "She suggested we might need to reassess our capacity and potentially extend some deadlines to ensure thorough testing. "
            "Mike from the design team shared updates on the user interface mockups and gathered feedback from stakeholders. "
            "The team discussed various implementation approaches and their trade-offs. "
            "<PERSON> presented the marketing strategy for the upcoming product launch, including social media campaigns and PR initiatives. "
            "We reviewed the budget allocations and discussed potential risks and mitigation strategies. "
            "The conversation covered technical debt, performance optimization, and user experience improvements. "
            "Everyone agreed on the importance of maintaining clear communication channels throughout the project. "
            "Action items were assigned to team members with specific deadlines and ownership. "
            "The meeting concluded with a commitment to weekly check-ins and status updates. "
            "Overall, it was a productive discussion that set clear expectations for the coming quarter."
        )

        transcript1 = SharedTranscriptionDTO(
            batchID="batch1",
            id="transcript1",
            sessionID=session_info.session_id,
            sessionRecurrenceID=session_info.recurrence_id,
            speakerFullName="John Doe",
            speakerUID="speaker1",
            speakerUserID="user_123",
            text=transcript_content,
            timeUnix=1640995200,
        )

        transcript2 = SharedTranscriptionDTO(
            batchID="batch2",
            id="transcript2",
            sessionID=session_info.session_id,
            sessionRecurrenceID=session_info.recurrence_id,
            speakerFullName="Sarah Smith",
            speakerUID="speaker2",
            speakerUserID="user_456",
            text="Additional transcript content to ensure we have sufficient length for processing.",
            timeUnix=1640995300,
        )

        transcript3 = SharedTranscriptionDTO(
            batchID="batch3",
            id="transcript3",
            sessionID=session_info.session_id,
            sessionRecurrenceID=session_info.recurrence_id,
            speakerFullName="Test User",
            speakerUID="speaker3",
            speakerUserID="user_789",
            text="I agree with the previous points and would like to add my perspective on the implementation strategy.",
            timeUnix=1640995400,
        )

        return TranscriptionBatchesResult(
            transcription_batches=[transcript1, transcript2, transcript3]
        )

    @activity.defn
    async def get_session_data(
        self, session_info: Union[MetadataWorkflowParams, MeetingSuggestionsWorkflowParams]
    ) -> SessionDataModel:
        """Shared activity to get session data for any workflow"""        
        return SessionDataModel(
            meeting_id=f"{session_info.session_id}_{session_info.recurrence_id}",
            title="Mock Meeting - Quarterly Planning",
            participants=["John Doe", "Sarah Smith", "Test User"],
            type="Planning Meeting",
        )

    @activity.defn
    async def process_transcript(
        self, transcript_batches: TranscriptionBatchesResult
    ) -> ProcessedTranscriptResult:
        """Shared activity to process transcripts for any workflow"""
        # Combine all transcript content
        transcript_text = " ".join([
            batch.text for batch in transcript_batches.transcription_batches
        ])
        
        return ProcessedTranscriptResult(
            transcript=transcript_text,
            locale="en-US"
        ) 