# POSTTranscriptionsCreateBotForMeetingRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**meeting_link** | **str** |  | 

## Example

```python
from elio_client.models.post_transcriptions_create_bot_for_meeting_request import POSTTranscriptionsCreateBotForMeetingRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTTranscriptionsCreateBotForMeetingRequest from a JSON string
post_transcriptions_create_bot_for_meeting_request_instance = POSTTranscriptionsCreateBotForMeetingRequest.from_json(json)
# print the JSON string representation of the object
print(POSTTranscriptionsCreateBotForMeetingRequest.to_json())

# convert the object into a dict
post_transcriptions_create_bot_for_meeting_request_dict = post_transcriptions_create_bot_for_meeting_request_instance.to_dict()
# create an instance of POSTTranscriptionsCreateBotForMeetingRequest from a dict
post_transcriptions_create_bot_for_meeting_request_from_dict = POSTTranscriptionsCreateBotForMeetingRequest.from_dict(post_transcriptions_create_bot_for_meeting_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


