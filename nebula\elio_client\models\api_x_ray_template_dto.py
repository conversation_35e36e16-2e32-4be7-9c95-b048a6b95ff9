# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List
from elio_client.models.shared_user_dto import SharedUserDTO
from typing import Optional, Set
from typing_extensions import Self

class ApiXRayTemplateDTO(BaseModel):
    """
    ApiXRayTemplateDTO
    """ # noqa: E501
    created_at: StrictInt = Field(alias="createdAt")
    description: StrictStr
    icon: StrictStr
    id: StrictInt
    owner: SharedUserDTO
    owner_id: StrictInt = Field(alias="ownerId")
    prompt: StrictStr
    short_summary: StrictStr = Field(alias="shortSummary")
    title: StrictStr
    type: StrictStr
    updated_at: StrictInt = Field(alias="updatedAt")
    __properties: ClassVar[List[str]] = ["createdAt", "description", "icon", "id", "owner", "ownerId", "prompt", "shortSummary", "title", "type", "updatedAt"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ApiXRayTemplateDTO from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of owner
        if self.owner:
            _dict['owner'] = self.owner.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ApiXRayTemplateDTO from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "createdAt": obj.get("createdAt"),
            "description": obj.get("description"),
            "icon": obj.get("icon"),
            "id": obj.get("id"),
            "owner": SharedUserDTO.from_dict(obj["owner"]) if obj.get("owner") is not None else None,
            "ownerId": obj.get("ownerId"),
            "prompt": obj.get("prompt"),
            "shortSummary": obj.get("shortSummary"),
            "title": obj.get("title"),
            "type": obj.get("type"),
            "updatedAt": obj.get("updatedAt")
        })
        return _obj


