from typing import List, Dict, Any, Optional
from nebula.services.elio.service import get_session_participants
from nebula.services.search.common import (
    get_team_id_by_recurrence_id,
    get_team_members_by_team_id,
    get_user_by_id,
    get_team_info_by_id,
)
from nebula.temporal.activities.base import BaseActivity
from nebula.temporal.types import GetUserIdsForXRayScansActivityInput
from temporalio import activity


class UserActivities(BaseActivity):
    @activity.defn
    async def get_user_by_id(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Get user by id using direct database query, transforming to match Elio response structure TODO: move to elio service"""
        user = await get_user_by_id(user_id, self.mars_db)
        if not user:
            return None
        
        # Transform database result to match Elio response structure
        result = {
            "userId": user["userId"],
            "id": str(user["userId"]),  # Convert to string for compatibility
            "firstName": user["firstName"],
            "lastName": user["lastName"],
            "email": user["email"],
            "teamId": user["teamId"],
            "team": None,
        }
        
        # Create nested team object if user has a team (to match Elio response structure)
        if user["teamId"]:
            try:
                team_info = await get_team_info_by_id(user["teamId"], self.mars_db)
                if team_info:
                    result["team"] = {
                        "id": str(team_info["id"]),
                        "name": team_info["name"],
                        "domains": team_info["domains"],
                        "membersCount": team_info["totalMembers"],
                        "createdAt": team_info["createdAt"],
                        "updatedAt": team_info["createdAt"],  # Use createdAt as fallback
                        "role": "member",  # Default role, could be enhanced later
                    }
            except Exception:
                # If team info fetch fails, continue without team object
                pass
        
        return result

    @activity.defn
    async def get_user_ids_for_xray_scans(
        self, input: GetUserIdsForXRayScansActivityInput
    ) -> List[int]:
        """
        Get all user ids for xray scans. That is:
        1. All team members' user ids in case meeting is a team visible one
        2. All participants' user ids excluding frictionless users
        """

        team_id = await get_team_id_by_recurrence_id(
            recurrence_id=input.recurrence_id, mars_pool=self.mars_db
        )
        team_members_ids = []
        if team_id:
            team_members = await get_team_members_by_team_id(
                team_id=team_id, mars_pool=self.mars_db
            )
            team_members_ids = [int(member["userID"]) for member in team_members]

        session_participants = get_session_participants(
            session_id=str(input.session_id), recurrence_id=str(input.recurrence_id)
        )

        session_participants_ids = []
        for participant in session_participants:
            if participant.user is not None and participant.user.user_id is not None:
                session_participants_ids.append(int(participant.user.user_id))

        return list(set(team_members_ids + session_participants_ids))
