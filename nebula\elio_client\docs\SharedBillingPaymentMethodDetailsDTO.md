# SharedBillingPaymentMethodDetailsDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**card** | **object** |  | 
**type** | **str** |  | 

## Example

```python
from elio_client.models.shared_billing_payment_method_details_dto import SharedBillingPaymentMethodDetailsDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedBillingPaymentMethodDetailsDTO from a JSON string
shared_billing_payment_method_details_dto_instance = SharedBillingPaymentMethodDetailsDTO.from_json(json)
# print the JSON string representation of the object
print(SharedBillingPaymentMethodDetailsDTO.to_json())

# convert the object into a dict
shared_billing_payment_method_details_dto_dict = shared_billing_payment_method_details_dto_instance.to_dict()
# create an instance of SharedBillingPaymentMethodDetailsDTO from a dict
shared_billing_payment_method_details_dto_from_dict = SharedBillingPaymentMethodDetailsDTO.from_dict(shared_billing_payment_method_details_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


