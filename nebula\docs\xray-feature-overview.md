# X-Ray Feature - Deep Dive Documentation

## Overview & Purpose

**X-Ray** is Rumi's intelligent meeting analysis and documentation system that automatically processes meeting transcripts to create and maintain living documents based on user-defined intents. Think of it as "smart meeting intelligence" that continuously learns from meetings and builds knowledge repositories.

The system allows users to describe what they want to track from their meetings in plain language, then automatically generates structured prompts and processes all future (and historical) meetings to build comprehensive documentation.

## System Architecture

```mermaid
graph TD
    subgraph "🎯 X-Ray System Architecture"
        subgraph "🔥 Elio Service (Go)"
            A["🌐 REST API Gateway"]
            B["🔐 Authentication"]
            C["📋 Request Validation"]
        end
        
        subgraph "🐍 Nebula Service (Python)"
            D["🎯 X-Ray Service"]
            E["⚡ Temporal Workflows"]
            F["🤖 LLM Integration"]
            G["💾 Database Operations"]
        end
        
        subgraph "⏰ Temporal Workflows"
            H["🔍 XRayScanWorkflow<br/>(Meeting Processing)"]
            I["📊 XRayDigestWorkflow<br/>(Scheduled Summaries)"]
            J["🔄 XRayBackfillWorkflow<br/>(Historical Processing)"]
        end
        
        subgraph "🧠 AI/LLM Components"
            K["🎨 Document Creation<br/>(3-step process)"]
            L["⚡ Quick Scan<br/>(Relevance Detection)"]
            M["🔍 Deep Scan<br/>(Content Processing)"]
            N["🔔 Smart Notifications<br/>(0-3 per update)"]
        end
        
        subgraph "💾 Data Layer"
            O["🗄️ PostgreSQL<br/>(Primary Database)"]
            P["📊 Git-like Versioning<br/>(Commits & Blame)"]
            Q["🔔 Notification Storage"]
        end
        
        subgraph "🖥️ User Interface"
            R["📱 Notification Bell"]
            S["📋 X-Ray Dashboard"]
            T["📝 Document Viewer"]
            U["⚙️ Settings & Templates"]
        end
    end
    
    %% Data Flow
    A --> D
    D --> E
    E --> H
    E --> I
    E --> J
    
    H --> L
    H --> M
    M --> N
    
    D --> K
    K --> F
    F --> G
    G --> O
    
    O --> P
    O --> Q
    
    R --> S
    S --> T
    S --> U
    
    %% Temporal Scheduling
    I -.->|"Cron Schedule"| I
    
    style A fill:#e3f2fd
    style D fill:#e8f5e8
    style F fill:#fff3e0
    style O fill:#fce4ec
```

## Core Architecture

### Three X-Ray Types

The system supports three distinct document behaviors, each optimized for different use cases:

**1. Build Documents** 🔧
- **Living documents** that get intelligently updated
- Can modify, restructure, or remove outdated content
- Preserves human edits while updating AI-generated content
- Best for: Project documentation, meeting notes, evolving plans

**2. Monitor Documents** 📈  
- **Append-only logs** that track changes over time
- Never modifies existing content, only adds new information
- Maintains chronological audit trail with timestamps
- Best for: Status tracking, change logs, compliance documentation

**3. Digest Documents** 📊
- **Periodic summaries** generated on schedule (weekly/monthly)
- Replaces content with comprehensive time-period overview
- Aggregates information from multiple meetings
- Best for: Executive summaries, team updates, progress reports

### Service Split
- **Elio (Go)**: API gateway service that exposes REST endpoints
- **Nebula (Python)**: Core business logic, LLM processing, and Temporal workflows
- **Communication**: Elio forwards requests to Nebula via internal service calls

## X-Ray Creation Flow

The system uses a sophisticated 3-step creation process powered by structured LLM generation:

```mermaid
graph TD
    A["🚀 User Provides Description"] --> B["📋 Step 1: Generate Type & Prompt"]
    B --> C{{"🤖 OpenAI API<br/>Creates structured prompt<br/>and determines document type"}}
    C --> D["🔧 Step 2: Generate Metadata"]
    D --> E{{"🎨 OpenAI API<br/>Creates title, emoji,<br/>and short summary"}}
    E --> F["💾 Step 3: Create X-Ray Record"]
    F --> G{"🗂️ Document Type?"}
    
    G -->|Build/Monitor| H["✅ X-Ray Created & Ready"]
    G -->|Digest| I["⏰ Create Temporal Schedule"]
    I --> J["🔄 Schedule Cron Job<br/>(weekly/monthly)"]
    J --> H
    
    H --> K["🎯 X-Ray Active<br/>Will process future meetings"]
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style E fill:#fff3e0
    style H fill:#e8f5e8
    style K fill:#e8f5e8
```

### Step 1: Type & Prompt Generation
- **Input**: User's natural language description
- **LLM Task**: Determine optimal document type and create structured analysis prompt
- **Output**: `xray_type` (build/monitor/digest) + detailed `prompt`

### Step 2: Metadata Generation  
- **Input**: Document type and analysis prompt
- **LLM Task**: Generate user-friendly metadata
- **Output**: `title` (3-4 words), `emoji`, `short_summary`

### Step 3: Database Creation & Scheduling
- **Database**: Create X-Ray record with generated metadata
- **Digest Scheduling**: For digest types, automatically create Temporal cron schedule
- **Activation**: X-Ray becomes active and will process all future meetings

## Meeting Processing Flow

Every time a meeting ends, the system automatically processes it through all relevant X-Rays:

```mermaid
graph TD
    A["🎯 Meeting Ends"] --> B["🔥 AI End Meeting Worker"]
    B --> C["🔍 XRayScanWorkflow Triggered"]
    C --> D["📋 Get Active X-Rays<br/>(excluding digests)"]
    D --> E["📄 Get Meeting Transcript"]
    E --> F{{"📝 Transcript<br/>Available?"}}
    
    F -->|No| G["❌ Exit: No transcript"]
    F -->|Yes| H["⚡ Quick Scan Activity"]
    
    H --> I{{"🧠 LLM Analysis<br/>Identifies relevant<br/>X-Rays from transcript"}}
    I --> J["📊 Relevant X-Rays Found?"]
    
    J -->|No| K["✅ Exit: No relevant X-Rays"]
    J -->|Yes| L["🔄 For Each Relevant X-Ray"]
    
    L --> M["🔍 Deep Scan Activity"]
    M --> N{"📋 Document Type?"}
    
    N -->|Build| O["🔧 Process Build Document<br/>Update existing content"]
    N -->|Monitor| P["📈 Process Monitor Document<br/>Append new information"]
    N -->|Digest| Q["📊 Process Digest Document<br/>Create summary"]
    
    O --> R["💾 Create Commit"]
    P --> R
    Q --> R
    
    R --> S["🔔 Generate Smart Notifications<br/>(0-3 per update)"]
    S --> T["📱 Surface in UI"]
    T --> U["✅ Complete"]
    
    style A fill:#e3f2fd
    style I fill:#fff3e0
    style R fill:#e8f5e8
    style S fill:#f3e5f5
    style T fill:#e8f5e8
```

### Processing Steps Explained

#### 1. **Quick Scan** ⚡
- **Purpose**: Efficiently determine which X-Rays are relevant to the meeting
- **LLM Task**: Analyze transcript against all user's X-Ray prompts
- **Output**: List of relevant X-Ray IDs
- **Optimization**: Prevents expensive deep analysis on irrelevant documents

#### 2. **Deep Scan** 🔍  
- **Purpose**: Generate actual document updates for relevant X-Rays
- **Input**: Meeting transcript + existing document content + blame attribution
- **LLM Task**: Create new document version based on document type behavior
- **Output**: Updated document content

#### 3. **Document Type Processing**

```mermaid
graph LR
    subgraph "🔍 Document Processing Decision Tree"
        A["📝 Meeting Transcript"] --> B{{"🧠 Quick Scan<br/>LLM Analysis"}}
        B -->|"Relevant"| C["🔍 Deep Scan"]
        B -->|"Not Relevant"| D["❌ Skip Document"]
        
        C --> E{"📋 Document Type?"}
        
        E -->|"Build"| F["🔧 Build Processing"]
        E -->|"Monitor"| G["📈 Monitor Processing"]
        E -->|"Digest"| H["📊 Digest Processing"]
        
        F --> I["🎯 Build Behavior:<br/>• Update existing content<br/>• Modify/remove outdated info<br/>• Preserve human edits<br/>• Use blame attribution"]
        
        G --> J["📈 Monitor Behavior:<br/>• Append-only mode<br/>• Never modify existing<br/>• Add timestamps<br/>• Chronological order"]
        
        H --> K["📊 Digest Behavior:<br/>• Replace with summary<br/>• Aggregate time period<br/>• Comprehensive overview<br/>• Scheduled generation"]
        
        I --> L["💾 Create Commit"]
        J --> L
        K --> L
        
        L --> M["🔔 Generate 0-3<br/>Smart Notifications"]
        M --> N["📱 Surface in UI"]
    end
    
    style A fill:#e3f2fd
    style B fill:#fff3e0
    style E fill:#e3f2fd
    style I fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#fce4ec
    style M fill:#f3e5f5
```

## Digest Scheduling System

Digest-type X-Rays operate on automated schedules using Temporal's cron functionality:

```mermaid
graph TD
    A["⏰ Temporal Cron Schedule"] --> B["🔄 XRayDigestWorkflow Triggered"]
    B --> C["🔍 Get & Validate X-Ray"]
    C --> D{{"✅ Is Digest Type<br/>& Active?"}}
    
    D -->|No| E["❌ Exit: Invalid X-Ray"]
    D -->|Yes| F["👤 Get X-Ray Owner"]
    
    F --> G["📅 Calculate Time Range<br/>(since last_digest_at)"]
    G --> H["🔍 Get Meetings in Range"]
    H --> I{{"📋 Meetings<br/>Found?"}}
    
    I -->|No| J["✅ Exit: No new meetings"]
    I -->|Yes| K["📄 Aggregate All Transcripts"]
    
    K --> L["👥 Deduplicate Participants"]
    L --> M["📋 Get Current Document<br/>Content & Blame Info"]
    M --> N{{"🧠 LLM Analysis<br/>Generate comprehensive<br/>digest from all meetings"}}
    
    N --> O["💾 Create Digest Commit"]
    O --> P["🕒 Update last_digest_at"]
    P --> Q["✅ Digest Complete"]
    
    style A fill:#fff3e0
    style D fill:#e3f2fd
    style I fill:#e3f2fd
    style N fill:#fff3e0
    style O fill:#e8f5e8
    style Q fill:#e8f5e8
```

### Digest Features
- **Automatic Scheduling**: Created when digest X-Ray is created
- **Smart Time Ranges**: Processes meetings since `last_digest_at`
- **Meeting Aggregation**: Combines multiple meeting transcripts
- **Participant Deduplication**: Handles overlapping attendees
- **Schedule Updates**: Automatically updates when cron expression changes

## Manual Backfill Processing (Testing & Development)

The backfill system is primarily used for testing and manual historical processing:

```mermaid
graph TD
    A["🧪 Manual Backfill Trigger<br/>(Testing/Development)"] --> B["🔄 XRayBackfillScanWorkflow"]
    B --> C["👤 Get User Information"]
    C --> D["🔍 Get X-Ray Details"]
    D --> E{"🏢 Team or<br/>Personal Scope?"}
    
    E -->|Team| F["📊 Get Team Visible<br/>Past Recurrences"]
    E -->|Personal| G["👤 Get Personal<br/>Past Recurrences"]
    
    F --> H["📝 Combine All Recurrences"]
    G --> H
    
    H --> I["🔄 For Each Historical Meeting"]
    I --> J["🎯 Execute XRayScanWorkflow<br/>(Child Workflow)"]
    J --> K["📋 Process Meeting Transcript"]
    K --> L["💾 Update Document"]
    L --> M{{"🔄 More Meetings<br/>to Process?"}}
    
    M -->|Yes| I
    M -->|No| N["📊 Calculate Metrics"]
    N --> O["✅ Backfill Complete<br/>Report: Duration & Count"]
    
    style A fill:#fff3e0
    style E fill:#e3f2fd
    style J fill:#fff3e0
    style O fill:#e8f5e8
    
    classDef childWorkflow fill:#fce4ec
    class J childWorkflow
```

### Backfill Use Cases
- **Testing**: Validate X-Ray prompts against historical meetings
- **Development**: Debug and test workflow logic
- **Manual Processing**: Retroactively apply X-Rays to past meetings when needed
- **Quality Assurance**: Verify document generation quality

### Backfill Process Details
- **Manual Trigger**: Initiated via development tools or testing scripts
- **Scope Awareness**: Respects team vs. personal meeting visibility
- **Child Workflows**: Uses existing XRayScanWorkflow for each meeting
- **Progress Tracking**: Reports processing duration and meeting count
- **Efficient Processing**: Processes meetings in chronological order

## Technical Workflows

The system uses **Temporal workflows** for robust, fault-tolerant processing:

### Meeting Processing (`XRayScanWorkflow`)

**Purpose**: Process a single meeting against all user's X-Rays

**Trigger**: Automatically after meeting transcription completes

**Process Flow:**
1. **X-Ray Retrieval**: Get all active, non-digest X-Rays for user
2. **Transcript Retrieval**: Get meeting transcript
3. **Quick Scan**: LLM determines relevant X-Rays
4. **Deep Processing**: For each relevant X-Ray, generate document updates
5. **Commit Creation**: Save changes with git-like versioning
6. **Notification Generation**: Create 0-3 smart notifications per update

**Workflow Parameters:**
```python
XRayScanWorkflowParams(
    user_id: int,
    recurrence_id: int  # Meeting session ID
)
```

### Scheduled Digest Processing (`XRayDigestWorkflow`)

**Purpose**: Generate periodic summaries for digest-type X-Rays

**Trigger**: Cron-based Temporal schedule (e.g., weekly, monthly)

**Process Flow:**
1. **X-Ray Validation**: Verify X-Ray exists and is active
2. **Meeting Aggregation**: Collect meetings from the digest period
3. **Content Generation**: Use LLM to create comprehensive digest
4. **Commit Creation**: Save digest as new commit
5. **Timestamp Update**: Update `last_digest_at` for next scheduling

**Workflow Parameters:**
```python
XRayDigestWorkflowParams(
    xray_id: int
)
```

**Automatic Scheduling**:
- Created automatically when digest X-Ray is created
- Updated when `frequency` or `timezone` changes
- Uses Temporal's cron scheduling for reliability

### Backfill Processing (`XRayBackfillScanWorkflow`)

**Purpose**: Manual processing of historical meetings for testing and development

**Use Cases:**
- Testing X-Ray prompts against historical data
- Development and debugging workflows
- Quality assurance validation
- Manual retroactive processing when needed

**Process:**
- Retrieves user's past meetings (personal + team visible)
- Processes each meeting through the XRayScanWorkflow
- Builds comprehensive document from historical data
- Provides progress tracking and duration metrics

**Workflow Parameters:**
```python
XRayBackfillScanWorkflowParams(
    xray_id: int,
    user_id: int,
    tz: str
)
```

## Document Processing Logic

### Build Documents (`_process_build_document`)
```python
# Intelligently updates existing content
# Can modify, remove, or restructure information  
# Preserves human edits while updating AI content
# Uses blame info to understand edit history and author intent
```

**LLM Prompt Strategy:**
- Includes document attribution showing who edited each line
- Preserves human-authored content unless clearly outdated
- Modifies AI-generated content freely
- Maintains document structure and formatting

### Monitor Documents (`_process_monitor_document`)
```python
# Append-only behavior
# Never modifies existing content
# Adds timestamp separators for new entries
# Maintains chronological audit trail
```

**Processing Behavior:**
- Appends new content with meeting timestamp
- Preserves all existing information
- Creates clear separation between updates
- Notes contradictions or changes without removing old entries

### Digest Documents (`_process_digest_document`)
```python
# Complete content replacement
# Aggregates information from time period
# Creates comprehensive summary
# Uses previous digest as context
```

**Digest Strategy:**
- Analyzes all meetings since last digest
- Combines multiple transcripts and participants
- Replaces entire document content with new summary
- Maintains consistency with previous digest style

## Git-Like Version Control

### Commit System
Every document change creates a **commit** with:
- **Content**: Full document text at that point in time
- **Author**: User ID or AI system identifier
- **Timestamp**: When the change was made
- **Meeting Link**: Associated meeting (if applicable)
- **AI Generated Flag**: Whether change was human or AI-created

### Line-by-Line Blame Tracking
```python
blame_info = {
    line_number: {
        "author_name": "Sarah Chen",
        "author_type": "human",  # or "ai"
        "time_ago": "2 days ago",
        "commit_id": 42
    }
}
```

**Blame Attribution Uses:**
- **Build Documents**: Preserve human edits, update AI content
- **Context for LLM**: Help AI understand edit history and intent
- **User Interface**: Show who last edited each section
- **Change Tracking**: Understand evolution of document content

## Advanced Notification System

### Smart Notification Generation
The system generates **0-3 intelligent notifications** per document update using sophisticated LLM analysis:

**Notification Creation Process:**
1. **Content Analysis**: Compare previous vs. new document content
2. **Meeting Context**: Include meeting title, participants, duration
3. **LLM Generation**: Create contextual notifications
4. **Deduplication**: Prevent similar notifications
5. **User Targeting**: Attribute to document owner

### Notification Types
- **New Information Added**: "Project timeline updated with Q2 milestones"
- **Changes Detected**: "Budget constraints revised from $50k to $75k"
- **Action Items Created**: "3 new action items assigned to team members"

### With Notification System
- **Commit Notifications**: Creates 0-3 intelligent notifications per document update
- **UI Integration**: Surfaces in notification bell UI and X-Ray page notifications tab
- **Read Tracking**: Maintains seen/unseen status for notifications
- **Smart Generation**: Context-aware to prevent noise and duplication

## Data Models & Database Schema

### Database Schema Overview

```mermaid
erDiagram
    %% Core X-Ray Tables
    XRAYS {
        int id PK
        string title
        string emoji
        text prompt
        string xray_type
        string scope
        int owner_id FK
        string frequency
        string timezone
        datetime last_digest_at
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    XRAY_DOC_COMMITS {
        int id PK
        int xray_id FK
        text content
        int author_id FK
        int recurrence_id FK
        boolean is_ai_generated
        datetime created_at
    }
    
    XRAY_DOC_COMMIT_LINES {
        int id PK
        int commit_id FK
        int line_number
        text line_content
        datetime created_at
    }
    
    XRAY_DOC_COMMIT_NOTIFICATIONS {
        int id PK
        int xray_doc_commit_id FK
        int user_id FK
        string title
        text message
        boolean is_seen
        datetime created_at
    }
    
    XRAY_TEMPLATES {
        int id PK
        string template_name
        text prompt
        string xray_type
        string scope
        int created_by_user_id FK
        datetime created_at
    }
    
    %% Referenced Tables (External)
    USERS {
        int id PK
        string email
        string name
    }
    
    SESSION_RECURRENCES {
        int id PK
        string title
        datetime started_at
        datetime ended_at
    }
    
    %% Relationships
    XRAYS ||--o{ XRAY_DOC_COMMITS : "has commits"
    XRAY_DOC_COMMITS ||--o{ XRAY_DOC_COMMIT_LINES : "has lines"
    XRAY_DOC_COMMITS ||--o{ XRAY_DOC_COMMIT_NOTIFICATIONS : "generates notifications"
    USERS ||--o{ XRAYS : "owns"
    USERS ||--o{ XRAY_DOC_COMMITS : "authors"
    USERS ||--o{ XRAY_TEMPLATES : "creates"
    USERS ||--o{ XRAY_DOC_COMMIT_NOTIFICATIONS : "receives"
    SESSION_RECURRENCES ||--o{ XRAY_DOC_COMMITS : "triggers commits"
```

### Core Tables

**`xrays`**
- **Purpose**: Main X-Ray configuration and metadata
- **Key Fields**: `title`, `emoji`, `prompt`, `xray_type`, `scope`, `owner_id`
- **Digest Fields**: `frequency`, `timezone`, `last_digest_at`

**`xray_doc_commits`**
- **Purpose**: Git-like version history for documents
- **Key Fields**: `content`, `author_id`, `created_at`, `is_ai_generated`
- **Relationships**: Links to `xrays` and `session_recurrences`

**`xray_doc_commit_lines`**
- **Purpose**: Line-by-line blame tracking
- **Key Fields**: `line_number`, `line_content`, `commit_id`
- **Features**: Enables blame attribution and edit history

**`xray_doc_commit_notifications`**
- **Purpose**: Smart notifications for document changes
- **Key Fields**: `title`, `message`, `is_seen`, `user_id`
- **Features**: Tracks read status and user targeting

**`xray_templates`**
- **Purpose**: Shareable X-Ray configurations
- **Key Fields**: `template_name`, `prompt`, `xray_type`
- **Features**: Enable quick X-Ray creation from proven patterns

## API Endpoints & Integration

### X-Ray Management
- `POST /xrays` - Create new X-Ray from description
- `GET /xrays` - List user's X-Rays with filtering/pagination
- `PUT /xrays/{id}` - Update X-Ray settings (auto-updates schedules)
- `DELETE /xrays/{id}` - Delete X-Ray and cleanup schedules

### Content & History
- `GET /xrays/{id}/content` - Get current document content
- `GET /xrays/{id}/commits` - Get version history
- `GET /xrays/{id}/blame` - Get line-by-line attribution
- `POST /xrays/{id}/commits` - Create manual commit (human edits)

### Templates & Sharing
- `POST /xrays/{id}/share` - Share X-Ray as template
- `GET /xray-templates` - Browse available templates
- `POST /xrays/from-template` - Create X-Ray from template

### Notifications
- `GET /xrays/{id}/notifications` - Get X-Ray notifications
- `POST /xrays/{id}/notifications/mark-seen` - Mark notifications as read

## Testing & Development

### Test Coverage
- **Unit Tests**: Core service logic and LLM response parsing
- **Integration Tests**: Workflow execution and database operations
- **Contract Tests**: API endpoint behavior and validation
- **Mock Data**: Comprehensive fixtures for different scenarios

### Development Tools
- **Local Temporal**: Docker-compose setup for workflow testing
- **LLM Mocking**: Fake responses for deterministic testing
- **Database fixtures**: Pre-populated test data
- **Database fixtures and cleanup**

## Future Enhancements

### Planned Features
- **Enhanced Search**: Full-text search across all X-Ray documents
- **Collaboration Features**: Multi-user editing and commenting
- **Advanced Analytics**: Document evolution metrics and insights
- **Integration APIs**: Connect with external project management tools
- **Mobile Optimization**: Enhanced mobile UI for X-Ray management

### Performance Optimizations
- **Caching Layer**: Redis for frequently accessed documents
- **Batch Processing**: Optimize multiple X-Ray processing
- **LLM Response Caching**: Cache similar prompts to reduce API calls
- **Database Indexing**: Optimize queries for large document sets

### AI Enhancements
- **Better Context Awareness**: Improved meeting relevance detection
- **Custom Models**: Fine-tuned models for specific document types
- **Multi-language Support**: Process meetings in different languages
- **Sentiment Analysis**: Track team mood and engagement over time

---

## Summary

X-Ray represents a sophisticated **meeting intelligence system** that automatically transforms meeting conversations into structured, living documentation. By combining **advanced LLM processing**, **robust workflow orchestration**, and **git-like version control**, it provides users with powerful tools to capture, organize, and maintain knowledge from their meetings without manual effort.

The system's **three document types** (Build, Monitor, Digest) provide flexibility for different use cases, while the **intelligent notification system** ensures users stay informed about important changes. **Temporal workflows** provide fault-tolerance and scheduling reliability, making the system production-ready for enterprise use. 