package hubble

import (
	"context"
	"net/http"
	"strings"
	"time"

	"encore.app/pkg/middleware"
	"encore.dev"

	"encore.app/hubble/database"

	config2 "encore.app/hubble/config"
	"encore.app/hubble/repositories"
	"encore.app/hubble/services"
	"encore.app/hubble/utils"
	"encore.app/shared"
	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/config"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"github.com/casbin/casbin/v2"
	"github.com/go-jose/go-jose/v3/jwt"
	"github.com/jmoiron/sqlx"
	tld "github.com/jpillora/go-tld"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

var secrets struct {
	MarsUser            string
	MarsPassword        string
	AuroraUser          string
	AuroraPassword      string
	MailjetAPIKey       string
	MailjetAPISecret    string
	OAuth2ClientSecret  string
	JWTSecret           string
	JWTRefreshSecret    string
	ElioUser            string
	ElioPassword        string
	PostmarkServerToken string
}

//encore:service
type Hubble struct {
	DB                      *gorm.DB
	AuthUserRepository      *repositories.AuthUserRepository
	RedisRepository         *repositories.RedisRepository
	OAuthRepository         *repositories.OAuth2ClientCredentialRepository
	AuthenticationService   *services.AuthenticationService
	EmailService            *services.EmailService
	MarsService             *services.MarsService
	AuroraService           *services.AuroraService
	Enforcer                *casbin.SyncedCachedEnforcer
	CancelBroadcastListener *context.CancelFunc
}

var HubbleDB = sqldb.NewDatabase("hubble", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

var Config = config.Load[*config2.HubbleConfig]()

var namedMarsDB = sqldb.Named("mars")
var MarsDB = sqlx.NewDb(namedMarsDB.Stdlib(), "pgx")

var RedisRepo = &repositories.RedisRepository{
	VerifyEmailOTP:           verifyEmailOTP,
	OAuthVerifyEmailOTP:      oauthVerifyEmailOTP,
	OAuthCode:                oauthCode,
	InvalidatedTokens:        invalidatedTokens,
	InvalidatedRefreshTokens: invalidatedRefreshTokens,
	UserIssuedTokens:         userIssuedTokens,
}
var authService = services.NewAuthenticationService(RedisRepo, secrets.ElioUser, secrets.ElioPassword, secrets.JWTSecret, secrets.JWTRefreshSecret)

func IsEmailBlacklisted(ctx context.Context, email string) bool {
	s := strings.Split(email, "@")
	if len(s) != 2 {
		rlog.Debug("invalid email", "email", email)
		return true
	}
	url, err := tld.Parse("https://" + s[1])
	if err != nil {
		rlog.Error("error parsing email", "err", err.Error(), "email", email)
		return true
	}
	if url.Domain == "appleid" && url.TLD == "com" {
		return false
	}
	row := HubbleDB.QueryRow(ctx, `SELECT 1 FROM domain_blocklist WHERE domain = $1`, url.Domain+"."+url.TLD)
	foo := 0
	err = row.Scan(&foo)
	if err == nil {
		rlog.Debug("blacklisted email domain", "email", email, "domain", url.Domain)
		return true
	}
	return false
}

type MetricsLabels struct {
	Env    string
	Pod    string
	Metric string
}

func initHubble() (*Hubble, error) {
	var err error
	hubble := Hubble{}
	var db = HubbleDB.Stdlib()
	if Config.HubblePostgresOverride() != "" {
		sqlxDb, err := sqlx.Connect("pgx", Config.HubblePostgresOverride())
		if err != nil {
			return nil, err
		}
		db = sqlxDb.DB
	}
	hubble.DB, err = gorm.Open(postgres.New(postgres.Config{
		Conn: db,
	}))
	if err != nil {
		return nil, err
	}
	HasOverrideConfig := Config.MarsPostgresOverride() != ""
	if HasOverrideConfig {
		if MarsDB, err = sqlx.Connect("pgx", Config.MarsPostgresOverride()); err != nil {
			return nil, err
		}
	}
	hubble.AuthUserRepository = &repositories.AuthUserRepository{DB: hubble.DB}
	hubble.RedisRepository = RedisRepo
	hubble.OAuthRepository = repositories.NewOAuth2ClientCredentialRepository(hubble.DB, secrets.OAuth2ClientSecret)
	hubble.AuthenticationService = &authService
	hubble.EmailService = &services.EmailService{Config: Config}
	hubble.MarsService = services.NewMarsService(&Config.Mars, secrets.MarsUser, secrets.MarsPassword)
	hubble.AuroraService = services.NewAuroraService(
		&Config.Aurora, &Config.Mars,
		secrets.MarsUser, secrets.MarsPassword,
		secrets.AuroraUser, secrets.AuroraPassword,
	)
	hubble.Enforcer, err = GetEnforcer()
	if err != nil {
		return nil, err
	}
	if err = database.MarsStatements.ValidateAll(context.Background(), MarsDB, "mars"); err != nil {
		return nil, err
	}

	if encore.Meta().Environment.Type != encore.EnvTest {
		ctx, cancel := context.WithCancel(context.Background())
		// TODO: Investigate encore fair usage policy on keeping these goroutines running
		// Assuming encore is happy for us to run this for 300 seconds, then terminate it, and when the next request comes in, start it again
		if encore.Meta().Environment.Cloud == encore.EncoreCloud {
			ctx, cancel = context.WithTimeout(context.Background(), 300*time.Second)
		}
		hubble.CancelBroadcastListener = &cancel
		go initBroadcastListener(ctx, &hubble)
	}

	return &hubble, nil
}

func (h *Hubble) Shutdown(force context.Context) {
	if h.CancelBroadcastListener != nil {
		(*h.CancelBroadcastListener)()
	}
}

type AuthParams struct {
	Authorization string `header:"Authorization"`
}

//encore:authhandler
func AuthHandler(ctx context.Context, p *AuthParams) (auth.UID, *shared.DecodedToken, error) {
	authorizationHeader := p.Authorization
	if strings.HasPrefix(authorizationHeader, "Basic ") {
		err := authService.VerifyTrxitaBasicAuth(authorizationHeader[6:])
		if err == nil {
			return middleware.AuthTrixtaBasic, nil, nil
		}
		rlog.Error("error verifying Trixta basic auth", "err", err.Error())
	}

	if strings.HasPrefix(authorizationHeader, "Bearer ") {
		claims, err := authService.VerifyAuthToken(ctx, authorizationHeader[7:])
		if err != nil {
			return "", nil, shared.LogErrorHttpResponse(err, middleware.AuthFailedMessage, utils.Unauthorized, http.StatusUnauthorized)
		}

		if claims.GuestSurrogateID != 0 {
			return auth.UID(shared.FormatInt(claims.GuestSurrogateID)), claims, nil
		}

		return auth.UID(claims.UserID), claims, nil
	}

	_, jwtErr := jwt.ParseSigned(authorizationHeader)
	if jwtErr == nil {
		return "", nil, &errs.Error{Code: errs.Unauthenticated}
	}

	return "", nil, shared.HttpResponseError(middleware.AuthFailedMessage, utils.Unauthorized, http.StatusUnauthorized)
}
