from temporalio import workflow
from datetime import timedelta

with workflow.unsafe.imports_passed_through():
    from nebula.temporal.activities.tldr_act import TLDRActivities
    from nebula.temporal.activities.metadata_act import MetadataActivities
    from nebula.temporal.activities.post_session_summary_act import PostSessionSummaryActivities
    from nebula.temporal.types import (
        TLDRGenerationWorkflowParams,
        TLDRGenerationWorkflowResult,
        GenerateTLDRInput,
        UpdatePostSessionSummaryWithTLDRInput,
        NotifyLuxorTLDRReadyInput,
        MetadataWorkflowParams,
        PostSessionSummaryWorkflowParams,
    )


@workflow.defn(name="TLDRGeneration")
class TLDRGenerationWorkflow:
    """Workflow for TLDR generation from meeting transcripts"""

    @workflow.run
    async def run(
        self, params: TLDRGenerationWorkflowParams
    ) -> TLDRGenerationWorkflowResult:
        """
        Run the TLDR generation workflow.

        Args:
            params: TLDRGenerationWorkflowParams containing session_id and recurrence_id

        Returns:
            TLDRGenerationWorkflowResult containing the generated TLDR
        """
        workflow.logger.info(
            f"Starting TLDR generation workflow for session {params.session_id}, "
            f"recurrence {params.recurrence_id}"
        )

        start_time = workflow.now().timestamp()

        # Step 1: Get transcription batches
        transcription_result = await workflow.execute_activity(  # type: ignore
            MetadataActivities.get_transcription_batches,  # type: ignore
            MetadataWorkflowParams(
                session_id=params.session_id,
                recurrence_id=params.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=60),
        )

        if len(transcription_result.transcription_batches) == 0:
            workflow.logger.warn(
                f"No transcription batches found for session {params.session_id}, "
                f"recurrence {params.recurrence_id}"
            )
            return TLDRGenerationWorkflowResult(
                done=True,
                message="No transcription batches found",
                tldr_content=None,
                summary_id=None,
                processing_time_seconds=0,
            )

        # Step 2: Get session data  
        session_data = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.get_session_data_for_pss,  # type: ignore
            PostSessionSummaryWorkflowParams(
                session_id=params.session_id,
                recurrence_id=params.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 3: Process transcript
        processed_transcript = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.process_transcript_for_pss,  # type: ignore
            transcription_result,
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 4: Check transcript length
        if len(processed_transcript.transcript.split()) < 100:
            workflow.logger.info(
                f"TLDR workflow session {params.session_id} recurrence {params.recurrence_id} - skipping TLDR generation. transcripts too short"
            )
            return TLDRGenerationWorkflowResult(
                done=True,
                message="Transcript too short",
                tldr_content=None,
                summary_id=None,
                processing_time_seconds=0,
            )

        # Step 5: Generate TLDR summary
        tldr_result = await workflow.execute_activity(  # type: ignore
            TLDRActivities.generate_tldr_summary,  # type: ignore
            GenerateTLDRInput(
                transcript=processed_transcript.transcript,
                locale=processed_transcript.locale,
                meeting_type=session_data.meeting_type,
            ),
            start_to_close_timeout=timedelta(minutes=2),
        )

        # Step 6: Update post session summary record with TLDR
        update_result = await workflow.execute_activity(  # type: ignore
            TLDRActivities.update_post_session_summary_with_tldr,  # type: ignore
            UpdatePostSessionSummaryWithTLDRInput(
                session_id=params.session_id,
                recurrence_id=params.recurrence_id,
                tldr_content=tldr_result.tldr_content,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 7: Notify Luxor about TLDR completion
        if update_result.summary_id is None:
            raise ValueError("Failed to get summary_id from post session summary update")
        
        await workflow.execute_activity(  # type: ignore
            TLDRActivities.notify_luxor_tldr_ready,  # type: ignore
            NotifyLuxorTLDRReadyInput(
                summary_id=update_result.summary_id,
                session_id=params.session_id,
                recurrence_id=params.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        processing_time = workflow.now().timestamp() - start_time

        workflow.logger.info(
            f"Completed TLDR generation for session {params.session_id}, "
            f"recurrence {params.recurrence_id} in {processing_time:.2f} seconds"
        )

        return TLDRGenerationWorkflowResult(
            done=True,
            message="Successfully generated TLDR summary",
            tldr_content=tldr_result.tldr_content,
            summary_id=update_result.summary_id,
            processing_time_seconds=processing_time,
        ) 