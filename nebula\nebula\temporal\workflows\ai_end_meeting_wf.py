from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    import asyncio
    from datetime import timed<PERSON><PERSON>
    from typing import Any, Coroutine, List
    from nebula.temporal.types import XRayScanWorkflowParams
    from nebula.temporal.workflows.xray_wf import XRayScanWorkflow

    from nebula.temporal.types import (
        MetadataWorkflowParams,
        AiEndMeetingWorkflowParams,
        PostSessionSummaryWorkflowParams,
        TypesenseIndexingWorkflowParams,
        TLDRGenerationWorkflowParams,
    )
    from nebula.temporal.workflows.metadata_wf import MetadataWorkflow
    from nebula.temporal.workflows.post_session_summary_wf import (
        PostSessionSummaryWorkflow,
    )
    from nebula.temporal.workflows.typesense_indexing_wf import (
        TypesenseIndexingWorkflow,
    )
    from nebula.temporal.workflows.tldr_generation_wf import TLDRGenerationWorkflow
    from nebula.temporal.types import GetUserIdsForXRayScansActivityInput
    from nebula.temporal.activities.user_act import UserActivities

    from nebula.settings import settings


@workflow.defn(name="AiEndMeeting")
class AiEndMeetingWorkflow:
    """AI end-meeting workflow: kicks off all post-meeting child workflows in parallel."""

    @workflow.run
    async def run(self, params: AiEndMeetingWorkflowParams) -> None:
        logger = workflow.logger
        logger.info("Starting ai end-meeting workflow")

        # --- fan-out ---------------------------------------------------------
        # Awaitables
        handles: List[Coroutine[Any, Any, Any]] = [
            workflow.start_child_workflow(
                MetadataWorkflow.run,
                MetadataWorkflowParams(
                    session_id=params.session_id,
                    recurrence_id=params.recurrence_id,
                ),
                task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
            ),
            workflow.start_child_workflow(
                PostSessionSummaryWorkflow.run,
                PostSessionSummaryWorkflowParams(
                    session_id=params.session_id,
                    recurrence_id=params.recurrence_id,
                ),
                task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
            ),
            workflow.start_child_workflow(
                TypesenseIndexingWorkflow.run,
                TypesenseIndexingWorkflowParams(
                    session_id=params.session_id,
                    recurrence_id=params.recurrence_id,
                ),
                task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
            ),
            workflow.start_child_workflow(
                TLDRGenerationWorkflow.run,
                TLDRGenerationWorkflowParams(
                    session_id=params.session_id,
                    recurrence_id=params.recurrence_id,
                ),
                task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
            ),
        ]

        # Get user ids for XRayScanWorkflows (team members + session participants)
        user_ids_for_xray_wfs = await workflow.execute_activity_method(
            UserActivities.get_user_ids_for_xray_scans,
            GetUserIdsForXRayScansActivityInput(
                session_id=int(params.session_id),
                recurrence_id=int(params.recurrence_id),
            ),
            start_to_close_timeout=timedelta(seconds=120),
        )

        # Start XRayScanWorkflows for each user
        for user_id in user_ids_for_xray_wfs:
            handles.append(
                workflow.start_child_workflow(
                    XRayScanWorkflow.run,
                    XRayScanWorkflowParams(
                        user_id=user_id,
                        recurrence_id=int(params.recurrence_id),
                    ),
                )
            )

        # --- fan-in ----------------------------------------------------------
        try:
            # Wait for all child workflows to start, then wait for their results
            started_handles = await asyncio.gather(*handles)
            _ = await asyncio.gather(*started_handles)
        except Exception:
            logger.exception("At least one child workflow failed")
            raise

        logger.info(
            "Completed all child workflows for recurrence %s",
            params.recurrence_id,
        )
