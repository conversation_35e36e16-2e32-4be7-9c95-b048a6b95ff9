# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_transcriptions_create_bot_for_meeting200_response import POSTTranscriptionsCreateBotForMeeting200Response

class TestPOSTTranscriptionsCreateBotForMeeting200Response(unittest.TestCase):
    """POSTTranscriptionsCreateBotForMeeting200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTTranscriptionsCreateBotForMeeting200Response:
        """Test POSTTranscriptionsCreateBotForMeeting200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTTranscriptionsCreateBotForMeeting200Response`
        """
        model = POSTTranscriptionsCreateBotForMeeting200Response()
        if include_optional:
            return POSTTranscriptionsCreateBotForMeeting200Response(
                calendar_meetings = [
                    elio_client.models.recallai/calendar_meeting.recallai.CalendarMeeting(
                        calendar_user = elio_client.models.recallai/calendar_user.recallai.CalendarUser(
                            connections = [
                                elio_client.models.recallai/recall_connection.recallai.RecallConnection(
                                    connected = True, 
                                    email = '', 
                                    platform = '', )
                                ], 
                            external_id = '', 
                            id = '', 
                            preferences = elio_client.models.recallai/recording_preferences.recallai.RecordingPreferences(
                                bot_name = '', 
                                record_confirmed = True, 
                                record_external = True, 
                                record_internal = True, 
                                record_non_host = True, 
                                record_only_host = True, 
                                record_recurring = True, ), ), 
                        end_time = '', 
                        id = '', 
                        start_time = '', )
                    ],
                id = '',
                meeting_metadata = elio_client.models.recallai/meeting_metadata.recallai.MeetingMetadata(
                    title = '', 
                    zoom_meeting_uuid = '', ),
                meeting_participants = [
                    elio_client.models.recallai/meeting_participant.recallai.MeetingParticipant(
                        id = 56, 
                        is_host = True, 
                        name = '', 
                        platform = '', )
                    ],
                meeting_url = elio_client.models.recallai/bot_meeting_link.recallai.BotMeetingLink(
                    meeting_id = '', 
                    platform = '', ),
                video_url = ''
            )
        else:
            return POSTTranscriptionsCreateBotForMeeting200Response(
                calendar_meetings = [
                    elio_client.models.recallai/calendar_meeting.recallai.CalendarMeeting(
                        calendar_user = elio_client.models.recallai/calendar_user.recallai.CalendarUser(
                            connections = [
                                elio_client.models.recallai/recall_connection.recallai.RecallConnection(
                                    connected = True, 
                                    email = '', 
                                    platform = '', )
                                ], 
                            external_id = '', 
                            id = '', 
                            preferences = elio_client.models.recallai/recording_preferences.recallai.RecordingPreferences(
                                bot_name = '', 
                                record_confirmed = True, 
                                record_external = True, 
                                record_internal = True, 
                                record_non_host = True, 
                                record_only_host = True, 
                                record_recurring = True, ), ), 
                        end_time = '', 
                        id = '', 
                        start_time = '', )
                    ],
                id = '',
                meeting_metadata = elio_client.models.recallai/meeting_metadata.recallai.MeetingMetadata(
                    title = '', 
                    zoom_meeting_uuid = '', ),
                meeting_participants = [
                    elio_client.models.recallai/meeting_participant.recallai.MeetingParticipant(
                        id = 56, 
                        is_host = True, 
                        name = '', 
                        platform = '', )
                    ],
                meeting_url = elio_client.models.recallai/bot_meeting_link.recallai.BotMeetingLink(
                    meeting_id = '', 
                    platform = '', ),
                video_url = '',
        )
        """

    def testPOSTTranscriptionsCreateBotForMeeting200Response(self):
        """Test POSTTranscriptionsCreateBotForMeeting200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
