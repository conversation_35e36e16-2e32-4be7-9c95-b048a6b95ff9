# ApiMarkXRayNotificationsSeenResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**marked_count** | **int** |  | 

## Example

```python
from elio_client.models.api_mark_x_ray_notifications_seen_response_data import ApiMarkXRayNotificationsSeenResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiMarkXRayNotificationsSeenResponseData from a JSON string
api_mark_x_ray_notifications_seen_response_data_instance = ApiMarkXRayNotificationsSeenResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiMarkXRayNotificationsSeenResponseData.to_json())

# convert the object into a dict
api_mark_x_ray_notifications_seen_response_data_dict = api_mark_x_ray_notifications_seen_response_data_instance.to_dict()
# create an instance of ApiMarkXRayNotificationsSeenResponseData from a dict
api_mark_x_ray_notifications_seen_response_data_from_dict = ApiMarkXRayNotificationsSeenResponseData.from_dict(api_mark_x_ray_notifications_seen_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


