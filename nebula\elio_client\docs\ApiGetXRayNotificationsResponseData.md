# ApiGetXRayNotificationsResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**has_more** | **bool** |  | 
**notifications** | [**List[ApiXRayNotificationDTO]**](ApiXRayNotificationDTO.md) |  | 
**total_count** | **int** |  | 

## Example

```python
from elio_client.models.api_get_x_ray_notifications_response_data import ApiGetXRayNotificationsResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiGetXRayNotificationsResponseData from a JSON string
api_get_x_ray_notifications_response_data_instance = ApiGetXRayNotificationsResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiGetXRayNotificationsResponseData.to_json())

# convert the object into a dict
api_get_x_ray_notifications_response_data_dict = api_get_x_ray_notifications_response_data_instance.to_dict()
# create an instance of ApiGetXRayNotificationsResponseData from a dict
api_get_x_ray_notifications_response_data_from_dict = ApiGetXRayNotificationsResponseData.from_dict(api_get_x_ray_notifications_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


