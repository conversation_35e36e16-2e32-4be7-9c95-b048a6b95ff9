# Rumi Backend Monorepo - System Architecture Diagram

This diagram shows the complete inputs and outputs of the Rumi backend monorepo, including all services, external integrations, and data flows.

## How to View This Diagram

### Option 1: GitHub/GitLab
Simply view this file on GitHub or GitLab - they render Mermaid diagrams automatically.

### Option 2: Mermaid Live Editor
1. Copy the diagram code below
2. Go to [Mermaid Live Editor](https://mermaid.live/)
3. Paste the code to view and edit

### Option 3: VS Code
Install the "Mermaid Preview" extension in VS Code to view this diagram directly.

### Option 4: Other Tools
The diagram code below works with any Mermaid-compatible renderer including:
- Notion
- Obsidian
- Confluence (with Mermaid plugin)
- Documentation sites (GitBook, Docusaurus, etc.)

## Architecture Overview

### Inbound Connections (Inputs)
- **Client Applications**: Web app, mobile app, Chrome extension
- **External Webhooks**: Google Calendar, Recall.AI, Deepgram, LiveKit, Agora, Paddle, Nango

### Outbound Connections (Outputs)
- **External APIs**: AI services (OpenAI, Cohere), CRM systems (Salesforce, HubSpot), productivity tools (Slack, Linear, Asana, ClickUp), cloud services (AWS), and more

### Core Services
- **Elio** (Go/Encore.go): Main API gateway, auth, meetings, transcriptions
- **Nebula** (Python/FastAPI): AI processing, background jobs, search
- **Luxor** (NestJS): Integrations, video recording
- **Mars** (DEPRECATED): Legacy API server
- **Draconids**: Notifications
- **Aurora**: Additional services
- **IO-Server**: External service authorization

## Mermaid Diagram Code

```mermaid
graph TB
    %% External Clients
    WebApp["Web Application<br/>(Frontend)"]
    MobileApp["Mobile Application"]
    ChromeExt["Chrome Extension"]
    IOClient["IO Client"]

    %% External Services - Inbound
    GoogleCal["Google Calendar<br/>(OAuth & Events)"]
    RecallAI["Recall.AI<br/>(Webhooks)"]
    Deepgram["Deepgram<br/>(Webhooks)"]
    LiveKit["LiveKit<br/>(Webhooks)"]
    Agora["Agora<br/>(Webhooks)"]
    Paddle["Paddle<br/>(Webhooks)"]
    NangoWebhooks["Nango<br/>(Integration Webhooks)"]

    %% External Services - Outbound
    RecallAIOut["Recall.AI<br/>(API Calls)"]
    DeepgramOut["Deepgram<br/>(API Calls)"]
    LiveKitOut["LiveKit<br/>(API Calls)"]
    AgoraOut["Agora<br/>(API Calls)"]
    PaddleOut["Paddle<br/>(API Calls)"]
    GoogleOut["Google APIs<br/>(Calendar, OAuth)"]
    OpenAIOut["OpenAI<br/>(GPT Models)"]
    CohereOut["Cohere<br/>(Embeddings, Rerank)"]
    SalesforceOut["Salesforce<br/>(CRM API)"]
    HubSpotOut["HubSpot<br/>(CRM API)"]
    SlackOut["Slack<br/>(API)"]
    LinearOut["Linear<br/>(API)"]
    AsanaOut["Asana<br/>(API)"]
    ClickUpOut["ClickUp<br/>(API)"]
    OutlookOut["Microsoft Outlook<br/>(API)"]
    AWSOut["AWS<br/>(S3, DynamoDB)"]

    %% Core Services
    subgraph "Rumi Backend Monorepo"
        direction TB

        %% Elio Service (Encore.go)
        subgraph "Elio Service (Go - Encore.go)"
            direction TB
            Hubble["Hubble<br/>(Auth Service)"]
            Wormhole["Wormhole<br/>(API Gateway)"]
            Meetings["Meetings<br/>(Session Management)"]
            Transcriptions["Transcriptions<br/>(Audio Processing)"]
            LiveKitSvc["LiveKit Service<br/>(Video/Audio)"]
            Teams["Teams<br/>(Team Management)"]
            Lobbies["Lobbies<br/>(Pre-meeting)"]
            Chat["Chat<br/>(Real-time Messaging)"]
            Comms["Comms<br/>(Communication)"]
        end

        %% Nebula Service (Python/FastAPI)
        subgraph "Nebula Service (Python - FastAPI)"
            direction TB
            AIFeed["AI Feed<br/>(Content Generation)"]
            PostSession["Post Session Summaries<br/>(AI Analysis)"]
            Search["Search Service<br/>(Typesense)"]
            XRay["X-Ray<br/>(Analytics)"]
            NebulaKafka["Kafka Consumers<br/>(Event Processing)"]
            TemporalWF["Temporal Workflows<br/>(Background Jobs)"]
        end

        %% Luxor Service (NestJS/TypeScript)
        subgraph "Luxor Service (NestJS - TypeScript)"
            direction TB
            Integrations["Integrations<br/>(CRM Connectors)"]
            VideoRec["Video Recording<br/>(Processing)"]
            NangoSvc["Nango Service<br/>(OAuth Management)"]
            SalesforceInt["Salesforce Integration"]
            HubSpotInt["HubSpot Integration"]
            SlackInt["Slack Integration"]
        end

        %% Mars Service (DEPRECATED)
        subgraph "Mars Service (DEPRECATED - Node.js)"
            direction TB
            MarsAPI["Legacy API<br/>(Being Phased Out)"]
            MarsBilling["Billing<br/>(Paddle Integration)"]
            MarsUsers["User Management"]
        end

        %% Draconids Service
        subgraph "Draconids Service (Node.js)"
            direction TB
            Notifications["Notifications<br/>(Push/Email)"]
            NotifQueue["Notification Queue"]
        end

        %% Aurora Service
        subgraph "Aurora Service"
            direction TB
            AuroraAPI["Aurora API<br/>(Additional Services)"]
        end

        %% IO-Server
        subgraph "IO-Server (Microservice)"
            direction TB
            IOAuth["External Service<br/>Authorization"]
        end

        %% Shared Infrastructure
        subgraph "Shared Infrastructure"
            direction TB
            PostgresDB["PostgreSQL<br/>(Multiple Databases)"]
            RedisCache["Redis<br/>(Caching & Sessions)"]
            KafkaQueue["Kafka<br/>(Event Streaming)"]
            TypesenseSearch["Typesense<br/>(Search Engine)"]
        end
    end

    %% Client Connections
    WebApp --> Wormhole
    MobileApp --> Wormhole
    ChromeExt --> Wormhole
    IOClient --> IOAuth

    %% Inbound Webhooks
    GoogleCal --> Hubble
    RecallAI --> Transcriptions
    Deepgram --> Transcriptions
    LiveKit --> LiveKitSvc
    Agora --> Transcriptions
    Paddle --> MarsBilling
    NangoWebhooks --> NangoSvc

    %% Outbound API Calls
    Transcriptions --> RecallAIOut
    Transcriptions --> DeepgramOut
    LiveKitSvc --> LiveKitOut
    Meetings --> AgoraOut
    MarsBilling --> PaddleOut
    Hubble --> GoogleOut
    AIFeed --> OpenAIOut
    PostSession --> OpenAIOut
    Search --> CohereOut
    XRay --> OpenAIOut
    SalesforceInt --> SalesforceOut
    HubSpotInt --> HubSpotOut
    SlackInt --> SlackOut
    Integrations --> LinearOut
    Integrations --> AsanaOut
    Integrations --> ClickUpOut
    IOAuth --> OutlookOut
    MarsAPI --> AWSOut

    %% Inter-service Communication
    Wormhole --> Meetings
    Wormhole --> Hubble
    Wormhole --> Transcriptions
    Wormhole --> LiveKitSvc
    Wormhole --> Integrations
    Wormhole --> MarsAPI
    Wormhole --> AuroraAPI
    Meetings --> NebulaKafka
    Transcriptions --> NebulaKafka
    PostSession --> TemporalWF

    %% Database Connections
    Hubble --> PostgresDB
    Meetings --> PostgresDB
    Transcriptions --> PostgresDB
    MarsAPI --> PostgresDB
    Integrations --> PostgresDB
    Nebula --> PostgresDB

    %% Cache Connections
    Hubble --> RedisCache
    Meetings --> RedisCache
    MarsAPI --> RedisCache
    Integrations --> RedisCache

    %% Event Streaming
    Meetings --> KafkaQueue
    Transcriptions --> KafkaQueue
    NebulaKafka --> KafkaQueue
    Notifications --> KafkaQueue

    %% Search Engine
    Search --> TypesenseSearch

    %% Styling
    classDef service fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef external fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef infrastructure fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef deprecated fill:#ffebee,stroke:#b71c1c,stroke-width:2px

    class Hubble,Wormhole,Meetings,Transcriptions,LiveKitSvc,Teams,Lobbies,Chat,Comms,AIFeed,PostSession,Search,XRay,NebulaKafka,TemporalWF,Integrations,VideoRec,NangoSvc,SalesforceInt,HubSpotInt,SlackInt,Notifications,NotifQueue,AuroraAPI,IOAuth service
    class WebApp,MobileApp,ChromeExt,IOClient,GoogleCal,RecallAI,Deepgram,LiveKit,Agora,Paddle,NangoWebhooks,RecallAIOut,DeepgramOut,LiveKitOut,AgoraOut,PaddleOut,GoogleOut,OpenAIOut,CohereOut,SalesforceOut,HubSpotOut,SlackOut,LinearOut,AsanaOut,ClickUpOut,OutlookOut,AWSOut external
    class PostgresDB,RedisCache,KafkaQueue,TypesenseSearch infrastructure
    class MarsAPI,MarsBilling,MarsUsers deprecated
```

## Service Details

### Port Assignments
| Service | Port |
|---------|------|
| Mars | 3001 |
| Luxor | 3002 |
| Draconids | 3003 |
| Nebula | 3006 |
| Aurora | 3008 |
| Elio | 4000 |
| PostgreSQL | 5432 |
| Redis | 6379 |
| Kafka | 9092 |
| Kafka UI | 7070 |
| Zookeeper | 2181 |
| Typesense | 8108 |

### Technology Stack
- **Elio**: Go with Encore.go framework
- **Nebula**: Python with FastAPI and Poetry
- **Luxor**: TypeScript/Node.js with NestJS
- **Mars**: Node.js/TypeScript (DEPRECATED)
- **Draconids**: Node.js
- **Aurora**: Additional service
- **IO-Server**: Microservice for external authorizations

### Key External Integrations
- **AI/ML**: OpenAI (GPT models), Cohere (embeddings/rerank)
- **Real-time Communication**: LiveKit, Agora
- **Transcription**: Recall.AI, Deepgram
- **CRM**: Salesforce, HubSpot
- **Productivity**: Slack, Linear, Asana, ClickUp
- **Calendar**: Google Calendar, Microsoft Outlook
- **Payments**: Paddle
- **Cloud Storage**: AWS S3, DynamoDB
- **Integration Platform**: Nango

---

**Created**: $(date)
**Version**: Based on current monorepo structure
**Maintainer**: Development Team