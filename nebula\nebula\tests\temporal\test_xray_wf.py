import pytest
from datetime import timedelta
from temporalio.testing import WorkflowEnvironment
from temporalio.exceptions import ApplicationError

from nebula.temporal.types import (
    XRayQuickScanActivityParams,
    XRayScanWorkflowParams,
    XRayScanWorkflowResult,
)
from nebula.tests.helpers.temporal import create_ai_end_meet_worker_for_tests
from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
from nebula.temporal.converter import pydantic_data_converter
from nebula.settings import settings


@pytest.mark.asyncio
async def test_xray_scan_workflow_success():
    """Test the XRay scan workflow executes successfully with relevant documents."""
    # Create a workflow environment for testing
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        # Get client from the test environment
        client = env.client

        # Use the standard task queue from settings
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # Execute the XRay scan workflow
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=12345, recurrence_id=1),
                id="test-xray-scan-workflow-success",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify workflow execution succeeded
            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is True
            assert result.xrays_processed > 0
            assert "Successfully processed" in result.message
            assert result.processing_time_seconds is not None
            assert result.processing_time_seconds > 0


@pytest.mark.asyncio
async def test_xray_scan_workflow_no_relevant_documents():
    """Test the XRay scan workflow when no documents are relevant."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that returns no relevant XRays
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from nebula.temporal.types import XRayQuickScanResult
        from temporalio import activity

        class NoRelevantXRayMock(MockXRayActivities):
            @activity.defn
            async def quick_scan(self, params: XRayQuickScanActivityParams):
                """Mock that returns no relevant XRays"""
                return XRayQuickScanResult(xray_ids=[])

        mock_xray_activities = NoRelevantXRayMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayScanWorkflow],
            activities=[
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
            ],
        ):
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=12345, recurrence_id=1),
                id="test-xray-scan-workflow-no-relevant",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify workflow handled no relevant documents correctly
            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is True
            assert result.xrays_processed == 0
            assert "No relevant documents found" in result.message
            assert result.processing_time_seconds is not None


@pytest.mark.asyncio
async def test_xray_scan_workflow_with_missing_xray():
    """Test the XRay scan workflow when an XRay document is missing (returns None)."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that returns None for get_xray_by_id to test the null case
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from temporalio import activity

        class MissingXRayMock(MockXRayActivities):
            @activity.defn
            async def get_xray_by_id(self, doc_id: int):
                """Mock that returns None for missing XRay"""
                return None

        mock_xray_activities = MissingXRayMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayScanWorkflow],
            activities=[
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
            ],
        ):
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=12345, recurrence_id=1),
                id="test-xray-scan-workflow-missing-xray",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify workflow completed successfully even with missing XRay
            # The workflow should skip missing XRays and continue
            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is True
            assert result.xrays_processed == 0  # No documents were actually processed
            assert "Successfully processed 0 documents" in result.message
            assert result.processing_time_seconds is not None


@pytest.mark.asyncio
async def test_xray_scan_workflow_with_activity_failure():
    """Test the XRay scan workflow handles activity failures correctly."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that fails on get_xrays
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from temporalio import activity

        class FailingXRayMock(MockXRayActivities):
            @activity.defn
            async def get_xrays(self, user_id: int):
                """Mock that fails to get XRays"""
                raise ApplicationError(
                    "Simulated failure in get_xrays", non_retryable=True
                )

        mock_xray_activities = FailingXRayMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayScanWorkflow],
            activities=[
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
            ],
        ):
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=12345, recurrence_id=1),
                id="test-xray-scan-workflow-failure",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify workflow handled failure correctly
            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is False
            assert result.xrays_processed == 0
            assert "Workflow failed with error" in result.message
            assert "Simulated failure in get_xrays" in result.message
            assert result.processing_time_seconds is not None


@pytest.mark.asyncio
async def test_xray_scan_workflow_invalid_doc_id():
    """Test the XRay scan workflow with invalid doc_id (tests our null case logic)."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that returns invalid doc_id
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from nebula.temporal.types import XRayQuickScanResult
        from temporalio import activity

        class InvalidDocIdMock(MockXRayActivities):
            @activity.defn
            async def quick_scan(self, params: XRayQuickScanActivityParams):
                """Mock that returns invalid (negative) doc_id"""
                return XRayQuickScanResult(
                    xray_ids=[-1]
                )  # This will trigger the None case

        mock_xray_activities = InvalidDocIdMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayScanWorkflow],
            activities=[
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
            ],
        ):
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=12345, recurrence_id=1),
                id="test-xray-scan-workflow-invalid-doc-id",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify workflow completed successfully despite invalid doc_id
            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is True
            assert (
                result.xrays_processed == 0
            )  # No documents processed due to invalid ID
            assert "Successfully processed 0 documents" in result.message
            assert result.processing_time_seconds is not None


@pytest.mark.asyncio
async def test_xray_scan_workflow_parameters():
    """Test the XRay scan workflow with different parameter combinations."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # Test with team_id parameter
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=12345, recurrence_id=1, team_id=100),
                id="test-xray-scan-workflow-with-team",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is True

            # Test with different user_id and recurrence_id
            result = await client.execute_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(user_id=67890, recurrence_id=999),
                id="test-xray-scan-workflow-different-params",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            assert isinstance(result, XRayScanWorkflowResult)
            assert result.success is True
