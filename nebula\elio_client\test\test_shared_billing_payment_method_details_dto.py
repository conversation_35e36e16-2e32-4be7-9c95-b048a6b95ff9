# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.shared_billing_payment_method_details_dto import SharedBillingPaymentMethodDetailsDTO

class TestSharedBillingPaymentMethodDetailsDTO(unittest.TestCase):
    """SharedBillingPaymentMethodDetailsDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SharedBillingPaymentMethodDetailsDTO:
        """Test SharedBillingPaymentMethodDetailsDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SharedBillingPaymentMethodDetailsDTO`
        """
        model = SharedBillingPaymentMethodDetailsDTO()
        if include_optional:
            return SharedBillingPaymentMethodDetailsDTO(
                card = elio_client.models.shared/billing_card.shared.BillingCard(),
                type = ''
            )
        else:
            return SharedBillingPaymentMethodDetailsDTO(
                card = elio_client.models.shared/billing_card.shared.BillingCard(),
                type = '',
        )
        """

    def testSharedBillingPaymentMethodDetailsDTO(self):
        """Test SharedBillingPaymentMethodDetailsDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
