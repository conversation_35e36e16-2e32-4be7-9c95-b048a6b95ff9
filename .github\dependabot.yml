version: 2
updates:
  # Nebula dependencies
  - package-ecosystem: "npm"
    directory: "/nebula"
    schedule:
      interval: "weekly"
    target-branch: "staging"
    labels:
      - "dependencies"
      - "nebula"

  # Elio dependencies
  - package-ecosystem: "npm"
    directory: "/elio"
    schedule:
      interval: "weekly"
    target-branch: "staging"
    labels:
      - "dependencies"
      - "elio"

  # Mars dependencies
#  - package-ecosystem: "npm"
#    directory: "/mars"
#    schedule:
#      interval: "weekly"
#    target-branch: "staging"
#    labels:
#      - "dependencies"
#      - "mars"

  # Luxor dependencies
  - package-ecosystem: "npm"
    directory: "/luxor"
    schedule:
      interval: "weekly"
    target-branch: "staging"
    labels:
      - "dependencies"
      - "luxor"

  # IO-Server dependencies
  - package-ecosystem: "npm"
    directory: "/io-server"
    schedule:
      interval: "weekly"
    target-branch: "staging"
    labels:
      - "dependencies"
      - "io-server"

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
    target-branch: "staging"
    labels:
      - "dependencies"
      - "github-actions"
