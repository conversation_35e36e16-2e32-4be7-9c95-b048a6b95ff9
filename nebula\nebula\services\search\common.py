import asyncio
import datetime
import json
import re
import time
from typing import (
    Dict,
    List,
    Literal,
    Optional,
    Tuple,
    TypedDict,
    Union,
)

import aiopg
import backoff
import humanize
import modal
from loguru import logger
from psycopg2.extras import RealDictCursor
from pydantic import BaseModel

import nebula.services.search.typesense_client as typesense
from nebula.constants import (
    MODAL_INGEST_APP_NAME,
    RECURRENCES_COLLECTION_NAME,
    TRANSCRIPT_CHUNKS_COLLECTION_NAME,
)
from nebula.db.models.message import Message
from nebula.services.search.cohere_client import cohere_client
from nebula.settings import settings
from nebula.utils import (
    backoff_hdlr,
    duration_str_from_secs,
    relative_tz_dt_from_unix,
)


class PostSessionSummaryDict(TypedDict):
    id: int
    session_id: str
    session_recurrence_id: str
    status: (
        Literal["registered"]
        | Literal["job_completed"]
        | Literal["job_initiated"]
        | Literal["job_failed"]
    )
    content: str | None
    created_at: str
    updated_at: str


class AIFeedDict(TypedDict):
    id: int
    session_id: str
    session_recurrence_id: str
    transcription_batch_id: str
    lang: str | None
    msg_type: Literal["catch-up"] | Literal["action-item"] | Literal["insight"]
    msg: str | None
    msg_dynamic_fields: str | None
    metadata_dynamic_fields: str | None
    links: str | None
    start_transcription_id: str
    start_transcription_time: int
    end_transcription_id: str
    end_transcription_time: int
    duration: int

    created_at: str
    updated_at: str


class StateUpdatedAtItem(TypedDict):
    state: str
    updatedAt: int


class Organizer(TypedDict):
    id: int | None
    firstName: str | None
    lastName: str | None
    avatar: str | None
    email: str | None


class Participant(TypedDict):
    id: int
    # firstName and lastName are none if the user is frictionless / guest
    firstName: str | None
    lastName: str | None
    guestFullName: str | None
    avatar: str | None
    email: str
    recurrenceID: int


class TypesenseSessionRecurrence(TypedDict):
    id: str  # mars recurrence id
    sessionID: int
    teamID: str | None
    title: str
    description: str | None
    organizer: Organizer
    availableToUserIds: List[str]
    participants: List[Participant]
    createdAt: int


class TypesenseLiveTranscriptionBatchBase(TypedDict):
    id: str
    text: str
    createdAt: int
    embedding: Optional[List[float]]
    availableToUserIds: List[str]


class TypesenseTranscriptChunk(TypedDict):
    id: str
    recurrenceID: str
    sessionID: int
    text: str
    embedding: Optional[List[float]]
    createdAt: int


class TypesenseTeam(TypedDict):
    id: str
    name: str
    userIds: List[str]
    createdAt: int


class RerankedTypesenseLiveTranscriptionBatch(TypesenseLiveTranscriptionBatchBase):
    recurrenceID: str
    sessionID: str


class SessionRecurrence(TypedDict):
    id: int
    recurrenceID: int
    sessionID: int
    title: str
    about: str | None
    startTimestamp: int
    endTimestamp: int
    createdAt: int
    creatorID: int | None
    creatorFirstName: str | None
    creatorEmail: str | None
    creatorLastName: str | None
    creatorAvatar: str | None
    stateUpdatedAt: List[StateUpdatedAtItem]
    dataVisibility: str
    participants: List[Participant] | None


class TypesenseSessionRecurrenceData(TypedDict):
    """Simplified recurrence data for typesense indexing - only contains fields actually used"""

    recurrenceID: int
    sessionID: int
    title: str
    about: str | None
    dataVisibility: str
    creatorID: int | None
    creatorFirstName: str | None
    creatorLastName: str | None
    creatorEmail: str | None
    creatorAvatar: str | None
    createdAt: int


class TeamMember(TypedDict):
    id: int
    firstName: str
    lastName: str
    email: str


class Team(TypedDict):
    id: int
    name: str
    domains: List[str]
    allTeamMembers: List[TeamMember]
    totalMembers: int
    createdAt: int


class IndexMeetingPayload(TypedDict):
    recurrence: SessionRecurrence


class LiveTranscription(TypedDict):
    batchID: str
    userID: str
    userFirstName: str
    userLastName: str
    text: str
    timeUnix: int


class LiveTranscriptionsBatch(TypedDict):
    batch_id: str
    live_transcriptions: List[LiveTranscription]
    text: str


class GetRecurrenceTranscriptParagraphs(TypedDict):
    batches: List[LiveTranscriptionsBatch]
    full_text: str


def extract_name_from_email(email: str):
    match = re.match(r"([^@]+)@", email)
    if match:
        return match.group(1)
    else:
        return None


async def save_transcript_chunk_batch_document(
    document: TypesenseTranscriptChunk,
) -> bool:
    success = True
    try:
        upsert_response = typesense.client.collections[
            TRANSCRIPT_CHUNKS_COLLECTION_NAME
        ].documents.upsert(document)  # type: ignore

        print(
            f"[save_transcript_chunk_batch_document] upsert response {document['id']}. success: {upsert_response is not None}",
        )
    except Exception as e:
        success = False
        print(
            f"[save_transcript_chunk_batch_document] failed to upsert {document['id']}. e: {e}"
        )
    finally:
        return success


async def save_typesense_recurrence_document(
    document: TypesenseSessionRecurrence,
) -> bool:
    success = True
    try:
        upsert_response = typesense.client.collections[
            RECURRENCES_COLLECTION_NAME
        ].documents.upsert(document)  # type: ignore

        print(
            f"[save_typesense_recurrence_document] upsert response {document['id']}. success: {upsert_response is not None}",
        )
    except Exception as e:
        success = False
        print(
            f"[save_typesense_recurrence_document] failed to upsert {document['id']}. e: {e}"
        )
    finally:
        return success


def get_team_id_by_recurrence_id_query(recurrence_id: int):
    return f"""SELECT t.id as "teamID"
    FROM sessions_recurrences sr
    LEFT JOIN users us ON us.id = sr."primaryHostUserID"
    LEFT JOIN team_members tm ON tm."userID" = us.id
    LEFT JOIN teams t ON t.id = tm."teamID"
    WHERE sr."recurrenceID" = {recurrence_id}
    """


async def get_team_id_by_recurrence_id(
    recurrence_id: int, mars_pool: aiopg.Pool
) -> int | None:
    """
    Gets a team owning the recurrence
    """
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_team_id_by_recurrence_id_query(recurrence_id))
            records = await mars_cursor.fetchall()
            if not records:
                return None
            return records[0].get("teamID")


async def build_typesense_recurrence_document(
    recurrence: TypesenseSessionRecurrenceData,
    team_id: int | None,
    participants: List[Participant],
    participants_ids: List[str],
    chunks: List[str],
) -> TypesenseSessionRecurrence:
    recurrence_id = recurrence.get("recurrenceID")

    creator_id = recurrence.get("creatorID")
    ts_document: TypesenseSessionRecurrence = {
        "id": str(recurrence_id),
        "sessionID": recurrence.get("sessionID"),
        "teamID": str(team_id) if team_id else None,
        "title": recurrence.get("title"),
        "description": recurrence.get("about"),
        "organizer": {
            "id": creator_id,
            "firstName": recurrence.get("creatorFirstName"),
            "lastName": recurrence.get("creatorLastName"),
            "avatar": recurrence.get("creatorAvatar"),
            "email": recurrence.get("creatorEmail"),
        },
        "participants": [],
        "availableToUserIds": [str(creator_id)] if creator_id is not None else [],
        "createdAt": recurrence.get("createdAt"),
    }

    ts_document["participants"] = participants
    ts_document["availableToUserIds"] = participants_ids
    return ts_document


@backoff.on_exception(
    backoff.expo, Exception, max_tries=3, max_time=1800, on_backoff=backoff_hdlr
)
async def cohere_search_document_embed(texts: List[str]) -> List[List[float]]:
    try:
        co_resp = await cohere_client.embed(
            texts=texts,
            model=settings.cohere_embed_model,
            input_type="search_document",
        )

        embeddings: List[List[float]] = co_resp.embeddings  # type: ignore
        return embeddings
    except Exception as e:
        logger.error("[cohere_search_document_embed] failed e: {e}")
        raise e


async def typesense_index_meeting(
    recurrence: TypesenseSessionRecurrenceData,
    idx: int | None,
    transcript_fulltext: str,
    mars_pool: aiopg.Pool,
):
    start = time.time()

    session_id = recurrence["sessionID"]
    recurrence_id = recurrence["recurrenceID"]

    print(
        f"[typesense_index_meeting] starting. session id: {session_id}, recurrence id: {recurrence_id} idx: {idx}"
    )

    if len(transcript_fulltext) < 500:
        print("[typesense_index_meeting] Transcript too short. Skipping")
        return

    participants, participants_ids = await get_session_participants(
        session_id=session_id, mars_pool=mars_pool
    )

    ModalApp = modal.Cls.lookup(MODAL_INGEST_APP_NAME, "Modal")

    # Semantically chunking the transcript
    chunks: List[str] = ModalApp().transcript_to_chunks.remote(transcript_fulltext)

    # Embed chunks
    co_resp = await cohere_client.embed(
        texts=chunks,
        model=settings.cohere_embed_model,
        input_type="search_document",
    )

    embeddings: List[List[float]] = co_resp.embeddings  # type: ignore

    # Build transcript chunks documents (typesense)
    transcript_chunks_documents = await asyncio.gather(
        *[
            build_typesense_transcript_chunk_document(
                recurrence=recurrence,
                chunk_text=item[0],
                embeddings=item[1],
                idx=idx,
            )
            for idx, item in enumerate(zip(chunks, embeddings))
        ],
    )

    team_id = None
    if recurrence["dataVisibility"] == "team-visible":
        team_id = await get_team_id_by_recurrence_id(
            recurrence_id=recurrence["recurrenceID"], mars_pool=mars_pool
        )

    # Build recurrence document (typesense)
    recurrence_document = await build_typesense_recurrence_document(
        recurrence=recurrence,
        team_id=team_id,
        participants=participants,
        participants_ids=participants_ids,
        chunks=chunks,
    )

    # Write recurrence doc
    await save_typesense_recurrence_document(recurrence_document)

    if len(transcript_chunks_documents) > 0:
        # Write all transcript chunks typesense docs
        batch_w_results = await asyncio.gather(
            *[
                save_transcript_chunk_batch_document(
                    batch_document,
                )
                for batch_document in transcript_chunks_documents
            ],
        )

        print(
            f"[typesense_index_meeting] done. session_id: {session_id}, recurrence id: {recurrence_id}, idx: {idx}. duration: {time.time() - start}. writes count: {len(batch_w_results) + 1}"
        )


async def build_typesense_transcript_chunk_document(
    recurrence: TypesenseSessionRecurrenceData,
    embeddings: List[float],
    chunk_text: str,
    idx: int | None,
) -> TypesenseTranscriptChunk:
    print(
        f"[build_typesense_transcript_chunk_document-{recurrence['recurrenceID']}-{idx}] Starting"
    )
    start = time.time()

    recurrence_id = recurrence.get("recurrenceID")
    ts_document: TypesenseTranscriptChunk = {
        "id": f"{recurrence_id}-{time.time()}",
        "recurrenceID": str(recurrence_id),
        "sessionID": recurrence.get("sessionID"),
        "createdAt": recurrence.get("createdAt"),
        "embedding": None,
        "text": chunk_text,
    }

    if len(embeddings) > 0:
        ts_document["embedding"] = embeddings

    print(
        f"[build_typesense_transcript_chunk_document-{recurrence['recurrenceID']}-{idx}] done. duration: {time.time() - start}"
    )

    return ts_document


async def get_recurrence_transcript_paragraphs(
    recurrence_id: int, luxor_pool: aiopg.Pool
) -> GetRecurrenceTranscriptParagraphs:
    batches: List[LiveTranscriptionsBatch] = []
    full_text = ""

    async with luxor_pool.acquire() as luxor_conn:
        async with luxor_conn.cursor(cursor_factory=RealDictCursor) as luxor_cursor:
            transcriptions_total_count = (
                await get_recurrence_transcriptions_total_count(
                    recurrence_id=recurrence_id, luxor_cursor=luxor_cursor
                )
            )
            if transcriptions_total_count == 0:
                return {"batches": [], "full_text": ""}

            live_transcriptions = await get_all_recurrence_live_transcriptions(
                recurrence_id=recurrence_id, luxor_cursor=luxor_cursor
            )
            batches = create_recurrence_transcriptions_batches(live_transcriptions)
            for batch in batches:
                full_text += f"{batch['text']}\n"

            return {"batches": batches, "full_text": full_text}


def get_recurrence_transcriptions_total_count_query(recurrence_id: int) -> str:
    return f"""
    SELECT COUNT(*)
    FROM "LiveTranscription"
    WHERE "sessionRecurrenceID" = '{recurrence_id}'
    """


def find_batch_idx(batches: List[LiveTranscriptionsBatch], batch_id: str) -> int | None:
    for idx, b in enumerate(batches):
        if b["batch_id"] == batch_id:
            return idx
    return None


def create_recurrence_transcriptions_batches(
    live_transcriptions: List[LiveTranscription],
) -> List[LiveTranscriptionsBatch]:
    batches: List[LiveTranscriptionsBatch] = []

    for transcription in live_transcriptions:
        batch_id = transcription["batchID"]
        batch_idx = find_batch_idx(batches, batch_id)
        if batch_idx is not None:
            batches[batch_idx]["live_transcriptions"].append(transcription)
        else:
            batches.append(
                {
                    "batch_id": batch_id,
                    "live_transcriptions": [transcription],
                    "text": "",
                }
            )

    for batch in batches:
        batch["text"] = batch_live_transcriptions_to_transcript_str(
            batch["live_transcriptions"]
        )

    return batches


def batch_live_transcriptions_to_transcript_str(
    batch_live_transcriptions: List[LiveTranscription],
) -> str:
    transcript_str = ""
    first_name = None
    for transcription in batch_live_transcriptions:
        new_first = transcription["userFirstName"]
        if new_first != first_name:
            transcript_str += f"\n\n{new_first}: {transcription['text']}"
        else:
            transcript_str += transcription["text"]
        first_name = new_first
    return transcript_str


def get_recurrence_transcriptions_query(
    recurrence_id: int, limit: int, offset: int
) -> str:
    return f"""
    SELECT "userID", "userFirstName", "userLastName", "timeUnix", "batchID", "text"
    FROM "LiveTranscription"
    WHERE "sessionRecurrenceID" = '{recurrence_id}'
    ORDER BY "timeUnix" ASC
    OFFSET {offset}
    LIMIT {limit}
    """


def get_upcoming_sessions_by_user_id_query(user_id: int, limit: int) -> str:
    return f"""
    SELECT *
    FROM (
        SELECT DISTINCT ON(s.id)
            s.id AS "sessionID",
            s."recurrenceID",
            s."title",
            s."stateUpdatedAt",
            s."about",
            s."createdAt",
            s."settings",
            s."startTimestamp",
            s."endTimestamp",
            creator.id AS "creatorID",
            creator."lastName" AS "creatorLastName",
            creator."email" AS "creatorEmail",
            creator."avatar" AS "creatorAvatar"
        FROM sessions s
        LEFT JOIN users "creator"
                ON s."creatorUserID" = creator.id
        LEFT JOIN session_access_rules rule
                ON rule."sessionID" = s.id
        LEFT JOIN session_users su
                ON su."sessionID" = s.id
        LEFT JOIN users u
                ON su."userID" = u.id
        WHERE state in ('scheduled', 'ready', 'active')
        AND s."startTimestamp" > EXTRACT(EPOCH FROM NOW())
        AND rule.type = 'email'
        AND rule."accessType" = 'viewer'
        AND rule."restrictionStatus" = 'granted'
        AND rule.value = u.email
        AND su."userID" = {user_id}
        ) s
    ORDER BY s."startTimestamp" LIMIT {limit}
    """


def get_team_members_by_team_id_query(team_id: int) -> str:
    return f"""
    SELECT *
    FROM "team_members"
    WHERE "teamID" = {team_id}
    """


class DBTeamMember(TypedDict):
    id: int
    userID: int
    teamID: int
    role: str
    createdAt: int
    updatedAt: int


async def get_team_members_by_team_id(
    team_id: int, mars_pool: aiopg.Pool
) -> List[DBTeamMember]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_team_members_by_team_id_query(team_id))
            records: List[DBTeamMember] = await mars_cursor.fetchall()
            return records


def get_past_ai_enabled_recurrences_query(interval: str = "1 months") -> str:
    return f"""
    SELECT *
    FROM "sessions_recurrences" sr
    WHERE sr."settings"->>'summaAI' = 'true'
    AND sr.state = 'ended'
    AND sr."startedAt" >= EXTRACT(EPOCH FROM NOW() - INTERVAL '{interval}')
    ORDER BY "startedAt" DESC;
    """


def get_upcoming_team_visible_sessions_by_user_id_query(
    user_id: int, limit: int
) -> str:
    return f"""
    SELECT *
    FROM (
        SELECT DISTINCT ON(s.id)
            s.id AS "sessionID",
            s."recurrenceID",
            s."title",
            s."stateUpdatedAt",
            s."about",
            s."createdAt",
            s."settings",
            s."startTimestamp",
            s."endTimestamp",
            creator.id AS "creatorID",
            creator."lastName" AS "creatorLastName",
            creator."email" AS "creatorEmail",
            creator."avatar" AS "creatorAvatar"
        FROM sessions s
        LEFT JOIN users "creator"
                ON s."creatorUserID" = creator.id
        LEFT JOIN session_access_rules rule
                ON rule."sessionID" = s.id
        LEFT JOIN session_users su
                ON su."sessionID" = s.id
        LEFT JOIN users u
                ON su."userID" = u.id
        WHERE state in ('scheduled', 'ready', 'active')
        AND s."startTimestamp" > EXTRACT(EPOCH FROM NOW())
        AND s."dataVisibility" = 'team-visible'
        AND rule.type = 'email'
        AND rule."accessType" = 'viewer'
        AND rule."restrictionStatus" = 'granted'
        AND rule.value = u.email
        AND su."userID" = {user_id}
        ) s
    ORDER BY s."startTimestamp" LIMIT {limit}
    """


async def get_past_ai_enabled_recurrences(
    mars_pool: aiopg.Pool, interval: str
) -> List[SessionRecurrence]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_past_ai_enabled_recurrences_query(interval))
            records: List[SessionRecurrence] = await mars_cursor.fetchall()
            return records


Session = SessionRecurrence


async def get_upcoming_sessions_by_user_id(
    user_id: int, limit: int, mars_pool: aiopg.Pool
) -> List[Session]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_upcoming_sessions_by_user_id_query(user_id, limit)
            )
            records: List[Session] = await mars_cursor.fetchall()
            return records


async def get_upcoming_team_visible_sessions_by_user_id(
    user_id: int, limit: int, mars_pool: aiopg.Pool
) -> List[Session]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_upcoming_team_visible_sessions_by_user_id_query(user_id, limit)
            )
            records: List[Session] = await mars_cursor.fetchall()
            return records


async def get_recurrence_transcriptions_total_count(
    recurrence_id: int, luxor_cursor: aiopg.Cursor
) -> int:
    await luxor_cursor.execute(
        get_recurrence_transcriptions_total_count_query(recurrence_id)
    )
    records = await luxor_cursor.fetchall()
    if len(records) == 0:
        return 0
    return records[0].get("count")


def get_session_participants_query(session_id: int) -> str:
    return f"""
    SELECT DISTINCT ON (us.id)
           us."id" AS "id",
           us."firstName" AS "firstName",
           us."lastName" AS "lastName",
           us."avatar" AS "avatar",
           us."email" AS "email",
           su."roleIDs" AS "roleIDs",
           su."guestFullName" as "guestFullName",
           se."recurrenceID" AS "recurrenceID"
    FROM "session_users" su
    LEFT JOIN "sessions" se
           ON su."sessionID" = se."id"
    LEFT JOIN "users" us
           ON us."id" = su."userID"
    WHERE se."id" = {session_id}
    """


def get_recurrences_participants_by_ids_query(recurrence_ids: List[int]) -> str:
    ids = ",".join([str(rid) for rid in recurrence_ids])

    return f"""
    SELECT
        "users"."id" as "id",
        "users"."firstName" as "firstName",
        "users"."lastName" as "lastName",
        "users"."avatar" as "avatar",
        "users"."email" as "email",
        "session_users"."roleIDs" as "roleIDs",
        "session_users"."sessionRecurrenceID" as "recurrenceID",
        "session_users"."guestFullName" as "guestFullName"
    FROM "session_users"
    LEFT JOIN "sessions_recurrences"
        ON "session_users"."sessionRecurrenceID" = sessions_recurrences."recurrenceID"
    LEFT JOIN "users"
        ON users.id = "session_users"."userID"
    WHERE "sessions_recurrences"."recurrenceID" in ({ids})
    """


class GetRecurrenceAccessRulesRecord(TypedDict):
    type: Literal["email"] | Literal["domain"]
    value: str
    email: str | None
    firstName: str | None
    lastName: str | None
    joined: bool


async def get_recurrence_access_rules(
    recurrence_id: int, mars_pool: aiopg.Pool
) -> List[GetRecurrenceAccessRulesRecord]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_session_access_rules_query(recurrence_id))
            records: List[GetRecurrenceAccessRulesRecord] = await mars_cursor.fetchall()
            return records


async def get_session_participants(
    session_id: int, mars_pool: aiopg.Pool
) -> Tuple[List[Participant], List[str]]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_session_participants_query(session_id=session_id)
            )
            participants = await mars_cursor.fetchall()
            dicts: List[Participant] = []
            ids: List[str] = []

            for participant in participants:
                pID = participant.get("id", None)
                if pID is None:
                    continue

                dicts.append(
                    {
                        "recurrenceID": participant.get("recurrenceID"),
                        "id": pID,
                        "firstName": participant.get("firstName"),
                        "lastName": participant.get("lastName"),
                        "avatar": participant.get("avatar"),
                        "email": participant.get("email"),
                        "guestFullName": participant.get("guestFullName"),
                    }
                )
                ids.append(str(pID))
            return dicts, ids


async def get_recurrences_participants_by_recurrence_ids(
    recurrence_ids: List[int], mars_pool: aiopg.Pool
) -> Tuple[List[Participant], List[str]]:
    if not recurrence_ids:
        return [], []

    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_recurrences_participants_by_ids_query(recurrence_ids)
            )
            participants = await mars_cursor.fetchall()
            dicts: List[Participant] = []
            ids: List[str] = []

            for participant in participants:
                pID = participant.get("id")
                dicts.append(
                    {
                        "recurrenceID": participant.get("recurrenceID"),
                        "id": pID,
                        "firstName": participant.get("firstName"),
                        "lastName": participant.get("lastName"),
                        "avatar": participant.get("avatar"),
                        "email": participant.get("email"),
                        "guestFullName": participant.get("guestFullName"),
                    }
                )
                ids.append(str(pID))
            return dicts, ids


def get_participants_firstnames_by_recurrence_ids_query(
    recurrence_ids: List[int],
) -> str:
    ids = ",".join([str(rec_id) for rec_id in recurrence_ids])

    return f"""
    SELECT us."firstName", su."sessionRecurrenceID" as "recurrenceID" FROM session_users su
    LEFT JOIN users us
            ON su."userID" = us.id
    WHERE "sessionRecurrenceID" in (
        {ids}
    )
    """


async def get_participants_firstnames_by_recurrence_ids(
    recurrence_ids: List[int], mars_pool: aiopg.Pool
) -> Dict[int, List[str]]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_participants_firstnames_by_recurrence_ids_query(
                    recurrence_ids=recurrence_ids
                )
            )
            result: Dict[int, List[str]] = {}
            rows = await mars_cursor.fetchall()
            for r in rows:
                rid = r.get("recurrenceID")
                first_names = result.get(rid, [])
                first_name = r.get("firstName")
                if first_name:
                    first_names.append(first_name)
                    result[rid] = first_names
                else:
                    print("no first name... skipping ")
            return result


def get_recurrence_states_by_recurrence_ids_query(
    recurrence_ids: List[int],
) -> str:
    ids = ",".join([f"{str(id)}" for id in recurrence_ids])

    return f"""
    SELECT sr."stateUpdatedAt", sr."recurrenceID" FROM sessions_recurrences sr
    WHERE sr."recurrenceID" in (
       {ids}
    )
    """


def get_past_team_visible_user_session_recurrences_by_user_ids_query(
    user_ids: List[int], limit: int, offset: int
) -> str:
    comma_ids = ",".join(map(str, user_ids))
    return f"""
    SELECT sr."id" as "sessionID",
            sr."recurrenceID" as "recurrenceID",
            sr."title" as "title",
            sr."about" as "about",
            sr."createdAt" as "createdAt",
            sr."updatedAt" as "updatedAt",
            sr."stateUpdatedAt" as "stateUpdatedAt",
            sr."dataVisibility" as "dataVisibility",
            us.id as "creatorID",
            us."firstName" as "creatorFirstName",
            us."lastName" as "creatorLastName",
            us."avatar" as "creatorAvatar"
    FROM session_users su
    LEFT JOIN sessions_recurrences sr ON sr."recurrenceID" = su."sessionRecurrenceID"
    LEFT JOIN users us on us.id = su."userID"
    WHERE su."userID" IN ({comma_ids}) AND "sr"."settings"->>'summaAI' = 'true' AND sr.state = 'ended' AND sr."dataVisibility" = 'team-visible'
    ORDER BY (
        SELECT elem->>'updatedAt'
        FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
        WHERE elem->>'state' = 'ended'
        ORDER BY elem->>'updatedAt' DESC
        LIMIT 1
    ) DESC
    LIMIT {limit}
    OFFSET {offset}
    """


def get_past_team_visible_session_recurrences_by_team_id_query(
    team_id: int, limit: int, offset: int
) -> str:
    return f"EXECUTE get_past_team_visible_recurrences_by_team_id({team_id}, {limit}, {offset});"


def get_past_team_visible_session_recurrences_since_unix_ts_by_team_id_query(
    team_id: int, unix_ts: int, limit: int, offset: int
) -> str:
    return f"EXECUTE get_past_team_visible_recurrences_since_unix_ts_by_team_id({team_id}, {limit}, {offset}, {unix_ts});"


def get_team_info_by_id_query(team_id: int) -> str:
    return f"""SELECT
        tm.*,
        COUNT(tmem.id) AS totalMembers,
            ARRAY_AGG(
            JSON_BUILD_OBJECT(
                'id', us.id,
                'firstName', us."firstName",
                'lastName', us."lastName",
                'email', us.email
            )
        ) AS "allTeamMembers"
    FROM teams tm
    LEFT JOIN team_members tmem ON tmem."teamID" = tm.id
    LEFT JOIN users us on us.id = tmem."userID"
    WHERE tm.id = {team_id}
    GROUP BY tm.id;
    """


async def get_past_team_visible_user_session_recurrences_by_user_ids(
    user_ids: List[int],
    limit: int,
    offset: int,
    mars_pool: aiopg.Pool,
) -> List[SessionRecurrence]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_past_team_visible_user_session_recurrences_by_user_ids_query(
                    user_ids=user_ids,
                    limit=limit,
                    offset=offset,
                )
            )
            return await mars_cursor.fetchall()


async def get_past_team_visible_session_recurrences_by_team_id(
    team_id: int,
    limit: int,
    offset: int,
    mars_pool: aiopg.Pool,
) -> List[SessionRecurrence]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_past_team_visible_session_recurrences_by_team_id_query(
                    team_id=team_id,
                    limit=limit,
                    offset=offset,
                )
            )
            return await mars_cursor.fetchall()


async def get_past_team_visible_session_recurrences_since_unix_ts_by_team_id(
    team_id: int,
    unix_ts: int,
    limit: int,
    offset: int,
    mars_pool: aiopg.Pool,
) -> List[SessionRecurrence]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_past_team_visible_session_recurrences_since_unix_ts_by_team_id_query(
                    team_id=team_id,
                    unix_ts=unix_ts,
                    limit=limit,
                    offset=offset,
                )
            )
            return await mars_cursor.fetchall()


async def get_team_info_by_id(
    team_id: int,
    mars_pool: aiopg.Pool,
) -> Team | None:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_team_info_by_id_query(
                    team_id=team_id,
                )
            )
            records = await mars_cursor.fetchall()
            if len(records) == 0:
                return None
            return records[0]


async def get_recurrence_states_by_recurrence_ids(
    recurrence_ids: List[int], mars_pool: aiopg.Pool
) -> Dict[int, List[StateUpdatedAtItem]]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_recurrence_states_by_recurrence_ids_query(
                    recurrence_ids=recurrence_ids
                )
            )
            result: Dict[int, List[StateUpdatedAtItem]] = {}

            rows = await mars_cursor.fetchall()

            for r in rows:
                rid = r.get("recurrenceID")
                state_updated_at = r.get("stateUpdatedAt")
                result[rid] = state_updated_at
            return result


async def get_all_recurrence_live_transcriptions(
    recurrence_id: int, luxor_cursor: aiopg.Cursor, limit: int = 500
) -> List[LiveTranscription]:
    results: List[LiveTranscription] = []
    offset = 0
    while True:
        await luxor_cursor.execute(
            get_recurrence_transcriptions_query(
                recurrence_id=recurrence_id, limit=limit, offset=offset
            )
        )
        records: List[LiveTranscription] = await luxor_cursor.fetchall()
        if len(records) == 0:
            break

        for record in records:
            results.append(record)
        offset += len(records)
    return results


def get_recurrences_by_ids_query(recurrence_ids: List[int]) -> str:
    recurrence_ids_arg = ",".join([str(id) for id in recurrence_ids])
    return """SELECT
        sr.id AS "sessionID",
        sr."recurrenceID",
        sr."title",
        sr."stateUpdatedAt",
        sr."startTimestamp",
        sr."endTimestamp",
        sr."about",
        sr."createdAt",
        sr."settings",
        u.id AS "creatorID",
        u."firstName" AS "creatorFirstName",
        u."lastName" AS "creatorLastName",
        u."email" AS "creatorEmail",
        u."avatar" AS "creatorAvatar"
    FROM "sessions_recurrences" sr
    LEFT JOIN "users" u
        ON sr."creatorUserID" = u.id
    WHERE
        sr."recurrenceID" IN ({0});
    """.format(recurrence_ids_arg)


def get_session_access_rules_query(recurrence_id: int):
    return f"""SELECT DISTINCT ON(ru.value) ru.type, ru.value, u.email, u."firstName", u."lastName", su.joined
    FROM session_access_rules ru
    LEFT JOIN users u
            ON u.email = ru.value
    LEFT JOIN session_users su
            ON su."userID" = u.id
    WHERE ru."sessionRecurrenceID" = {recurrence_id}
    """


class User(TypedDict):
    userId: int
    email: str
    teamId: int
    firstName: str
    lastName: str


def get_user_by_id_query(user_id: int) -> str:
    return f"""
    SELECT t.id as "teamId",
    us.id as "userId",
    * FROM users us
    LEFT JOIN team_members tm ON tm."userID" = us.id
    LEFT JOIN teams t ON t.id = tm."teamID"
    WHERE us.id = {user_id}
    """


async def get_user_by_id(user_id: int, mars_pool: aiopg.Pool) -> User | None:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_user_by_id_query(user_id))
            records: List[User] = await mars_cursor.fetchall()
            if len(records) == 0:
                return None
            return records[0]


def get_session_recurrence_query(recurrence_id: int) -> str:
    return f"""
    SELECT DISTINCT ON (sessions_recurrences."recurrenceID") "sessions_recurrences"."id" as "sessionID",
                "sessions_recurrences"."recurrenceID" as "recurrenceID",
                "sessions_recurrences"."recurrenceID" as "id",
                "sessions_recurrences"."title" as "title",
                "sessions_recurrences"."about" as "about",
                "sessions_recurrences"."startTimestamp" as "startTimestamp",
                "sessions_recurrences"."endTimestamp" as "endTimestamp",
                "sessions_recurrences"."createdAt" as "createdAt",
                "sessions_recurrences"."updatedAt" as "updatedAt",
                "sessions_recurrences"."stateUpdatedAt" as "stateUpdatedAt",
                "sessions_recurrences"."dataVisibility" as "dataVisibility",
                "users".id as "creatorID",
                "users"."firstName" as "creatorFirstName",
                "users"."lastName" as "creatorLastName",
                "users"."email" as "creatorEmail",
                "users"."avatar" as "creatorAvatar"

        FROM sessions_recurrences
        LEFT JOIN session_users
            ON session_users."sessionRecurrenceID" = sessions_recurrences."recurrenceID"
        LEFT JOIN users
            ON users.id = session_users."userID"
        WHERE "sessions_recurrences"."recurrenceID" = {recurrence_id}
    """


async def get_recurrence(
    recurrence_id: int,
    mars_pool: aiopg.Pool,
) -> SessionRecurrence:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_session_recurrence_query(recurrence_id))
            return (await mars_cursor.fetchall())[0]


class SessionSeetings(TypedDict):
    enableRecording: bool


# https://github.com/pydantic/pydantic/discussions/6699
class SessionRecurrenceBatchItem(SessionRecurrence):
    stateUpdatedAt: List[StateUpdatedAtItem]
    settings: SessionSeetings


MessageItemType = (
    Literal["action_items"]
    | Literal["pms"]
    | Literal["meetings_list"]
    | Literal["text"]
)


class MessageItemSourceData(BaseModel):
    recurrenceId: str
    sessionId: str
    title: str
    about: Optional[Union[str, None]] = None
    participants: List[str]
    startedAt: int
    endedAt: int
    organizer: Optional[Union[str, None]] = None


class MessageItemSource(BaseModel):
    type: Literal["session"]
    data: MessageItemSourceData


Sources = Dict[str, List[MessageItemSource]]
MessageItemContentType = Literal["markdown"]


class MessageItem(BaseModel):
    type: MessageItemType
    content: str
    contentType: MessageItemContentType = "markdown"


MessageRole = Literal["user", "ai", "tool"]
OpenAIMessageRole = Literal["user", "assistant", "system", "tool"]


class MeetingMemoryMessage(BaseModel):
    id: str
    role: MessageRole
    items: List[MessageItem]
    feedback: int  # 0,1,2
    sources: Sources | None = None


class OpenAIMessage(BaseModel):
    content: str
    role: str


class AskAIBody(BaseModel):
    """Ask AI Body DTO"""

    query: str | None = (
        None  # not present when refresh=True as query is retrieved from the DB
    )
    requestId: str
    threadId: str | None = None
    tz: str | None = None  # IANA timezone string
    recurrenceIds: List[str] | None = None

    messageId: str | None = None  # present when refresh=True and used only for that
    refresh: bool | None = False

    retry: bool | None = False


class CreateTeamBody(BaseModel):
    """Create team body DTO"""

    teamId: str
    name: str
    memberIds: List[str]


class PatchTypesenseRecurrenceTeamAccess(BaseModel):
    """Patch typesense recurrence team access DTO"""

    teamId: str
    value: bool


class CreateTeamMemberBody(BaseModel):
    """Add team member Body DTO"""

    userId: str


class DeleteTeamMemberBody(BaseModel):
    """Delete team member Body DTO"""

    userId: str


class StopRunResponseDTO(BaseModel):
    """Stop run reponse DTO"""

    class Data(BaseModel):
        threadId: str
        providerThreadId: str | None
        runId: str | None

    data: Data | None
    message: str
    success: bool


class StopRunBody(BaseModel):
    """Stop run Body DTO"""

    threadId: str


class PatchUserFeedbackBody(BaseModel):
    messageId: str
    feedback: int  # 0,1,2


class Thread(BaseModel):
    id: str
    title: str
    lastMessageAt: int
    createdAt: int
    updatedAt: int


class GetUserThreadsResponse(BaseModel):
    class Data(BaseModel):
        total: int
        threads: List[Thread]
        hasMore: bool

    data: Data | None
    success: bool
    message: str


class MessageOut(BaseModel):
    id: str
    role: MessageRole
    threadId: str
    content: str
    feedback: int  # 0,1,2
    sources: Sources | None
    createdAt: int
    updatedAt: int


class GetThreadResponse(BaseModel):
    class Data(BaseModel):
        thread: Thread

    data: Data | None
    success: bool
    message: str


class GetThreadMessagesResponse(BaseModel):
    class Data(BaseModel):
        total: int
        messages: List[MessageOut]
        hasMore: bool

    data: Data | None
    success: bool
    message: str


class TypesenseDocumentMutationResponse(BaseModel):
    success: bool
    message: str


class DeleteThreadResponse(BaseModel):
    class Data(BaseModel):
        threadId: str

    data: Data | None
    success: bool
    message: str


class AskAIResponseDTO(BaseModel):
    """Ask AI Reponse DTO"""

    class Data(BaseModel):
        messages: List[MeetingMemoryMessage]
        threadId: str | None = None

    data: Data | None
    message: str


class PatchUserFeedbackResponse(BaseModel):
    class Data(BaseModel):
        message: MeetingMemoryMessage

    data: Data | None
    message: str


def str_to_message_role(role_str: str) -> MessageRole:
    if role_str == "user" or role_str == "ai":
        return role_str
    else:
        raise ValueError(f"Invalid MessageRole: {role_str}")


def message_role_to_openai_message_role(
    role: MessageRole | str,
) -> OpenAIMessageRole:
    if role == "ai":
        return "assistant"
    if role == "user" or role == "tool":
        return role
    raise ValueError("Invalid message role")


async def get_recurrences_by_ids(
    recurrence_ids: List[int], mars_pool: aiopg.Pool
) -> List[SessionRecurrenceBatchItem]:
    st = time.perf_counter()
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(get_recurrences_by_ids_query(recurrence_ids))
            records: List[SessionRecurrenceBatchItem] = await mars_cursor.fetchall()
            logger.info(
                f"[get_recurrences_by_ids] duration: {time.perf_counter() - st}"
            )
            return records


def source_items_dicts_from_models(sources: Sources):
    new_sources = {}
    for k in sources:
        curr_group = sources[k]
        serialized = []
        for item in curr_group:
            serialized.append(item.model_dump())
        new_sources[k] = serialized
    return new_sources


def sources_json_from_dict(sources: Sources) -> str:
    return json.dumps(source_items_dicts_from_models(sources))


async def create_meeting_memory_sources_from_recurrences(
    recurrences: List[SessionRecurrenceBatchItem], mars_pool: aiopg.Pool
) -> Sources:
    st = time.perf_counter()
    sources: Sources = {}
    recurrence_ids = list(map(lambda r: r["recurrenceID"], recurrences))

    p_names_map, r_states_map = await asyncio.gather(
        get_participants_firstnames_by_recurrence_ids(
            recurrence_ids,
            mars_pool=mars_pool,
        ),
        get_recurrence_states_by_recurrence_ids(recurrence_ids, mars_pool),
    )

    for rec in recurrences:
        session_id = str(rec["sessionID"])
        recurrence_id = rec["recurrenceID"]

        rec_state_updated_at = r_states_map.get(recurrence_id, [])
        timestamps = parse_state_updated_at(rec_state_updated_at)

        # timestamps will be 0 for upcoming meetings
        if timestamps.get("started_at") == 0 or timestamps.get("ended_at") == 0:
            if rec.get("startTimestamp"):
                timestamps["started_at"] = rec["startTimestamp"]
            if rec.get("endTimestamp"):
                timestamps["ended_at"] = rec["endTimestamp"]

        organizer = rec.get("creatorFirstName", None)
        participants = list(
            filter(lambda p: p != organizer, p_names_map.get(recurrence_id, []))
        )

        dto = MessageItemSource(
            type="session",
            data=MessageItemSourceData(
                sessionId=session_id,
                recurrenceId=str(rec["recurrenceID"]),
                title=rec["title"],
                about=rec["about"],
                participants=participants,
                organizer=organizer,
                startedAt=timestamps["started_at"],
                endedAt=timestamps["ended_at"],
            ),
        )

        if sources.get(session_id) is not None:
            sources[session_id].append(dto)
        else:
            sources[session_id] = [dto]
    logger.info(
        f"[create_meeting_memory_sources_from_recurrences] duration: {time.perf_counter() - st}"
    )
    return sources


def get_post_session_summaries_by_recurrence_ids_query(
    recurrence_ids: List[int],
) -> str:
    ids = ",".join([f"'{str(id)}'" for id in recurrence_ids])
    return f"""
    SELECT * FROM post_session_summary WHERE session_recurrence_id IN ({ids})"""


def get_action_items_by_recurrence_ids_query(
    recurrence_ids: List[int],
) -> str:
    ids = ",".join([f"'{str(id)}'" for id in recurrence_ids])
    return f"""
    SELECT * FROM ai_feed WHERE session_recurrence_id IN ({ids}) AND msg_type = 'action-item'"""


async def get_post_session_summaries_by_recurrence_ids(
    recurrence_ids: List[int], nebula_pool: aiopg.Pool
) -> List[PostSessionSummaryDict]:
    async with nebula_pool.acquire() as nebula_conn:
        async with nebula_conn.cursor(cursor_factory=RealDictCursor) as nebula_cursor:
            await nebula_cursor.execute(
                get_post_session_summaries_by_recurrence_ids_query(recurrence_ids)
            )
            records: List[PostSessionSummaryDict] = await nebula_cursor.fetchall()
            return records


async def get_action_items_by_recurrence_ids(
    recurrence_ids: List[int], nebula_pool: aiopg.Pool
) -> List[AIFeedDict]:
    async with nebula_pool.acquire() as nebula_conn:
        async with nebula_conn.cursor(cursor_factory=RealDictCursor) as nebula_cursor:
            await nebula_cursor.execute(
                get_action_items_by_recurrence_ids_query(recurrence_ids)
            )
            records: List[AIFeedDict] = await nebula_cursor.fetchall()
            return records


def get_past_user_session_recurrences_by_user_ids_query(
    user_ids: List[int], limit: int, offset: int
) -> str:
    comma_ids = ",".join(map(str, user_ids))
    return f"""
    WITH ranked_sessions AS (
        SELECT DISTINCT ON (sr."recurrenceID")
               sr."id" AS "sessionID",
               sr."recurrenceID" AS "recurrenceID",
               sr."title" AS "title",
               sr."about" AS "about",
               sr."createdAt" AS "createdAt",
               sr."updatedAt" AS "updatedAt",
               sr."stateUpdatedAt" AS "stateUpdatedAt",
               sr."dataVisibility" AS "dataVisibility",
               us.id AS "creatorID",
               us."firstName" AS "creatorFirstName",
               us."lastName" AS "creatorLastName",
               us."avatar" AS "creatorAvatar",
               (
                   SELECT elem->>'updatedAt'
                   FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
                   WHERE elem->>'state' = 'ended'
                   ORDER BY elem->>'updatedAt' DESC
                   LIMIT 1
               ) AS "lastUpdatedAt"
        FROM session_users su
        LEFT JOIN sessions_recurrences sr ON sr."recurrenceID" = su."sessionRecurrenceID"
        LEFT JOIN users us ON us.id = su."userID"
        WHERE su."userID" IN ({comma_ids})
          AND sr."settings"->>'summaAI' = 'true'
          AND sr.state = 'ended'
          AND EXISTS (
              SELECT 1
              FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
              WHERE elem->>'state' = 'active'
          )
        ORDER BY sr."recurrenceID", "lastUpdatedAt" DESC
    )
    SELECT *
    FROM ranked_sessions
    ORDER BY "lastUpdatedAt" DESC
    LIMIT {limit}
    OFFSET {offset};

    """


def get_past_user_session_recurrences_since_unix_ts_by_user_ids_query(
    user_ids: List[int], unix_ts: int, limit: int, offset: int
) -> str:
    comma_ids = ",".join(map(str, user_ids))
    return f"""
    WITH ranked_sessions AS (
        SELECT DISTINCT ON (sr."recurrenceID")
               sr."id" AS "sessionID",
               sr."recurrenceID" AS "recurrenceID",
               sr."title" AS "title",
               sr."about" AS "about",
               sr."createdAt" AS "createdAt",
               sr."updatedAt" AS "updatedAt",
               sr."stateUpdatedAt" AS "stateUpdatedAt",
               sr."dataVisibility" AS "dataVisibility",
               us.id AS "creatorID",
               us."firstName" AS "creatorFirstName",
               us."lastName" AS "creatorLastName",
               us."avatar" AS "creatorAvatar",
               (
                   SELECT elem->>'updatedAt'
                   FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
                   WHERE elem->>'state' = 'ended'
                   ORDER BY elem->>'updatedAt' DESC
                   LIMIT 1
               ) AS "lastUpdatedAt"
        FROM session_users su
        LEFT JOIN sessions_recurrences sr ON sr."recurrenceID" = su."sessionRecurrenceID"
        LEFT JOIN users us ON us.id = su."userID"
        WHERE su."userID" IN ({comma_ids})
          AND COALESCE(sr."startedAt", sr."startTimestamp") >= {unix_ts}
          AND COALESCE(sr."startedAt", sr."startTimestamp") < EXTRACT(EPOCH FROM NOW())
          AND sr."settings"->>'summaAI' = 'true'
          AND sr.state = 'ended'
          AND EXISTS (
              SELECT 1
              FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
              WHERE elem->>'state' = 'active'
          )
        ORDER BY sr."recurrenceID", "lastUpdatedAt" DESC
    )
    SELECT *
    FROM ranked_sessions
    ORDER BY "lastUpdatedAt" DESC
    LIMIT {limit}
    OFFSET {offset};

    """


async def get_past_user_session_recurrences_since_unix_ts_by_user_ids(
    user_ids: List[int],
    unix_ts: int,
    limit: int,
    offset: int,
    mars_pool: aiopg.Pool,
) -> List[SessionRecurrence]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_past_user_session_recurrences_since_unix_ts_by_user_ids_query(
                    user_ids=user_ids,
                    unix_ts=unix_ts,
                    limit=limit,
                    offset=offset,
                )
            )
            return await mars_cursor.fetchall()


async def get_past_user_session_recurrences_by_user_ids(
    user_ids: List[int],
    limit: int,
    offset: int,
    mars_pool: aiopg.Pool,
) -> List[SessionRecurrence]:
    async with mars_pool.acquire() as mars_conn:
        async with mars_conn.cursor(cursor_factory=RealDictCursor) as mars_cursor:
            await mars_cursor.execute(
                get_past_user_session_recurrences_by_user_ids_query(
                    user_ids=user_ids,
                    limit=limit,
                    offset=offset,
                )
            )
            return await mars_cursor.fetchall()


ActionItemsMap = Dict[int, List[AIFeedDict]]


class SessionActivityTimestamps(TypedDict):
    scheduled_at: int
    started_at: int
    ended_at: int


def parse_state_updated_at(
    items: List[StateUpdatedAtItem],
) -> SessionActivityTimestamps:
    scheduled_at = 0
    started_at = 0
    ended_at = 0

    for item in items:
        ts = item["updatedAt"]
        if item["state"] == "active":
            started_at = ts
        if item["state"] == "ended":
            ended_at = ts
        if item["state"] == "scheduled":
            scheduled_at = ts

    return {
        "scheduled_at": scheduled_at,
        "started_at": started_at,
        "ended_at": ended_at,
    }


ParticipantsMap = Dict[int, List[Participant]]


def participants_map_from_participants_list(
    participants: List[Participant],
) -> ParticipantsMap:
    participants_map: ParticipantsMap = {}

    for p in participants:
        parts = participants_map.get(p["recurrenceID"], [])
        parts.append(p)
        participants_map[p["recurrenceID"]] = parts

    return participants_map


class Names(TypedDict):
    organizer_name: str
    participant_names: str


def user_or_guest_name(p: Participant) -> str:
    if p.get("firstName") is None or p.get("lastName") is None:
        return p.get("guestFullName") or ""
    return f"{p['firstName']} {p['lastName']}"


def get_participant_and_organizer_names(
    participants: List[Participant], creator_id: int | None
) -> Names:
    organizer_name = ""
    pnames: List[str] = []

    for p in participants:
        # check if participant was a guest / frictionless user
        pnames.append(user_or_guest_name(p))

        if creator_id is not None and p["id"] == creator_id:
            organizer_name = user_or_guest_name(p)

    return {
        "participant_names": ", ".join(pnames),
        "organizer_name": organizer_name,
    }


class Timestamps(TypedDict):
    started_at: int
    ended_at: int


def get_past_recurrence_timestamps(rec: SessionRecurrence) -> Timestamps:
    started_at = 0
    ended_at = 0
    for item in rec["stateUpdatedAt"]:
        if item["state"] == "active":
            started_at = item["updatedAt"]

        if item["state"] == "ended":
            ended_at = item["updatedAt"]
    return {"started_at": started_at, "ended_at": ended_at}


def get_past_recurrence_llm_context(
    rec: SessionRecurrence, tz: str, participants: List[Participant]
):
    recurrence_user_names = get_participant_and_organizer_names(
        participants, rec["creatorID"]
    )
    recurrence_timestamps = get_past_recurrence_timestamps(rec)

    # refactor this to a list of json objects
    context = f"""Meeting title: {rec["title"]}
    Meeting Session ID: {rec["sessionID"]}
    Meeting Recurrence ID : {rec["recurrenceID"]}
    Meeting started at: {relative_tz_dt_from_unix(recurrence_timestamps["started_at"], tz)}
    Meeting ended at: {relative_tz_dt_from_unix(recurrence_timestamps["ended_at"], tz)}
    Meeting duration: {duration_str_from_secs(recurrence_timestamps["ended_at"] - recurrence_timestamps["started_at"])}
    Meeting organizer/host: {recurrence_user_names["organizer_name"]}
    Meeting participants: {recurrence_user_names["participant_names"]}
    """
    return context


def prepare_docs_for_cohere_rerank(
    ts_tbatches_resp: Dict,
) -> List[RerankedTypesenseLiveTranscriptionBatch]:
    docs: List[RerankedTypesenseLiveTranscriptionBatch] = []
    for result in ts_tbatches_resp["results"]:
        for hit in result["hits"]:
            doc = hit["document"]
            doc["recurrenceID"] = str(doc["recurrenceID"])
            recurrence = doc.get(RECURRENCES_COLLECTION_NAME)  # joined collection
            if recurrence:
                doc["meetingTitle"] = recurrence["title"]
            doc["sessionID"] = str(doc["recurrenceID"])
            doc["createdAt"] = humanize.naturaltime(
                datetime.datetime.now().timestamp() - doc["createdAt"]
            )
            docs.append(doc)

    return docs


def message_to_mm_message(msg: Message) -> MeetingMemoryMessage:
    return MeetingMemoryMessage(
        id=msg.id,
        role=str_to_message_role(msg.role),
        items=json.loads(msg.items),
        feedback=msg.user_feedback,
        sources=json.loads(msg.sources) if msg.sources else None,
    )


def msg_content_from_msg(msg: Message) -> str:
    return json.loads(msg.items)[0].get("content")


class MetadataContent(TypedDict, total=False):
    topics: List[str]
    short_summary: str
    key_decisions: List[str]
    keywords: List[str]


# Map of recurrence_id to metadata
MetadataMap = Dict[int, MetadataContent]
