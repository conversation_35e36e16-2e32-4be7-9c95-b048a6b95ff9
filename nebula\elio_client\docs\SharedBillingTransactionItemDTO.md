# SharedBillingTransactionItemDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**price** | **object** |  | 

## Example

```python
from elio_client.models.shared_billing_transaction_item_dto import SharedBillingTransactionItemDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedBillingTransactionItemDTO from a JSON string
shared_billing_transaction_item_dto_instance = SharedBillingTransactionItemDTO.from_json(json)
# print the JSON string representation of the object
print(SharedBillingTransactionItemDTO.to_json())

# convert the object into a dict
shared_billing_transaction_item_dto_dict = shared_billing_transaction_item_dto_instance.to_dict()
# create an instance of SharedBillingTransactionItemDTO from a dict
shared_billing_transaction_item_dto_from_dict = SharedBillingTransactionItemDTO.from_dict(shared_billing_transaction_item_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


