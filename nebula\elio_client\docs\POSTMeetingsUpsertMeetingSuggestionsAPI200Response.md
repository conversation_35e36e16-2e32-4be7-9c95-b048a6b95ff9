# POSTMeetingsUpsertMeetingSuggestionsAPI200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**count** | **int** |  | 
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.post_meetings_upsert_meeting_suggestions_api200_response import POSTMeetingsUpsertMeetingSuggestionsAPI200Response

# TODO update the JSON string below
json = "{}"
# create an instance of POSTMeetingsUpsertMeetingSuggestionsAPI200Response from a JSON string
post_meetings_upsert_meeting_suggestions_api200_response_instance = POSTMeetingsUpsertMeetingSuggestionsAPI200Response.from_json(json)
# print the JSON string representation of the object
print(POSTMeetingsUpsertMeetingSuggestionsAPI200Response.to_json())

# convert the object into a dict
post_meetings_upsert_meeting_suggestions_api200_response_dict = post_meetings_upsert_meeting_suggestions_api200_response_instance.to_dict()
# create an instance of POSTMeetingsUpsertMeetingSuggestionsAPI200Response from a dict
post_meetings_upsert_meeting_suggestions_api200_response_from_dict = POSTMeetingsUpsertMeetingSuggestionsAPI200Response.from_dict(post_meetings_upsert_meeting_suggestions_api200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


