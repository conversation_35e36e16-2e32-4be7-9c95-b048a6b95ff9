FrontendURL: string | *""
RecallAiWebhookToken: string | *"0BtYHRfxjOKEYFnU8mnsctRlgxgI332h"
GoogleOAuth2ClientID: string | *"732264686334-lmj49g2t4okd8jcq5tia405blhcl7t6d.apps.googleusercontent.com"
RecallWebhookURL: string | *"https://api.staging.rumi.ai/preview.HandleRecallWebhook?token=mgyQy7bhglInaMA2TYXiLMtJ73eobgJh"
MicrosoftOAuth2ClientID: string | *"f6d4f12d-2fa2-460a-876d-fb28bcb8a00a"
MarsPostgresOverride: string | *""
WebsocketBaseURL: string | *#Meta.APIBaseURL
DeepgramModel: string | *"nova-3"
EndpointMS: string | *"5000"
Vocabulary: [ ...string ] | *[
	"romy:<PERSON>umi",
	"Romy:<PERSON><PERSON>",
	"roombi:<PERSON><PERSON>",
	"Roombi:<PERSON><PERSON>",
	"roomie:<PERSON><PERSON>",
	"Roomie:<PERSON><PERSON>",
	"roomie's:<PERSON><PERSON>'s",
	"Roomie's:<PERSON><PERSON>'s",
	"roomie.ai:<PERSON>umi.ai",
	"Roomie.ai:Rumi.ai",
	"roomy:Rumi",
	"Roomy:Rumi",
	"Roomi:Rumi",
	"roomi:Rumi",
	"trumy:Rumi",
	"Trumy:Rumi",
	"twitchroom:Waitroom",
	"Twitchroom:Waitroom",
	"waitrumb:Waitroom",
	"Waitrumb:Waitroom",
]

Kafka: {
	TLS: bool | *false
	SASL: bool | *false
	TopicLiveTranscripts: string | *""
}

AWSRegion: string | *"us-east-1"
AWSBucketName: string | *""

GoogleLoginGroupId: string | *"4c7187aa-23db-4c5f-9502-d4aaee626ef4"
