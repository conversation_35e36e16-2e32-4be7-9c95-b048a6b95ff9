# POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**recurrence_id** | **str** |  | 
**session_id** | **str** |  | 

## Example

```python
from elio_client.models.post_meetings_get_meeting_suggestions_by_session_api_request import POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest from a JSON string
post_meetings_get_meeting_suggestions_by_session_api_request_instance = POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest.from_json(json)
# print the JSON string representation of the object
print(POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest.to_json())

# convert the object into a dict
post_meetings_get_meeting_suggestions_by_session_api_request_dict = post_meetings_get_meeting_suggestions_by_session_api_request_instance.to_dict()
# create an instance of POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest from a dict
post_meetings_get_meeting_suggestions_by_session_api_request_from_dict = POSTMeetingsGetMeetingSuggestionsBySessionAPIRequest.from_dict(post_meetings_get_meeting_suggestions_by_session_api_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


