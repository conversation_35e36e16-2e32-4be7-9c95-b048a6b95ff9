package billing_test

import (
	"context"
	"encore.app/billing"
	"encore.app/billing/api"
	"encore.app/shared"
	"fmt"
	"github.com/samber/lo"
	"path"
	"testing"
)

const mocksPath = "./mocks"

// This test file does not run actual test, it only runs functions that fetch data from Paddle
// and write mocks to the mocks folder.
// The mocks are then used by the other tests.
// To run the tests
// 1. Comment out the t.Skip() lines
// 2. cd /billing
// 3. encore test paddle_test.go

func TestFetchAndWriteListProductsMocks(t *testing.T) {
	t.Run("Fetches and writes list-products-mock.json", func(t *testing.T) {
		t.Skip()

		err := fetchAndWriteListProductsMock(context.Background())
		if err != nil {
			t.Fatal(err)
		}
	})
}

func TestFetchAndWriteListTransactionsByCustomerIDMocks(t *testing.T) {
	t.Run("Fetches and writes list-transactions-mock.json", func(t *testing.T) {
		t.Skip()

		err := fetchAndWriteListTransactionsByCustomerIDMock(context.Background())
		if err != nil {
			t.Fatal(err)
		}
	})
}

func TestFetchAndWriteGetInvoiceByTransactionIDMocks(t *testing.T) {
	t.Run("Fetches and writes get-invoice-by-transaction-id-mock.json", func(t *testing.T) {
		t.Skip()

		err := fetchAndWriteGetInvoiceByTransactionIDMock(context.Background())
		if err != nil {
			t.Fatal(err)
		}
	})
}

func TestFetchAndWriteGetSubscriptionUpdatePaymentMethodTransactionMocks(t *testing.T) {
	t.Run("Fetches and writes get-subscription-update-payment-method-transaction-mock.json", func(t *testing.T) {
		t.Skip()

		err := fetchAndWriteGetSubscriptionUpdatePaymentMethodTransactionMock(context.Background())
		if err != nil {
			t.Fatal(err)
		}
	})
}

func fetchAndWriteListProductsMock(ctx context.Context) error {
	listProductsRes, err := billing.ListProducts(ctx, api.ListProductsRequest{
		IncludePrices: true,
	})
	if err != nil {
		return err
	}

	err = shared.MarshalAndSaveJSONToFile(path.Join(mocksPath, "list-products-mock.json"), listProductsRes)
	if err != nil {
		return err
	}

	fmt.Println("updated list-products-mock.json")

	return nil
}

func fetchAndWriteListTransactionsByCustomerIDMock(ctx context.Context) error {
	listTransactionsRes, err := billing.ListTransactionsByCustomerID(ctx, api.ListTransactionsByCustomerIDRequest{
		CustomerID: "ctm_01hq87v6jn14vn84yzkhma2yf1",
		Limit:      lo.ToPtr(1),
	})
	if err != nil {
		return err
	}

	// take just the one transaction that we want to mock
	transactions := listTransactionsRes.Transactions
	transactions = lo.Filter(transactions, func(t shared.BillingTransactionDTO, _ int) bool {
		return t.ID == "txn_01jvvtr22p66z9e2nfxrspfbp8"
	})

	err = shared.MarshalAndSaveJSONToFile(path.Join(mocksPath, "list-transactions-mock.json"), api.ListTransactionsByCustomerIDResponse{
		Transactions: transactions,
	})
	if err != nil {
		return err
	}

	fmt.Println("updated list-transactions-mock.json")

	return nil
}

func fetchAndWriteGetInvoiceByTransactionIDMock(ctx context.Context) error {
	getInvoiceByTransactionIDRes, err := billing.GetInvoiceByTransactionID(ctx, api.GetInvoiceByTransactionIDRequest{
		TransactionID: "txn_01jn1h1fn2hnt6bqgny6293mam",
	})
	if err != nil {
		return err
	}

	err = shared.MarshalAndSaveJSONToFile(path.Join(mocksPath, "get-invoice-by-transaction-id-mock.json"), getInvoiceByTransactionIDRes)
	if err != nil {
		return err
	}

	fmt.Println("updated get-invoice-by-transaction-id-mock.json")

	return nil
}

func fetchAndWriteGetSubscriptionUpdatePaymentMethodTransactionMock(ctx context.Context) error {
	getSubscriptionUpdatePaymentMethodTransactionRes, err := billing.GetSubscriptionUpdatePaymentMethodTransaction(ctx,
		api.GetSubscriptionUpdatePaymentMethodTransactionRequest{
			SubscriptionID: "sub_01hq87vxrvcsqg7gxc930ps4yt",
		})
	if err != nil {
		return err
	}

	err = shared.MarshalAndSaveJSONToFile(path.Join(mocksPath, "get-subscription-update-payment-method-transaction-mock.json"),
		getSubscriptionUpdatePaymentMethodTransactionRes)
	if err != nil {
		return err
	}

	fmt.Println("updated get-subscription-update-payment-method-transaction-mock.json")

	return nil
}
