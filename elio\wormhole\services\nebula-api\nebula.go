// Package nebula_api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package nebula_api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/oapi-codegen/runtime"
)

const (
	HTTPBasicScopes = "HTTPBasic.Scopes"
)

// Defines values for MessageDTOFeedback.
const (
	N0 MessageDTOFeedback = 0
	N1 MessageDTOFeedback = 1
	N2 MessageDTOFeedback = 2
)

// Defines values for MessageDTORole.
const (
	Ai   MessageDTORole = "ai"
	Tool MessageDTORole = "tool"
	User MessageDTORole = "user"
)

// Defines values for MessageItemSourceDTOType.
const (
	Session MessageItemSourceDTOType = "session"
)

// Defines values for XRaySortBy.
const (
	Alphabetical XRaySortBy = "alphabetical"
	LastUpdated  XRaySortBy = "last_updated"
)

// Defines values for XRayTypeFilter.
const (
	Build   XRayTypeFilter = "build"
	Digest  XRayTypeFilter = "digest"
	Monitor XRayTypeFilter = "monitor"
)

// AskAIRequestBodyDTO defines model for AskAIRequestBodyDTO.
type AskAIRequestBodyDTO struct {
	MessageId     *string   `json:"messageId"`
	Query         *string   `json:"query,omitempty"`
	RecurrenceIds *[]string `json:"recurrenceIds"`
	Refresh       *bool     `json:"refresh"`
	RequestId     string    `json:"requestId"`
	Retry         *bool     `json:"retry"`
	ThreadId      *string   `json:"threadId"`

	// Timeout Timeout in seconds for the sync request
	Timeout *float32 `json:"timeout"`
	Tz      *string  `json:"tz"`
}

// AskAISyncResponseDTO defines model for AskAISyncResponseDTO.
type AskAISyncResponseDTO struct {
	Data *struct {
		Content  *string                 `json:"content,omitempty"`
		Sources  *map[string]interface{} `json:"sources,omitempty"`
		ThreadId *string                 `json:"threadId"`
	} `json:"data,omitempty"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// DeleteThreadResponse defines model for DeleteThreadResponse.
type DeleteThreadResponse struct {
	Data struct {
		ThreadId *string `json:"threadId,omitempty"`
	} `json:"data"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// DummyResponse defines model for DummyResponse.
type DummyResponse = map[string]interface{}

// ErrorResponseDTO defines model for ErrorResponseDTO.
type ErrorResponseDTO struct {
	Data     *map[string]interface{} `json:"data"`
	Message  string                  `json:"message"`
	Sources  *map[string]interface{} `json:"sources"`
	Success  bool                    `json:"success"`
	ThreadId *string                 `json:"threadId"`
}

// GetMeetingSuggestionsResponse Response for getting meeting suggestions.
type GetMeetingSuggestionsResponse struct {
	// Data Complete meeting suggestions DTO.
	Data    *MeetingSuggestionsDTO `json:"data,omitempty"`
	Message string                 `json:"message"`
}

// GetSuggestionsResponse defines model for GetSuggestionsResponse.
type GetSuggestionsResponse struct {
	Data *struct {
		Suggestions []struct {
			CreatedAt   *int                    `json:"createdAt,omitempty"`
			Explanation *string                 `json:"explanation,omitempty"`
			Id          *int                    `json:"id,omitempty"`
			Short       *string                 `json:"short,omitempty"`
			Sources     *map[string]interface{} `json:"sources,omitempty"`
			Text        *string                 `json:"text,omitempty"`
			UpdatedAt   *int                    `json:"updatedAt,omitempty"`
		} `json:"suggestions"`
		Total int `json:"total"`
	} `json:"data,omitempty"`
	Message string `json:"message"`
}

// GetThreadByIdResponse defines model for GetThreadByIdResponse.
type GetThreadByIdResponse struct {
	Data struct {
		Thread ThreadDTO `json:"thread"`
	} `json:"data"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// GetThreadMessagesResponse defines model for GetThreadMessagesResponse.
type GetThreadMessagesResponse struct {
	Data struct {
		HasMore  bool         `json:"hasMore"`
		Messages []MessageDTO `json:"messages"`
		Total    int          `json:"total"`
	} `json:"data"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// GetThreadsResponse defines model for GetThreadsResponse.
type GetThreadsResponse struct {
	Data struct {
		HasMore bool        `json:"hasMore"`
		Threads []ThreadDTO `json:"threads"`
		Total   int         `json:"total"`
	} `json:"data"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// HTTPValidationError defines model for HTTPValidationError.
type HTTPValidationError struct {
	Detail *[]ValidationError `json:"detail,omitempty"`
}

// MarkNotificationsSeenResponse Response model for marking notifications as seen
type MarkNotificationsSeenResponse struct {
	Data    *MarkNotificationsSeenResponseData `json:"data,omitempty"`
	Message string                             `json:"message"`
	Success bool                               `json:"success"`
}

// MarkNotificationsSeenResponseData defines model for MarkNotificationsSeenResponseData.
type MarkNotificationsSeenResponseData struct {
	MarkedCount int `json:"markedCount"`
}

// MeetingMetadataResponse defines model for MeetingMetadataResponse.
type MeetingMetadataResponse struct {
	Data struct {
		Metadata *struct {
			Description  *string   `json:"description,omitempty"`
			EndTime      *string   `json:"end_time,omitempty"`
			Location     *string   `json:"location,omitempty"`
			Participants *[]string `json:"participants,omitempty"`
			StartTime    *string   `json:"start_time,omitempty"`
			Title        *string   `json:"title,omitempty"`
		} `json:"metadata,omitempty"`
	} `json:"data"`
	Message string `json:"message"`
}

// MeetingSuggestionsDTO Complete meeting suggestions DTO.
type MeetingSuggestionsDTO struct {
	CreatedAt           string   `json:"created_at"`
	Id                  int      `json:"id"`
	SessionId           string   `json:"session_id"`
	SessionRecurrenceId string   `json:"session_recurrence_id"`
	Suggestions         []string `json:"suggestions"`
	UpdatedAt           string   `json:"updated_at"`
	UserId              string   `json:"user_id"`
}

// MessageDTO defines model for MessageDTO.
type MessageDTO struct {
	Content   string                             `json:"content"`
	CreatedAt int                                `json:"createdAt"`
	Feedback  MessageDTOFeedback                 `json:"feedback"`
	Id        string                             `json:"id"`
	Role      MessageDTORole                     `json:"role"`
	Sources   *map[string][]MessageItemSourceDTO `json:"sources"`
	ThreadId  string                             `json:"threadId"`
	UpdatedAt int                                `json:"updatedAt"`
}

// MessageDTOFeedback defines model for MessageDTO.Feedback.
type MessageDTOFeedback int

// MessageDTORole defines model for MessageDTO.Role.
type MessageDTORole string

// MessageItemSourceDTO defines model for MessageItemSourceDTO.
type MessageItemSourceDTO struct {
	Data MessageItemSourceDataDTO `json:"data"`
	Type MessageItemSourceDTOType `json:"type"`
}

// MessageItemSourceDTOType defines model for MessageItemSourceDTO.Type.
type MessageItemSourceDTOType string

// MessageItemSourceDataDTO defines model for MessageItemSourceDataDTO.
type MessageItemSourceDataDTO struct {
	About        *string  `json:"about"`
	EndedAt      int      `json:"endedAt"`
	Organizer    *string  `json:"organizer"`
	Participants []string `json:"participants"`
	RecurrenceId string   `json:"recurrenceId"`
	SessionId    string   `json:"sessionId"`
	StartedAt    int      `json:"startedAt"`
	Title        string   `json:"title"`
}

// PatchRecurrenceTeamAccessBody defines model for PatchRecurrenceTeamAccessBody.
type PatchRecurrenceTeamAccessBody struct {
	TeamId string `json:"teamId"`
	Value  bool   `json:"value"`
}

// PatchRecurrenceTeamAccessResponse defines model for PatchRecurrenceTeamAccessResponse.
type PatchRecurrenceTeamAccessResponse struct {
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// PostSessionSummaryDTO Response model for post session summary.
type PostSessionSummaryDTO struct {
	Data    PostSessionSummaryData `json:"data"`
	Message string                 `json:"message"`
}

// PostSessionSummaryData defines model for PostSessionSummaryData.
type PostSessionSummaryData struct {
	Content             *map[string]interface{} `json:"content"`
	CreatedAt           string                  `json:"created_at"`
	Html                *string                 `json:"html"`
	Id                  int                     `json:"id"`
	Md                  *string                 `json:"md"`
	SessionId           string                  `json:"session_id"`
	SessionRecurrenceId string                  `json:"session_recurrence_id"`
	Tldr                *string                 `json:"tldr"`
	UpdatedAt           string                  `json:"updated_at"`
}

// PostTeamMemberRequestBody defines model for PostTeamMemberRequestBody.
type PostTeamMemberRequestBody struct {
	UserId string `json:"userId"`
}

// PostTeamRequestBody defines model for PostTeamRequestBody.
type PostTeamRequestBody struct {
	Id        string    `json:"id"`
	MemberIds *[]string `json:"memberIds,omitempty"`
	Name      string    `json:"name"`
}

// StopRunBodyDTO defines model for StopRunBodyDTO.
type StopRunBodyDTO struct {
	ThreadId *string `json:"threadId,omitempty"`
}

// StopRunResponse defines model for StopRunResponse.
type StopRunResponse struct {
	Data *struct {
		ProviderThreadId *string `json:"providerThreadId,omitempty"`
		RunId            *string `json:"runId,omitempty"`
		ThreadId         *string `json:"threadId,omitempty"`
	} `json:"data,omitempty"`
	Message string `json:"message"`
	Success bool   `json:"success"`
}

// ThreadDTO defines model for ThreadDTO.
type ThreadDTO struct {
	CreatedAt     int    `json:"createdAt"`
	Id            string `json:"id"`
	LastMessageAt int    `json:"lastMessageAt"`
	Title         string `json:"title"`
	UpdatedAt     int    `json:"updatedAt"`
}

// UserFeedbackRequestDTO defines model for UserFeedbackRequestDTO.
type UserFeedbackRequestDTO struct {
	Feedback  int    `json:"feedback"`
	MessageId string `json:"messageId"`
}

// ValidationError defines model for ValidationError.
type ValidationError struct {
	Loc  []ValidationError_Loc_Item `json:"loc"`
	Msg  string                     `json:"msg"`
	Type string                     `json:"type"`
}

// ValidationErrorLoc0 defines model for .
type ValidationErrorLoc0 = string

// ValidationErrorLoc1 defines model for .
type ValidationErrorLoc1 = int

// ValidationError_Loc_Item defines model for ValidationError.loc.Item.
type ValidationError_Loc_Item struct {
	union json.RawMessage
}

// XRayCreateStep1Body Request model for X-Ray description generation
type XRayCreateStep1Body struct {
	Description string `json:"description"`
}

// XRayCreateStep1Response Response model for X-Ray xrayType and prompt generation
type XRayCreateStep1Response struct {
	Data    *XRayCreateStep1ResponseData `json:"data,omitempty"`
	Message string                       `json:"message"`
	Success bool                         `json:"success"`
}

// XRayCreateStep1ResponseData defines model for XRayCreateStep1ResponseData.
type XRayCreateStep1ResponseData struct {
	Prompt   string `json:"prompt"`
	XrayType string `json:"xrayType"`
}

// XRayCreateStep2Body Request model for X-Ray title, emoji, and short summary generation
type XRayCreateStep2Body struct {
	Prompt   string `json:"prompt"`
	XrayType string `json:"xrayType"`
}

// XRayCreateStep2Response Response model for X-Ray title, emoji, and short summary generation
type XRayCreateStep2Response struct {
	Data    *XRayCreateStep2ResponseData `json:"data,omitempty"`
	Message string                       `json:"message"`
	Success bool                         `json:"success"`
}

// XRayCreateStep2ResponseData defines model for XRayCreateStep2ResponseData.
type XRayCreateStep2ResponseData struct {
	Emoji        string `json:"emoji"`
	ShortSummary string `json:"shortSummary"`
	Title        string `json:"title"`
}

// XRayCreateStep3Body Request model for creating an X-Ray with all generated data
type XRayCreateStep3Body struct {
	AlertChannels *map[string]bool `json:"alertChannels"`
	Description   string           `json:"description"`
	Emoji         string           `json:"emoji"`

	// Frequency Cron expression for digest scheduling (required for digest type X-Rays)
	Frequency    *string `json:"frequency"`
	Prompt       string  `json:"prompt"`
	ShortSummary string  `json:"shortSummary"`

	// Timezone Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC')
	Timezone *string `json:"timezone"`
	Title    string  `json:"title"`
	XrayType string  `json:"xrayType"`
}

// XRayDTO X-Ray data transfer object
type XRayDTO struct {
	AlertChannels map[string]bool `json:"alertChannels"`
	CreatedAt     int             `json:"createdAt"`

	// CurrentCommit X-Ray document commit
	CurrentCommit   *XRayDocCommit `json:"currentCommit,omitempty"`
	CurrentCommitId *int           `json:"currentCommitId"`
	Description     string         `json:"description"`

	// Frequency Cron expression for digest scheduling (only present for digest type X-Rays)
	Frequency *string `json:"frequency"`
	Icon      string  `json:"icon"`
	Id        int     `json:"id"`
	IsActive  bool    `json:"isActive"`

	// LastDigestAt Timestamp when the digest was last generated (only present for digest type X-Rays)
	LastDigestAt *int   `json:"lastDigestAt"`
	OwnerId      int    `json:"ownerId"`
	Prompt       string `json:"prompt"`
	Scope        string `json:"scope"`
	ShortSummary string `json:"shortSummary"`

	// Timezone Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC')
	Timezone                 *string `json:"timezone"`
	Title                    string  `json:"title"`
	UnreadNotificationsCount *int    `json:"unreadNotificationsCount"`
	UpdatedAt                int     `json:"updatedAt"`
	Visibility               string  `json:"visibility"`
	XrayType                 string  `json:"xrayType"`
}

// XRayDocCommit X-Ray document commit
type XRayDocCommit struct {
	AuthorId  int    `json:"authorId"`
	Content   string `json:"content"`
	CreatedAt int    `json:"createdAt"`
	Id        int    `json:"id"`
	UpdatedAt int    `json:"updatedAt"`
	XrayId    int    `json:"xrayId"`
}

// XRayListResponse Response model for X-Ray list operations
type XRayListResponse struct {
	Data    *XRayListResponseData `json:"data,omitempty"`
	Message string                `json:"message"`
	Success bool                  `json:"success"`
}

// XRayListResponseData defines model for XRayListResponseData.
type XRayListResponseData struct {
	HasMore bool      `json:"hasMore"`
	Total   int       `json:"total"`
	Xrays   []XRayDTO `json:"xrays"`
}

// XRayNotificationDTO X-Ray notification data transfer object
type XRayNotificationDTO struct {
	Content         string                 `json:"content"`
	CreatedAt       int                    `json:"createdAt"`
	Id              int                    `json:"id"`
	Seen            bool                   `json:"seen"`
	Source          map[string]interface{} `json:"source"`
	Title           string                 `json:"title"`
	UpdatedAt       int                    `json:"updatedAt"`
	UserId          int                    `json:"userId"`
	XrayDocCommitId int                    `json:"xrayDocCommitId"`
}

// XRayNotificationResponse Response model for X-Ray notifications
type XRayNotificationResponse struct {
	Data    *XRayNotificationResponseData `json:"data,omitempty"`
	Message string                        `json:"message"`
	Success bool                          `json:"success"`
}

// XRayNotificationResponseData defines model for XRayNotificationResponseData.
type XRayNotificationResponseData struct {
	HasMore       bool                  `json:"hasMore"`
	Notifications []XRayNotificationDTO `json:"notifications"`
	Total         int                   `json:"total"`
}

// XRayResponse Response model for X-Ray operations
type XRayResponse struct {
	Data    *XRayResponseData `json:"data,omitempty"`
	Message string            `json:"message"`
	Success bool              `json:"success"`
}

// XRayResponseData defines model for XRayResponseData.
type XRayResponseData struct {
	// Xray X-Ray data transfer object
	Xray XRayDTO `json:"xray"`
}

// XRaySortBy Sort options for X-Rays
type XRaySortBy string

// XRayTemplateDTO X-Ray template data transfer object
type XRayTemplateDTO struct {
	CreatedAt    int    `json:"createdAt"`
	Description  string `json:"description"`
	Icon         string `json:"icon"`
	Id           int    `json:"id"`
	OwnerId      int    `json:"ownerId"`
	Prompt       string `json:"prompt"`
	ShortSummary string `json:"shortSummary"`
	Title        string `json:"title"`
	UpdatedAt    int    `json:"updatedAt"`
	XrayType     string `json:"xrayType"`
}

// XRayTemplateListResponse Response model for X-Ray template list operations
type XRayTemplateListResponse struct {
	Data    *XRayTemplateListResponseData `json:"data,omitempty"`
	Message string                        `json:"message"`
	Success bool                          `json:"success"`
}

// XRayTemplateListResponseData defines model for XRayTemplateListResponseData.
type XRayTemplateListResponseData struct {
	HasMore   bool              `json:"hasMore"`
	Templates []XRayTemplateDTO `json:"templates"`
	Total     int               `json:"total"`
}

// XRayTemplateResponse Response model for X-Ray template operations
type XRayTemplateResponse struct {
	Data    *XRayTemplateResponseData `json:"data,omitempty"`
	Message string                    `json:"message"`
	Success bool                      `json:"success"`
}

// XRayTemplateResponseData defines model for XRayTemplateResponseData.
type XRayTemplateResponseData struct {
	// Template X-Ray template data transfer object
	Template XRayTemplateDTO `json:"template"`
}

// XRayTypeFilter Filter X-Rays by type
type XRayTypeFilter string

// XRayUpdateRequestBody Request model for updating an X-Ray
type XRayUpdateRequestBody struct {
	AlertChannels *map[string]bool `json:"alertChannels"`

	// Frequency Cron expression for digest scheduling (only applicable for digest type X-Rays)
	Frequency *string `json:"frequency"`
	IsActive  *bool   `json:"isActive"`

	// Timezone Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC')
	Timezone *string `json:"timezone"`
	Title    *string `json:"title"`
}

// MemoryControllerAskAISyncParams defines parameters for MemoryControllerAskAISync.
type MemoryControllerAskAISyncParams struct {
	XUserId string `json:"X-User-Id"`
}

// MemoryControllerGetSuggestionsParams defines parameters for MemoryControllerGetSuggestions.
type MemoryControllerGetSuggestionsParams struct {
	Limit   *string `form:"limit,omitempty" json:"limit,omitempty"`
	Skip    *string `form:"skip,omitempty" json:"skip,omitempty"`
	XUserId int     `json:"X-User-Id"`
}

// MemoryControllerGetUserThreadsParams defines parameters for MemoryControllerGetUserThreads.
type MemoryControllerGetUserThreadsParams struct {
	Limit   *string `form:"limit,omitempty" json:"limit,omitempty"`
	Skip    *string `form:"skip,omitempty" json:"skip,omitempty"`
	XUserId int     `json:"X-User-Id"`
}

// MemoryControllerDeleteThreadByIDParams defines parameters for MemoryControllerDeleteThreadByID.
type MemoryControllerDeleteThreadByIDParams struct {
	XUserId string `json:"X-User-Id"`
}

// MemoryControllerGetThreadByIDParams defines parameters for MemoryControllerGetThreadByID.
type MemoryControllerGetThreadByIDParams struct {
	XUserId string `json:"X-User-Id"`
}

// MemoryControllerGetThreadMessagesParams defines parameters for MemoryControllerGetThreadMessages.
type MemoryControllerGetThreadMessagesParams struct {
	Limit   *string `form:"limit,omitempty" json:"limit,omitempty"`
	Skip    *string `form:"skip,omitempty" json:"skip,omitempty"`
	XUserId string  `json:"X-User-Id"`
}

// GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams defines parameters for GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet.
type GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams struct {
	SessionRecurrenceID string `form:"sessionRecurrenceID" json:"sessionRecurrenceID"`
}

// GetByIdV10PostSessionSummariesSessionIDGetParams defines parameters for GetByIdV10PostSessionSummariesSessionIDGet.
type GetByIdV10PostSessionSummariesSessionIDGetParams struct {
	Formats *string `form:"formats,omitempty" json:"formats,omitempty"`
}

// GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams defines parameters for GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGet.
type GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams struct {
	XUserId string `json:"x-user-id"`
}

// ListXraysV10XRayGetParams defines parameters for ListXraysV10XRayGet.
type ListXraysV10XRayGetParams struct {
	Limit      *int            `form:"limit,omitempty" json:"limit,omitempty"`
	Offset     *int            `form:"offset,omitempty" json:"offset,omitempty"`
	TypeFilter *XRayTypeFilter `form:"type_filter,omitempty" json:"type_filter,omitempty"`
	SortBy     *XRaySortBy     `form:"sort_by,omitempty" json:"sort_by,omitempty"`
	XUserId    string          `json:"x-user-id"`
}

// CreateXrayStep1V10XRayCreateStep1PostParams defines parameters for CreateXrayStep1V10XRayCreateStep1Post.
type CreateXrayStep1V10XRayCreateStep1PostParams struct {
	XUserId string `json:"x-user-id"`
}

// CreateXrayStep2V10XRayCreateStep2PostParams defines parameters for CreateXrayStep2V10XRayCreateStep2Post.
type CreateXrayStep2V10XRayCreateStep2PostParams struct {
	XUserId string `json:"x-user-id"`
}

// CreateXrayStep3V10XRayCreateStep3PostParams defines parameters for CreateXrayStep3V10XRayCreateStep3Post.
type CreateXrayStep3V10XRayCreateStep3PostParams struct {
	XUserId string `json:"x-user-id"`
}

// ListXrayTemplatesV10XRayTemplatesGetParams defines parameters for ListXrayTemplatesV10XRayTemplatesGet.
type ListXrayTemplatesV10XRayTemplatesGetParams struct {
	Limit   *int   `form:"limit,omitempty" json:"limit,omitempty"`
	Offset  *int   `form:"offset,omitempty" json:"offset,omitempty"`
	XUserId string `json:"x-user-id"`
}

// GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams defines parameters for GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet.
type GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams struct {
	XUserId string `json:"x-user-id"`
}

// DeleteXrayV10XRayXrayIdDeleteParams defines parameters for DeleteXrayV10XRayXrayIdDelete.
type DeleteXrayV10XRayXrayIdDeleteParams struct {
	XUserId string `json:"x-user-id"`
}

// GetXrayV10XRayXrayIdGetParams defines parameters for GetXrayV10XRayXrayIdGet.
type GetXrayV10XRayXrayIdGetParams struct {
	XUserId string `json:"x-user-id"`
}

// UpdateXrayV10XRayXrayIdPatchParams defines parameters for UpdateXrayV10XRayXrayIdPatch.
type UpdateXrayV10XRayXrayIdPatchParams struct {
	XUserId string `json:"x-user-id"`
}

// GetXrayNotificationsV10XRayXrayIdNotificationsGetParams defines parameters for GetXrayNotificationsV10XRayXrayIdNotificationsGet.
type GetXrayNotificationsV10XRayXrayIdNotificationsGetParams struct {
	Limit   *int   `form:"limit,omitempty" json:"limit,omitempty"`
	Offset  *int   `form:"offset,omitempty" json:"offset,omitempty"`
	XUserId string `json:"x-user-id"`
}

// MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams defines parameters for MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch.
type MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams struct {
	XUserId string `json:"x-user-id"`
}

// ShareXrayAsTemplateV10XRayXrayIdSharePostParams defines parameters for ShareXrayAsTemplateV10XRayXrayIdSharePost.
type ShareXrayAsTemplateV10XRayXrayIdSharePostParams struct {
	XUserId string `json:"x-user-id"`
}

// MemoryControllerAskAISyncJSONRequestBody defines body for MemoryControllerAskAISync for application/json ContentType.
type MemoryControllerAskAISyncJSONRequestBody = AskAIRequestBodyDTO

// MemoryControllerAskV2JSONRequestBody defines body for MemoryControllerAskV2 for application/json ContentType.
type MemoryControllerAskV2JSONRequestBody = AskAIRequestBodyDTO

// MemoryControllerFeedbackJSONRequestBody defines body for MemoryControllerFeedback for application/json ContentType.
type MemoryControllerFeedbackJSONRequestBody = UserFeedbackRequestDTO

// MemoryControllerStopRunJSONRequestBody defines body for MemoryControllerStopRun for application/json ContentType.
type MemoryControllerStopRunJSONRequestBody = StopRunBodyDTO

// MemoryControllerPostTeamJSONRequestBody defines body for MemoryControllerPostTeam for application/json ContentType.
type MemoryControllerPostTeamJSONRequestBody = PostTeamRequestBody

// MemoryControllerPostTeamMemberJSONRequestBody defines body for MemoryControllerPostTeamMember for application/json ContentType.
type MemoryControllerPostTeamMemberJSONRequestBody = PostTeamMemberRequestBody

// MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody defines body for MemoryControllerPatchRecurrenceTeamAccess for application/json ContentType.
type MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody = PatchRecurrenceTeamAccessBody

// CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody defines body for CreateXrayStep1V10XRayCreateStep1Post for application/json ContentType.
type CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody = XRayCreateStep1Body

// CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody defines body for CreateXrayStep2V10XRayCreateStep2Post for application/json ContentType.
type CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody = XRayCreateStep2Body

// CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody defines body for CreateXrayStep3V10XRayCreateStep3Post for application/json ContentType.
type CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody = XRayCreateStep3Body

// UpdateXrayV10XRayXrayIdPatchJSONRequestBody defines body for UpdateXrayV10XRayXrayIdPatch for application/json ContentType.
type UpdateXrayV10XRayXrayIdPatchJSONRequestBody = XRayUpdateRequestBody

// AsValidationErrorLoc0 returns the union data inside the ValidationError_Loc_Item as a ValidationErrorLoc0
func (t ValidationError_Loc_Item) AsValidationErrorLoc0() (ValidationErrorLoc0, error) {
	var body ValidationErrorLoc0
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromValidationErrorLoc0 overwrites any union data inside the ValidationError_Loc_Item as the provided ValidationErrorLoc0
func (t *ValidationError_Loc_Item) FromValidationErrorLoc0(v ValidationErrorLoc0) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeValidationErrorLoc0 performs a merge with any union data inside the ValidationError_Loc_Item, using the provided ValidationErrorLoc0
func (t *ValidationError_Loc_Item) MergeValidationErrorLoc0(v ValidationErrorLoc0) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

// AsValidationErrorLoc1 returns the union data inside the ValidationError_Loc_Item as a ValidationErrorLoc1
func (t ValidationError_Loc_Item) AsValidationErrorLoc1() (ValidationErrorLoc1, error) {
	var body ValidationErrorLoc1
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromValidationErrorLoc1 overwrites any union data inside the ValidationError_Loc_Item as the provided ValidationErrorLoc1
func (t *ValidationError_Loc_Item) FromValidationErrorLoc1(v ValidationErrorLoc1) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeValidationErrorLoc1 performs a merge with any union data inside the ValidationError_Loc_Item, using the provided ValidationErrorLoc1
func (t *ValidationError_Loc_Item) MergeValidationErrorLoc1(v ValidationErrorLoc1) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ValidationError_Loc_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ValidationError_Loc_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// RequestEditorFn  is the function signature for the RequestEditor callback function
type RequestEditorFn func(ctx context.Context, req *http.Request) error

// Doer performs HTTP requests.
//
// The standard http.Client implements this interface.
type HttpRequestDoer interface {
	Do(req *http.Request) (*http.Response, error)
}

// Client which conforms to the OpenAPI3 specification for this service.
type Client struct {
	// The endpoint of the server conforming to this interface, with scheme,
	// https://api.deepmap.com for example. This can contain a path relative
	// to the server, such as https://api.deepmap.com/dev-test, and all the
	// paths in the swagger spec will be appended to the server.
	Server string

	// Doer for performing requests, typically a *http.Client with any
	// customized settings, such as certificate chains.
	Client HttpRequestDoer

	// A list of callbacks for modifying requests which are generated before sending over
	// the network.
	RequestEditors []RequestEditorFn
}

// ClientOption allows setting custom parameters during construction
type ClientOption func(*Client) error

// Creates a new Client, with reasonable defaults
func NewClient(server string, opts ...ClientOption) (*Client, error) {
	// create a client with sane default values
	client := Client{
		Server: server,
	}
	// mutate client and add all optional params
	for _, o := range opts {
		if err := o(&client); err != nil {
			return nil, err
		}
	}
	// ensure the server URL always has a trailing slash
	if !strings.HasSuffix(client.Server, "/") {
		client.Server += "/"
	}
	// create httpClient, if not already present
	if client.Client == nil {
		client.Client = &http.Client{}
	}
	return &client, nil
}

// WithHTTPClient allows overriding the default Doer, which is
// automatically created using http.Client. This is useful for tests.
func WithHTTPClient(doer HttpRequestDoer) ClientOption {
	return func(c *Client) error {
		c.Client = doer
		return nil
	}
}

// WithRequestEditorFn allows setting up a callback function, which will be
// called right before sending the request. This can be used to mutate the request.
func WithRequestEditorFn(fn RequestEditorFn) ClientOption {
	return func(c *Client) error {
		c.RequestEditors = append(c.RequestEditors, fn)
		return nil
	}
}

// The interface specification for the client above.
type ClientInterface interface {
	// GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGet request
	GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGet(ctx context.Context, sessionId string, recurrenceId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetMetadataByIdV10MeetingMetadataMetadataIdGet request
	GetMetadataByIdV10MeetingMetadataMetadataIdGet(ctx context.Context, metadataId int, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerAskAISyncWithBody request with any body
	MemoryControllerAskAISyncWithBody(ctx context.Context, params *MemoryControllerAskAISyncParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerAskAISync(ctx context.Context, params *MemoryControllerAskAISyncParams, body MemoryControllerAskAISyncJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerAskV2WithBody request with any body
	MemoryControllerAskV2WithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerAskV2(ctx context.Context, body MemoryControllerAskV2JSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerFeedbackWithBody request with any body
	MemoryControllerFeedbackWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerFeedback(ctx context.Context, body MemoryControllerFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerStopRunWithBody request with any body
	MemoryControllerStopRunWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerStopRun(ctx context.Context, body MemoryControllerStopRunJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerGetSuggestions request
	MemoryControllerGetSuggestions(ctx context.Context, params *MemoryControllerGetSuggestionsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerPostTeamWithBody request with any body
	MemoryControllerPostTeamWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerPostTeam(ctx context.Context, body MemoryControllerPostTeamJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerPostTeamMemberWithBody request with any body
	MemoryControllerPostTeamMemberWithBody(ctx context.Context, teamId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerPostTeamMember(ctx context.Context, teamId string, body MemoryControllerPostTeamMemberJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerDeleteTeamMember request
	MemoryControllerDeleteTeamMember(ctx context.Context, teamId string, teamMemberId string, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerGetUserThreads request
	MemoryControllerGetUserThreads(ctx context.Context, params *MemoryControllerGetUserThreadsParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerDeleteThreadByID request
	MemoryControllerDeleteThreadByID(ctx context.Context, threadID string, params *MemoryControllerDeleteThreadByIDParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerGetThreadByID request
	MemoryControllerGetThreadByID(ctx context.Context, threadID string, params *MemoryControllerGetThreadByIDParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerGetThreadMessages request
	MemoryControllerGetThreadMessages(ctx context.Context, threadID string, params *MemoryControllerGetThreadMessagesParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet request
	GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet(ctx context.Context, sessionID string, params *GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetByIdV10PostSessionSummariesSessionIDGet request
	GetByIdV10PostSessionSummariesSessionIDGet(ctx context.Context, sessionID int, params *GetByIdV10PostSessionSummariesSessionIDGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MemoryControllerPatchRecurrenceTeamAccessWithBody request with any body
	MemoryControllerPatchRecurrenceTeamAccessWithBody(ctx context.Context, recurrenceId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	MemoryControllerPatchRecurrenceTeamAccess(ctx context.Context, recurrenceId string, body MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGet request
	GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGet(ctx context.Context, sessionId string, recurrenceId string, params *GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListXraysV10XRayGet request
	ListXraysV10XRayGet(ctx context.Context, params *ListXraysV10XRayGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateXrayStep1V10XRayCreateStep1PostWithBody request with any body
	CreateXrayStep1V10XRayCreateStep1PostWithBody(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateXrayStep1V10XRayCreateStep1Post(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, body CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateXrayStep2V10XRayCreateStep2PostWithBody request with any body
	CreateXrayStep2V10XRayCreateStep2PostWithBody(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateXrayStep2V10XRayCreateStep2Post(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, body CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// CreateXrayStep3V10XRayCreateStep3PostWithBody request with any body
	CreateXrayStep3V10XRayCreateStep3PostWithBody(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	CreateXrayStep3V10XRayCreateStep3Post(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, body CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ListXrayTemplatesV10XRayTemplatesGet request
	ListXrayTemplatesV10XRayTemplatesGet(ctx context.Context, params *ListXrayTemplatesV10XRayTemplatesGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet request
	GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet(ctx context.Context, templateId int, params *GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// DeleteXrayV10XRayXrayIdDelete request
	DeleteXrayV10XRayXrayIdDelete(ctx context.Context, xrayId int, params *DeleteXrayV10XRayXrayIdDeleteParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetXrayV10XRayXrayIdGet request
	GetXrayV10XRayXrayIdGet(ctx context.Context, xrayId int, params *GetXrayV10XRayXrayIdGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// UpdateXrayV10XRayXrayIdPatchWithBody request with any body
	UpdateXrayV10XRayXrayIdPatchWithBody(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error)

	UpdateXrayV10XRayXrayIdPatch(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, body UpdateXrayV10XRayXrayIdPatchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error)

	// GetXrayNotificationsV10XRayXrayIdNotificationsGet request
	GetXrayNotificationsV10XRayXrayIdNotificationsGet(ctx context.Context, xrayId int, params *GetXrayNotificationsV10XRayXrayIdNotificationsGetParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch request
	MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch(ctx context.Context, xrayId int, params *MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams, reqEditors ...RequestEditorFn) (*http.Response, error)

	// ShareXrayAsTemplateV10XRayXrayIdSharePost request
	ShareXrayAsTemplateV10XRayXrayIdSharePost(ctx context.Context, xrayId int, params *ShareXrayAsTemplateV10XRayXrayIdSharePostParams, reqEditors ...RequestEditorFn) (*http.Response, error)
}

func (c *Client) GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGet(ctx context.Context, sessionId string, recurrenceId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetRequest(c.Server, sessionId, recurrenceId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetMetadataByIdV10MeetingMetadataMetadataIdGet(ctx context.Context, metadataId int, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetMetadataByIdV10MeetingMetadataMetadataIdGetRequest(c.Server, metadataId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerAskAISyncWithBody(ctx context.Context, params *MemoryControllerAskAISyncParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerAskAISyncRequestWithBody(c.Server, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerAskAISync(ctx context.Context, params *MemoryControllerAskAISyncParams, body MemoryControllerAskAISyncJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerAskAISyncRequest(c.Server, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerAskV2WithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerAskV2RequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerAskV2(ctx context.Context, body MemoryControllerAskV2JSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerAskV2Request(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerFeedbackWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerFeedbackRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerFeedback(ctx context.Context, body MemoryControllerFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerFeedbackRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerStopRunWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerStopRunRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerStopRun(ctx context.Context, body MemoryControllerStopRunJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerStopRunRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerGetSuggestions(ctx context.Context, params *MemoryControllerGetSuggestionsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerGetSuggestionsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerPostTeamWithBody(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerPostTeamRequestWithBody(c.Server, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerPostTeam(ctx context.Context, body MemoryControllerPostTeamJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerPostTeamRequest(c.Server, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerPostTeamMemberWithBody(ctx context.Context, teamId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerPostTeamMemberRequestWithBody(c.Server, teamId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerPostTeamMember(ctx context.Context, teamId string, body MemoryControllerPostTeamMemberJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerPostTeamMemberRequest(c.Server, teamId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerDeleteTeamMember(ctx context.Context, teamId string, teamMemberId string, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerDeleteTeamMemberRequest(c.Server, teamId, teamMemberId)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerGetUserThreads(ctx context.Context, params *MemoryControllerGetUserThreadsParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerGetUserThreadsRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerDeleteThreadByID(ctx context.Context, threadID string, params *MemoryControllerDeleteThreadByIDParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerDeleteThreadByIDRequest(c.Server, threadID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerGetThreadByID(ctx context.Context, threadID string, params *MemoryControllerGetThreadByIDParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerGetThreadByIDRequest(c.Server, threadID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerGetThreadMessages(ctx context.Context, threadID string, params *MemoryControllerGetThreadMessagesParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerGetThreadMessagesRequest(c.Server, threadID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet(ctx context.Context, sessionID string, params *GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetRequest(c.Server, sessionID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetByIdV10PostSessionSummariesSessionIDGet(ctx context.Context, sessionID int, params *GetByIdV10PostSessionSummariesSessionIDGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetByIdV10PostSessionSummariesSessionIDGetRequest(c.Server, sessionID, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerPatchRecurrenceTeamAccessWithBody(ctx context.Context, recurrenceId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerPatchRecurrenceTeamAccessRequestWithBody(c.Server, recurrenceId, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MemoryControllerPatchRecurrenceTeamAccess(ctx context.Context, recurrenceId string, body MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMemoryControllerPatchRecurrenceTeamAccessRequest(c.Server, recurrenceId, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGet(ctx context.Context, sessionId string, recurrenceId string, params *GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetRequest(c.Server, sessionId, recurrenceId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListXraysV10XRayGet(ctx context.Context, params *ListXraysV10XRayGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListXraysV10XRayGetRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateXrayStep1V10XRayCreateStep1PostWithBody(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateXrayStep1V10XRayCreateStep1PostRequestWithBody(c.Server, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateXrayStep1V10XRayCreateStep1Post(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, body CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateXrayStep1V10XRayCreateStep1PostRequest(c.Server, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateXrayStep2V10XRayCreateStep2PostWithBody(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateXrayStep2V10XRayCreateStep2PostRequestWithBody(c.Server, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateXrayStep2V10XRayCreateStep2Post(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, body CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateXrayStep2V10XRayCreateStep2PostRequest(c.Server, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateXrayStep3V10XRayCreateStep3PostWithBody(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateXrayStep3V10XRayCreateStep3PostRequestWithBody(c.Server, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) CreateXrayStep3V10XRayCreateStep3Post(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, body CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewCreateXrayStep3V10XRayCreateStep3PostRequest(c.Server, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ListXrayTemplatesV10XRayTemplatesGet(ctx context.Context, params *ListXrayTemplatesV10XRayTemplatesGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewListXrayTemplatesV10XRayTemplatesGetRequest(c.Server, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet(ctx context.Context, templateId int, params *GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetXrayTemplateV10XRayXrayTemplatesTemplateIdGetRequest(c.Server, templateId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) DeleteXrayV10XRayXrayIdDelete(ctx context.Context, xrayId int, params *DeleteXrayV10XRayXrayIdDeleteParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewDeleteXrayV10XRayXrayIdDeleteRequest(c.Server, xrayId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetXrayV10XRayXrayIdGet(ctx context.Context, xrayId int, params *GetXrayV10XRayXrayIdGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetXrayV10XRayXrayIdGetRequest(c.Server, xrayId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateXrayV10XRayXrayIdPatchWithBody(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateXrayV10XRayXrayIdPatchRequestWithBody(c.Server, xrayId, params, contentType, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) UpdateXrayV10XRayXrayIdPatch(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, body UpdateXrayV10XRayXrayIdPatchJSONRequestBody, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewUpdateXrayV10XRayXrayIdPatchRequest(c.Server, xrayId, params, body)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) GetXrayNotificationsV10XRayXrayIdNotificationsGet(ctx context.Context, xrayId int, params *GetXrayNotificationsV10XRayXrayIdNotificationsGetParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewGetXrayNotificationsV10XRayXrayIdNotificationsGetRequest(c.Server, xrayId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch(ctx context.Context, xrayId int, params *MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewMarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchRequest(c.Server, xrayId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

func (c *Client) ShareXrayAsTemplateV10XRayXrayIdSharePost(ctx context.Context, xrayId int, params *ShareXrayAsTemplateV10XRayXrayIdSharePostParams, reqEditors ...RequestEditorFn) (*http.Response, error) {
	req, err := NewShareXrayAsTemplateV10XRayXrayIdSharePostRequest(c.Server, xrayId, params)
	if err != nil {
		return nil, err
	}
	req = req.WithContext(ctx)
	if err := c.applyEditors(ctx, req, reqEditors); err != nil {
		return nil, err
	}
	return c.Client.Do(req)
}

// NewGetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetRequest generates requests for GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGet
func NewGetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetRequest(server string, sessionId string, recurrenceId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "session_id", runtime.ParamLocationPath, sessionId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "recurrence_id", runtime.ParamLocationPath, recurrenceId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/meeting-metadata/by-session/%s/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetMetadataByIdV10MeetingMetadataMetadataIdGetRequest generates requests for GetMetadataByIdV10MeetingMetadataMetadataIdGet
func NewGetMetadataByIdV10MeetingMetadataMetadataIdGetRequest(server string, metadataId int) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "metadata_id", runtime.ParamLocationPath, metadataId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/meeting-metadata/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewMemoryControllerAskAISyncRequest calls the generic MemoryControllerAskAISync builder with application/json body
func NewMemoryControllerAskAISyncRequest(server string, params *MemoryControllerAskAISyncParams, body MemoryControllerAskAISyncJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerAskAISyncRequestWithBody(server, params, "application/json", bodyReader)
}

// NewMemoryControllerAskAISyncRequestWithBody generates requests for MemoryControllerAskAISync with any type of body
func NewMemoryControllerAskAISyncRequestWithBody(server string, params *MemoryControllerAskAISyncParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/ask-ai-sync")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-User-Id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("X-User-Id", headerParam0)

	}

	return req, nil
}

// NewMemoryControllerAskV2Request calls the generic MemoryControllerAskV2 builder with application/json body
func NewMemoryControllerAskV2Request(server string, body MemoryControllerAskV2JSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerAskV2RequestWithBody(server, "application/json", bodyReader)
}

// NewMemoryControllerAskV2RequestWithBody generates requests for MemoryControllerAskV2 with any type of body
func NewMemoryControllerAskV2RequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/ask/")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewMemoryControllerFeedbackRequest calls the generic MemoryControllerFeedback builder with application/json body
func NewMemoryControllerFeedbackRequest(server string, body MemoryControllerFeedbackJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerFeedbackRequestWithBody(server, "application/json", bodyReader)
}

// NewMemoryControllerFeedbackRequestWithBody generates requests for MemoryControllerFeedback with any type of body
func NewMemoryControllerFeedbackRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/feedback")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewMemoryControllerStopRunRequest calls the generic MemoryControllerStopRun builder with application/json body
func NewMemoryControllerStopRunRequest(server string, body MemoryControllerStopRunJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerStopRunRequestWithBody(server, "application/json", bodyReader)
}

// NewMemoryControllerStopRunRequestWithBody generates requests for MemoryControllerStopRun with any type of body
func NewMemoryControllerStopRunRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/stop")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewMemoryControllerGetSuggestionsRequest generates requests for MemoryControllerGetSuggestions
func NewMemoryControllerGetSuggestionsRequest(server string, params *MemoryControllerGetSuggestionsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/suggestions")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Skip != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "skip", runtime.ParamLocationQuery, *params.Skip); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-User-Id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("X-User-Id", headerParam0)

	}

	return req, nil
}

// NewMemoryControllerPostTeamRequest calls the generic MemoryControllerPostTeam builder with application/json body
func NewMemoryControllerPostTeamRequest(server string, body MemoryControllerPostTeamJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerPostTeamRequestWithBody(server, "application/json", bodyReader)
}

// NewMemoryControllerPostTeamRequestWithBody generates requests for MemoryControllerPostTeam with any type of body
func NewMemoryControllerPostTeamRequestWithBody(server string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/teams")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewMemoryControllerPostTeamMemberRequest calls the generic MemoryControllerPostTeamMember builder with application/json body
func NewMemoryControllerPostTeamMemberRequest(server string, teamId string, body MemoryControllerPostTeamMemberJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerPostTeamMemberRequestWithBody(server, teamId, "application/json", bodyReader)
}

// NewMemoryControllerPostTeamMemberRequestWithBody generates requests for MemoryControllerPostTeamMember with any type of body
func NewMemoryControllerPostTeamMemberRequestWithBody(server string, teamId string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "team_id", runtime.ParamLocationPath, teamId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/teams/%s/members", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewMemoryControllerDeleteTeamMemberRequest generates requests for MemoryControllerDeleteTeamMember
func NewMemoryControllerDeleteTeamMemberRequest(server string, teamId string, teamMemberId string) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "team_id", runtime.ParamLocationPath, teamId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "team_member_id", runtime.ParamLocationPath, teamMemberId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/teams/%s/members/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewMemoryControllerGetUserThreadsRequest generates requests for MemoryControllerGetUserThreads
func NewMemoryControllerGetUserThreadsRequest(server string, params *MemoryControllerGetUserThreadsParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/threads")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Skip != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "skip", runtime.ParamLocationQuery, *params.Skip); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-User-Id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("X-User-Id", headerParam0)

	}

	return req, nil
}

// NewMemoryControllerDeleteThreadByIDRequest generates requests for MemoryControllerDeleteThreadByID
func NewMemoryControllerDeleteThreadByIDRequest(server string, threadID string, params *MemoryControllerDeleteThreadByIDParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "threadID", runtime.ParamLocationPath, threadID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/threads/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-User-Id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("X-User-Id", headerParam0)

	}

	return req, nil
}

// NewMemoryControllerGetThreadByIDRequest generates requests for MemoryControllerGetThreadByID
func NewMemoryControllerGetThreadByIDRequest(server string, threadID string, params *MemoryControllerGetThreadByIDParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "threadID", runtime.ParamLocationPath, threadID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/threads/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-User-Id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("X-User-Id", headerParam0)

	}

	return req, nil
}

// NewMemoryControllerGetThreadMessagesRequest generates requests for MemoryControllerGetThreadMessages
func NewMemoryControllerGetThreadMessagesRequest(server string, threadID string, params *MemoryControllerGetThreadMessagesParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "threadID", runtime.ParamLocationPath, threadID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/memory/threads/%s/messages", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Skip != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "skip", runtime.ParamLocationQuery, *params.Skip); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "X-User-Id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("X-User-Id", headerParam0)

	}

	return req, nil
}

// NewGetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetRequest generates requests for GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet
func NewGetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetRequest(server string, sessionID string, params *GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "sessionID", runtime.ParamLocationPath, sessionID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/post-session-summaries/by-session-ids/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if queryFrag, err := runtime.StyleParamWithLocation("form", true, "sessionRecurrenceID", runtime.ParamLocationQuery, params.SessionRecurrenceID); err != nil {
			return nil, err
		} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
			return nil, err
		} else {
			for k, v := range parsed {
				for _, v2 := range v {
					queryValues.Add(k, v2)
				}
			}
		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewGetByIdV10PostSessionSummariesSessionIDGetRequest generates requests for GetByIdV10PostSessionSummariesSessionIDGet
func NewGetByIdV10PostSessionSummariesSessionIDGetRequest(server string, sessionID int, params *GetByIdV10PostSessionSummariesSessionIDGetParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "sessionID", runtime.ParamLocationPath, sessionID)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/post-session-summaries/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Formats != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "formats", runtime.ParamLocationQuery, *params.Formats); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	return req, nil
}

// NewMemoryControllerPatchRecurrenceTeamAccessRequest calls the generic MemoryControllerPatchRecurrenceTeamAccess builder with application/json body
func NewMemoryControllerPatchRecurrenceTeamAccessRequest(server string, recurrenceId string, body MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewMemoryControllerPatchRecurrenceTeamAccessRequestWithBody(server, recurrenceId, "application/json", bodyReader)
}

// NewMemoryControllerPatchRecurrenceTeamAccessRequestWithBody generates requests for MemoryControllerPatchRecurrenceTeamAccess with any type of body
func NewMemoryControllerPatchRecurrenceTeamAccessRequestWithBody(server string, recurrenceId string, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "recurrence_id", runtime.ParamLocationPath, recurrenceId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/recurrences/%s/access", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	return req, nil
}

// NewGetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetRequest generates requests for GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGet
func NewGetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetRequest(server string, sessionId string, recurrenceId string, params *GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "session_id", runtime.ParamLocationPath, sessionId)
	if err != nil {
		return nil, err
	}

	var pathParam1 string

	pathParam1, err = runtime.StyleParamWithLocation("simple", false, "recurrence_id", runtime.ParamLocationPath, recurrenceId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/suggestions/%s/%s", pathParam0, pathParam1)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewListXraysV10XRayGetRequest generates requests for ListXraysV10XRayGet
func NewListXraysV10XRayGetRequest(server string, params *ListXraysV10XRayGetParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.TypeFilter != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "type_filter", runtime.ParamLocationQuery, *params.TypeFilter); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.SortBy != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "sort_by", runtime.ParamLocationQuery, *params.SortBy); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewCreateXrayStep1V10XRayCreateStep1PostRequest calls the generic CreateXrayStep1V10XRayCreateStep1Post builder with application/json body
func NewCreateXrayStep1V10XRayCreateStep1PostRequest(server string, params *CreateXrayStep1V10XRayCreateStep1PostParams, body CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateXrayStep1V10XRayCreateStep1PostRequestWithBody(server, params, "application/json", bodyReader)
}

// NewCreateXrayStep1V10XRayCreateStep1PostRequestWithBody generates requests for CreateXrayStep1V10XRayCreateStep1Post with any type of body
func NewCreateXrayStep1V10XRayCreateStep1PostRequestWithBody(server string, params *CreateXrayStep1V10XRayCreateStep1PostParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/create/step1")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewCreateXrayStep2V10XRayCreateStep2PostRequest calls the generic CreateXrayStep2V10XRayCreateStep2Post builder with application/json body
func NewCreateXrayStep2V10XRayCreateStep2PostRequest(server string, params *CreateXrayStep2V10XRayCreateStep2PostParams, body CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateXrayStep2V10XRayCreateStep2PostRequestWithBody(server, params, "application/json", bodyReader)
}

// NewCreateXrayStep2V10XRayCreateStep2PostRequestWithBody generates requests for CreateXrayStep2V10XRayCreateStep2Post with any type of body
func NewCreateXrayStep2V10XRayCreateStep2PostRequestWithBody(server string, params *CreateXrayStep2V10XRayCreateStep2PostParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/create/step2")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewCreateXrayStep3V10XRayCreateStep3PostRequest calls the generic CreateXrayStep3V10XRayCreateStep3Post builder with application/json body
func NewCreateXrayStep3V10XRayCreateStep3PostRequest(server string, params *CreateXrayStep3V10XRayCreateStep3PostParams, body CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewCreateXrayStep3V10XRayCreateStep3PostRequestWithBody(server, params, "application/json", bodyReader)
}

// NewCreateXrayStep3V10XRayCreateStep3PostRequestWithBody generates requests for CreateXrayStep3V10XRayCreateStep3Post with any type of body
func NewCreateXrayStep3V10XRayCreateStep3PostRequestWithBody(server string, params *CreateXrayStep3V10XRayCreateStep3PostParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/create/step3")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewListXrayTemplatesV10XRayTemplatesGetRequest generates requests for ListXrayTemplatesV10XRayTemplatesGet
func NewListXrayTemplatesV10XRayTemplatesGetRequest(server string, params *ListXrayTemplatesV10XRayTemplatesGetParams) (*http.Request, error) {
	var err error

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/templates")
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewGetXrayTemplateV10XRayXrayTemplatesTemplateIdGetRequest generates requests for GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet
func NewGetXrayTemplateV10XRayXrayTemplatesTemplateIdGetRequest(server string, templateId int, params *GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "template_id", runtime.ParamLocationPath, templateId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/xray-templates/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewDeleteXrayV10XRayXrayIdDeleteRequest generates requests for DeleteXrayV10XRayXrayIdDelete
func NewDeleteXrayV10XRayXrayIdDeleteRequest(server string, xrayId int, params *DeleteXrayV10XRayXrayIdDeleteParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "xray_id", runtime.ParamLocationPath, xrayId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("DELETE", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewGetXrayV10XRayXrayIdGetRequest generates requests for GetXrayV10XRayXrayIdGet
func NewGetXrayV10XRayXrayIdGetRequest(server string, xrayId int, params *GetXrayV10XRayXrayIdGetParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "xray_id", runtime.ParamLocationPath, xrayId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewUpdateXrayV10XRayXrayIdPatchRequest calls the generic UpdateXrayV10XRayXrayIdPatch builder with application/json body
func NewUpdateXrayV10XRayXrayIdPatchRequest(server string, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, body UpdateXrayV10XRayXrayIdPatchJSONRequestBody) (*http.Request, error) {
	var bodyReader io.Reader
	buf, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}
	bodyReader = bytes.NewReader(buf)
	return NewUpdateXrayV10XRayXrayIdPatchRequestWithBody(server, xrayId, params, "application/json", bodyReader)
}

// NewUpdateXrayV10XRayXrayIdPatchRequestWithBody generates requests for UpdateXrayV10XRayXrayIdPatch with any type of body
func NewUpdateXrayV10XRayXrayIdPatchRequestWithBody(server string, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, contentType string, body io.Reader) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "xray_id", runtime.ParamLocationPath, xrayId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/%s", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), body)
	if err != nil {
		return nil, err
	}

	req.Header.Add("Content-Type", contentType)

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewGetXrayNotificationsV10XRayXrayIdNotificationsGetRequest generates requests for GetXrayNotificationsV10XRayXrayIdNotificationsGet
func NewGetXrayNotificationsV10XRayXrayIdNotificationsGetRequest(server string, xrayId int, params *GetXrayNotificationsV10XRayXrayIdNotificationsGetParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "xray_id", runtime.ParamLocationPath, xrayId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/%s/notifications", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	if params != nil {
		queryValues := queryURL.Query()

		if params.Limit != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "limit", runtime.ParamLocationQuery, *params.Limit); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		if params.Offset != nil {

			if queryFrag, err := runtime.StyleParamWithLocation("form", true, "offset", runtime.ParamLocationQuery, *params.Offset); err != nil {
				return nil, err
			} else if parsed, err := url.ParseQuery(queryFrag); err != nil {
				return nil, err
			} else {
				for k, v := range parsed {
					for _, v2 := range v {
						queryValues.Add(k, v2)
					}
				}
			}

		}

		queryURL.RawQuery = queryValues.Encode()
	}

	req, err := http.NewRequest("GET", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewMarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchRequest generates requests for MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch
func NewMarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchRequest(server string, xrayId int, params *MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "xray_id", runtime.ParamLocationPath, xrayId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/%s/notifications/mark-seen", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("PATCH", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

// NewShareXrayAsTemplateV10XRayXrayIdSharePostRequest generates requests for ShareXrayAsTemplateV10XRayXrayIdSharePost
func NewShareXrayAsTemplateV10XRayXrayIdSharePostRequest(server string, xrayId int, params *ShareXrayAsTemplateV10XRayXrayIdSharePostParams) (*http.Request, error) {
	var err error

	var pathParam0 string

	pathParam0, err = runtime.StyleParamWithLocation("simple", false, "xray_id", runtime.ParamLocationPath, xrayId)
	if err != nil {
		return nil, err
	}

	serverURL, err := url.Parse(server)
	if err != nil {
		return nil, err
	}

	operationPath := fmt.Sprintf("/v1.0/x-ray/%s/share", pathParam0)
	if operationPath[0] == '/' {
		operationPath = "." + operationPath
	}

	queryURL, err := serverURL.Parse(operationPath)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", queryURL.String(), nil)
	if err != nil {
		return nil, err
	}

	if params != nil {

		var headerParam0 string

		headerParam0, err = runtime.StyleParamWithLocation("simple", false, "x-user-id", runtime.ParamLocationHeader, params.XUserId)
		if err != nil {
			return nil, err
		}

		req.Header.Set("x-user-id", headerParam0)

	}

	return req, nil
}

func (c *Client) applyEditors(ctx context.Context, req *http.Request, additionalEditors []RequestEditorFn) error {
	for _, r := range c.RequestEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	for _, r := range additionalEditors {
		if err := r(ctx, req); err != nil {
			return err
		}
	}
	return nil
}

// ClientWithResponses builds on ClientInterface to offer response payloads
type ClientWithResponses struct {
	ClientInterface
}

// NewClientWithResponses creates a new ClientWithResponses, which wraps
// Client with return type handling
func NewClientWithResponses(server string, opts ...ClientOption) (*ClientWithResponses, error) {
	client, err := NewClient(server, opts...)
	if err != nil {
		return nil, err
	}
	return &ClientWithResponses{client}, nil
}

// WithBaseURL overrides the baseURL.
func WithBaseURL(baseURL string) ClientOption {
	return func(c *Client) error {
		newBaseURL, err := url.Parse(baseURL)
		if err != nil {
			return err
		}
		c.Server = newBaseURL.String()
		return nil
	}
}

// ClientWithResponsesInterface is the interface specification for the client with responses above.
type ClientWithResponsesInterface interface {
	// GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetWithResponse request
	GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetWithResponse(ctx context.Context, sessionId string, recurrenceId string, reqEditors ...RequestEditorFn) (*GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse, error)

	// GetMetadataByIdV10MeetingMetadataMetadataIdGetWithResponse request
	GetMetadataByIdV10MeetingMetadataMetadataIdGetWithResponse(ctx context.Context, metadataId int, reqEditors ...RequestEditorFn) (*GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse, error)

	// MemoryControllerAskAISyncWithBodyWithResponse request with any body
	MemoryControllerAskAISyncWithBodyWithResponse(ctx context.Context, params *MemoryControllerAskAISyncParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerAskAISyncResponse, error)

	MemoryControllerAskAISyncWithResponse(ctx context.Context, params *MemoryControllerAskAISyncParams, body MemoryControllerAskAISyncJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerAskAISyncResponse, error)

	// MemoryControllerAskV2WithBodyWithResponse request with any body
	MemoryControllerAskV2WithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerAskV2Response, error)

	MemoryControllerAskV2WithResponse(ctx context.Context, body MemoryControllerAskV2JSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerAskV2Response, error)

	// MemoryControllerFeedbackWithBodyWithResponse request with any body
	MemoryControllerFeedbackWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerFeedbackResponse, error)

	MemoryControllerFeedbackWithResponse(ctx context.Context, body MemoryControllerFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerFeedbackResponse, error)

	// MemoryControllerStopRunWithBodyWithResponse request with any body
	MemoryControllerStopRunWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerStopRunResponse, error)

	MemoryControllerStopRunWithResponse(ctx context.Context, body MemoryControllerStopRunJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerStopRunResponse, error)

	// MemoryControllerGetSuggestionsWithResponse request
	MemoryControllerGetSuggestionsWithResponse(ctx context.Context, params *MemoryControllerGetSuggestionsParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetSuggestionsResponse, error)

	// MemoryControllerPostTeamWithBodyWithResponse request with any body
	MemoryControllerPostTeamWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamResponse, error)

	MemoryControllerPostTeamWithResponse(ctx context.Context, body MemoryControllerPostTeamJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamResponse, error)

	// MemoryControllerPostTeamMemberWithBodyWithResponse request with any body
	MemoryControllerPostTeamMemberWithBodyWithResponse(ctx context.Context, teamId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamMemberResponse, error)

	MemoryControllerPostTeamMemberWithResponse(ctx context.Context, teamId string, body MemoryControllerPostTeamMemberJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamMemberResponse, error)

	// MemoryControllerDeleteTeamMemberWithResponse request
	MemoryControllerDeleteTeamMemberWithResponse(ctx context.Context, teamId string, teamMemberId string, reqEditors ...RequestEditorFn) (*MemoryControllerDeleteTeamMemberResponse, error)

	// MemoryControllerGetUserThreadsWithResponse request
	MemoryControllerGetUserThreadsWithResponse(ctx context.Context, params *MemoryControllerGetUserThreadsParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetUserThreadsResponse, error)

	// MemoryControllerDeleteThreadByIDWithResponse request
	MemoryControllerDeleteThreadByIDWithResponse(ctx context.Context, threadID string, params *MemoryControllerDeleteThreadByIDParams, reqEditors ...RequestEditorFn) (*MemoryControllerDeleteThreadByIDResponse, error)

	// MemoryControllerGetThreadByIDWithResponse request
	MemoryControllerGetThreadByIDWithResponse(ctx context.Context, threadID string, params *MemoryControllerGetThreadByIDParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetThreadByIDResponse, error)

	// MemoryControllerGetThreadMessagesWithResponse request
	MemoryControllerGetThreadMessagesWithResponse(ctx context.Context, threadID string, params *MemoryControllerGetThreadMessagesParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetThreadMessagesResponse, error)

	// GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetWithResponse request
	GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetWithResponse(ctx context.Context, sessionID string, params *GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams, reqEditors ...RequestEditorFn) (*GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse, error)

	// GetByIdV10PostSessionSummariesSessionIDGetWithResponse request
	GetByIdV10PostSessionSummariesSessionIDGetWithResponse(ctx context.Context, sessionID int, params *GetByIdV10PostSessionSummariesSessionIDGetParams, reqEditors ...RequestEditorFn) (*GetByIdV10PostSessionSummariesSessionIDGetResponse, error)

	// MemoryControllerPatchRecurrenceTeamAccessWithBodyWithResponse request with any body
	MemoryControllerPatchRecurrenceTeamAccessWithBodyWithResponse(ctx context.Context, recurrenceId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerPatchRecurrenceTeamAccessResponse, error)

	MemoryControllerPatchRecurrenceTeamAccessWithResponse(ctx context.Context, recurrenceId string, body MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerPatchRecurrenceTeamAccessResponse, error)

	// GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetWithResponse request
	GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetWithResponse(ctx context.Context, sessionId string, recurrenceId string, params *GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams, reqEditors ...RequestEditorFn) (*GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse, error)

	// ListXraysV10XRayGetWithResponse request
	ListXraysV10XRayGetWithResponse(ctx context.Context, params *ListXraysV10XRayGetParams, reqEditors ...RequestEditorFn) (*ListXraysV10XRayGetResponse, error)

	// CreateXrayStep1V10XRayCreateStep1PostWithBodyWithResponse request with any body
	CreateXrayStep1V10XRayCreateStep1PostWithBodyWithResponse(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateXrayStep1V10XRayCreateStep1PostResponse, error)

	CreateXrayStep1V10XRayCreateStep1PostWithResponse(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, body CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateXrayStep1V10XRayCreateStep1PostResponse, error)

	// CreateXrayStep2V10XRayCreateStep2PostWithBodyWithResponse request with any body
	CreateXrayStep2V10XRayCreateStep2PostWithBodyWithResponse(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateXrayStep2V10XRayCreateStep2PostResponse, error)

	CreateXrayStep2V10XRayCreateStep2PostWithResponse(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, body CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateXrayStep2V10XRayCreateStep2PostResponse, error)

	// CreateXrayStep3V10XRayCreateStep3PostWithBodyWithResponse request with any body
	CreateXrayStep3V10XRayCreateStep3PostWithBodyWithResponse(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateXrayStep3V10XRayCreateStep3PostResponse, error)

	CreateXrayStep3V10XRayCreateStep3PostWithResponse(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, body CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateXrayStep3V10XRayCreateStep3PostResponse, error)

	// ListXrayTemplatesV10XRayTemplatesGetWithResponse request
	ListXrayTemplatesV10XRayTemplatesGetWithResponse(ctx context.Context, params *ListXrayTemplatesV10XRayTemplatesGetParams, reqEditors ...RequestEditorFn) (*ListXrayTemplatesV10XRayTemplatesGetResponse, error)

	// GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetWithResponse request
	GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetWithResponse(ctx context.Context, templateId int, params *GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams, reqEditors ...RequestEditorFn) (*GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse, error)

	// DeleteXrayV10XRayXrayIdDeleteWithResponse request
	DeleteXrayV10XRayXrayIdDeleteWithResponse(ctx context.Context, xrayId int, params *DeleteXrayV10XRayXrayIdDeleteParams, reqEditors ...RequestEditorFn) (*DeleteXrayV10XRayXrayIdDeleteResponse, error)

	// GetXrayV10XRayXrayIdGetWithResponse request
	GetXrayV10XRayXrayIdGetWithResponse(ctx context.Context, xrayId int, params *GetXrayV10XRayXrayIdGetParams, reqEditors ...RequestEditorFn) (*GetXrayV10XRayXrayIdGetResponse, error)

	// UpdateXrayV10XRayXrayIdPatchWithBodyWithResponse request with any body
	UpdateXrayV10XRayXrayIdPatchWithBodyWithResponse(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateXrayV10XRayXrayIdPatchResponse, error)

	UpdateXrayV10XRayXrayIdPatchWithResponse(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, body UpdateXrayV10XRayXrayIdPatchJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateXrayV10XRayXrayIdPatchResponse, error)

	// GetXrayNotificationsV10XRayXrayIdNotificationsGetWithResponse request
	GetXrayNotificationsV10XRayXrayIdNotificationsGetWithResponse(ctx context.Context, xrayId int, params *GetXrayNotificationsV10XRayXrayIdNotificationsGetParams, reqEditors ...RequestEditorFn) (*GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse, error)

	// MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchWithResponse request
	MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchWithResponse(ctx context.Context, xrayId int, params *MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams, reqEditors ...RequestEditorFn) (*MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse, error)

	// ShareXrayAsTemplateV10XRayXrayIdSharePostWithResponse request
	ShareXrayAsTemplateV10XRayXrayIdSharePostWithResponse(ctx context.Context, xrayId int, params *ShareXrayAsTemplateV10XRayXrayIdSharePostParams, reqEditors ...RequestEditorFn) (*ShareXrayAsTemplateV10XRayXrayIdSharePostResponse, error)
}

type GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *MeetingMetadataResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *MeetingMetadataResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerAskAISyncResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *AskAISyncResponseDTO
	JSON401      *ErrorResponseDTO
	JSON500      *ErrorResponseDTO
	JSON504      *ErrorResponseDTO
}

// Status returns HTTPResponse.Status
func (r MemoryControllerAskAISyncResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerAskAISyncResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerAskV2Response struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DummyResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerAskV2Response) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerAskV2Response) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerFeedbackResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DummyResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerFeedbackResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerFeedbackResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerStopRunResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *StopRunResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerStopRunResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerStopRunResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerGetSuggestionsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetSuggestionsResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerGetSuggestionsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerGetSuggestionsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerPostTeamResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DummyResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerPostTeamResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerPostTeamResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerPostTeamMemberResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DummyResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerPostTeamMemberResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerPostTeamMemberResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerDeleteTeamMemberResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DummyResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerDeleteTeamMemberResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerDeleteTeamMemberResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerGetUserThreadsResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetThreadsResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerGetUserThreadsResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerGetUserThreadsResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerDeleteThreadByIDResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *DeleteThreadResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerDeleteThreadByIDResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerDeleteThreadByIDResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerGetThreadByIDResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetThreadByIdResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerGetThreadByIDResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerGetThreadByIDResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerGetThreadMessagesResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetThreadMessagesResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerGetThreadMessagesResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerGetThreadMessagesResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostSessionSummaryDTO
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetByIdV10PostSessionSummariesSessionIDGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PostSessionSummaryDTO
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetByIdV10PostSessionSummariesSessionIDGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetByIdV10PostSessionSummariesSessionIDGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MemoryControllerPatchRecurrenceTeamAccessResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *PatchRecurrenceTeamAccessResponse
	JSON500      *PatchRecurrenceTeamAccessResponse
}

// Status returns HTTPResponse.Status
func (r MemoryControllerPatchRecurrenceTeamAccessResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MemoryControllerPatchRecurrenceTeamAccessResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *GetMeetingSuggestionsResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListXraysV10XRayGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayListResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r ListXraysV10XRayGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListXraysV10XRayGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateXrayStep1V10XRayCreateStep1PostResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayCreateStep1Response
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r CreateXrayStep1V10XRayCreateStep1PostResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateXrayStep1V10XRayCreateStep1PostResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateXrayStep2V10XRayCreateStep2PostResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayCreateStep2Response
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r CreateXrayStep2V10XRayCreateStep2PostResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateXrayStep2V10XRayCreateStep2PostResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type CreateXrayStep3V10XRayCreateStep3PostResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r CreateXrayStep3V10XRayCreateStep3PostResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r CreateXrayStep3V10XRayCreateStep3PostResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ListXrayTemplatesV10XRayTemplatesGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayTemplateListResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r ListXrayTemplatesV10XRayTemplatesGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ListXrayTemplatesV10XRayTemplatesGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayTemplateResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type DeleteXrayV10XRayXrayIdDeleteResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *interface{}
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r DeleteXrayV10XRayXrayIdDeleteResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r DeleteXrayV10XRayXrayIdDeleteResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetXrayV10XRayXrayIdGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetXrayV10XRayXrayIdGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetXrayV10XRayXrayIdGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type UpdateXrayV10XRayXrayIdPatchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r UpdateXrayV10XRayXrayIdPatchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r UpdateXrayV10XRayXrayIdPatchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayNotificationResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *MarkNotificationsSeenResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

type ShareXrayAsTemplateV10XRayXrayIdSharePostResponse struct {
	Body         []byte
	HTTPResponse *http.Response
	JSON200      *XRayTemplateResponse
	JSON422      *HTTPValidationError
}

// Status returns HTTPResponse.Status
func (r ShareXrayAsTemplateV10XRayXrayIdSharePostResponse) Status() string {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.Status
	}
	return http.StatusText(0)
}

// StatusCode returns HTTPResponse.StatusCode
func (r ShareXrayAsTemplateV10XRayXrayIdSharePostResponse) StatusCode() int {
	if r.HTTPResponse != nil {
		return r.HTTPResponse.StatusCode
	}
	return 0
}

// GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetWithResponse request returning *GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse
func (c *ClientWithResponses) GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetWithResponse(ctx context.Context, sessionId string, recurrenceId string, reqEditors ...RequestEditorFn) (*GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse, error) {
	rsp, err := c.GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGet(ctx, sessionId, recurrenceId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse(rsp)
}

// GetMetadataByIdV10MeetingMetadataMetadataIdGetWithResponse request returning *GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse
func (c *ClientWithResponses) GetMetadataByIdV10MeetingMetadataMetadataIdGetWithResponse(ctx context.Context, metadataId int, reqEditors ...RequestEditorFn) (*GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse, error) {
	rsp, err := c.GetMetadataByIdV10MeetingMetadataMetadataIdGet(ctx, metadataId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetMetadataByIdV10MeetingMetadataMetadataIdGetResponse(rsp)
}

// MemoryControllerAskAISyncWithBodyWithResponse request with arbitrary body returning *MemoryControllerAskAISyncResponse
func (c *ClientWithResponses) MemoryControllerAskAISyncWithBodyWithResponse(ctx context.Context, params *MemoryControllerAskAISyncParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerAskAISyncResponse, error) {
	rsp, err := c.MemoryControllerAskAISyncWithBody(ctx, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerAskAISyncResponse(rsp)
}

func (c *ClientWithResponses) MemoryControllerAskAISyncWithResponse(ctx context.Context, params *MemoryControllerAskAISyncParams, body MemoryControllerAskAISyncJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerAskAISyncResponse, error) {
	rsp, err := c.MemoryControllerAskAISync(ctx, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerAskAISyncResponse(rsp)
}

// MemoryControllerAskV2WithBodyWithResponse request with arbitrary body returning *MemoryControllerAskV2Response
func (c *ClientWithResponses) MemoryControllerAskV2WithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerAskV2Response, error) {
	rsp, err := c.MemoryControllerAskV2WithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerAskV2Response(rsp)
}

func (c *ClientWithResponses) MemoryControllerAskV2WithResponse(ctx context.Context, body MemoryControllerAskV2JSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerAskV2Response, error) {
	rsp, err := c.MemoryControllerAskV2(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerAskV2Response(rsp)
}

// MemoryControllerFeedbackWithBodyWithResponse request with arbitrary body returning *MemoryControllerFeedbackResponse
func (c *ClientWithResponses) MemoryControllerFeedbackWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerFeedbackResponse, error) {
	rsp, err := c.MemoryControllerFeedbackWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerFeedbackResponse(rsp)
}

func (c *ClientWithResponses) MemoryControllerFeedbackWithResponse(ctx context.Context, body MemoryControllerFeedbackJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerFeedbackResponse, error) {
	rsp, err := c.MemoryControllerFeedback(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerFeedbackResponse(rsp)
}

// MemoryControllerStopRunWithBodyWithResponse request with arbitrary body returning *MemoryControllerStopRunResponse
func (c *ClientWithResponses) MemoryControllerStopRunWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerStopRunResponse, error) {
	rsp, err := c.MemoryControllerStopRunWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerStopRunResponse(rsp)
}

func (c *ClientWithResponses) MemoryControllerStopRunWithResponse(ctx context.Context, body MemoryControllerStopRunJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerStopRunResponse, error) {
	rsp, err := c.MemoryControllerStopRun(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerStopRunResponse(rsp)
}

// MemoryControllerGetSuggestionsWithResponse request returning *MemoryControllerGetSuggestionsResponse
func (c *ClientWithResponses) MemoryControllerGetSuggestionsWithResponse(ctx context.Context, params *MemoryControllerGetSuggestionsParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetSuggestionsResponse, error) {
	rsp, err := c.MemoryControllerGetSuggestions(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerGetSuggestionsResponse(rsp)
}

// MemoryControllerPostTeamWithBodyWithResponse request with arbitrary body returning *MemoryControllerPostTeamResponse
func (c *ClientWithResponses) MemoryControllerPostTeamWithBodyWithResponse(ctx context.Context, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamResponse, error) {
	rsp, err := c.MemoryControllerPostTeamWithBody(ctx, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerPostTeamResponse(rsp)
}

func (c *ClientWithResponses) MemoryControllerPostTeamWithResponse(ctx context.Context, body MemoryControllerPostTeamJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamResponse, error) {
	rsp, err := c.MemoryControllerPostTeam(ctx, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerPostTeamResponse(rsp)
}

// MemoryControllerPostTeamMemberWithBodyWithResponse request with arbitrary body returning *MemoryControllerPostTeamMemberResponse
func (c *ClientWithResponses) MemoryControllerPostTeamMemberWithBodyWithResponse(ctx context.Context, teamId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamMemberResponse, error) {
	rsp, err := c.MemoryControllerPostTeamMemberWithBody(ctx, teamId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerPostTeamMemberResponse(rsp)
}

func (c *ClientWithResponses) MemoryControllerPostTeamMemberWithResponse(ctx context.Context, teamId string, body MemoryControllerPostTeamMemberJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerPostTeamMemberResponse, error) {
	rsp, err := c.MemoryControllerPostTeamMember(ctx, teamId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerPostTeamMemberResponse(rsp)
}

// MemoryControllerDeleteTeamMemberWithResponse request returning *MemoryControllerDeleteTeamMemberResponse
func (c *ClientWithResponses) MemoryControllerDeleteTeamMemberWithResponse(ctx context.Context, teamId string, teamMemberId string, reqEditors ...RequestEditorFn) (*MemoryControllerDeleteTeamMemberResponse, error) {
	rsp, err := c.MemoryControllerDeleteTeamMember(ctx, teamId, teamMemberId, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerDeleteTeamMemberResponse(rsp)
}

// MemoryControllerGetUserThreadsWithResponse request returning *MemoryControllerGetUserThreadsResponse
func (c *ClientWithResponses) MemoryControllerGetUserThreadsWithResponse(ctx context.Context, params *MemoryControllerGetUserThreadsParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetUserThreadsResponse, error) {
	rsp, err := c.MemoryControllerGetUserThreads(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerGetUserThreadsResponse(rsp)
}

// MemoryControllerDeleteThreadByIDWithResponse request returning *MemoryControllerDeleteThreadByIDResponse
func (c *ClientWithResponses) MemoryControllerDeleteThreadByIDWithResponse(ctx context.Context, threadID string, params *MemoryControllerDeleteThreadByIDParams, reqEditors ...RequestEditorFn) (*MemoryControllerDeleteThreadByIDResponse, error) {
	rsp, err := c.MemoryControllerDeleteThreadByID(ctx, threadID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerDeleteThreadByIDResponse(rsp)
}

// MemoryControllerGetThreadByIDWithResponse request returning *MemoryControllerGetThreadByIDResponse
func (c *ClientWithResponses) MemoryControllerGetThreadByIDWithResponse(ctx context.Context, threadID string, params *MemoryControllerGetThreadByIDParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetThreadByIDResponse, error) {
	rsp, err := c.MemoryControllerGetThreadByID(ctx, threadID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerGetThreadByIDResponse(rsp)
}

// MemoryControllerGetThreadMessagesWithResponse request returning *MemoryControllerGetThreadMessagesResponse
func (c *ClientWithResponses) MemoryControllerGetThreadMessagesWithResponse(ctx context.Context, threadID string, params *MemoryControllerGetThreadMessagesParams, reqEditors ...RequestEditorFn) (*MemoryControllerGetThreadMessagesResponse, error) {
	rsp, err := c.MemoryControllerGetThreadMessages(ctx, threadID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerGetThreadMessagesResponse(rsp)
}

// GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetWithResponse request returning *GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse
func (c *ClientWithResponses) GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetWithResponse(ctx context.Context, sessionID string, params *GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetParams, reqEditors ...RequestEditorFn) (*GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse, error) {
	rsp, err := c.GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGet(ctx, sessionID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse(rsp)
}

// GetByIdV10PostSessionSummariesSessionIDGetWithResponse request returning *GetByIdV10PostSessionSummariesSessionIDGetResponse
func (c *ClientWithResponses) GetByIdV10PostSessionSummariesSessionIDGetWithResponse(ctx context.Context, sessionID int, params *GetByIdV10PostSessionSummariesSessionIDGetParams, reqEditors ...RequestEditorFn) (*GetByIdV10PostSessionSummariesSessionIDGetResponse, error) {
	rsp, err := c.GetByIdV10PostSessionSummariesSessionIDGet(ctx, sessionID, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetByIdV10PostSessionSummariesSessionIDGetResponse(rsp)
}

// MemoryControllerPatchRecurrenceTeamAccessWithBodyWithResponse request with arbitrary body returning *MemoryControllerPatchRecurrenceTeamAccessResponse
func (c *ClientWithResponses) MemoryControllerPatchRecurrenceTeamAccessWithBodyWithResponse(ctx context.Context, recurrenceId string, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*MemoryControllerPatchRecurrenceTeamAccessResponse, error) {
	rsp, err := c.MemoryControllerPatchRecurrenceTeamAccessWithBody(ctx, recurrenceId, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerPatchRecurrenceTeamAccessResponse(rsp)
}

func (c *ClientWithResponses) MemoryControllerPatchRecurrenceTeamAccessWithResponse(ctx context.Context, recurrenceId string, body MemoryControllerPatchRecurrenceTeamAccessJSONRequestBody, reqEditors ...RequestEditorFn) (*MemoryControllerPatchRecurrenceTeamAccessResponse, error) {
	rsp, err := c.MemoryControllerPatchRecurrenceTeamAccess(ctx, recurrenceId, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMemoryControllerPatchRecurrenceTeamAccessResponse(rsp)
}

// GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetWithResponse request returning *GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse
func (c *ClientWithResponses) GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetWithResponse(ctx context.Context, sessionId string, recurrenceId string, params *GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetParams, reqEditors ...RequestEditorFn) (*GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse, error) {
	rsp, err := c.GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGet(ctx, sessionId, recurrenceId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse(rsp)
}

// ListXraysV10XRayGetWithResponse request returning *ListXraysV10XRayGetResponse
func (c *ClientWithResponses) ListXraysV10XRayGetWithResponse(ctx context.Context, params *ListXraysV10XRayGetParams, reqEditors ...RequestEditorFn) (*ListXraysV10XRayGetResponse, error) {
	rsp, err := c.ListXraysV10XRayGet(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListXraysV10XRayGetResponse(rsp)
}

// CreateXrayStep1V10XRayCreateStep1PostWithBodyWithResponse request with arbitrary body returning *CreateXrayStep1V10XRayCreateStep1PostResponse
func (c *ClientWithResponses) CreateXrayStep1V10XRayCreateStep1PostWithBodyWithResponse(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateXrayStep1V10XRayCreateStep1PostResponse, error) {
	rsp, err := c.CreateXrayStep1V10XRayCreateStep1PostWithBody(ctx, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateXrayStep1V10XRayCreateStep1PostResponse(rsp)
}

func (c *ClientWithResponses) CreateXrayStep1V10XRayCreateStep1PostWithResponse(ctx context.Context, params *CreateXrayStep1V10XRayCreateStep1PostParams, body CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateXrayStep1V10XRayCreateStep1PostResponse, error) {
	rsp, err := c.CreateXrayStep1V10XRayCreateStep1Post(ctx, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateXrayStep1V10XRayCreateStep1PostResponse(rsp)
}

// CreateXrayStep2V10XRayCreateStep2PostWithBodyWithResponse request with arbitrary body returning *CreateXrayStep2V10XRayCreateStep2PostResponse
func (c *ClientWithResponses) CreateXrayStep2V10XRayCreateStep2PostWithBodyWithResponse(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateXrayStep2V10XRayCreateStep2PostResponse, error) {
	rsp, err := c.CreateXrayStep2V10XRayCreateStep2PostWithBody(ctx, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateXrayStep2V10XRayCreateStep2PostResponse(rsp)
}

func (c *ClientWithResponses) CreateXrayStep2V10XRayCreateStep2PostWithResponse(ctx context.Context, params *CreateXrayStep2V10XRayCreateStep2PostParams, body CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateXrayStep2V10XRayCreateStep2PostResponse, error) {
	rsp, err := c.CreateXrayStep2V10XRayCreateStep2Post(ctx, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateXrayStep2V10XRayCreateStep2PostResponse(rsp)
}

// CreateXrayStep3V10XRayCreateStep3PostWithBodyWithResponse request with arbitrary body returning *CreateXrayStep3V10XRayCreateStep3PostResponse
func (c *ClientWithResponses) CreateXrayStep3V10XRayCreateStep3PostWithBodyWithResponse(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*CreateXrayStep3V10XRayCreateStep3PostResponse, error) {
	rsp, err := c.CreateXrayStep3V10XRayCreateStep3PostWithBody(ctx, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateXrayStep3V10XRayCreateStep3PostResponse(rsp)
}

func (c *ClientWithResponses) CreateXrayStep3V10XRayCreateStep3PostWithResponse(ctx context.Context, params *CreateXrayStep3V10XRayCreateStep3PostParams, body CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody, reqEditors ...RequestEditorFn) (*CreateXrayStep3V10XRayCreateStep3PostResponse, error) {
	rsp, err := c.CreateXrayStep3V10XRayCreateStep3Post(ctx, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseCreateXrayStep3V10XRayCreateStep3PostResponse(rsp)
}

// ListXrayTemplatesV10XRayTemplatesGetWithResponse request returning *ListXrayTemplatesV10XRayTemplatesGetResponse
func (c *ClientWithResponses) ListXrayTemplatesV10XRayTemplatesGetWithResponse(ctx context.Context, params *ListXrayTemplatesV10XRayTemplatesGetParams, reqEditors ...RequestEditorFn) (*ListXrayTemplatesV10XRayTemplatesGetResponse, error) {
	rsp, err := c.ListXrayTemplatesV10XRayTemplatesGet(ctx, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseListXrayTemplatesV10XRayTemplatesGetResponse(rsp)
}

// GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetWithResponse request returning *GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse
func (c *ClientWithResponses) GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetWithResponse(ctx context.Context, templateId int, params *GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams, reqEditors ...RequestEditorFn) (*GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse, error) {
	rsp, err := c.GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet(ctx, templateId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse(rsp)
}

// DeleteXrayV10XRayXrayIdDeleteWithResponse request returning *DeleteXrayV10XRayXrayIdDeleteResponse
func (c *ClientWithResponses) DeleteXrayV10XRayXrayIdDeleteWithResponse(ctx context.Context, xrayId int, params *DeleteXrayV10XRayXrayIdDeleteParams, reqEditors ...RequestEditorFn) (*DeleteXrayV10XRayXrayIdDeleteResponse, error) {
	rsp, err := c.DeleteXrayV10XRayXrayIdDelete(ctx, xrayId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseDeleteXrayV10XRayXrayIdDeleteResponse(rsp)
}

// GetXrayV10XRayXrayIdGetWithResponse request returning *GetXrayV10XRayXrayIdGetResponse
func (c *ClientWithResponses) GetXrayV10XRayXrayIdGetWithResponse(ctx context.Context, xrayId int, params *GetXrayV10XRayXrayIdGetParams, reqEditors ...RequestEditorFn) (*GetXrayV10XRayXrayIdGetResponse, error) {
	rsp, err := c.GetXrayV10XRayXrayIdGet(ctx, xrayId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetXrayV10XRayXrayIdGetResponse(rsp)
}

// UpdateXrayV10XRayXrayIdPatchWithBodyWithResponse request with arbitrary body returning *UpdateXrayV10XRayXrayIdPatchResponse
func (c *ClientWithResponses) UpdateXrayV10XRayXrayIdPatchWithBodyWithResponse(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, contentType string, body io.Reader, reqEditors ...RequestEditorFn) (*UpdateXrayV10XRayXrayIdPatchResponse, error) {
	rsp, err := c.UpdateXrayV10XRayXrayIdPatchWithBody(ctx, xrayId, params, contentType, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateXrayV10XRayXrayIdPatchResponse(rsp)
}

func (c *ClientWithResponses) UpdateXrayV10XRayXrayIdPatchWithResponse(ctx context.Context, xrayId int, params *UpdateXrayV10XRayXrayIdPatchParams, body UpdateXrayV10XRayXrayIdPatchJSONRequestBody, reqEditors ...RequestEditorFn) (*UpdateXrayV10XRayXrayIdPatchResponse, error) {
	rsp, err := c.UpdateXrayV10XRayXrayIdPatch(ctx, xrayId, params, body, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseUpdateXrayV10XRayXrayIdPatchResponse(rsp)
}

// GetXrayNotificationsV10XRayXrayIdNotificationsGetWithResponse request returning *GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse
func (c *ClientWithResponses) GetXrayNotificationsV10XRayXrayIdNotificationsGetWithResponse(ctx context.Context, xrayId int, params *GetXrayNotificationsV10XRayXrayIdNotificationsGetParams, reqEditors ...RequestEditorFn) (*GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse, error) {
	rsp, err := c.GetXrayNotificationsV10XRayXrayIdNotificationsGet(ctx, xrayId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseGetXrayNotificationsV10XRayXrayIdNotificationsGetResponse(rsp)
}

// MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchWithResponse request returning *MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse
func (c *ClientWithResponses) MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchWithResponse(ctx context.Context, xrayId int, params *MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams, reqEditors ...RequestEditorFn) (*MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse, error) {
	rsp, err := c.MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch(ctx, xrayId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseMarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse(rsp)
}

// ShareXrayAsTemplateV10XRayXrayIdSharePostWithResponse request returning *ShareXrayAsTemplateV10XRayXrayIdSharePostResponse
func (c *ClientWithResponses) ShareXrayAsTemplateV10XRayXrayIdSharePostWithResponse(ctx context.Context, xrayId int, params *ShareXrayAsTemplateV10XRayXrayIdSharePostParams, reqEditors ...RequestEditorFn) (*ShareXrayAsTemplateV10XRayXrayIdSharePostResponse, error) {
	rsp, err := c.ShareXrayAsTemplateV10XRayXrayIdSharePost(ctx, xrayId, params, reqEditors...)
	if err != nil {
		return nil, err
	}
	return ParseShareXrayAsTemplateV10XRayXrayIdSharePostResponse(rsp)
}

// ParseGetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse parses an HTTP response from a GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetWithResponse call
func ParseGetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse(rsp *http.Response) (*GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetMetadataBySessionIdV10MeetingMetadataBySessionSessionIdRecurrenceIdGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest MeetingMetadataResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetMetadataByIdV10MeetingMetadataMetadataIdGetResponse parses an HTTP response from a GetMetadataByIdV10MeetingMetadataMetadataIdGetWithResponse call
func ParseGetMetadataByIdV10MeetingMetadataMetadataIdGetResponse(rsp *http.Response) (*GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetMetadataByIdV10MeetingMetadataMetadataIdGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest MeetingMetadataResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseMemoryControllerAskAISyncResponse parses an HTTP response from a MemoryControllerAskAISyncWithResponse call
func ParseMemoryControllerAskAISyncResponse(rsp *http.Response) (*MemoryControllerAskAISyncResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerAskAISyncResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest AskAISyncResponseDTO
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 401:
		var dest ErrorResponseDTO
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON401 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest ErrorResponseDTO
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 504:
		var dest ErrorResponseDTO
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON504 = &dest

	}

	return response, nil
}

// ParseMemoryControllerAskV2Response parses an HTTP response from a MemoryControllerAskV2WithResponse call
func ParseMemoryControllerAskV2Response(rsp *http.Response) (*MemoryControllerAskV2Response, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerAskV2Response{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DummyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerFeedbackResponse parses an HTTP response from a MemoryControllerFeedbackWithResponse call
func ParseMemoryControllerFeedbackResponse(rsp *http.Response) (*MemoryControllerFeedbackResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerFeedbackResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DummyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerStopRunResponse parses an HTTP response from a MemoryControllerStopRunWithResponse call
func ParseMemoryControllerStopRunResponse(rsp *http.Response) (*MemoryControllerStopRunResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerStopRunResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest StopRunResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerGetSuggestionsResponse parses an HTTP response from a MemoryControllerGetSuggestionsWithResponse call
func ParseMemoryControllerGetSuggestionsResponse(rsp *http.Response) (*MemoryControllerGetSuggestionsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerGetSuggestionsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetSuggestionsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerPostTeamResponse parses an HTTP response from a MemoryControllerPostTeamWithResponse call
func ParseMemoryControllerPostTeamResponse(rsp *http.Response) (*MemoryControllerPostTeamResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerPostTeamResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DummyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerPostTeamMemberResponse parses an HTTP response from a MemoryControllerPostTeamMemberWithResponse call
func ParseMemoryControllerPostTeamMemberResponse(rsp *http.Response) (*MemoryControllerPostTeamMemberResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerPostTeamMemberResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DummyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerDeleteTeamMemberResponse parses an HTTP response from a MemoryControllerDeleteTeamMemberWithResponse call
func ParseMemoryControllerDeleteTeamMemberResponse(rsp *http.Response) (*MemoryControllerDeleteTeamMemberResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerDeleteTeamMemberResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DummyResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerGetUserThreadsResponse parses an HTTP response from a MemoryControllerGetUserThreadsWithResponse call
func ParseMemoryControllerGetUserThreadsResponse(rsp *http.Response) (*MemoryControllerGetUserThreadsResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerGetUserThreadsResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetThreadsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerDeleteThreadByIDResponse parses an HTTP response from a MemoryControllerDeleteThreadByIDWithResponse call
func ParseMemoryControllerDeleteThreadByIDResponse(rsp *http.Response) (*MemoryControllerDeleteThreadByIDResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerDeleteThreadByIDResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest DeleteThreadResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerGetThreadByIDResponse parses an HTTP response from a MemoryControllerGetThreadByIDWithResponse call
func ParseMemoryControllerGetThreadByIDResponse(rsp *http.Response) (*MemoryControllerGetThreadByIDResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerGetThreadByIDResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetThreadByIdResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseMemoryControllerGetThreadMessagesResponse parses an HTTP response from a MemoryControllerGetThreadMessagesWithResponse call
func ParseMemoryControllerGetThreadMessagesResponse(rsp *http.Response) (*MemoryControllerGetThreadMessagesResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerGetThreadMessagesResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetThreadMessagesResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	}

	return response, nil
}

// ParseGetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse parses an HTTP response from a GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetWithResponse call
func ParseGetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse(rsp *http.Response) (*GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetBySessionIdsV10PostSessionSummariesBySessionIdsSessionIDGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostSessionSummaryDTO
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetByIdV10PostSessionSummariesSessionIDGetResponse parses an HTTP response from a GetByIdV10PostSessionSummariesSessionIDGetWithResponse call
func ParseGetByIdV10PostSessionSummariesSessionIDGetResponse(rsp *http.Response) (*GetByIdV10PostSessionSummariesSessionIDGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetByIdV10PostSessionSummariesSessionIDGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PostSessionSummaryDTO
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseMemoryControllerPatchRecurrenceTeamAccessResponse parses an HTTP response from a MemoryControllerPatchRecurrenceTeamAccessWithResponse call
func ParseMemoryControllerPatchRecurrenceTeamAccessResponse(rsp *http.Response) (*MemoryControllerPatchRecurrenceTeamAccessResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MemoryControllerPatchRecurrenceTeamAccessResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest PatchRecurrenceTeamAccessResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 500:
		var dest PatchRecurrenceTeamAccessResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON500 = &dest

	}

	return response, nil
}

// ParseGetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse parses an HTTP response from a GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetWithResponse call
func ParseGetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse(rsp *http.Response) (*GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetMeetingSuggestionsV10SuggestionsSessionIdRecurrenceIdGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest GetMeetingSuggestionsResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseListXraysV10XRayGetResponse parses an HTTP response from a ListXraysV10XRayGetWithResponse call
func ParseListXraysV10XRayGetResponse(rsp *http.Response) (*ListXraysV10XRayGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListXraysV10XRayGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayListResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseCreateXrayStep1V10XRayCreateStep1PostResponse parses an HTTP response from a CreateXrayStep1V10XRayCreateStep1PostWithResponse call
func ParseCreateXrayStep1V10XRayCreateStep1PostResponse(rsp *http.Response) (*CreateXrayStep1V10XRayCreateStep1PostResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateXrayStep1V10XRayCreateStep1PostResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayCreateStep1Response
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseCreateXrayStep2V10XRayCreateStep2PostResponse parses an HTTP response from a CreateXrayStep2V10XRayCreateStep2PostWithResponse call
func ParseCreateXrayStep2V10XRayCreateStep2PostResponse(rsp *http.Response) (*CreateXrayStep2V10XRayCreateStep2PostResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateXrayStep2V10XRayCreateStep2PostResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayCreateStep2Response
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseCreateXrayStep3V10XRayCreateStep3PostResponse parses an HTTP response from a CreateXrayStep3V10XRayCreateStep3PostWithResponse call
func ParseCreateXrayStep3V10XRayCreateStep3PostResponse(rsp *http.Response) (*CreateXrayStep3V10XRayCreateStep3PostResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &CreateXrayStep3V10XRayCreateStep3PostResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseListXrayTemplatesV10XRayTemplatesGetResponse parses an HTTP response from a ListXrayTemplatesV10XRayTemplatesGetWithResponse call
func ParseListXrayTemplatesV10XRayTemplatesGetResponse(rsp *http.Response) (*ListXrayTemplatesV10XRayTemplatesGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ListXrayTemplatesV10XRayTemplatesGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayTemplateListResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse parses an HTTP response from a GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetWithResponse call
func ParseGetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse(rsp *http.Response) (*GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayTemplateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseDeleteXrayV10XRayXrayIdDeleteResponse parses an HTTP response from a DeleteXrayV10XRayXrayIdDeleteWithResponse call
func ParseDeleteXrayV10XRayXrayIdDeleteResponse(rsp *http.Response) (*DeleteXrayV10XRayXrayIdDeleteResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &DeleteXrayV10XRayXrayIdDeleteResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest interface{}
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetXrayV10XRayXrayIdGetResponse parses an HTTP response from a GetXrayV10XRayXrayIdGetWithResponse call
func ParseGetXrayV10XRayXrayIdGetResponse(rsp *http.Response) (*GetXrayV10XRayXrayIdGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetXrayV10XRayXrayIdGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseUpdateXrayV10XRayXrayIdPatchResponse parses an HTTP response from a UpdateXrayV10XRayXrayIdPatchWithResponse call
func ParseUpdateXrayV10XRayXrayIdPatchResponse(rsp *http.Response) (*UpdateXrayV10XRayXrayIdPatchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &UpdateXrayV10XRayXrayIdPatchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseGetXrayNotificationsV10XRayXrayIdNotificationsGetResponse parses an HTTP response from a GetXrayNotificationsV10XRayXrayIdNotificationsGetWithResponse call
func ParseGetXrayNotificationsV10XRayXrayIdNotificationsGetResponse(rsp *http.Response) (*GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &GetXrayNotificationsV10XRayXrayIdNotificationsGetResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayNotificationResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseMarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse parses an HTTP response from a MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchWithResponse call
func ParseMarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse(rsp *http.Response) (*MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest MarkNotificationsSeenResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}

// ParseShareXrayAsTemplateV10XRayXrayIdSharePostResponse parses an HTTP response from a ShareXrayAsTemplateV10XRayXrayIdSharePostWithResponse call
func ParseShareXrayAsTemplateV10XRayXrayIdSharePostResponse(rsp *http.Response) (*ShareXrayAsTemplateV10XRayXrayIdSharePostResponse, error) {
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, err
	}

	response := &ShareXrayAsTemplateV10XRayXrayIdSharePostResponse{
		Body:         bodyBytes,
		HTTPResponse: rsp,
	}

	switch {
	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 200:
		var dest XRayTemplateResponse
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON200 = &dest

	case strings.Contains(rsp.Header.Get("Content-Type"), "json") && rsp.StatusCode == 422:
		var dest HTTPValidationError
		if err := json.Unmarshal(bodyBytes, &dest); err != nil {
			return nil, err
		}
		response.JSON422 = &dest

	}

	return response, nil
}
