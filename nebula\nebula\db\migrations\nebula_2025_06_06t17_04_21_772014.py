from piccolo.apps.migrations.auto.migration_manager import MigrationManager
from piccolo.table import Table


# dummy table used for raw sql migrations
class RawTable(Table):
    pass


ID = "2025-06-06T17:04:21:772014"
VERSION = "1.23.0"
DESCRIPTION = "Creates XRay-related tables: xrays, xray_doc_commits, xray_doc_commit_notifications, xray_doc_line_blames, xray_templates"

# Table names and class names
xray_table_name = "xrays"
xray_class_name = "XRay"

xray_doc_commit_table_name = "xray_doc_commits"
xray_doc_commit_class_name = "XRayDocumentCommit"

xray_doc_commit_notif_table_name = "xray_doc_commit_notifications"
xray_doc_commit_notif_class_name = "XRayDocumentCommitNotification"

xray_doc_line_blame_table_name = "xray_doc_line_blames"
xray_doc_line_blame_class_name = "XRayDocumentLineBlame"

xray_template_table_name = "xray_templates"
xray_template_class_name = "XRayTemplate"


async def forwards():
    manager = MigrationManager(
        migration_id=ID,
        app_name="nebula",
        description=DESCRIPTION,
    )

    # Create all tables using raw SQL for better control and reliability
    async def create_all_tables():
        from nebula.db.models.ai_feed import AIFeed

        # Create XRay table (without current_commit_id initially to avoid circular reference)
        await RawTable.raw(f"""
            CREATE TABLE {xray_table_name} (
                id BIGSERIAL PRIMARY KEY,
                owner_id BIGINT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                prompt TEXT NOT NULL,
                icon TEXT NOT NULL,
                short_summary TEXT,
                alert_channels JSONB NOT NULL DEFAULT jsonb_build_object('notifyAuthorEmail', false),
                is_active BOOLEAN NOT NULL DEFAULT true,
                visibility TEXT NOT NULL DEFAULT 'user' CHECK (visibility IN ('user', 'team')),
                xray_type TEXT NOT NULL DEFAULT 'build' CHECK (xray_type IN ('build', 'monitor', 'digest')),
                scope TEXT NOT NULL DEFAULT 'all' CHECK (scope IN ('personal', 'team', 'all')),
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            )
        """)

        # Create indexes for XRay table
        await RawTable.raw(f"CREATE INDEX ON {xray_table_name} (visibility)")
        await RawTable.raw(f"CREATE INDEX ON {xray_table_name} (xray_type)")
        await RawTable.raw(f"CREATE INDEX ON {xray_table_name} (scope)")

        # Create XRayDocumentCommit table
        await RawTable.raw(f"""
            CREATE TABLE {xray_doc_commit_table_name} (
                id BIGSERIAL PRIMARY KEY,
                xray_id BIGINT NOT NULL REFERENCES {xray_table_name}(id) ON DELETE CASCADE ON UPDATE CASCADE,
                author_id BIGINT NOT NULL,
                content TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            )
        """)

        # Create XRayDocumentCommitNotification table
        await RawTable.raw(f"""
            CREATE TABLE {xray_doc_commit_notif_table_name} (
                id BIGSERIAL PRIMARY KEY,
                user_id BIGINT NOT NULL,
                xray_doc_commit_id BIGINT NOT NULL REFERENCES {xray_doc_commit_table_name}(id) ON DELETE CASCADE ON UPDATE CASCADE,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                seen BOOLEAN NOT NULL DEFAULT false,
                source JSONB NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            )
        """)

        # Create XRayDocumentLineBlame table
        await RawTable.raw(f"""
            CREATE TABLE {xray_doc_line_blame_table_name} (
                id BIGSERIAL PRIMARY KEY,
                xray_id BIGINT NOT NULL REFERENCES {xray_table_name}(id) ON DELETE CASCADE ON UPDATE CASCADE,
                commit_id BIGINT NOT NULL REFERENCES {xray_doc_commit_table_name}(id) ON DELETE CASCADE ON UPDATE CASCADE,
                line_number INTEGER NOT NULL,
                content TEXT NOT NULL,
                author_id BIGINT,
                created_at TIMESTAMP NOT NULL DEFAULT NOW()
            )
        """)

        # Create compound index for XRayDocumentLineBlame
        await RawTable.raw(
            f"CREATE INDEX idx_{xray_doc_line_blame_table_name}_xray_id_line_number ON {xray_doc_line_blame_table_name} (xray_id, line_number)"
        )

        # Create XRayTemplate table
        await RawTable.raw(f"""
            CREATE TABLE {xray_template_table_name} (
                id BIGSERIAL PRIMARY KEY,
                owner_id BIGINT,
                xray_type TEXT NOT NULL DEFAULT 'build' CHECK (xray_type IN ('build', 'monitor', 'digest')),
                icon TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                prompt TEXT NOT NULL,
                short_summary TEXT NOT NULL,
                created_at TIMESTAMP NOT NULL DEFAULT NOW(),
                updated_at TIMESTAMP NOT NULL DEFAULT NOW()
            )
        """)

        # Create index for XRayTemplate table
        await RawTable.raw(f"CREATE INDEX ON {xray_template_table_name} (xray_type)")

        # Finally, add the current_commit_id column to XRay table (circular reference)
        await RawTable.raw(
            f"ALTER TABLE {xray_table_name} ADD COLUMN current_commit_id BIGINT REFERENCES {xray_doc_commit_table_name}(id) ON DELETE SET NULL ON UPDATE CASCADE"
        )

    manager.add_raw(create_all_tables)

    return manager


async def backwards():
    manager = MigrationManager(
        migration_id=ID,
        app_name="nebula",
        description=DESCRIPTION,
    )

    # Drop all tables using raw SQL
    async def drop_all_tables():
        # Drop the compound index first
        await RawTable.raw(
            f"DROP INDEX IF EXISTS idx_{xray_doc_line_blame_table_name}_xray_id_line_number"
        )

        # Drop tables in reverse order (due to foreign key dependencies)
        await RawTable.raw(f"DROP TABLE IF EXISTS {xray_template_table_name}")
        await RawTable.raw(f"DROP TABLE IF EXISTS {xray_doc_line_blame_table_name}")
        await RawTable.raw(f"DROP TABLE IF EXISTS {xray_doc_commit_notif_table_name}")
        await RawTable.raw(f"DROP TABLE IF EXISTS {xray_doc_commit_table_name}")
        await RawTable.raw(f"DROP TABLE IF EXISTS {xray_table_name}")

    manager.add_raw(drop_all_tables)

    return manager
