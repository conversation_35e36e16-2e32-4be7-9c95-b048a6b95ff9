# RecallaiBotMeetingLink


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**meeting_id** | **str** |  | 
**platform** | **str** |  | 

## Example

```python
from elio_client.models.recallai_bot_meeting_link import RecallaiBotMeetingLink

# TODO update the JSON string below
json = "{}"
# create an instance of RecallaiBotMeetingLink from a JSON string
recallai_bot_meeting_link_instance = RecallaiBotMeetingLink.from_json(json)
# print the JSON string representation of the object
print(RecallaiBotMeetingLink.to_json())

# convert the object into a dict
recallai_bot_meeting_link_dict = recallai_bot_meeting_link_instance.to_dict()
# create an instance of RecallaiBotMeetingLink from a dict
recallai_bot_meeting_link_from_dict = RecallaiBotMeetingLink.from_dict(recallai_bot_meeting_link_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


