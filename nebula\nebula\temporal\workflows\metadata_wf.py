from temporalio import workflow
from datetime import timedelta


with workflow.unsafe.imports_passed_through():
    from nebula.temporal.activities.metadata_act import MetadataActivities
    from nebula.temporal.types import (
        MetadataWorkflowParams,
        MetadataGenerationResult,
        ExtractMetadataInput,
        MetadataGenerationResultData,
        CreateMetadataInput,
        CreateElioUpdateInput,
    )


@workflow.defn(name="MetadataGeneration")
class MetadataWorkflow:
    """Workflow for metadata generation from meeting transcripts"""

    @workflow.run
    async def run(
        self, session_info: MetadataWorkflowParams
    ) -> MetadataGenerationResult:
        """
        Run the metadata generation workflow.

        Args:
            session_info: Session identification information containing session_id and recurrence_id

        Returns:
            MetadataGenerationResult containing the results of metadata generation
        """
        # Step 1: Get transcription batches
        transcription_result = await workflow.execute_activity(  # type: ignore
            MetadataActivities.get_transcription_batches,  # type: ignore
            session_info,
            start_to_close_timeout=timedelta(seconds=60),
        )

        if len(transcription_result.transcription_batches) == 0:
            workflow.logger.warn(
                f"No transcription batches found for session {session_info.session_id}, "
                f"recurrence {session_info.recurrence_id}"
            )
            return MetadataGenerationResult(
                done=True,
                message="No transcription batches found",
                data=None,
            )

        # Step 2: Get session data
        session_data = await workflow.execute_activity(  # type: ignore
            MetadataActivities.get_session_data,  # type: ignore
            session_info,
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 3: Process transcript
        processed_transcript = await workflow.execute_activity(  # type: ignore
            MetadataActivities.process_transcript,  # type: ignore
            transcription_result,
            start_to_close_timeout=timedelta(seconds=30),
        )

        if len(processed_transcript.transcript) <= 500:
            workflow.logger.info(
                f"Session {session_info.session_id} - skipping metadata generation "
                f"(transcript too short: {len(processed_transcript.transcript)} tokens)"
            )
            return MetadataGenerationResult(
                done=True,
                message="Transcript too short",
                data=None,
            )

        # Step 4: Extract metadata using LLM
        start_time = workflow.now().timestamp()
        metadata = await workflow.execute_activity(  # type: ignore
            MetadataActivities.extract_metadata,  # type: ignore
            ExtractMetadataInput(
                transcript=processed_transcript.transcript,
                session_data=session_data,
            ),
            start_to_close_timeout=timedelta(minutes=2),
        )

        processing_time = workflow.now().timestamp() - start_time

        # Check if metadata is empty or invalid
        if not metadata or not metadata.model_dump():
            workflow.logger.info(
                f"No metadata extracted for session {session_info.session_id}, "
                f"recurrence {session_info.recurrence_id}"
            )
            return MetadataGenerationResult(
                done=False,
                message="Metadata extraction failed - AI returned empty results",
                data=None,
            )

        # Step 5: Create metadata record with metadata
        metadata_record = await workflow.execute_activity(  # type: ignore
            MetadataActivities.create_metadata_record,  # type: ignore
            CreateMetadataInput(
                session_info=session_info,
                metadata=metadata,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 5: Update Elio about this metadata creation
        # TODO: This should be replaced with a more elegant solution?
        await workflow.execute_activity(  # type: ignore
            MetadataActivities.update_elio_about_metadata_creation,  # type: ignore
            CreateElioUpdateInput(
                session_info=session_info,
                metadata=metadata,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        workflow.logger.info(
            f"Completed metadata generation for session {session_info.session_id}, "
            f"recurrence {session_info.recurrence_id} in {processing_time:.2f} seconds"
        )

        return MetadataGenerationResult(
            done=True,
            message="Successfully generated metadata",
            data=MetadataGenerationResultData(
                metadata_id=metadata_record.metadata_id,
                metadata=metadata,
                processing_time_seconds=processing_time,
            ),
        )
