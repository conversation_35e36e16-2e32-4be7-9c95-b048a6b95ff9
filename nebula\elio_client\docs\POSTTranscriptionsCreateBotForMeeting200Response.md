# POSTTranscriptionsCreateBotForMeeting200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**calendar_meetings** | [**List[RecallaiCalendarMeeting]**](RecallaiCalendarMeeting.md) |  | 
**id** | **str** |  | 
**meeting_metadata** | [**RecallaiMeetingMetadata**](RecallaiMeetingMetadata.md) |  | 
**meeting_participants** | [**List[RecallaiMeetingParticipant]**](RecallaiMeetingParticipant.md) |  | 
**meeting_url** | [**RecallaiBotMeetingLink**](RecallaiBotMeetingLink.md) |  | 
**video_url** | **str** |  | 

## Example

```python
from elio_client.models.post_transcriptions_create_bot_for_meeting200_response import POSTTranscriptionsCreateBotForMeeting200Response

# TODO update the JSON string below
json = "{}"
# create an instance of POSTTranscriptionsCreateBotForMeeting200Response from a JSON string
post_transcriptions_create_bot_for_meeting200_response_instance = POSTTranscriptionsCreateBotForMeeting200Response.from_json(json)
# print the JSON string representation of the object
print(POSTTranscriptionsCreateBotForMeeting200Response.to_json())

# convert the object into a dict
post_transcriptions_create_bot_for_meeting200_response_dict = post_transcriptions_create_bot_for_meeting200_response_instance.to_dict()
# create an instance of POSTTranscriptionsCreateBotForMeeting200Response from a dict
post_transcriptions_create_bot_for_meeting200_response_from_dict = POSTTranscriptionsCreateBotForMeeting200Response.from_dict(post_transcriptions_create_bot_for_meeting200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


