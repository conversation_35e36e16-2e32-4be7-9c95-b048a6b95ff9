package meetings

import (
	"context"

	"encore.app/hubble"
	hubble_api "encore.app/hubble/api"
	"encore.app/meetings/api"
	"encore.app/meetings/database"
	"encore.app/shared"
	"encore.dev/beta/auth"
	"encore.dev/rlog"
	"github.com/samber/lo"
)

// UpsertMeetingSuggestions stores or updates meeting suggestions for a session (called by Nebula)
//
//encore:api auth method=POST tag:trixta
func (m *Meetings) UpsertMeetingSuggestions(ctx context.Context, req *api.UpsertMeetingSuggestionsRequest) (*api.UpsertMeetingSuggestionsResponse, error) {
	rlog.Debug("UpsertMeetingSuggestions called", "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID, "suggestionCount", len(req.Suggestions))

	err := database.UpsertMeetingSuggestions(ctx, req.SessionID, req.RecurrenceID, req.Suggestions)
	if err != nil {
		rlog.Error("Failed to upsert meeting suggestions", "err", err, "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	rlog.Info("Successfully upserted meeting suggestions", "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID, "count", len(req.Suggestions))

	return &api.UpsertMeetingSuggestionsResponse{
		Count: len(req.Suggestions),
	}, nil
}

// GetMeetingSuggestionsByUserInternal retrieves meeting suggestions for a specific user (internal service calls)
//
//encore:api auth method=GET tag:trixta
func (m *Meetings) GetMeetingSuggestionsByUserInternal(ctx context.Context, req *api.GetMeetingSuggestionsInternalRequest) (*api.GetMeetingSuggestionsResponse, error) {
	rlog.Debug("GetMeetingSuggestionsByUserInternal called", "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID, "userID", req.UserID)

	suggestionRows, err := database.GetMeetingSuggestionRowsByUser(ctx, req.SessionID, req.RecurrenceID, req.UserID)
	if err != nil {
		rlog.Error("Failed to get meeting suggestions by user", "err", err, "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID, "userID", req.UserID)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	// Ensure suggestions is never nil
	if suggestionRows == nil {
		suggestionRows = []database.MeetingSuggestionRow{}
	}

	// Convert to SuggestionResponseDTO
	suggestions := make([]api.SuggestionResponseDTO, len(suggestionRows))
	for i, row := range suggestionRows {
		suggestions[i] = api.SuggestionResponseDTO{
			Id:         shared.FormatInt(row.Id),
			Content:    row.Prompt,
			Category:   row.Category,
			IsPersonal: true, // All suggestions from this endpoint are personal (user-specific)
			CreatedAt:  row.CreatedAt,
		}
	}

	rlog.Debug("Successfully retrieved meeting suggestions by user", "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID, "userID", req.UserID, "count", len(suggestions))

	return &api.GetMeetingSuggestionsResponse{
		Data: struct {
			Suggestions []api.SuggestionResponseDTO `json:"suggestions"`
			Total       int                         `json:"total"`
		}{
			Suggestions: suggestions,
			Total:       len(suggestions),
		},
		Message: "Successfully retrieved meeting suggestions",
	}, nil
}

// GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user
//
//encore:api auth method=GET path=/v1.0/meetings/:sessionID/:recurrenceID/suggestions/user
func (m *Meetings) GetMeetingSuggestionsByUser(ctx context.Context, sessionID, recurrenceID string, req *api.GetMeetingSuggestionsRequest) (*api.GetMeetingSuggestionsResponse, error) {
	userID, authed := auth.UserID()
	if !authed {
		rlog.Error("User not authenticated")
		return nil, shared.B().WithCode(shared.Unauthorized)
	}

	// Check if user has access to the session recurrence
	if err := hubble.IsAuthorized(ctx, &hubble_api.IsAuthorizedRequest{
		Sub: shared.GetUserSubjectWithRole(),
		Obj: shared.GetSessionRecurrenceObjectByID(sessionID, recurrenceID),
		Act: shared.AuthPolicyActionGet,
	}); err != nil {
		rlog.Error("User not authorized to access session", "sessionID", sessionID, "userID", string(userID), "err", err)
		return nil, shared.B().WithCode(shared.UnauthorizedToAccessSessionErrorCode)
	}

	rlog.Debug("GetMeetingSuggestionsByUser called", "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", string(userID))

	suggestionRows, err := database.GetMeetingSuggestionRowsByUser(ctx, sessionID, recurrenceID, string(userID))
	if err != nil {
		rlog.Error("Failed to get meeting suggestions by user", "err", err, "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", string(userID))
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	// Ensure suggestions is never nil
	if suggestionRows == nil {
		suggestionRows = []database.MeetingSuggestionRow{}
	}

	// Convert to SuggestionResponseDTO
	suggestions := make([]api.SuggestionResponseDTO, len(suggestionRows))
	for i, row := range suggestionRows {
		suggestions[i] = api.SuggestionResponseDTO{
			Id:         shared.FormatInt(row.Id),
			Content:    row.Prompt,
			Category:   row.Category,
			IsPersonal: true, // All suggestions from this endpoint are personal (user-specific)
			CreatedAt:  row.CreatedAt,
		}
	}

	rlog.Debug("Successfully retrieved meeting suggestions by user", "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", string(userID), "count", len(suggestions))

	return &api.GetMeetingSuggestionsResponse{
		Data: struct {
			Suggestions []api.SuggestionResponseDTO `json:"suggestions"`
			Total       int                         `json:"total"`
		}{
			Suggestions: suggestions,
			Total:       len(suggestions),
		},
		Message: "Successfully retrieved meeting suggestions",
	}, nil
}

// GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session (for future use)
//
//encore:api private method=GET tag:trixta
func (m *Meetings) GetMeetingSuggestionsBySession(ctx context.Context, req *api.GetSessionSuggestionsRequest) (*api.GetSessionSuggestionsResponse, error) {
	rlog.Debug("GetMeetingSuggestionsBySession called", "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID)

	suggestionRows, err := database.GetMeetingSuggestionsBySession(ctx, req.SessionID, req.RecurrenceID)
	if err != nil {
		rlog.Error("Failed to get meeting suggestions by session", "err", err, "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	// Convert rows to DTOs
	suggestions := lo.Map(suggestionRows, func(row database.MeetingSuggestionRow, _ int) api.MeetingSuggestionDTO {
		return row.ToMeetingSuggestionDTO()
	})

	// Ensure suggestions is never nil
	if suggestions == nil {
		suggestions = []api.MeetingSuggestionDTO{}
	}

	rlog.Debug("Successfully retrieved meeting suggestions by session", "sessionID", req.SessionID, "recurrenceID", req.RecurrenceID, "count", len(suggestions))

	return &api.GetSessionSuggestionsResponse{
		Suggestions: suggestions,
	}, nil
} 