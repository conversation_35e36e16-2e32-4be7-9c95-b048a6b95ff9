package xray

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"strconv"

	"encore.app/meetings"
	meetings_api "encore.app/meetings/api"
	"encore.app/shared"
	nebula_api "encore.app/wormhole/services/nebula-api"
	"encore.app/xray/api"
	config2 "encore.app/xray/config"
	"encore.dev/beta/auth"
	"encore.dev/config"
	"encore.dev/rlog"
	"github.com/samber/lo"
)

//encore:service
type XRay struct {
	NebulaClient *nebula_api.Client
}

var Config = config.Load[*config2.XRayConfig]()

func initXRay() (*XRay, error) {
	xray := &XRay{}

	nebulaClient := nebula_api.CreateNebulaClient(
		Config.Nebula.HostUrl,
		Config.Nebula.BasicAuthUsername,
		Config.Nebula.BasicAuthPassword,
	)
	xray.NebulaClient = nebulaClient

	return xray, nil
}

//encore:api auth method=POST path=/v1.0/xrays/generate/prompt
func (x *XRay) GenerateXRayPrompt(ctx context.Context, req *api.GenerateXRayPromptRequest) (*api.GenerateXRayPromptResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.CreateXrayStep1V10XRayCreateStep1PostParams{
		XUserId: data.UserID,
	}

	body := nebula_api.CreateXrayStep1V10XRayCreateStep1PostJSONRequestBody{
		Description: req.Description,
	}

	httpResp, err := x.NebulaClient.CreateXrayStep1V10XRayCreateStep1Post(ctx, params, body)
	if err != nil {
		rlog.Error("failed to call nebula CreateXrayStep1", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula CreateXrayStep1 returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayCreateStep1Response
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GenerateXRayPromptResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		result.Data = &api.GenerateXRayPromptResponseData{
			Type: nebulaResp.Data.XrayType,
			Prompt:   nebulaResp.Data.Prompt,
		}
	}

	return result, nil
}

//encore:api auth method=POST path=/v1.0/xrays/generate/info
func (x *XRay) GenerateXRayInfo(ctx context.Context, req *api.GenerateXRayInfoRequest) (*api.GenerateXRayInfoResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.CreateXrayStep2V10XRayCreateStep2PostParams{
		XUserId: data.UserID,
	}

	body := nebula_api.CreateXrayStep2V10XRayCreateStep2PostJSONRequestBody{
		XrayType: req.Type,
		Prompt:   req.Prompt,
	}

	httpResp, err := x.NebulaClient.CreateXrayStep2V10XRayCreateStep2Post(ctx, params, body)
	if err != nil {
		rlog.Error("failed to call nebula CreateXrayStep2", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula CreateXrayStep2 returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayCreateStep2Response
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GenerateXRayInfoResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		result.Data = &api.GenerateXRayInfoResponseData{
			Title:        nebulaResp.Data.Title,
			Icon:         nebulaResp.Data.Emoji,
			ShortSummary: nebulaResp.Data.ShortSummary,
		}
	}

	return result, nil
}

//encore:api auth method=POST path=/v1.0/xrays
func (x *XRay) Create(ctx context.Context, req *api.CreateXRayRequest) (*api.CreateXRayResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.CreateXrayStep3V10XRayCreateStep3PostParams{
		XUserId: data.UserID,
	}

	body := nebula_api.CreateXrayStep3V10XRayCreateStep3PostJSONRequestBody{
		Description:  req.Description,
		XrayType:     req.Type,
		Prompt:       req.Prompt,
		Title:        req.Title,
		Emoji:        req.Icon,
		ShortSummary: req.ShortSummary,
		Frequency:    lo.ToPtr(req.Frequency),
		Timezone: 	  lo.ToPtr("UTC"),
	}

	if req.AlertChannels != nil {
		body.AlertChannels = &req.AlertChannels
	}

	httpResp, err := x.NebulaClient.CreateXrayStep3V10XRayCreateStep3Post(ctx, params, body)
	if err != nil {
		rlog.Error("failed to call nebula CreateXrayStep3", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula CreateXrayStep3 returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.CreateXRayResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		xrayDTO := convertNebulaXRayToAPI(nebulaResp.Data.Xray)
		result.Data = &xrayDTO
	}

	return result, nil
}

//encore:api auth method=GET path=/v1.0/xrays
func (x *XRay) ListXRays(ctx context.Context, req *api.ListXRaysRequest) (*api.ListXRaysResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.ListXraysV10XRayGetParams{
		XUserId: data.UserID,
	}

	// Set optional query parameters
	if req.Limit != 0 {
		params.Limit = &req.Limit
	}
	if req.Offset != 0 {
		params.Offset = &req.Offset
	}
	if req.TypeFilter != "" {
		typeFilter := nebula_api.XRayTypeFilter(req.TypeFilter)
		params.TypeFilter = &typeFilter
	}
	if req.SortBy != "" {
		sortBy := nebula_api.XRaySortBy(req.SortBy)
		params.SortBy = &sortBy
	}

	httpResp, err := x.NebulaClient.ListXraysV10XRayGet(ctx, params)
	if err != nil {
		rlog.Error("failed to call nebula ListXrays", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula ListXrays returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayListResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.ListXRaysResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		xrays := make([]api.XRayDTO, len(nebulaResp.Data.Xrays))
		for i, xray := range nebulaResp.Data.Xrays {
			xrays[i] = convertNebulaXRayToAPI(xray)
		}

		result.Data = &api.ListXRaysResponseData{
			Xrays:   	xrays,
			TotalCount: nebulaResp.Data.Total,
			HasMore: 	nebulaResp.Data.HasMore,
		}
	}

	return result, nil
}

//encore:api auth method=GET path=/v1.0/xrays/id/:xrayID
func (x *XRay) GetXRay(ctx context.Context, xrayID string) (*api.GetXRayResponse, error) {
	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.GetXrayV10XRayXrayIdGetParams{
		XUserId: data.UserID,
	}

	xrayIDInt := shared.ParseInt(xrayID)
	if xrayIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid xrayID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	httpResp, err := x.NebulaClient.GetXrayV10XRayXrayIdGet(ctx, int(xrayIDInt), params)
	if err != nil {
		rlog.Error("failed to call nebula GetXray", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode == http.StatusNotFound {
		return nil, shared.HttpResponseError("X-Ray not found", shared.UserNotFound, http.StatusNotFound)
	}

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula GetXray returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GetXRayResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		xrayDTO := convertNebulaXRayToAPI(nebulaResp.Data.Xray)
		result.Data = &xrayDTO
	}

	return result, nil
}

//encore:api auth method=PATCH path=/v1.0/xrays/id/:xrayID
func (x *XRay) UpdateXRay(ctx context.Context, xrayID string, req *api.UpdateXRayRequest) (*api.GetXRayResponse, error) {
	// Validate request if it has a Validate method
	if req != nil {
		if err := req.Validate(); err != nil {
			return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
		}
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Parse and validate xrayID
	xrayIDInt := shared.ParseInt(xrayID)
	if xrayIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid xrayID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	params := &nebula_api.UpdateXrayV10XRayXrayIdPatchParams{
		XUserId: data.UserID,
	}

	// Build request body dynamically to avoid sending null values
	requestBody := make(map[string]interface{})

	if req != nil {
		if req.Title != nil {
			requestBody["title"] = *req.Title
		}
		if req.Prompt != nil {
			requestBody["prompt"] = *req.Prompt
		}
		if req.Icon != nil {
			requestBody["icon"] = *req.Icon
		}
		if req.AlertChannels != nil {
			requestBody["alertChannels"] = *req.AlertChannels
		}
		if req.IsActive != nil {
			requestBody["isActive"] = *req.IsActive
		}
		if req.Frequency != nil {
			requestBody["frequency"] = *req.Frequency
		}
		if len(requestBody) == 0 {
			return nil, shared.HttpResponseError("No fields to update", shared.ValidationErrorCode, http.StatusBadRequest)
		}
	}

	rlog.Debug("requestBody", "requestBody", requestBody)

	// Marshal to JSON manually
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		rlog.Error("failed to marshal request body", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Call Nebula API with raw JSON body
	httpResp, err := x.NebulaClient.UpdateXrayV10XRayXrayIdPatchWithBody(ctx, int(xrayIDInt), params, "application/json", bytes.NewReader(jsonBody))
	if err != nil {
		rlog.Error("failed to call nebula UpdateXray", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode == http.StatusNotFound {
		return nil, shared.HttpResponseError("X-Ray not found", shared.UserNotFound, http.StatusNotFound)
	}

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula UpdateXray returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GetXRayResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		xrayDTO := convertNebulaXRayToAPI(nebulaResp.Data.Xray)
		result.Data = &xrayDTO
	}

	return result, nil
}

//encore:api auth method=DELETE path=/v1.0/xrays/:xrayID
func (x *XRay) DeleteXRay(ctx context.Context, xrayID string) (*api.DeleteXRayResponse, error) {
	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Parse and validate xrayID
	xrayIDInt := shared.ParseInt(xrayID)
	if xrayIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid xrayID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Call Nebula API
	params := &nebula_api.DeleteXrayV10XRayXrayIdDeleteParams{
		XUserId: data.UserID,
	}

	httpResp, err := x.NebulaClient.DeleteXrayV10XRayXrayIdDelete(ctx, int(xrayIDInt), params)
	if err != nil {
		rlog.Error("failed to call nebula DeleteXray", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode == http.StatusNotFound {
		return nil, shared.HttpResponseError("X-Ray not found", shared.UserNotFound, http.StatusNotFound)
	}

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula DeleteXray returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Validate response body is valid JSON TODO: fix in nebula
	var responseBody map[string]interface{}
	if err := json.NewDecoder(httpResp.Body).Decode(&responseBody); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response (delete endpoint returns empty object)
	result := &api.DeleteXRayResponse{
		Success: true,
		Message: "X-Ray deleted successfully",
	}

	return result, nil
}

//encore:api auth method=POST path=/v1.0/xrays/id/:xrayID/share
func (x *XRay) ShareXRayAsTemplate(ctx context.Context, xrayID string) (*api.GetXRayResponse, error) {
	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.ShareXrayAsTemplateV10XRayXrayIdSharePostParams{
		XUserId: data.UserID,
	}

	xrayIDInt := shared.ParseInt(xrayID)
	if xrayIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid xrayID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	httpResp, err := x.NebulaClient.ShareXrayAsTemplateV10XRayXrayIdSharePost(ctx, int(xrayIDInt), params)
	if err != nil {
		rlog.Error("failed to call nebula ShareXrayAsTemplate", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode == http.StatusNotFound {
		return nil, shared.HttpResponseError("X-Ray not found", shared.UserNotFound, http.StatusNotFound)
	}

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula ShareXrayAsTemplate returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayTemplateResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GetXRayResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		template := nebulaResp.Data.Template
		xray := api.XRayDTO{
			ID:           template.Id,
			OwnerID:      template.OwnerId,
			Title:        template.Title,
			Description:  template.Description,
			Prompt:       template.Prompt,
			Icon:         template.Icon,
			ShortSummary: template.ShortSummary,
			Type:     	  template.XrayType,
			CreatedAt:    template.CreatedAt,
			UpdatedAt:    template.UpdatedAt,
		}

		result.Data = &xray
	}

	return result, nil
}

//encore:api auth method=GET path=/v1.0/xrays/id/:xrayID/notifications
func (x *XRay) GetXRayNotifications(ctx context.Context, xrayID string, req *api.GetXRayNotificationsRequest) (*api.GetXRayNotificationsResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Parse and validate xrayID
	xrayIDInt := shared.ParseInt(xrayID)
	if xrayIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid xrayID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Call Nebula API
	params := &nebula_api.GetXrayNotificationsV10XRayXrayIdNotificationsGetParams{
		XUserId: data.UserID,
	}

	// Set optional query parameters
	if req.Limit != 0 {
		params.Limit = &req.Limit
	}
	if req.Offset != 0 {
		params.Offset = &req.Offset
	}

	httpResp, err := x.NebulaClient.GetXrayNotificationsV10XRayXrayIdNotificationsGet(ctx, int(xrayIDInt), params)
	if err != nil {
		rlog.Error("failed to call nebula GetXrayNotifications", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula GetXrayNotifications returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayNotificationResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GetXRayNotificationsResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		notifications := make([]api.XRayNotificationDTO, len(nebulaResp.Data.Notifications))
		for i, notification := range nebulaResp.Data.Notifications {
			sourceBytes, err := json.Marshal(notification.Source)
			if err != nil {
				rlog.Error("failed to marshal notification source", "error", err)
				return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
			}

			source := json.RawMessage(sourceBytes)

			notifications[i] = api.XRayNotificationDTO{
				ID:              notification.Id,
				XrayDocCommitID: notification.XrayDocCommitId,
				UserID:          notification.UserId,
				Seen:            notification.Seen,
				Content:         notification.Content,
				CreatedAt:       notification.CreatedAt,
				UpdatedAt:       notification.UpdatedAt,
				Source:          &source,
			}
		}

		result.Data = &api.GetXRayNotificationsResponseData{
			Notifications: notifications,
			TotalCount:    nebulaResp.Data.Total,
			HasMore:       nebulaResp.Data.HasMore,
		}
	}

	return result, nil
}

//encore:api auth method=PATCH path=/v1.0/xrays/id/:xrayID/notifications/mark-seen
func (x *XRay) MarkXRayNotificationsSeen(ctx context.Context, xrayID string) (*api.MarkXRayNotificationsSeenResponse, error) {
	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Parse and validate xrayID
	xrayIDInt := shared.ParseInt(xrayID)
	if xrayIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid xrayID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Call Nebula API
	params := &nebula_api.MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatchParams{
		XUserId: data.UserID,
	}

	httpResp, err := x.NebulaClient.MarkXrayNotificationsSeenV10XRayXrayIdNotificationsMarkSeenPatch(ctx, int(xrayIDInt), params)
	if err != nil {
		rlog.Error("failed to call nebula MarkXrayNotificationsSeen", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula MarkXrayNotificationsSeen returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.MarkNotificationsSeenResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.MarkXRayNotificationsSeenResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		result.Data = &api.MarkXRayNotificationsSeenResponseData{
			MarkedCount: nebulaResp.Data.MarkedCount,
		}
	}

	return result, nil
}

//encore:api auth method=GET path=/v1.0/xray-templates/:templateID
func (x *XRay) GetXRayTemplate(ctx context.Context, templateID string) (*api.GetXRayTemplateResponse, error) {
	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Parse and validate templateID
	templateIDInt := shared.ParseInt(templateID)
	if templateIDInt == 0 {
		return nil, shared.HttpResponseError("Invalid templateID", shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Call Nebula API
	params := &nebula_api.GetXrayTemplateV10XRayXrayTemplatesTemplateIdGetParams{
		XUserId: data.UserID,
	}

	httpResp, err := x.NebulaClient.GetXrayTemplateV10XRayXrayTemplatesTemplateIdGet(ctx, int(templateIDInt), params)
	if err != nil {
		rlog.Error("failed to call nebula GetXrayTemplate", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode == http.StatusNotFound {
		return nil, shared.HttpResponseError("Template not found", shared.UserNotFound, http.StatusNotFound)
	}

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula GetXrayTemplate returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayTemplateResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.GetXRayTemplateResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		XrayTemplate := api.XRayTemplateDTO{
			ID:           nebulaResp.Data.Template.Id,
			OwnerID:      nebulaResp.Data.Template.OwnerId,
			Title:        nebulaResp.Data.Template.Title,
			Description:  nebulaResp.Data.Template.Description,
			Prompt:       nebulaResp.Data.Template.Prompt,
			Icon:         nebulaResp.Data.Template.Icon,
			ShortSummary: nebulaResp.Data.Template.ShortSummary,
			Type:     nebulaResp.Data.Template.XrayType,
			CreatedAt:    nebulaResp.Data.Template.CreatedAt,
			UpdatedAt:    nebulaResp.Data.Template.UpdatedAt,
		}
		result.Data = &XrayTemplate

		// If it's a user generated template, we fetch the owner and add it to the xray template struct
		if nebulaResp.Data.Template.OwnerId != 0 {
			owner, err := meetings.GetUserByID(ctx, &meetings_api.GetUserByIDRequest{
				UserID: strconv.Itoa(nebulaResp.Data.Template.OwnerId),
			})
			if err != nil {
				rlog.Error("failed to get xray template owner by user id", "error", err)
				return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
			}

			result.Data.Owner = owner
		}
	}

	return result, nil
}

//encore:api auth method=GET path=/v1.0/xray-templates
func (x *XRay) ListXRayTemplates(ctx context.Context, req *api.ListXRaysRequest) (*api.ListXRayTemplatesResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.ValidationErrorCode, http.StatusBadRequest)
	}

	// Get user ID from auth context
	data := auth.Data().(*shared.DecodedToken)

	// Call Nebula API
	params := &nebula_api.ListXrayTemplatesV10XRayTemplatesGetParams{
		XUserId: data.UserID,
	}

	// Set optional query parameters
	if req.Limit != 0 {
		params.Limit = &req.Limit
	}
	if req.Offset != 0 {
		params.Offset = &req.Offset
	}

	httpResp, err := x.NebulaClient.ListXrayTemplatesV10XRayTemplatesGet(ctx, params)
	if err != nil {
		rlog.Error("failed to call nebula ListXrayTemplates", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}
	defer httpResp.Body.Close()

	if httpResp.StatusCode != http.StatusOK {
		rlog.Error("nebula ListXrayTemplates returned non-200", "status", httpResp.StatusCode)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Parse response
	var nebulaResp nebula_api.XRayTemplateListResponse
	if err := json.NewDecoder(httpResp.Body).Decode(&nebulaResp); err != nil {
		rlog.Error("failed to decode nebula response", "error", err)
		return nil, shared.HttpResponseError("internal server error", shared.InternalServerError, http.StatusInternalServerError)
	}

	// Convert response
	result := &api.ListXRayTemplatesResponse{
		Success: nebulaResp.Success,
		Message: nebulaResp.Message,
	}

	if nebulaResp.Data != nil {
		templates := make([]api.XRayTemplateDTO, len(nebulaResp.Data.Templates))
		for i, template := range nebulaResp.Data.Templates {
			templates[i] = api.XRayTemplateDTO{
				ID:           template.Id,
				OwnerID:      template.OwnerId,
				Title:        template.Title,
				Description:  template.Description,
				Prompt:       template.Prompt,
				Icon:         template.Icon,
				ShortSummary: template.ShortSummary,
				Type:     template.XrayType,
				CreatedAt:    template.CreatedAt,
				UpdatedAt:    template.UpdatedAt,
			}
		}

		result.Data = &api.ListXRayTemplatesResponseData{
			Templates: 	templates,
			TotalCount: nebulaResp.Data.Total,
			HasMore:   	nebulaResp.Data.HasMore,
		}
	}

	return result, nil
}

// Helper function to convert Nebula XRay to API XRay
func convertNebulaXRayToAPI(nebulaXRay nebula_api.XRayDTO) api.XRayDTO {
	apiXRay := api.XRayDTO{
		ID:            nebulaXRay.Id,
		OwnerID:       nebulaXRay.OwnerId,
		Title:         nebulaXRay.Title,
		Description:   nebulaXRay.Description,
		Prompt:        nebulaXRay.Prompt,
		Icon:          nebulaXRay.Icon,
		ShortSummary:  nebulaXRay.ShortSummary,
		AlertChannels: nebulaXRay.AlertChannels,
		IsActive:      nebulaXRay.IsActive,
		Visibility:    nebulaXRay.Visibility,
		Type:          nebulaXRay.XrayType,
		Scope:         nebulaXRay.Scope,
		CreatedAt:     nebulaXRay.CreatedAt,
		UpdatedAt:     nebulaXRay.UpdatedAt,
		Frequency:     nebulaXRay.Frequency,
		LastDigestAt:  nebulaXRay.LastDigestAt,
	}

	// Handle optional fields
	if nebulaXRay.CurrentCommitId != nil {
		apiXRay.CurrentCommitID = nebulaXRay.CurrentCommitId
	}

	if nebulaXRay.CurrentCommit != nil {
		apiXRay.CurrentCommit = &api.XRayDocCommit{
			ID:        nebulaXRay.CurrentCommit.Id,
			XrayID:    nebulaXRay.CurrentCommit.XrayId,
			Content:   nebulaXRay.CurrentCommit.Content,
			AuthorID:  nebulaXRay.CurrentCommit.AuthorId,
			CreatedAt: nebulaXRay.CurrentCommit.CreatedAt,
			UpdatedAt: nebulaXRay.CurrentCommit.UpdatedAt,
		}
	}

	if nebulaXRay.UnreadNotificationsCount != nil {
		apiXRay.UnreadNotificationsCount = nebulaXRay.UnreadNotificationsCount
	}
	return apiXRay
}
