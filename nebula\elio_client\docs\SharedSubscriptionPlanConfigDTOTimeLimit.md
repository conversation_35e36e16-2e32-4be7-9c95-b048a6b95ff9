# SharedSubscriptionPlanConfigDTOTimeLimit


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**enabled** | **bool** |  | 
**max** | **int** |  | 

## Example

```python
from elio_client.models.shared_subscription_plan_config_dto_time_limit import SharedSubscriptionPlanConfigDTOTimeLimit

# TODO update the JSON string below
json = "{}"
# create an instance of SharedSubscriptionPlanConfigDTOTimeLimit from a JSON string
shared_subscription_plan_config_dto_time_limit_instance = SharedSubscriptionPlanConfigDTOTimeLimit.from_json(json)
# print the JSON string representation of the object
print(SharedSubscriptionPlanConfigDTOTimeLimit.to_json())

# convert the object into a dict
shared_subscription_plan_config_dto_time_limit_dict = shared_subscription_plan_config_dto_time_limit_instance.to_dict()
# create an instance of SharedSubscriptionPlanConfigDTOTimeLimit from a dict
shared_subscription_plan_config_dto_time_limit_from_dict = SharedSubscriptionPlanConfigDTOTimeLimit.from_dict(shared_subscription_plan_config_dto_time_limit_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


