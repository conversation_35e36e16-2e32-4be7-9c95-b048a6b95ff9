package billing_test

import (
	"context"
	"encore.app/billing"
	api "encore.app/billing/api"
	"encore.app/billing/mocks"
	"encore.app/lobbies"
	"encore.app/meetings"
	"encore.app/shared"
	"encore.dev/et"
	"github.com/jmoiron/sqlx"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
	"os"
	"path"
	"testing"
)

func setUpBillingTest() {
	et.EnableServiceInstanceIsolation()
	testDB, err := et.NewTestDatabase(context.Background(), "mars")
	if err != nil {
		panic(err)
	}

	testDBX := sqlx.NewDb(testDB.Stdlib(), "pgx")
	lobbies.MarsDB = testDBX
	meetings.MarsDB = testDBX
	billing.MarsDB = testDBX

	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("seed_users.sql"))
	// Seeds both user_subscription_plans and subscription_plan_configs
	shared.SeedDBTestDataFromFixtureX(testDBX, shared.GetFixtureByName("user_subscription_plans.sql"))
}

var listProductsMock = (func() *api.ListProductsResponse {
	listProductsMock, err := mocks.GetListProductsMock("..")
	if err != nil {
		panic(err)
	}

	return listProductsMock
})()

func TestListPlans(t *testing.T) {
	setUpBillingTest()

	listProductsMockOnlyPremiumProduct := lo.Filter(listProductsMock.Products, func(product shared.BillingProductDTO, _ int) bool {
		return product.Name == "Premium"
	})

	t.Run("Lists plans", func(t *testing.T) {
		// Arrange

		// Mock the ListProducts endpoint
		// For simplicity in testing, make listProducts return only the "premium" product (ID: pro_01hdd98039vq8qsd44rx9gvybh)
		// it would usually return all the products
		et.MockEndpoint(billing.ListProducts, func(ctx context.Context, req api.ListProductsRequest) (*api.ListProductsResponse, error) {
			return &api.ListProductsResponse{
				Products: listProductsMockOnlyPremiumProduct,
			}, nil
		})

		// Act
		listPlansRes, err := billing.ListPlans(context.Background())
		assert.NoError(t, err)

		// Assert
		resJSON := shared.MarshalResponse(t, listPlansRes)
		expectedResJSON := readFixture(t, "list-plans.json")
		shared.AssertEqualJSON(t, expectedResJSON, resJSON)
	})
}

func readFixture(t *testing.T, fixtureName string) []byte {
	expectedJson, err := os.ReadFile(path.Join("./", "fixtures", fixtureName))
	if err != nil {
		t.Fatal(err)
	}

	return expectedJson
}
