package mocks

import (
	"encoding/json"
	"encore.app/billing/api"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
)

// GetListProductsMock list-products-mock.json is a mock file generated from the real Paddle API response
// of the ListProducts endpoint
func GetListProductsMock(pathToElioRoot string) (*api.ListProductsResponse, error) {
	filePath := filepath.Join(pathToElioRoot, "/billing/mocks/list-products-mock.json")

	jsonFile, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %s", err)
	}
	defer jsonFile.Close()

	byteValue, err := ioutil.ReadAll(jsonFile)
	if err != nil {
		return nil, fmt.Errorf("error reading file: %s", err)
	}

	var listProductsRes api.ListProductsResponse

	err = json.Unmarshal(byteValue, &listProductsRes)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling JSON: %s", err)
	}

	return &listProductsRes, nil
}

// GetListTransactionsMock list-transactions-mock.json is a mock file generated from the real Paddle API response
// of the GetTransactions endpoint
func GetListTransactionsMock(pathToElioRoot string) (*api.ListTransactionsByCustomerIDResponse, error) {
	filePath := filepath.Join(pathToElioRoot, "/billing/mocks/list-transactions-mock.json")

	jsonFile, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %s", err)
	}
	defer jsonFile.Close()

	byteValue, err := ioutil.ReadAll(jsonFile)
	if err != nil {
		return nil, fmt.Errorf("error reading file: %s", err)
	}

	var listTransactionsRes api.ListTransactionsByCustomerIDResponse

	err = json.Unmarshal(byteValue, &listTransactionsRes)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling JSON: %s", err)
	}

	return &listTransactionsRes, nil
}

// GetGetSubscriptionUpdatePaymentMethodTransactionMock get-subscription-update-payment-method-transaction-mock.json is a mock file generated from the real Paddle API response
// of the GetSubscriptionUpdatePaymentMethodTransaction endpoint
func GetGetSubscriptionUpdatePaymentMethodTransactionMock(pathToElioRoot string) (*api.GetSubscriptionUpdatePaymentMethodTransactionResponse, error) {
	filePath := filepath.Join(pathToElioRoot, "/billing/mocks/get-subscription-update-payment-method-transaction-mock.json")

	jsonFile, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %s", err)
	}
	defer jsonFile.Close()

	byteValue, err := ioutil.ReadAll(jsonFile)
	if err != nil {
		return nil, fmt.Errorf("error reading file: %s", err)
	}

	var getSubscriptionUpdatePaymentMethodTransactionRes api.GetSubscriptionUpdatePaymentMethodTransactionResponse

	err = json.Unmarshal(byteValue, &getSubscriptionUpdatePaymentMethodTransactionRes)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling JSON: %s", err)
	}

	return &getSubscriptionUpdatePaymentMethodTransactionRes, nil
}

func GetGetInvoiceByTransactionIDMock(pathToElioRoot string) (*api.GetInvoiceByTransactionIDResponse, error) {
	filePath := filepath.Join(pathToElioRoot, "/billing/mocks/get-invoice-by-transaction-id-mock.json")

	jsonFile, err := os.Open(filePath)
	if err != nil {
		return nil, fmt.Errorf("error opening file: %s", err)
	}
	defer jsonFile.Close()

	byteValue, err := ioutil.ReadAll(jsonFile)
	if err != nil {
		return nil, fmt.Errorf("error reading file: %s", err)
	}

	var getInvoiceByTransactionIDRes api.GetInvoiceByTransactionIDResponse

	err = json.Unmarshal(byteValue, &getInvoiceByTransactionIDRes)
	if err != nil {
		return nil, fmt.Errorf("error unmarshalling JSON: %s", err)
	}

	return &getInvoiceByTransactionIDRes, nil
}
