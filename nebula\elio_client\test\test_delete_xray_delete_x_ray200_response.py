# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.delete_xray_delete_x_ray200_response import DELETEXrayDeleteXRay200Response

class TestDELETEXrayDeleteXRay200Response(unittest.TestCase):
    """DELETEXrayDeleteXRay200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> DELETEXrayDeleteXRay200Response:
        """Test DELETEXrayDeleteXRay200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `DELETEXrayDeleteXRay200Response`
        """
        model = DELETEXrayDeleteXRay200Response()
        if include_optional:
            return DELETEXrayDeleteXRay200Response(
                message = '',
                success = True
            )
        else:
            return DELETEXrayDeleteXRay200Response(
                message = '',
                success = True,
        )
        """

    def testDELETEXrayDeleteXRay200Response(self):
        """Test DELETEXrayDeleteXRay200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
