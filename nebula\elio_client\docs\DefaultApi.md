# elio_client.DefaultApi

All URIs are relative to *http://localhost:4000*

Method | HTTP request | Description
------------- | ------------- | -------------
[**d_elete_meetings_post_session_summary_optimistic**](DefaultApi.md#d_elete_meetings_post_session_summary_optimistic) | **DELETE** /v1.0/post-session-summaries/by-session-ids/{sessionID} | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
[**d_elete_meetings_remove_session_access_control_rules**](DefaultApi.md#d_elete_meetings_remove_session_access_control_rules) | **DELETE** /v1.0/sessions/access/remove | 
[**d_elete_transcriptions_identity_associated**](DefaultApi.md#d_elete_transcriptions_identity_associated) | **DELETE** /.well-known/microsoft-identity-association.json | Needed to verify domains for Microsoft OAuth2 integration 
[**d_elete_transcriptions_microsoft_redirect_of_redirect**](DefaultApi.md#d_elete_transcriptions_microsoft_redirect_of_redirect) | **DELETE** /v1.0/calendar/ms_oauth_callback | Needed redirect back to recall 
[**d_elete_xray_delete_x_ray**](DefaultApi.md#d_elete_xray_delete_x_ray) | **DELETE** /v1.0/xrays/{xrayID} | 
[**g_et_comms_post_session_summary_ready**](DefaultApi.md#g_et_comms_post_session_summary_ready) | **GET** /comms.PostSessionSummaryReady | PostSessionSummaryReady: Receive a request from nebula letting us know that the PSS was generated 
[**g_et_meetings_get_access_requests_braidable**](DefaultApi.md#g_et_meetings_get_access_requests_braidable) | **GET** /v1.0/sessions/access/grouped | 
[**g_et_meetings_get_ai_feed**](DefaultApi.md#g_et_meetings_get_ai_feed) | **GET** /v1.0/ai-feed/get | 
[**g_et_meetings_get_all_access_requests**](DefaultApi.md#g_et_meetings_get_all_access_requests) | **GET** /meetings.GetAllAccessRequests | GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead 
[**g_et_meetings_get_chat_token**](DefaultApi.md#g_et_meetings_get_chat_token) | **GET** /v1.0/chat/token | 
[**g_et_meetings_get_future_sessions**](DefaultApi.md#g_et_meetings_get_future_sessions) | **GET** /v1.0/sessions/future | 
[**g_et_meetings_get_in_review_braidable**](DefaultApi.md#g_et_meetings_get_in_review_braidable) | **GET** /v1.0/sessions/access/in-review | GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead 
[**g_et_meetings_get_latest_recurrence_compact**](DefaultApi.md#g_et_meetings_get_latest_recurrence_compact) | **GET** /meetings.GetLatestRecurrenceCompact | 
[**g_et_meetings_get_library_upcoming_sessions**](DefaultApi.md#g_et_meetings_get_library_upcoming_sessions) | **GET** /v1.0/sessions/library/upcoming | Get future sessions 
[**g_et_meetings_get_meeting_memory_sessions**](DefaultApi.md#g_et_meetings_get_meeting_memory_sessions) | **GET** /v1.0/sessions/meeting-memory | GetMeetingMemorySessions returns all past sessions that have summaAI enabled 
[**g_et_meetings_get_meeting_suggestions_by_session**](DefaultApi.md#g_et_meetings_get_meeting_suggestions_by_session) | **GET** /meetings.GetMeetingSuggestionsBySession | GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session (for future use) 
[**g_et_meetings_get_meeting_suggestions_by_user**](DefaultApi.md#g_et_meetings_get_meeting_suggestions_by_user) | **GET** /v1.0/meetings/{sessionID}/{recurrenceID}/suggestions/user | GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user 
[**g_et_meetings_get_meeting_suggestions_by_user_internal**](DefaultApi.md#g_et_meetings_get_meeting_suggestions_by_user_internal) | **GET** /meetings.GetMeetingSuggestionsByUserInternal | GetMeetingSuggestionsByUserInternal retrieves meeting suggestions for a specific user (internal service calls) 
[**g_et_meetings_get_past_session_recurrences**](DefaultApi.md#g_et_meetings_get_past_session_recurrences) | **GET** /v1.0/recurrences/past | 
[**g_et_meetings_get_past_sessions**](DefaultApi.md#g_et_meetings_get_past_sessions) | **GET** /v1.0/sessions/past | GetPastSessions returns all past sessions 
[**g_et_meetings_get_session_by_id**](DefaultApi.md#g_et_meetings_get_session_by_id) | **GET** /meetings.GetSessionByID | GetSessionByID Replaces &#x60;MarsClient.SessionControllerGetSessionByID&#x60;, that used to sent a request to: src/modules/sessions/controllers/session.controller.ts in mars 
[**g_et_meetings_get_session_recurrence_by_id_braidable_with_access**](DefaultApi.md#g_et_meetings_get_session_recurrence_by_id_braidable_with_access) | **GET** /v3.0/sessions/{sessionID}/recurrences/{recurrenceID} | GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session and access DTO if any access rule is associated with the authenticated user. 
[**g_et_meetings_get_session_recurrences_by_id**](DefaultApi.md#g_et_meetings_get_session_recurrences_by_id) | **GET** /meetings.GetSessionRecurrencesById | GetSessionRecurrencesById returns a list of recurrences for the passed sessionID, can optionally search for specific 
[**g_et_meetings_get_session_users**](DefaultApi.md#g_et_meetings_get_session_users) | **GET** /v1.0/sessions/users/{sessionID}/{recurrenceID} | 
[**g_et_meetings_get_session_users_by_session_id**](DefaultApi.md#g_et_meetings_get_session_users_by_session_id) | **GET** /meetings.GetSessionUsersBySessionID | 
[**g_et_meetings_get_update_user_payment_method_transaction**](DefaultApi.md#g_et_meetings_get_update_user_payment_method_transaction) | **GET** /v1.0/users/id/{userID}/update-payment-method-transaction | 
[**g_et_meetings_get_user_payment_method_details**](DefaultApi.md#g_et_meetings_get_user_payment_method_details) | **GET** /v1.0/users/id/{userID}/payment-method-details | 
[**g_et_meetings_get_user_plan_by_user_id**](DefaultApi.md#g_et_meetings_get_user_plan_by_user_id) | **GET** /v1.0/users/id/{userID}/plan | 
[**g_et_meetings_list_user_transactions**](DefaultApi.md#g_et_meetings_list_user_transactions) | **GET** /v1.0/users/id/{userID}/transactions | 
[**g_et_meetings_list_users**](DefaultApi.md#g_et_meetings_list_users) | **GET** /users | 
[**g_et_meetings_post_session_summary_optimistic**](DefaultApi.md#g_et_meetings_post_session_summary_optimistic) | **GET** /v1.0/post-session-summaries/by-session-ids/{sessionID} | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
[**g_et_meetings_presence**](DefaultApi.md#g_et_meetings_presence) | **GET** /v1.0/sessions/presence/{id} | 
[**g_et_transcriptions_audio_ingress_websocket**](DefaultApi.md#g_et_transcriptions_audio_ingress_websocket) | **GET** /v1.0/transcriptions/audio-ingress | AudioIngressWebsocket handles the websocket connection for raw pcm audio from Livekit/other sources 
[**g_et_transcriptions_cleanup_recall_users**](DefaultApi.md#g_et_transcriptions_cleanup_recall_users) | **GET** /transcriptions.CleanupRecallUsers | CleanupRecallUsers is a cron job that removes users from Recall that have no connections. 
[**g_et_transcriptions_create_end_of_session_transcripts_batch**](DefaultApi.md#g_et_transcriptions_create_end_of_session_transcripts_batch) | **GET** /transcriptions.CreateEndOfSessionTranscriptsBatch | Private endpoint to be called from the session ended event handler in 
[**g_et_transcriptions_download**](DefaultApi.md#g_et_transcriptions_download) | **GET** /v1.0/session/transcriptions | 
[**g_et_transcriptions_fetch_heard_speakers**](DefaultApi.md#g_et_transcriptions_fetch_heard_speakers) | **GET** /v1.0/transcriptions/speakers | FetchHeardSpeakers fetches array of speakers heard in a session 
[**g_et_transcriptions_generate_bot_google_auth_url**](DefaultApi.md#g_et_transcriptions_generate_bot_google_auth_url) | **GET** /v1.0/transcriptions/bot/google-auth | GenerateBotAuthURL Generate a URL for the user to authenticate with Google Calendar. 
[**g_et_transcriptions_generate_bot_microsoft_auth_url**](DefaultApi.md#g_et_transcriptions_generate_bot_microsoft_auth_url) | **GET** /v1.0/transcriptions/bot/microsoft-auth | GenerateBotAuthURL Generate a URL for the user to authenticate with Microsoft Calendar. 
[**g_et_transcriptions_get_batch_ids**](DefaultApi.md#g_et_transcriptions_get_batch_ids) | **GET** /transcriptions.GetBatchIDs | 
[**g_et_transcriptions_get_calendar_user**](DefaultApi.md#g_et_transcriptions_get_calendar_user) | **GET** /v1.0/transcriptions/bot/calendar-user | 
[**g_et_transcriptions_get_speakers_as_session_guests_by_session_id**](DefaultApi.md#g_et_transcriptions_get_speakers_as_session_guests_by_session_id) | **GET** /transcriptions.GetSpeakersAsSessionGuestsBySessionID | 
[**g_et_transcriptions_get_transcription_batch**](DefaultApi.md#g_et_transcriptions_get_transcription_batch) | **GET** /transcriptions.GetTranscriptionBatch | 
[**g_et_transcriptions_identity_associated**](DefaultApi.md#g_et_transcriptions_identity_associated) | **GET** /.well-known/microsoft-identity-association.json | Needed to verify domains for Microsoft OAuth2 integration 
[**g_et_transcriptions_microsoft_redirect_of_redirect**](DefaultApi.md#g_et_transcriptions_microsoft_redirect_of_redirect) | **GET** /v1.0/calendar/ms_oauth_callback | Needed redirect back to recall 
[**g_et_xray_get_x_ray**](DefaultApi.md#g_et_xray_get_x_ray) | **GET** /v1.0/xrays/id/{xrayID} | 
[**g_et_xray_get_x_ray_notifications**](DefaultApi.md#g_et_xray_get_x_ray_notifications) | **GET** /v1.0/xrays/id/{xrayID}/notifications | 
[**g_et_xray_get_x_ray_template**](DefaultApi.md#g_et_xray_get_x_ray_template) | **GET** /v1.0/xray-templates/{templateID} | 
[**g_et_xray_list_x_ray_templates**](DefaultApi.md#g_et_xray_list_x_ray_templates) | **GET** /v1.0/xray-templates | 
[**g_et_xray_list_x_rays**](DefaultApi.md#g_et_xray_list_x_rays) | **GET** /v1.0/xrays | 
[**h_ead_meetings_post_session_summary_optimistic**](DefaultApi.md#h_ead_meetings_post_session_summary_optimistic) | **HEAD** /v1.0/post-session-summaries/by-session-ids/{sessionID} | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
[**h_ead_transcriptions_identity_associated**](DefaultApi.md#h_ead_transcriptions_identity_associated) | **HEAD** /.well-known/microsoft-identity-association.json | Needed to verify domains for Microsoft OAuth2 integration 
[**h_ead_transcriptions_microsoft_redirect_of_redirect**](DefaultApi.md#h_ead_transcriptions_microsoft_redirect_of_redirect) | **HEAD** /v1.0/calendar/ms_oauth_callback | Needed redirect back to recall 
[**p_atch_meetings_post_session_summary_optimistic**](DefaultApi.md#p_atch_meetings_post_session_summary_optimistic) | **PATCH** /v1.0/post-session-summaries/by-session-ids/{sessionID} | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
[**p_atch_meetings_recur_session**](DefaultApi.md#p_atch_meetings_recur_session) | **PATCH** /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur-at | 
[**p_atch_meetings_recur_session_no_request**](DefaultApi.md#p_atch_meetings_recur_session_no_request) | **PATCH** /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur | 
[**p_atch_meetings_update_session**](DefaultApi.md#p_atch_meetings_update_session) | **PATCH** /v1.0/sessions/{sessionID}/recurrence/{recurrenceID} | 
[**p_atch_transcriptions_identity_associated**](DefaultApi.md#p_atch_transcriptions_identity_associated) | **PATCH** /.well-known/microsoft-identity-association.json | Needed to verify domains for Microsoft OAuth2 integration 
[**p_atch_transcriptions_microsoft_redirect_of_redirect**](DefaultApi.md#p_atch_transcriptions_microsoft_redirect_of_redirect) | **PATCH** /v1.0/calendar/ms_oauth_callback | Needed redirect back to recall 
[**p_atch_xray_mark_x_ray_notifications_seen**](DefaultApi.md#p_atch_xray_mark_x_ray_notifications_seen) | **PATCH** /v1.0/xrays/id/{xrayID}/notifications/mark-seen | 
[**p_atch_xray_update_x_ray**](DefaultApi.md#p_atch_xray_update_x_ray) | **PATCH** /v1.0/xrays/id/{xrayID} | 
[**p_ost_comms_meeting_metadata_ready**](DefaultApi.md#p_ost_comms_meeting_metadata_ready) | **POST** /comms.MeetingMetadataReady | MeetingMetadataReady is a endpoint to notify Elio that a specific session&#39;s metadata has been generated. 
[**p_ost_comms_trigger_guest_waiting_in_lobby_notification**](DefaultApi.md#p_ost_comms_trigger_guest_waiting_in_lobby_notification) | **POST** /comms.TriggerGuestWaitingInLobbyNotification | 
[**p_ost_meetings_ai_feed_event**](DefaultApi.md#p_ost_meetings_ai_feed_event) | **POST** /v1.0/ai-feed/event | 
[**p_ost_meetings_count_in_review_requests**](DefaultApi.md#p_ost_meetings_count_in_review_requests) | **POST** /v1.0/sessions/access/get-in-review-access-requests-count | 
[**p_ost_meetings_create_access_request**](DefaultApi.md#p_ost_meetings_create_access_request) | **POST** /v1.0/sessions/access/request-access | 
[**p_ost_meetings_create_session**](DefaultApi.md#p_ost_meetings_create_session) | **POST** /v1.0/sessions | 
[**p_ost_meetings_create_session_with_user**](DefaultApi.md#p_ost_meetings_create_session_with_user) | **POST** /meetings.CreateSessionWithUser | 
[**p_ost_meetings_create_user**](DefaultApi.md#p_ost_meetings_create_user) | **POST** /users | 
[**p_ost_meetings_get_lobby_guests**](DefaultApi.md#p_ost_meetings_get_lobby_guests) | **POST** /meetings.GetLobbyGuests | GetLobbyGuests returns the list of guests in the lobby 
[**p_ost_meetings_get_session_access_rules**](DefaultApi.md#p_ost_meetings_get_session_access_rules) | **POST** /meetings.GetSessionAccessRules | GetSessionAccessRules returns all access rules for a session 
[**p_ost_meetings_get_user_by_id**](DefaultApi.md#p_ost_meetings_get_user_by_id) | **POST** /meetings.GetUserByID | GetUserByID gets user details by ID 
[**p_ost_meetings_get_user_by_id_with_relations**](DefaultApi.md#p_ost_meetings_get_user_by_id_with_relations) | **POST** /meetings.GetUserByIDWithRelations | 
[**p_ost_meetings_join_lobby_as_guest**](DefaultApi.md#p_ost_meetings_join_lobby_as_guest) | **POST** /meetings.JoinLobbyAsGuest | JoinLobbyAsGuest adds a guest to the lobby and publishes the event to the lobby listeners 
[**p_ost_meetings_list_sessions_by_lobby_id**](DefaultApi.md#p_ost_meetings_list_sessions_by_lobby_id) | **POST** /meetings.ListSessionsByLobbyID | 
[**p_ost_meetings_login_guest_user_with_session**](DefaultApi.md#p_ost_meetings_login_guest_user_with_session) | **POST** /v1.0/sessions/guest | 
[**p_ost_meetings_post_session_summary_optimistic**](DefaultApi.md#p_ost_meetings_post_session_summary_optimistic) | **POST** /v1.0/post-session-summaries/by-session-ids/{sessionID} | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
[**p_ost_meetings_remove_guest_from_lobby**](DefaultApi.md#p_ost_meetings_remove_guest_from_lobby) | **POST** /meetings.RemoveGuestFromLobby | RemoveGuestFromLobby removes a guest from the lobby and publishes the event to the lobby listeners 
[**p_ost_meetings_revoke_access_session_user**](DefaultApi.md#p_ost_meetings_revoke_access_session_user) | **POST** /meetings.RevokeAccessSessionUser | 
[**p_ost_meetings_update_session_access_control_rules**](DefaultApi.md#p_ost_meetings_update_session_access_control_rules) | **POST** /v1.0/sessions/access | 
[**p_ost_meetings_upsert_meeting_suggestions**](DefaultApi.md#p_ost_meetings_upsert_meeting_suggestions) | **POST** /meetings.UpsertMeetingSuggestions | UpsertMeetingSuggestions stores or updates meeting suggestions for a session (called by Nebula) 
[**p_ost_meetings_upsert_session_user**](DefaultApi.md#p_ost_meetings_upsert_session_user) | **POST** /meetings.UpsertSessionUser | 
[**p_ost_transcriptions_cleanup_recall_users**](DefaultApi.md#p_ost_transcriptions_cleanup_recall_users) | **POST** /transcriptions.CleanupRecallUsers | CleanupRecallUsers is a cron job that removes users from Recall that have no connections. 
[**p_ost_transcriptions_create_bot_for_meeting**](DefaultApi.md#p_ost_transcriptions_create_bot_for_meeting) | **POST** /v1.0/transcriptions/bot/create-for-meeting | 
[**p_ost_transcriptions_disconnect_calendar**](DefaultApi.md#p_ost_transcriptions_disconnect_calendar) | **POST** /v1.0/transcriptions/bot/disconnect | Disconnect a user&#39;s calendar platform from our Rumi bots recall.ai account 
[**p_ost_transcriptions_generate_audio_ingress_key**](DefaultApi.md#p_ost_transcriptions_generate_audio_ingress_key) | **POST** /transcriptions.GenerateAudioIngressKey | GenerateAudioIngressKey represents a key used to authenticate audio ingress 
[**p_ost_transcriptions_identify_heard_speakers**](DefaultApi.md#p_ost_transcriptions_identify_heard_speakers) | **POST** /v1.0/transcriptions/speakers | IdentifyHeardSpeakers identifies an array of speakers heard in a session 
[**p_ost_transcriptions_identity_associated**](DefaultApi.md#p_ost_transcriptions_identity_associated) | **POST** /.well-known/microsoft-identity-association.json | Needed to verify domains for Microsoft OAuth2 integration 
[**p_ost_transcriptions_microsoft_redirect_of_redirect**](DefaultApi.md#p_ost_transcriptions_microsoft_redirect_of_redirect) | **POST** /v1.0/calendar/ms_oauth_callback | Needed redirect back to recall 
[**p_ost_transcriptions_process_bot_recording**](DefaultApi.md#p_ost_transcriptions_process_bot_recording) | **POST** /transcriptions.ProcessBotRecording | 
[**p_ost_transcriptions_recall_transcriptions_webhook**](DefaultApi.md#p_ost_transcriptions_recall_transcriptions_webhook) | **POST** /v1.0/transcriptions/bot/webhook | 
[**p_ost_transcriptions_remove_users_from_recall_account**](DefaultApi.md#p_ost_transcriptions_remove_users_from_recall_account) | **POST** /transcriptions.RemoveUsersFromRecallAccount | RemoveUsersFromRecallAccount Remove all users from the token passed in the params. 
[**p_ost_transcriptions_update**](DefaultApi.md#p_ost_transcriptions_update) | **POST** /transcriptions.Update | 
[**p_ost_xray_create**](DefaultApi.md#p_ost_xray_create) | **POST** /v1.0/xrays | 
[**p_ost_xray_generate_x_ray_info**](DefaultApi.md#p_ost_xray_generate_x_ray_info) | **POST** /v1.0/xrays/generate/info | 
[**p_ost_xray_generate_x_ray_prompt**](DefaultApi.md#p_ost_xray_generate_x_ray_prompt) | **POST** /v1.0/xrays/generate/prompt | 
[**p_ost_xray_share_x_ray_as_template**](DefaultApi.md#p_ost_xray_share_x_ray_as_template) | **POST** /v1.0/xrays/id/{xrayID}/share | 
[**p_ut_meetings_add_session_access_control_rules**](DefaultApi.md#p_ut_meetings_add_session_access_control_rules) | **PUT** /v1.0/sessions/access/add | AddSessionAccessControlRules is now deprecated, please use UpdateSessionAccessControlRules (later down in this file) 
[**p_ut_meetings_approve_or_denyed_session_access_request**](DefaultApi.md#p_ut_meetings_approve_or_denyed_session_access_request) | **PUT** /v1.0/sessions/access/request | ApproveOrDenyedSessionAccessRequest is deprecated and should not be used. 
[**p_ut_meetings_post_session_summary_optimistic**](DefaultApi.md#p_ut_meetings_post_session_summary_optimistic) | **PUT** /v1.0/post-session-summaries/by-session-ids/{sessionID} | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
[**p_ut_meetings_update_user_by_id**](DefaultApi.md#p_ut_meetings_update_user_by_id) | **PUT** /v1.0/users/id/{userID} | UpdateUserByID updates user details by their ID 
[**p_ut_transcriptions_identity_associated**](DefaultApi.md#p_ut_transcriptions_identity_associated) | **PUT** /.well-known/microsoft-identity-association.json | Needed to verify domains for Microsoft OAuth2 integration 
[**p_ut_transcriptions_microsoft_redirect_of_redirect**](DefaultApi.md#p_ut_transcriptions_microsoft_redirect_of_redirect) | **PUT** /v1.0/calendar/ms_oauth_callback | Needed redirect back to recall 


# **d_elete_meetings_post_session_summary_optimistic**
> d_elete_meetings_post_session_summary_optimistic(session_id)

PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 

202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
        api_instance.d_elete_meetings_post_session_summary_optimistic(session_id)
    except Exception as e:
        print("Exception when calling DefaultApi->d_elete_meetings_post_session_summary_optimistic: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **d_elete_meetings_remove_session_access_control_rules**
> d_elete_meetings_remove_session_access_control_rules()



### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        api_instance.d_elete_meetings_remove_session_access_control_rules()
    except Exception as e:
        print("Exception when calling DefaultApi->d_elete_meetings_remove_session_access_control_rules: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **d_elete_transcriptions_identity_associated**
> d_elete_transcriptions_identity_associated()

Needed to verify domains for Microsoft OAuth2 integration 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed to verify domains for Microsoft OAuth2 integration 
        api_instance.d_elete_transcriptions_identity_associated()
    except Exception as e:
        print("Exception when calling DefaultApi->d_elete_transcriptions_identity_associated: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **d_elete_transcriptions_microsoft_redirect_of_redirect**
> d_elete_transcriptions_microsoft_redirect_of_redirect()

Needed redirect back to recall 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed redirect back to recall 
        api_instance.d_elete_transcriptions_microsoft_redirect_of_redirect()
    except Exception as e:
        print("Exception when calling DefaultApi->d_elete_transcriptions_microsoft_redirect_of_redirect: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **d_elete_xray_delete_x_ray**
> DELETEXrayDeleteXRay200Response d_elete_xray_delete_x_ray(xray_id)



### Example


```python
import elio_client
from elio_client.models.delete_xray_delete_x_ray200_response import DELETEXrayDeleteXRay200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    xray_id = 'xray_id_example' # str | 

    try:
        api_response = api_instance.d_elete_xray_delete_x_ray(xray_id)
        print("The response of DefaultApi->d_elete_xray_delete_x_ray:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->d_elete_xray_delete_x_ray: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **xray_id** | **str**|  | 

### Return type

[**DELETEXrayDeleteXRay200Response**](DELETEXrayDeleteXRay200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_comms_post_session_summary_ready**
> GETCommsPostSessionSummaryReady200Response g_et_comms_post_session_summary_ready(session_id, session_recurrence_id)

PostSessionSummaryReady: Receive a request from nebula letting us know that the PSS was generated 

TODO when tag:internal is merged, is that instead of tag:trixta 

### Example


```python
import elio_client
from elio_client.models.get_comms_post_session_summary_ready200_response import GETCommsPostSessionSummaryReady200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    session_recurrence_id = 'session_recurrence_id_example' # str | 

    try:
        # PostSessionSummaryReady: Receive a request from nebula letting us know that the PSS was generated 
        api_response = api_instance.g_et_comms_post_session_summary_ready(session_id, session_recurrence_id)
        print("The response of DefaultApi->g_et_comms_post_session_summary_ready:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_comms_post_session_summary_ready: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **session_recurrence_id** | **str**|  | 

### Return type

[**GETCommsPostSessionSummaryReady200Response**](GETCommsPostSessionSummaryReady200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_access_requests_braidable**
> g_et_meetings_get_access_requests_braidable()



### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        api_instance.g_et_meetings_get_access_requests_braidable()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_access_requests_braidable: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_ai_feed**
> g_et_meetings_get_ai_feed()



### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        api_instance.g_et_meetings_get_ai_feed()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_ai_feed: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_all_access_requests**
> GETMeetingsGetAllAccessRequests200Response g_et_meetings_get_all_access_requests(session_id, session_recurrence_id, restriction_status)

GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead 

TODO evaluate tag:trixta in this endpoint, if it's used we should make a specific one for external requests 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_all_access_requests200_response import GETMeetingsGetAllAccessRequests200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    session_recurrence_id = 'session_recurrence_id_example' # str | 
    restriction_status = 'restriction_status_example' # str | 

    try:
        # GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead 
        api_response = api_instance.g_et_meetings_get_all_access_requests(session_id, session_recurrence_id, restriction_status)
        print("The response of DefaultApi->g_et_meetings_get_all_access_requests:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_all_access_requests: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **session_recurrence_id** | **str**|  | 
 **restriction_status** | **str**|  | 

### Return type

[**GETMeetingsGetAllAccessRequests200Response**](GETMeetingsGetAllAccessRequests200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_chat_token**
> GETMeetingsGetChatToken200Response g_et_meetings_get_chat_token(session_id, session_recurrence_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_chat_token200_response import GETMeetingsGetChatToken200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | The session ID of the user 
    session_recurrence_id = 'session_recurrence_id_example' # str | The session recurrence ID of the user 

    try:
        api_response = api_instance.g_et_meetings_get_chat_token(session_id, session_recurrence_id)
        print("The response of DefaultApi->g_et_meetings_get_chat_token:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_chat_token: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**| The session ID of the user  | 
 **session_recurrence_id** | **str**| The session recurrence ID of the user  | 

### Return type

[**GETMeetingsGetChatToken200Response**](GETMeetingsGetChatToken200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_future_sessions**
> GETMeetingsGetPastSessionRecurrences200Response g_et_meetings_get_future_sessions(limit, cursor)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_past_session_recurrences200_response import GETMeetingsGetPastSessionRecurrences200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    cursor = 56 # int | 

    try:
        api_response = api_instance.g_et_meetings_get_future_sessions(limit, cursor)
        print("The response of DefaultApi->g_et_meetings_get_future_sessions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_future_sessions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **cursor** | **int**|  | 

### Return type

[**GETMeetingsGetPastSessionRecurrences200Response**](GETMeetingsGetPastSessionRecurrences200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_in_review_braidable**
> g_et_meetings_get_in_review_braidable()

GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead 
        api_instance.g_et_meetings_get_in_review_braidable()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_in_review_braidable: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_latest_recurrence_compact**
> GETMeetingsGetLatestRecurrenceCompact200Response g_et_meetings_get_latest_recurrence_compact(session_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_latest_recurrence_compact200_response import GETMeetingsGetLatestRecurrenceCompact200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_latest_recurrence_compact(session_id)
        print("The response of DefaultApi->g_et_meetings_get_latest_recurrence_compact:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_latest_recurrence_compact: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

[**GETMeetingsGetLatestRecurrenceCompact200Response**](GETMeetingsGetLatestRecurrenceCompact200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_library_upcoming_sessions**
> GETMeetingsGetPastSessionRecurrences200Response g_et_meetings_get_library_upcoming_sessions(limit, cursor)

Get future sessions 

Deprecated: Use GetFutureSessions instead 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_past_session_recurrences200_response import GETMeetingsGetPastSessionRecurrences200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    cursor = 56 # int | 

    try:
        # Get future sessions 
        api_response = api_instance.g_et_meetings_get_library_upcoming_sessions(limit, cursor)
        print("The response of DefaultApi->g_et_meetings_get_library_upcoming_sessions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_library_upcoming_sessions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **cursor** | **int**|  | 

### Return type

[**GETMeetingsGetPastSessionRecurrences200Response**](GETMeetingsGetPastSessionRecurrences200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_meeting_memory_sessions**
> GETMeetingsGetPastSessionRecurrences200Response g_et_meetings_get_meeting_memory_sessions(limit, cursor)

GetMeetingMemorySessions returns all past sessions that have summaAI enabled 

Deprecated: Use GetPastSessionRecurrences instead, with SummaAI=true Remove once the FE is updated to use GetPastSessionRecurrences 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_past_session_recurrences200_response import GETMeetingsGetPastSessionRecurrences200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    cursor = 56 # int | 

    try:
        # GetMeetingMemorySessions returns all past sessions that have summaAI enabled 
        api_response = api_instance.g_et_meetings_get_meeting_memory_sessions(limit, cursor)
        print("The response of DefaultApi->g_et_meetings_get_meeting_memory_sessions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_meeting_memory_sessions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **cursor** | **int**|  | 

### Return type

[**GETMeetingsGetPastSessionRecurrences200Response**](GETMeetingsGetPastSessionRecurrences200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_meeting_suggestions_by_session**
> GETMeetingsGetMeetingSuggestionsBySession200Response g_et_meetings_get_meeting_suggestions_by_session(session_id, recurrence_id)

GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session (for future use) 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_meeting_suggestions_by_session200_response import GETMeetingsGetMeetingSuggestionsBySession200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        # GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session (for future use) 
        api_response = api_instance.g_et_meetings_get_meeting_suggestions_by_session(session_id, recurrence_id)
        print("The response of DefaultApi->g_et_meetings_get_meeting_suggestions_by_session:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_meeting_suggestions_by_session: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

[**GETMeetingsGetMeetingSuggestionsBySession200Response**](GETMeetingsGetMeetingSuggestionsBySession200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_meeting_suggestions_by_user**
> GETMeetingsGetMeetingSuggestionsByUserInternal200Response g_et_meetings_get_meeting_suggestions_by_user(session_id, recurrence_id)

GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_meeting_suggestions_by_user_internal200_response import GETMeetingsGetMeetingSuggestionsByUserInternal200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        # GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user 
        api_response = api_instance.g_et_meetings_get_meeting_suggestions_by_user(session_id, recurrence_id)
        print("The response of DefaultApi->g_et_meetings_get_meeting_suggestions_by_user:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_meeting_suggestions_by_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

[**GETMeetingsGetMeetingSuggestionsByUserInternal200Response**](GETMeetingsGetMeetingSuggestionsByUserInternal200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_meeting_suggestions_by_user_internal**
> GETMeetingsGetMeetingSuggestionsByUserInternal200Response g_et_meetings_get_meeting_suggestions_by_user_internal(session_id, recurrence_id, user_id)

GetMeetingSuggestionsByUserInternal retrieves meeting suggestions for a specific user (internal service calls) 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_meeting_suggestions_by_user_internal200_response import GETMeetingsGetMeetingSuggestionsByUserInternal200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 
    user_id = 'user_id_example' # str | 

    try:
        # GetMeetingSuggestionsByUserInternal retrieves meeting suggestions for a specific user (internal service calls) 
        api_response = api_instance.g_et_meetings_get_meeting_suggestions_by_user_internal(session_id, recurrence_id, user_id)
        print("The response of DefaultApi->g_et_meetings_get_meeting_suggestions_by_user_internal:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_meeting_suggestions_by_user_internal: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 
 **user_id** | **str**|  | 

### Return type

[**GETMeetingsGetMeetingSuggestionsByUserInternal200Response**](GETMeetingsGetMeetingSuggestionsByUserInternal200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_past_session_recurrences**
> GETMeetingsGetPastSessionRecurrences200Response g_et_meetings_get_past_session_recurrences(limit, cursor, summa_ai)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_past_session_recurrences200_response import GETMeetingsGetPastSessionRecurrences200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    cursor = 56 # int | 
    summa_ai = 'summa_ai_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_past_session_recurrences(limit, cursor, summa_ai)
        print("The response of DefaultApi->g_et_meetings_get_past_session_recurrences:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_past_session_recurrences: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **cursor** | **int**|  | 
 **summa_ai** | **str**|  | 

### Return type

[**GETMeetingsGetPastSessionRecurrences200Response**](GETMeetingsGetPastSessionRecurrences200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_past_sessions**
> GETMeetingsGetPastSessionRecurrences200Response g_et_meetings_get_past_sessions(limit, cursor)

GetPastSessions returns all past sessions 

Deprecated: Use GetPastSessionRecurrences instead Remove once the FE is updated to use GetPastSessionRecurrences 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_past_session_recurrences200_response import GETMeetingsGetPastSessionRecurrences200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    cursor = 56 # int | 

    try:
        # GetPastSessions returns all past sessions 
        api_response = api_instance.g_et_meetings_get_past_sessions(limit, cursor)
        print("The response of DefaultApi->g_et_meetings_get_past_sessions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_past_sessions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **cursor** | **int**|  | 

### Return type

[**GETMeetingsGetPastSessionRecurrences200Response**](GETMeetingsGetPastSessionRecurrences200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_session_by_id**
> POSTMeetingsCreateSessionWithUser200ResponseData g_et_meetings_get_session_by_id(session_id, recurrence_id)

GetSessionByID Replaces `MarsClient.SessionControllerGetSessionByID`, that used to sent a request to: src/modules/sessions/controllers/session.controller.ts in mars 

TODO replace tag:trixta with tag:internal when the tag is merged 

### Example


```python
import elio_client
from elio_client.models.post_meetings_create_session_with_user200_response_data import POSTMeetingsCreateSessionWithUser200ResponseData
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | SessionID is the snowflake ID of the session to get 
    recurrence_id = 'recurrence_id_example' # str | RecurrenceID is the snowflake ID of the recurrence to get, when empty the latest recurrence is returned 

    try:
        # GetSessionByID Replaces `MarsClient.SessionControllerGetSessionByID`, that used to sent a request to: src/modules/sessions/controllers/session.controller.ts in mars 
        api_response = api_instance.g_et_meetings_get_session_by_id(session_id, recurrence_id)
        print("The response of DefaultApi->g_et_meetings_get_session_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_session_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**| SessionID is the snowflake ID of the session to get  | 
 **recurrence_id** | **str**| RecurrenceID is the snowflake ID of the recurrence to get, when empty the latest recurrence is returned  | 

### Return type

[**POSTMeetingsCreateSessionWithUser200ResponseData**](POSTMeetingsCreateSessionWithUser200ResponseData.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_session_recurrence_by_id_braidable_with_access**
> g_et_meetings_get_session_recurrence_by_id_braidable_with_access(session_id, recurrence_id)

GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session and access DTO if any access rule is associated with the authenticated user. 

It listens for updates on the session and in-review topics to refresh the session and access request data. When recurrenceID is \"latest\", it fetches the latest recurrence ID of the session. 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        # GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session and access DTO if any access rule is associated with the authenticated user. 
        api_instance.g_et_meetings_get_session_recurrence_by_id_braidable_with_access(session_id, recurrence_id)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_session_recurrence_by_id_braidable_with_access: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_session_recurrences_by_id**
> GETMeetingsGetSessionRecurrencesById200Response g_et_meetings_get_session_recurrences_by_id(session_id, filter_states)

GetSessionRecurrencesById returns a list of recurrences for the passed sessionID, can optionally search for specific 

states the recurrence should have been in to appear in the response. When no sessions match the sessionID or optional filter the resulting \\`Sessions\\` array in api.GetSessionRecurrencesByIDResponse will be empty. 

### Example


```python
import elio_client
from elio_client.models.get_meetings_get_session_recurrences_by_id200_response import GETMeetingsGetSessionRecurrencesById200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | SessionID is the snowflake ID of the session 
    filter_states = 'filter_states_example' # str | FilterStates is a comma separated list of states to filter by 

    try:
        # GetSessionRecurrencesById returns a list of recurrences for the passed sessionID, can optionally search for specific 
        api_response = api_instance.g_et_meetings_get_session_recurrences_by_id(session_id, filter_states)
        print("The response of DefaultApi->g_et_meetings_get_session_recurrences_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_session_recurrences_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**| SessionID is the snowflake ID of the session  | 
 **filter_states** | **str**| FilterStates is a comma separated list of states to filter by  | 

### Return type

[**GETMeetingsGetSessionRecurrencesById200Response**](GETMeetingsGetSessionRecurrencesById200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_session_users**
> GETMeetingsGetSessionUsers200Response g_et_meetings_get_session_users(session_id, recurrence_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_session_users200_response import GETMeetingsGetSessionUsers200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_session_users(session_id, recurrence_id)
        print("The response of DefaultApi->g_et_meetings_get_session_users:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_session_users: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

[**GETMeetingsGetSessionUsers200Response**](GETMeetingsGetSessionUsers200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_session_users_by_session_id**
> GETMeetingsGetSessionUsersBySessionID200Response g_et_meetings_get_session_users_by_session_id(session_id, session_recurrence_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_session_users_by_session_id200_response import GETMeetingsGetSessionUsersBySessionID200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    session_recurrence_id = 'session_recurrence_id_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_session_users_by_session_id(session_id, session_recurrence_id)
        print("The response of DefaultApi->g_et_meetings_get_session_users_by_session_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_session_users_by_session_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **session_recurrence_id** | **str**|  | 

### Return type

[**GETMeetingsGetSessionUsersBySessionID200Response**](GETMeetingsGetSessionUsersBySessionID200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_update_user_payment_method_transaction**
> GETMeetingsGetUpdateUserPaymentMethodTransaction200Response g_et_meetings_get_update_user_payment_method_transaction(user_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_update_user_payment_method_transaction200_response import GETMeetingsGetUpdateUserPaymentMethodTransaction200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    user_id = 'user_id_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_update_user_payment_method_transaction(user_id)
        print("The response of DefaultApi->g_et_meetings_get_update_user_payment_method_transaction:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_update_user_payment_method_transaction: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_id** | **str**|  | 

### Return type

[**GETMeetingsGetUpdateUserPaymentMethodTransaction200Response**](GETMeetingsGetUpdateUserPaymentMethodTransaction200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_user_payment_method_details**
> GETMeetingsGetUserPaymentMethodDetails200Response g_et_meetings_get_user_payment_method_details(user_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_user_payment_method_details200_response import GETMeetingsGetUserPaymentMethodDetails200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    user_id = 'user_id_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_user_payment_method_details(user_id)
        print("The response of DefaultApi->g_et_meetings_get_user_payment_method_details:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_user_payment_method_details: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_id** | **str**|  | 

### Return type

[**GETMeetingsGetUserPaymentMethodDetails200Response**](GETMeetingsGetUserPaymentMethodDetails200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_get_user_plan_by_user_id**
> GETMeetingsGetUserPlanByUserID200Response g_et_meetings_get_user_plan_by_user_id(user_id)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_user_plan_by_user_id200_response import GETMeetingsGetUserPlanByUserID200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    user_id = 'user_id_example' # str | 

    try:
        api_response = api_instance.g_et_meetings_get_user_plan_by_user_id(user_id)
        print("The response of DefaultApi->g_et_meetings_get_user_plan_by_user_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_get_user_plan_by_user_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_id** | **str**|  | 

### Return type

[**GETMeetingsGetUserPlanByUserID200Response**](GETMeetingsGetUserPlanByUserID200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_list_user_transactions**
> GETMeetingsListUserTransactions200Response g_et_meetings_list_user_transactions(user_id, limit=limit, cursor=cursor, include_invoices=include_invoices)



### Example


```python
import elio_client
from elio_client.models.get_meetings_list_user_transactions200_response import GETMeetingsListUserTransactions200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    user_id = 'user_id_example' # str | 
    limit = 56 # int |  (optional)
    cursor = 'cursor_example' # str |  (optional)
    include_invoices = True # bool |  (optional)

    try:
        api_response = api_instance.g_et_meetings_list_user_transactions(user_id, limit=limit, cursor=cursor, include_invoices=include_invoices)
        print("The response of DefaultApi->g_et_meetings_list_user_transactions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_list_user_transactions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_id** | **str**|  | 
 **limit** | **int**|  | [optional] 
 **cursor** | **str**|  | [optional] 
 **include_invoices** | **bool**|  | [optional] 

### Return type

[**GETMeetingsListUserTransactions200Response**](GETMeetingsListUserTransactions200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_list_users**
> GETMeetingsListUsers200Response g_et_meetings_list_users(limit=limit, offset=offset, order_by=order_by, order_direction=order_direction, email=email)



### Example


```python
import elio_client
from elio_client.models.get_meetings_list_users200_response import GETMeetingsListUsers200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int |  (optional)
    offset = 56 # int |  (optional)
    order_by = 'order_by_example' # str |  (optional)
    order_direction = 'order_direction_example' # str |  (optional)
    email = 'email_example' # str |  (optional)

    try:
        api_response = api_instance.g_et_meetings_list_users(limit=limit, offset=offset, order_by=order_by, order_direction=order_direction, email=email)
        print("The response of DefaultApi->g_et_meetings_list_users:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_list_users: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | [optional] 
 **offset** | **int**|  | [optional] 
 **order_by** | **str**|  | [optional] 
 **order_direction** | **str**|  | [optional] 
 **email** | **str**|  | [optional] 

### Return type

[**GETMeetingsListUsers200Response**](GETMeetingsListUsers200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_post_session_summary_optimistic**
> g_et_meetings_post_session_summary_optimistic(session_id)

PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 

202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
        api_instance.g_et_meetings_post_session_summary_optimistic(session_id)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_post_session_summary_optimistic: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_meetings_presence**
> g_et_meetings_presence(id)



### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    id = 'id_example' # str | 

    try:
        api_instance.g_et_meetings_presence(id)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_meetings_presence: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_audio_ingress_websocket**
> g_et_transcriptions_audio_ingress_websocket()

AudioIngressWebsocket handles the websocket connection for raw pcm audio from Livekit/other sources 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # AudioIngressWebsocket handles the websocket connection for raw pcm audio from Livekit/other sources 
        api_instance.g_et_transcriptions_audio_ingress_websocket()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_audio_ingress_websocket: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_cleanup_recall_users**
> g_et_transcriptions_cleanup_recall_users()

CleanupRecallUsers is a cron job that removes users from Recall that have no connections. 

This prevents us from having tons of users with no connections on the recall side. 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # CleanupRecallUsers is a cron job that removes users from Recall that have no connections. 
        api_instance.g_et_transcriptions_cleanup_recall_users()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_cleanup_recall_users: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_create_end_of_session_transcripts_batch**
> g_et_transcriptions_create_end_of_session_transcripts_batch(session_id, recurrence_id, meeting_type)

Private endpoint to be called from the session ended event handler in 

elio/livekit/session-consumer.go#L74 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 
    meeting_type = 'meeting_type_example' # str | 

    try:
        # Private endpoint to be called from the session ended event handler in 
        api_instance.g_et_transcriptions_create_end_of_session_transcripts_batch(session_id, recurrence_id, meeting_type)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_create_end_of_session_transcripts_batch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 
 **meeting_type** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_download**
> g_et_transcriptions_download()



### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        api_instance.g_et_transcriptions_download()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_download: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_fetch_heard_speakers**
> GETTranscriptionsFetchHeardSpeakers200Response g_et_transcriptions_fetch_heard_speakers(session_id, recurrence_id, include_snippets)

FetchHeardSpeakers fetches array of speakers heard in a session 

### Example


```python
import elio_client
from elio_client.models.get_transcriptions_fetch_heard_speakers200_response import GETTranscriptionsFetchHeardSpeakers200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | SessionID is the session for which we want to fetch heard speakers 
    recurrence_id = 'recurrence_id_example' # str | RecurrenceID is the recurrence for which we want to fetch heard speakers 
    include_snippets = True # bool | IncludeSnippets determines whether to include speaker snippets in the response, defaults to false 

    try:
        # FetchHeardSpeakers fetches array of speakers heard in a session 
        api_response = api_instance.g_et_transcriptions_fetch_heard_speakers(session_id, recurrence_id, include_snippets)
        print("The response of DefaultApi->g_et_transcriptions_fetch_heard_speakers:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_fetch_heard_speakers: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**| SessionID is the session for which we want to fetch heard speakers  | 
 **recurrence_id** | **str**| RecurrenceID is the recurrence for which we want to fetch heard speakers  | 
 **include_snippets** | **bool**| IncludeSnippets determines whether to include speaker snippets in the response, defaults to false  | 

### Return type

[**GETTranscriptionsFetchHeardSpeakers200Response**](GETTranscriptionsFetchHeardSpeakers200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_generate_bot_google_auth_url**
> GETTranscriptionsGenerateBotGoogleAuthURL200Response g_et_transcriptions_generate_bot_google_auth_url()

GenerateBotAuthURL Generate a URL for the user to authenticate with Google Calendar. 

### Example


```python
import elio_client
from elio_client.models.get_transcriptions_generate_bot_google_auth_url200_response import GETTranscriptionsGenerateBotGoogleAuthURL200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # GenerateBotAuthURL Generate a URL for the user to authenticate with Google Calendar. 
        api_response = api_instance.g_et_transcriptions_generate_bot_google_auth_url()
        print("The response of DefaultApi->g_et_transcriptions_generate_bot_google_auth_url:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_generate_bot_google_auth_url: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**GETTranscriptionsGenerateBotGoogleAuthURL200Response**](GETTranscriptionsGenerateBotGoogleAuthURL200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_generate_bot_microsoft_auth_url**
> GETTranscriptionsGenerateBotGoogleAuthURL200Response g_et_transcriptions_generate_bot_microsoft_auth_url()

GenerateBotAuthURL Generate a URL for the user to authenticate with Microsoft Calendar. 

### Example


```python
import elio_client
from elio_client.models.get_transcriptions_generate_bot_google_auth_url200_response import GETTranscriptionsGenerateBotGoogleAuthURL200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # GenerateBotAuthURL Generate a URL for the user to authenticate with Microsoft Calendar. 
        api_response = api_instance.g_et_transcriptions_generate_bot_microsoft_auth_url()
        print("The response of DefaultApi->g_et_transcriptions_generate_bot_microsoft_auth_url:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_generate_bot_microsoft_auth_url: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**GETTranscriptionsGenerateBotGoogleAuthURL200Response**](GETTranscriptionsGenerateBotGoogleAuthURL200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_get_batch_ids**
> GETTranscriptionsGetBatchIDs200Response g_et_transcriptions_get_batch_ids(session_id, recurrence_id)



### Example


```python
import elio_client
from elio_client.models.get_transcriptions_get_batch_ids200_response import GETTranscriptionsGetBatchIDs200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        api_response = api_instance.g_et_transcriptions_get_batch_ids(session_id, recurrence_id)
        print("The response of DefaultApi->g_et_transcriptions_get_batch_ids:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_get_batch_ids: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

[**GETTranscriptionsGetBatchIDs200Response**](GETTranscriptionsGetBatchIDs200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_get_calendar_user**
> GETTranscriptionsGetCalendarUser200Response g_et_transcriptions_get_calendar_user()



### Example


```python
import elio_client
from elio_client.models.get_transcriptions_get_calendar_user200_response import GETTranscriptionsGetCalendarUser200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        api_response = api_instance.g_et_transcriptions_get_calendar_user()
        print("The response of DefaultApi->g_et_transcriptions_get_calendar_user:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_get_calendar_user: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

[**GETTranscriptionsGetCalendarUser200Response**](GETTranscriptionsGetCalendarUser200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_get_speakers_as_session_guests_by_session_id**
> GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response g_et_transcriptions_get_speakers_as_session_guests_by_session_id(session_id, session_recurrence_id)



### Example


```python
import elio_client
from elio_client.models.get_transcriptions_get_speakers_as_session_guests_by_session_id200_response import GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    session_recurrence_id = 'session_recurrence_id_example' # str | 

    try:
        api_response = api_instance.g_et_transcriptions_get_speakers_as_session_guests_by_session_id(session_id, session_recurrence_id)
        print("The response of DefaultApi->g_et_transcriptions_get_speakers_as_session_guests_by_session_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_get_speakers_as_session_guests_by_session_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **session_recurrence_id** | **str**|  | 

### Return type

[**GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response**](GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_get_transcription_batch**
> GETTranscriptionsGetTranscriptionBatch200Response g_et_transcriptions_get_transcription_batch(batch_id, session_id, recurrence_id)



### Example


```python
import elio_client
from elio_client.models.get_transcriptions_get_transcription_batch200_response import GETTranscriptionsGetTranscriptionBatch200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    batch_id = 'batch_id_example' # str | 
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        api_response = api_instance.g_et_transcriptions_get_transcription_batch(batch_id, session_id, recurrence_id)
        print("The response of DefaultApi->g_et_transcriptions_get_transcription_batch:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_get_transcription_batch: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **batch_id** | **str**|  | 
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

[**GETTranscriptionsGetTranscriptionBatch200Response**](GETTranscriptionsGetTranscriptionBatch200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_identity_associated**
> g_et_transcriptions_identity_associated()

Needed to verify domains for Microsoft OAuth2 integration 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed to verify domains for Microsoft OAuth2 integration 
        api_instance.g_et_transcriptions_identity_associated()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_identity_associated: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_transcriptions_microsoft_redirect_of_redirect**
> g_et_transcriptions_microsoft_redirect_of_redirect()

Needed redirect back to recall 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed redirect back to recall 
        api_instance.g_et_transcriptions_microsoft_redirect_of_redirect()
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_transcriptions_microsoft_redirect_of_redirect: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_xray_get_x_ray**
> POSTXrayCreate200Response g_et_xray_get_x_ray(xray_id)



### Example


```python
import elio_client
from elio_client.models.post_xray_create200_response import POSTXrayCreate200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    xray_id = 'xray_id_example' # str | 

    try:
        api_response = api_instance.g_et_xray_get_x_ray(xray_id)
        print("The response of DefaultApi->g_et_xray_get_x_ray:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_xray_get_x_ray: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **xray_id** | **str**|  | 

### Return type

[**POSTXrayCreate200Response**](POSTXrayCreate200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_xray_get_x_ray_notifications**
> GETXrayGetXRayNotifications200Response g_et_xray_get_x_ray_notifications(xray_id, limit, offset)



### Example


```python
import elio_client
from elio_client.models.get_xray_get_x_ray_notifications200_response import GETXrayGetXRayNotifications200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    xray_id = 'xray_id_example' # str | 
    limit = 56 # int | 
    offset = 56 # int | 

    try:
        api_response = api_instance.g_et_xray_get_x_ray_notifications(xray_id, limit, offset)
        print("The response of DefaultApi->g_et_xray_get_x_ray_notifications:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_xray_get_x_ray_notifications: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **xray_id** | **str**|  | 
 **limit** | **int**|  | 
 **offset** | **int**|  | 

### Return type

[**GETXrayGetXRayNotifications200Response**](GETXrayGetXRayNotifications200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_xray_get_x_ray_template**
> GETXrayGetXRayTemplate200Response g_et_xray_get_x_ray_template(template_id)



### Example


```python
import elio_client
from elio_client.models.get_xray_get_x_ray_template200_response import GETXrayGetXRayTemplate200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    template_id = 'template_id_example' # str | 

    try:
        api_response = api_instance.g_et_xray_get_x_ray_template(template_id)
        print("The response of DefaultApi->g_et_xray_get_x_ray_template:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_xray_get_x_ray_template: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **template_id** | **str**|  | 

### Return type

[**GETXrayGetXRayTemplate200Response**](GETXrayGetXRayTemplate200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_xray_list_x_ray_templates**
> GETXrayListXRayTemplates200Response g_et_xray_list_x_ray_templates(limit, offset, type_filter, sort_by)



### Example


```python
import elio_client
from elio_client.models.get_xray_list_x_ray_templates200_response import GETXrayListXRayTemplates200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    offset = 56 # int | 
    type_filter = 'type_filter_example' # str | 
    sort_by = 'sort_by_example' # str | 

    try:
        api_response = api_instance.g_et_xray_list_x_ray_templates(limit, offset, type_filter, sort_by)
        print("The response of DefaultApi->g_et_xray_list_x_ray_templates:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_xray_list_x_ray_templates: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **offset** | **int**|  | 
 **type_filter** | **str**|  | 
 **sort_by** | **str**|  | 

### Return type

[**GETXrayListXRayTemplates200Response**](GETXrayListXRayTemplates200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **g_et_xray_list_x_rays**
> GETXrayListXRays200Response g_et_xray_list_x_rays(limit, offset, type_filter, sort_by)



### Example


```python
import elio_client
from elio_client.models.get_xray_list_x_rays200_response import GETXrayListXRays200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    limit = 56 # int | 
    offset = 56 # int | 
    type_filter = 'type_filter_example' # str | 
    sort_by = 'sort_by_example' # str | 

    try:
        api_response = api_instance.g_et_xray_list_x_rays(limit, offset, type_filter, sort_by)
        print("The response of DefaultApi->g_et_xray_list_x_rays:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->g_et_xray_list_x_rays: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **limit** | **int**|  | 
 **offset** | **int**|  | 
 **type_filter** | **str**|  | 
 **sort_by** | **str**|  | 

### Return type

[**GETXrayListXRays200Response**](GETXrayListXRays200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **h_ead_meetings_post_session_summary_optimistic**
> h_ead_meetings_post_session_summary_optimistic(session_id)

PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 

202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
        api_instance.h_ead_meetings_post_session_summary_optimistic(session_id)
    except Exception as e:
        print("Exception when calling DefaultApi->h_ead_meetings_post_session_summary_optimistic: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **h_ead_transcriptions_identity_associated**
> h_ead_transcriptions_identity_associated()

Needed to verify domains for Microsoft OAuth2 integration 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed to verify domains for Microsoft OAuth2 integration 
        api_instance.h_ead_transcriptions_identity_associated()
    except Exception as e:
        print("Exception when calling DefaultApi->h_ead_transcriptions_identity_associated: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **h_ead_transcriptions_microsoft_redirect_of_redirect**
> h_ead_transcriptions_microsoft_redirect_of_redirect()

Needed redirect back to recall 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed redirect back to recall 
        api_instance.h_ead_transcriptions_microsoft_redirect_of_redirect()
    except Exception as e:
        print("Exception when calling DefaultApi->h_ead_transcriptions_microsoft_redirect_of_redirect: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_meetings_post_session_summary_optimistic**
> p_atch_meetings_post_session_summary_optimistic(session_id)

PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 

202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
        api_instance.p_atch_meetings_post_session_summary_optimistic(session_id)
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_meetings_post_session_summary_optimistic: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_meetings_recur_session**
> PATCHMeetingsRecurSessionNoRequest200Response p_atch_meetings_recur_session(session_id, recurrence_id, x_client_info, patch_meetings_recur_session_request=patch_meetings_recur_session_request)



### Example


```python
import elio_client
from elio_client.models.patch_meetings_recur_session_no_request200_response import PATCHMeetingsRecurSessionNoRequest200Response
from elio_client.models.patch_meetings_recur_session_request import PATCHMeetingsRecurSessionRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 
    x_client_info = 'x_client_info_example' # str | 
    patch_meetings_recur_session_request = elio_client.PATCHMeetingsRecurSessionRequest() # PATCHMeetingsRecurSessionRequest |  (optional)

    try:
        api_response = api_instance.p_atch_meetings_recur_session(session_id, recurrence_id, x_client_info, patch_meetings_recur_session_request=patch_meetings_recur_session_request)
        print("The response of DefaultApi->p_atch_meetings_recur_session:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_meetings_recur_session: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 
 **x_client_info** | **str**|  | 
 **patch_meetings_recur_session_request** | [**PATCHMeetingsRecurSessionRequest**](PATCHMeetingsRecurSessionRequest.md)|  | [optional] 

### Return type

[**PATCHMeetingsRecurSessionNoRequest200Response**](PATCHMeetingsRecurSessionNoRequest200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_meetings_recur_session_no_request**
> PATCHMeetingsRecurSessionNoRequest200Response p_atch_meetings_recur_session_no_request(session_id, recurrence_id)



### Example


```python
import elio_client
from elio_client.models.patch_meetings_recur_session_no_request200_response import PATCHMeetingsRecurSessionNoRequest200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 

    try:
        api_response = api_instance.p_atch_meetings_recur_session_no_request(session_id, recurrence_id)
        print("The response of DefaultApi->p_atch_meetings_recur_session_no_request:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_meetings_recur_session_no_request: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 

### Return type

[**PATCHMeetingsRecurSessionNoRequest200Response**](PATCHMeetingsRecurSessionNoRequest200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_meetings_update_session**
> PATCHMeetingsUpdateSession200Response p_atch_meetings_update_session(session_id, recurrence_id, patch_meetings_update_session_request=patch_meetings_update_session_request)



### Example


```python
import elio_client
from elio_client.models.patch_meetings_update_session200_response import PATCHMeetingsUpdateSession200Response
from elio_client.models.patch_meetings_update_session_request import PATCHMeetingsUpdateSessionRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    recurrence_id = 'recurrence_id_example' # str | 
    patch_meetings_update_session_request = elio_client.PATCHMeetingsUpdateSessionRequest() # PATCHMeetingsUpdateSessionRequest |  (optional)

    try:
        api_response = api_instance.p_atch_meetings_update_session(session_id, recurrence_id, patch_meetings_update_session_request=patch_meetings_update_session_request)
        print("The response of DefaultApi->p_atch_meetings_update_session:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_meetings_update_session: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **recurrence_id** | **str**|  | 
 **patch_meetings_update_session_request** | [**PATCHMeetingsUpdateSessionRequest**](PATCHMeetingsUpdateSessionRequest.md)|  | [optional] 

### Return type

[**PATCHMeetingsUpdateSession200Response**](PATCHMeetingsUpdateSession200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_transcriptions_identity_associated**
> p_atch_transcriptions_identity_associated()

Needed to verify domains for Microsoft OAuth2 integration 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed to verify domains for Microsoft OAuth2 integration 
        api_instance.p_atch_transcriptions_identity_associated()
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_transcriptions_identity_associated: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_transcriptions_microsoft_redirect_of_redirect**
> p_atch_transcriptions_microsoft_redirect_of_redirect()

Needed redirect back to recall 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed redirect back to recall 
        api_instance.p_atch_transcriptions_microsoft_redirect_of_redirect()
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_transcriptions_microsoft_redirect_of_redirect: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_xray_mark_x_ray_notifications_seen**
> PATCHXrayMarkXRayNotificationsSeen200Response p_atch_xray_mark_x_ray_notifications_seen(xray_id)



### Example


```python
import elio_client
from elio_client.models.patch_xray_mark_x_ray_notifications_seen200_response import PATCHXrayMarkXRayNotificationsSeen200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    xray_id = 'xray_id_example' # str | 

    try:
        api_response = api_instance.p_atch_xray_mark_x_ray_notifications_seen(xray_id)
        print("The response of DefaultApi->p_atch_xray_mark_x_ray_notifications_seen:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_xray_mark_x_ray_notifications_seen: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **xray_id** | **str**|  | 

### Return type

[**PATCHXrayMarkXRayNotificationsSeen200Response**](PATCHXrayMarkXRayNotificationsSeen200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_atch_xray_update_x_ray**
> POSTXrayCreate200Response p_atch_xray_update_x_ray(xray_id, patch_xray_update_x_ray_request=patch_xray_update_x_ray_request)



### Example


```python
import elio_client
from elio_client.models.patch_xray_update_x_ray_request import PATCHXrayUpdateXRayRequest
from elio_client.models.post_xray_create200_response import POSTXrayCreate200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    xray_id = 'xray_id_example' # str | 
    patch_xray_update_x_ray_request = elio_client.PATCHXrayUpdateXRayRequest() # PATCHXrayUpdateXRayRequest |  (optional)

    try:
        api_response = api_instance.p_atch_xray_update_x_ray(xray_id, patch_xray_update_x_ray_request=patch_xray_update_x_ray_request)
        print("The response of DefaultApi->p_atch_xray_update_x_ray:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_atch_xray_update_x_ray: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **xray_id** | **str**|  | 
 **patch_xray_update_x_ray_request** | [**PATCHXrayUpdateXRayRequest**](PATCHXrayUpdateXRayRequest.md)|  | [optional] 

### Return type

[**POSTXrayCreate200Response**](POSTXrayCreate200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_comms_meeting_metadata_ready**
> p_ost_comms_meeting_metadata_ready(post_comms_meeting_metadata_ready_request=post_comms_meeting_metadata_ready_request)

MeetingMetadataReady is a endpoint to notify Elio that a specific session's metadata has been generated. 

Elio will then store the metadata title if it's a Recall or Listening Mode session. 

### Example


```python
import elio_client
from elio_client.models.post_comms_meeting_metadata_ready_request import POSTCommsMeetingMetadataReadyRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_comms_meeting_metadata_ready_request = elio_client.POSTCommsMeetingMetadataReadyRequest() # POSTCommsMeetingMetadataReadyRequest |  (optional)

    try:
        # MeetingMetadataReady is a endpoint to notify Elio that a specific session's metadata has been generated. 
        api_instance.p_ost_comms_meeting_metadata_ready(post_comms_meeting_metadata_ready_request=post_comms_meeting_metadata_ready_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_comms_meeting_metadata_ready: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_comms_meeting_metadata_ready_request** | [**POSTCommsMeetingMetadataReadyRequest**](POSTCommsMeetingMetadataReadyRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_comms_trigger_guest_waiting_in_lobby_notification**
> p_ost_comms_trigger_guest_waiting_in_lobby_notification(post_comms_trigger_guest_waiting_in_lobby_notification_request=post_comms_trigger_guest_waiting_in_lobby_notification_request)



### Example


```python
import elio_client
from elio_client.models.post_comms_trigger_guest_waiting_in_lobby_notification_request import POSTCommsTriggerGuestWaitingInLobbyNotificationRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_comms_trigger_guest_waiting_in_lobby_notification_request = elio_client.POSTCommsTriggerGuestWaitingInLobbyNotificationRequest() # POSTCommsTriggerGuestWaitingInLobbyNotificationRequest |  (optional)

    try:
        api_instance.p_ost_comms_trigger_guest_waiting_in_lobby_notification(post_comms_trigger_guest_waiting_in_lobby_notification_request=post_comms_trigger_guest_waiting_in_lobby_notification_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_comms_trigger_guest_waiting_in_lobby_notification: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_comms_trigger_guest_waiting_in_lobby_notification_request** | [**POSTCommsTriggerGuestWaitingInLobbyNotificationRequest**](POSTCommsTriggerGuestWaitingInLobbyNotificationRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_ai_feed_event**
> p_ost_meetings_ai_feed_event(post_meetings_ai_feed_event_request=post_meetings_ai_feed_event_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_ai_feed_event_request import POSTMeetingsAIFeedEventRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_ai_feed_event_request = elio_client.POSTMeetingsAIFeedEventRequest() # POSTMeetingsAIFeedEventRequest |  (optional)

    try:
        api_instance.p_ost_meetings_ai_feed_event(post_meetings_ai_feed_event_request=post_meetings_ai_feed_event_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_ai_feed_event: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_ai_feed_event_request** | [**POSTMeetingsAIFeedEventRequest**](POSTMeetingsAIFeedEventRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_count_in_review_requests**
> POSTMeetingsCountInReviewRequests200Response p_ost_meetings_count_in_review_requests(post_meetings_count_in_review_requests_request=post_meetings_count_in_review_requests_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_count_in_review_requests200_response import POSTMeetingsCountInReviewRequests200Response
from elio_client.models.post_meetings_count_in_review_requests_request import POSTMeetingsCountInReviewRequestsRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_count_in_review_requests_request = elio_client.POSTMeetingsCountInReviewRequestsRequest() # POSTMeetingsCountInReviewRequestsRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_count_in_review_requests(post_meetings_count_in_review_requests_request=post_meetings_count_in_review_requests_request)
        print("The response of DefaultApi->p_ost_meetings_count_in_review_requests:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_count_in_review_requests: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_count_in_review_requests_request** | [**POSTMeetingsCountInReviewRequestsRequest**](POSTMeetingsCountInReviewRequestsRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsCountInReviewRequests200Response**](POSTMeetingsCountInReviewRequests200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_create_access_request**
> p_ost_meetings_create_access_request(post_meetings_create_access_request_request=post_meetings_create_access_request_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_create_access_request_request import POSTMeetingsCreateAccessRequestRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_create_access_request_request = elio_client.POSTMeetingsCreateAccessRequestRequest() # POSTMeetingsCreateAccessRequestRequest |  (optional)

    try:
        api_instance.p_ost_meetings_create_access_request(post_meetings_create_access_request_request=post_meetings_create_access_request_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_create_access_request: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_create_access_request_request** | [**POSTMeetingsCreateAccessRequestRequest**](POSTMeetingsCreateAccessRequestRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_create_session**
> POSTMeetingsCreateSessionWithUser200Response p_ost_meetings_create_session(x_fingerprint, post_meetings_create_session_with_user_request=post_meetings_create_session_with_user_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_create_session_with_user200_response import POSTMeetingsCreateSessionWithUser200Response
from elio_client.models.post_meetings_create_session_with_user_request import POSTMeetingsCreateSessionWithUserRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    x_fingerprint = 'x_fingerprint_example' # str | This should match the old request payload from Mars: 
    post_meetings_create_session_with_user_request = elio_client.POSTMeetingsCreateSessionWithUserRequest() # POSTMeetingsCreateSessionWithUserRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_create_session(x_fingerprint, post_meetings_create_session_with_user_request=post_meetings_create_session_with_user_request)
        print("The response of DefaultApi->p_ost_meetings_create_session:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_create_session: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fingerprint** | **str**| This should match the old request payload from Mars:  | 
 **post_meetings_create_session_with_user_request** | [**POSTMeetingsCreateSessionWithUserRequest**](POSTMeetingsCreateSessionWithUserRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsCreateSessionWithUser200Response**](POSTMeetingsCreateSessionWithUser200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_create_session_with_user**
> POSTMeetingsCreateSessionWithUser200Response p_ost_meetings_create_session_with_user(x_fingerprint, post_meetings_create_session_with_user_request=post_meetings_create_session_with_user_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_create_session_with_user200_response import POSTMeetingsCreateSessionWithUser200Response
from elio_client.models.post_meetings_create_session_with_user_request import POSTMeetingsCreateSessionWithUserRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    x_fingerprint = 'x_fingerprint_example' # str | This should match the old request payload from Mars: 
    post_meetings_create_session_with_user_request = elio_client.POSTMeetingsCreateSessionWithUserRequest() # POSTMeetingsCreateSessionWithUserRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_create_session_with_user(x_fingerprint, post_meetings_create_session_with_user_request=post_meetings_create_session_with_user_request)
        print("The response of DefaultApi->p_ost_meetings_create_session_with_user:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_create_session_with_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fingerprint** | **str**| This should match the old request payload from Mars:  | 
 **post_meetings_create_session_with_user_request** | [**POSTMeetingsCreateSessionWithUserRequest**](POSTMeetingsCreateSessionWithUserRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsCreateSessionWithUser200Response**](POSTMeetingsCreateSessionWithUser200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_create_user**
> POSTMeetingsGetUserByIDRequest p_ost_meetings_create_user(post_meetings_create_user_request=post_meetings_create_user_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_create_user_request import POSTMeetingsCreateUserRequest
from elio_client.models.post_meetings_get_user_by_id_request import POSTMeetingsGetUserByIDRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_create_user_request = elio_client.POSTMeetingsCreateUserRequest() # POSTMeetingsCreateUserRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_create_user(post_meetings_create_user_request=post_meetings_create_user_request)
        print("The response of DefaultApi->p_ost_meetings_create_user:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_create_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_create_user_request** | [**POSTMeetingsCreateUserRequest**](POSTMeetingsCreateUserRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsGetUserByIDRequest**](POSTMeetingsGetUserByIDRequest.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_get_lobby_guests**
> POSTMeetingsGetLobbyGuests200Response p_ost_meetings_get_lobby_guests(lobby_id)

GetLobbyGuests returns the list of guests in the lobby 

### Example


```python
import elio_client
from elio_client.models.post_meetings_get_lobby_guests200_response import POSTMeetingsGetLobbyGuests200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    lobby_id = 'lobby_id_example' # str | LobbyID is the unique ID of the lobby 

    try:
        # GetLobbyGuests returns the list of guests in the lobby 
        api_response = api_instance.p_ost_meetings_get_lobby_guests(lobby_id)
        print("The response of DefaultApi->p_ost_meetings_get_lobby_guests:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_get_lobby_guests: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **lobby_id** | **str**| LobbyID is the unique ID of the lobby  | 

### Return type

[**POSTMeetingsGetLobbyGuests200Response**](POSTMeetingsGetLobbyGuests200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_get_session_access_rules**
> POSTMeetingsGetSessionAccessRules200Response p_ost_meetings_get_session_access_rules(session_id, session_recurrence_id)

GetSessionAccessRules returns all access rules for a session 

### Example


```python
import elio_client
from elio_client.models.post_meetings_get_session_access_rules200_response import POSTMeetingsGetSessionAccessRules200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 
    session_recurrence_id = 'session_recurrence_id_example' # str | 

    try:
        # GetSessionAccessRules returns all access rules for a session 
        api_response = api_instance.p_ost_meetings_get_session_access_rules(session_id, session_recurrence_id)
        print("The response of DefaultApi->p_ost_meetings_get_session_access_rules:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_get_session_access_rules: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 
 **session_recurrence_id** | **str**|  | 

### Return type

[**POSTMeetingsGetSessionAccessRules200Response**](POSTMeetingsGetSessionAccessRules200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_get_user_by_id**
> POSTMeetingsGetUserByID200Response p_ost_meetings_get_user_by_id(post_meetings_get_user_by_id_request=post_meetings_get_user_by_id_request)

GetUserByID gets user details by ID 

### Example


```python
import elio_client
from elio_client.models.post_meetings_get_user_by_id200_response import POSTMeetingsGetUserByID200Response
from elio_client.models.post_meetings_get_user_by_id_request import POSTMeetingsGetUserByIDRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_get_user_by_id_request = elio_client.POSTMeetingsGetUserByIDRequest() # POSTMeetingsGetUserByIDRequest |  (optional)

    try:
        # GetUserByID gets user details by ID 
        api_response = api_instance.p_ost_meetings_get_user_by_id(post_meetings_get_user_by_id_request=post_meetings_get_user_by_id_request)
        print("The response of DefaultApi->p_ost_meetings_get_user_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_get_user_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_get_user_by_id_request** | [**POSTMeetingsGetUserByIDRequest**](POSTMeetingsGetUserByIDRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsGetUserByID200Response**](POSTMeetingsGetUserByID200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_get_user_by_id_with_relations**
> POSTMeetingsGetUserByIDWithRelations200Response p_ost_meetings_get_user_by_id_with_relations(post_meetings_get_user_by_id_request=post_meetings_get_user_by_id_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_get_user_by_id_request import POSTMeetingsGetUserByIDRequest
from elio_client.models.post_meetings_get_user_by_id_with_relations200_response import POSTMeetingsGetUserByIDWithRelations200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_get_user_by_id_request = elio_client.POSTMeetingsGetUserByIDRequest() # POSTMeetingsGetUserByIDRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_get_user_by_id_with_relations(post_meetings_get_user_by_id_request=post_meetings_get_user_by_id_request)
        print("The response of DefaultApi->p_ost_meetings_get_user_by_id_with_relations:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_get_user_by_id_with_relations: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_get_user_by_id_request** | [**POSTMeetingsGetUserByIDRequest**](POSTMeetingsGetUserByIDRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsGetUserByIDWithRelations200Response**](POSTMeetingsGetUserByIDWithRelations200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_join_lobby_as_guest**
> p_ost_meetings_join_lobby_as_guest(post_meetings_join_lobby_as_guest_request=post_meetings_join_lobby_as_guest_request)

JoinLobbyAsGuest adds a guest to the lobby and publishes the event to the lobby listeners 

### Example


```python
import elio_client
from elio_client.models.post_meetings_join_lobby_as_guest_request import POSTMeetingsJoinLobbyAsGuestRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_join_lobby_as_guest_request = elio_client.POSTMeetingsJoinLobbyAsGuestRequest() # POSTMeetingsJoinLobbyAsGuestRequest |  (optional)

    try:
        # JoinLobbyAsGuest adds a guest to the lobby and publishes the event to the lobby listeners 
        api_instance.p_ost_meetings_join_lobby_as_guest(post_meetings_join_lobby_as_guest_request=post_meetings_join_lobby_as_guest_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_join_lobby_as_guest: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_join_lobby_as_guest_request** | [**POSTMeetingsJoinLobbyAsGuestRequest**](POSTMeetingsJoinLobbyAsGuestRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_list_sessions_by_lobby_id**
> GETMeetingsGetSessionRecurrencesById200Response p_ost_meetings_list_sessions_by_lobby_id(post_meetings_list_sessions_by_lobby_id_request=post_meetings_list_sessions_by_lobby_id_request)



### Example


```python
import elio_client
from elio_client.models.get_meetings_get_session_recurrences_by_id200_response import GETMeetingsGetSessionRecurrencesById200Response
from elio_client.models.post_meetings_list_sessions_by_lobby_id_request import POSTMeetingsListSessionsByLobbyIDRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_list_sessions_by_lobby_id_request = elio_client.POSTMeetingsListSessionsByLobbyIDRequest() # POSTMeetingsListSessionsByLobbyIDRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_list_sessions_by_lobby_id(post_meetings_list_sessions_by_lobby_id_request=post_meetings_list_sessions_by_lobby_id_request)
        print("The response of DefaultApi->p_ost_meetings_list_sessions_by_lobby_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_list_sessions_by_lobby_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_list_sessions_by_lobby_id_request** | [**POSTMeetingsListSessionsByLobbyIDRequest**](POSTMeetingsListSessionsByLobbyIDRequest.md)|  | [optional] 

### Return type

[**GETMeetingsGetSessionRecurrencesById200Response**](GETMeetingsGetSessionRecurrencesById200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_login_guest_user_with_session**
> POSTMeetingsLoginGuestUserWithSession200Response p_ost_meetings_login_guest_user_with_session(post_meetings_login_guest_user_with_session_request=post_meetings_login_guest_user_with_session_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_login_guest_user_with_session200_response import POSTMeetingsLoginGuestUserWithSession200Response
from elio_client.models.post_meetings_login_guest_user_with_session_request import POSTMeetingsLoginGuestUserWithSessionRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_login_guest_user_with_session_request = elio_client.POSTMeetingsLoginGuestUserWithSessionRequest() # POSTMeetingsLoginGuestUserWithSessionRequest |  (optional)

    try:
        api_response = api_instance.p_ost_meetings_login_guest_user_with_session(post_meetings_login_guest_user_with_session_request=post_meetings_login_guest_user_with_session_request)
        print("The response of DefaultApi->p_ost_meetings_login_guest_user_with_session:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_login_guest_user_with_session: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_login_guest_user_with_session_request** | [**POSTMeetingsLoginGuestUserWithSessionRequest**](POSTMeetingsLoginGuestUserWithSessionRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsLoginGuestUserWithSession200Response**](POSTMeetingsLoginGuestUserWithSession200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_post_session_summary_optimistic**
> p_ost_meetings_post_session_summary_optimistic(session_id)

PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 

202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
        api_instance.p_ost_meetings_post_session_summary_optimistic(session_id)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_post_session_summary_optimistic: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_remove_guest_from_lobby**
> p_ost_meetings_remove_guest_from_lobby(post_meetings_remove_guest_from_lobby_request=post_meetings_remove_guest_from_lobby_request)

RemoveGuestFromLobby removes a guest from the lobby and publishes the event to the lobby listeners 

### Example


```python
import elio_client
from elio_client.models.post_meetings_remove_guest_from_lobby_request import POSTMeetingsRemoveGuestFromLobbyRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_remove_guest_from_lobby_request = elio_client.POSTMeetingsRemoveGuestFromLobbyRequest() # POSTMeetingsRemoveGuestFromLobbyRequest |  (optional)

    try:
        # RemoveGuestFromLobby removes a guest from the lobby and publishes the event to the lobby listeners 
        api_instance.p_ost_meetings_remove_guest_from_lobby(post_meetings_remove_guest_from_lobby_request=post_meetings_remove_guest_from_lobby_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_remove_guest_from_lobby: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_remove_guest_from_lobby_request** | [**POSTMeetingsRemoveGuestFromLobbyRequest**](POSTMeetingsRemoveGuestFromLobbyRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_revoke_access_session_user**
> p_ost_meetings_revoke_access_session_user(post_meetings_revoke_access_session_user_request=post_meetings_revoke_access_session_user_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_revoke_access_session_user_request import POSTMeetingsRevokeAccessSessionUserRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_revoke_access_session_user_request = elio_client.POSTMeetingsRevokeAccessSessionUserRequest() # POSTMeetingsRevokeAccessSessionUserRequest |  (optional)

    try:
        api_instance.p_ost_meetings_revoke_access_session_user(post_meetings_revoke_access_session_user_request=post_meetings_revoke_access_session_user_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_revoke_access_session_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_revoke_access_session_user_request** | [**POSTMeetingsRevokeAccessSessionUserRequest**](POSTMeetingsRevokeAccessSessionUserRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_update_session_access_control_rules**
> p_ost_meetings_update_session_access_control_rules(post_meetings_update_session_access_control_rules_request=post_meetings_update_session_access_control_rules_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_update_session_access_control_rules_request import POSTMeetingsUpdateSessionAccessControlRulesRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_update_session_access_control_rules_request = elio_client.POSTMeetingsUpdateSessionAccessControlRulesRequest() # POSTMeetingsUpdateSessionAccessControlRulesRequest |  (optional)

    try:
        api_instance.p_ost_meetings_update_session_access_control_rules(post_meetings_update_session_access_control_rules_request=post_meetings_update_session_access_control_rules_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_update_session_access_control_rules: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_update_session_access_control_rules_request** | [**POSTMeetingsUpdateSessionAccessControlRulesRequest**](POSTMeetingsUpdateSessionAccessControlRulesRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_upsert_meeting_suggestions**
> POSTMeetingsUpsertMeetingSuggestions200Response p_ost_meetings_upsert_meeting_suggestions(post_meetings_upsert_meeting_suggestions_request=post_meetings_upsert_meeting_suggestions_request)

UpsertMeetingSuggestions stores or updates meeting suggestions for a session (called by Nebula) 

### Example


```python
import elio_client
from elio_client.models.post_meetings_upsert_meeting_suggestions200_response import POSTMeetingsUpsertMeetingSuggestions200Response
from elio_client.models.post_meetings_upsert_meeting_suggestions_request import POSTMeetingsUpsertMeetingSuggestionsRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_upsert_meeting_suggestions_request = elio_client.POSTMeetingsUpsertMeetingSuggestionsRequest() # POSTMeetingsUpsertMeetingSuggestionsRequest |  (optional)

    try:
        # UpsertMeetingSuggestions stores or updates meeting suggestions for a session (called by Nebula) 
        api_response = api_instance.p_ost_meetings_upsert_meeting_suggestions(post_meetings_upsert_meeting_suggestions_request=post_meetings_upsert_meeting_suggestions_request)
        print("The response of DefaultApi->p_ost_meetings_upsert_meeting_suggestions:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_upsert_meeting_suggestions: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_upsert_meeting_suggestions_request** | [**POSTMeetingsUpsertMeetingSuggestionsRequest**](POSTMeetingsUpsertMeetingSuggestionsRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsUpsertMeetingSuggestions200Response**](POSTMeetingsUpsertMeetingSuggestions200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_meetings_upsert_session_user**
> p_ost_meetings_upsert_session_user(post_meetings_upsert_session_user_request=post_meetings_upsert_session_user_request)



### Example


```python
import elio_client
from elio_client.models.post_meetings_upsert_session_user_request import POSTMeetingsUpsertSessionUserRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_meetings_upsert_session_user_request = elio_client.POSTMeetingsUpsertSessionUserRequest() # POSTMeetingsUpsertSessionUserRequest |  (optional)

    try:
        api_instance.p_ost_meetings_upsert_session_user(post_meetings_upsert_session_user_request=post_meetings_upsert_session_user_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_meetings_upsert_session_user: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_meetings_upsert_session_user_request** | [**POSTMeetingsUpsertSessionUserRequest**](POSTMeetingsUpsertSessionUserRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_cleanup_recall_users**
> p_ost_transcriptions_cleanup_recall_users()

CleanupRecallUsers is a cron job that removes users from Recall that have no connections. 

This prevents us from having tons of users with no connections on the recall side. 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # CleanupRecallUsers is a cron job that removes users from Recall that have no connections. 
        api_instance.p_ost_transcriptions_cleanup_recall_users()
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_cleanup_recall_users: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_create_bot_for_meeting**
> POSTTranscriptionsCreateBotForMeeting200Response p_ost_transcriptions_create_bot_for_meeting(post_transcriptions_create_bot_for_meeting_request=post_transcriptions_create_bot_for_meeting_request)



### Example


```python
import elio_client
from elio_client.models.post_transcriptions_create_bot_for_meeting200_response import POSTTranscriptionsCreateBotForMeeting200Response
from elio_client.models.post_transcriptions_create_bot_for_meeting_request import POSTTranscriptionsCreateBotForMeetingRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_create_bot_for_meeting_request = elio_client.POSTTranscriptionsCreateBotForMeetingRequest() # POSTTranscriptionsCreateBotForMeetingRequest |  (optional)

    try:
        api_response = api_instance.p_ost_transcriptions_create_bot_for_meeting(post_transcriptions_create_bot_for_meeting_request=post_transcriptions_create_bot_for_meeting_request)
        print("The response of DefaultApi->p_ost_transcriptions_create_bot_for_meeting:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_create_bot_for_meeting: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_create_bot_for_meeting_request** | [**POSTTranscriptionsCreateBotForMeetingRequest**](POSTTranscriptionsCreateBotForMeetingRequest.md)|  | [optional] 

### Return type

[**POSTTranscriptionsCreateBotForMeeting200Response**](POSTTranscriptionsCreateBotForMeeting200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_disconnect_calendar**
> p_ost_transcriptions_disconnect_calendar(post_transcriptions_disconnect_calendar_request=post_transcriptions_disconnect_calendar_request)

Disconnect a user's calendar platform from our Rumi bots recall.ai account 

### Example


```python
import elio_client
from elio_client.models.post_transcriptions_disconnect_calendar_request import POSTTranscriptionsDisconnectCalendarRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_disconnect_calendar_request = elio_client.POSTTranscriptionsDisconnectCalendarRequest() # POSTTranscriptionsDisconnectCalendarRequest |  (optional)

    try:
        # Disconnect a user's calendar platform from our Rumi bots recall.ai account 
        api_instance.p_ost_transcriptions_disconnect_calendar(post_transcriptions_disconnect_calendar_request=post_transcriptions_disconnect_calendar_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_disconnect_calendar: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_disconnect_calendar_request** | [**POSTTranscriptionsDisconnectCalendarRequest**](POSTTranscriptionsDisconnectCalendarRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_generate_audio_ingress_key**
> POSTTranscriptionsGenerateAudioIngressKey200Response p_ost_transcriptions_generate_audio_ingress_key(post_transcriptions_generate_audio_ingress_key_request=post_transcriptions_generate_audio_ingress_key_request)

GenerateAudioIngressKey represents a key used to authenticate audio ingress 

### Example


```python
import elio_client
from elio_client.models.post_transcriptions_generate_audio_ingress_key200_response import POSTTranscriptionsGenerateAudioIngressKey200Response
from elio_client.models.post_transcriptions_generate_audio_ingress_key_request import POSTTranscriptionsGenerateAudioIngressKeyRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_generate_audio_ingress_key_request = elio_client.POSTTranscriptionsGenerateAudioIngressKeyRequest() # POSTTranscriptionsGenerateAudioIngressKeyRequest |  (optional)

    try:
        # GenerateAudioIngressKey represents a key used to authenticate audio ingress 
        api_response = api_instance.p_ost_transcriptions_generate_audio_ingress_key(post_transcriptions_generate_audio_ingress_key_request=post_transcriptions_generate_audio_ingress_key_request)
        print("The response of DefaultApi->p_ost_transcriptions_generate_audio_ingress_key:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_generate_audio_ingress_key: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_generate_audio_ingress_key_request** | [**POSTTranscriptionsGenerateAudioIngressKeyRequest**](POSTTranscriptionsGenerateAudioIngressKeyRequest.md)|  | [optional] 

### Return type

[**POSTTranscriptionsGenerateAudioIngressKey200Response**](POSTTranscriptionsGenerateAudioIngressKey200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_identify_heard_speakers**
> p_ost_transcriptions_identify_heard_speakers(post_transcriptions_identify_heard_speakers_request=post_transcriptions_identify_heard_speakers_request)

IdentifyHeardSpeakers identifies an array of speakers heard in a session 

### Example


```python
import elio_client
from elio_client.models.post_transcriptions_identify_heard_speakers_request import POSTTranscriptionsIdentifyHeardSpeakersRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_identify_heard_speakers_request = elio_client.POSTTranscriptionsIdentifyHeardSpeakersRequest() # POSTTranscriptionsIdentifyHeardSpeakersRequest |  (optional)

    try:
        # IdentifyHeardSpeakers identifies an array of speakers heard in a session 
        api_instance.p_ost_transcriptions_identify_heard_speakers(post_transcriptions_identify_heard_speakers_request=post_transcriptions_identify_heard_speakers_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_identify_heard_speakers: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_identify_heard_speakers_request** | [**POSTTranscriptionsIdentifyHeardSpeakersRequest**](POSTTranscriptionsIdentifyHeardSpeakersRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_identity_associated**
> p_ost_transcriptions_identity_associated()

Needed to verify domains for Microsoft OAuth2 integration 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed to verify domains for Microsoft OAuth2 integration 
        api_instance.p_ost_transcriptions_identity_associated()
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_identity_associated: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_microsoft_redirect_of_redirect**
> p_ost_transcriptions_microsoft_redirect_of_redirect()

Needed redirect back to recall 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed redirect back to recall 
        api_instance.p_ost_transcriptions_microsoft_redirect_of_redirect()
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_microsoft_redirect_of_redirect: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_process_bot_recording**
> p_ost_transcriptions_process_bot_recording(post_transcriptions_process_bot_recording_request=post_transcriptions_process_bot_recording_request)



### Example


```python
import elio_client
from elio_client.models.post_transcriptions_process_bot_recording_request import POSTTranscriptionsProcessBotRecordingRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_process_bot_recording_request = elio_client.POSTTranscriptionsProcessBotRecordingRequest() # POSTTranscriptionsProcessBotRecordingRequest |  (optional)

    try:
        api_instance.p_ost_transcriptions_process_bot_recording(post_transcriptions_process_bot_recording_request=post_transcriptions_process_bot_recording_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_process_bot_recording: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_process_bot_recording_request** | [**POSTTranscriptionsProcessBotRecordingRequest**](POSTTranscriptionsProcessBotRecordingRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_recall_transcriptions_webhook**
> p_ost_transcriptions_recall_transcriptions_webhook(x_svix_id, x_svix_timestamp, x_svix_signature, token, post_transcriptions_recall_transcriptions_webhook_request=post_transcriptions_recall_transcriptions_webhook_request)



### Example


```python
import elio_client
from elio_client.models.post_transcriptions_recall_transcriptions_webhook_request import POSTTranscriptionsRecallTranscriptionsWebhookRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    x_svix_id = 'x_svix_id_example' # str | 
    x_svix_timestamp = 56 # int | 
    x_svix_signature = 'x_svix_signature_example' # str | 
    token = 'token_example' # str | 
    post_transcriptions_recall_transcriptions_webhook_request = elio_client.POSTTranscriptionsRecallTranscriptionsWebhookRequest() # POSTTranscriptionsRecallTranscriptionsWebhookRequest |  (optional)

    try:
        api_instance.p_ost_transcriptions_recall_transcriptions_webhook(x_svix_id, x_svix_timestamp, x_svix_signature, token, post_transcriptions_recall_transcriptions_webhook_request=post_transcriptions_recall_transcriptions_webhook_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_recall_transcriptions_webhook: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_svix_id** | **str**|  | 
 **x_svix_timestamp** | **int**|  | 
 **x_svix_signature** | **str**|  | 
 **token** | **str**|  | 
 **post_transcriptions_recall_transcriptions_webhook_request** | [**POSTTranscriptionsRecallTranscriptionsWebhookRequest**](POSTTranscriptionsRecallTranscriptionsWebhookRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_remove_users_from_recall_account**
> p_ost_transcriptions_remove_users_from_recall_account(post_transcriptions_remove_users_from_recall_account_request=post_transcriptions_remove_users_from_recall_account_request)

RemoveUsersFromRecallAccount Remove all users from the token passed in the params. 

We need to take into account that the delete user endpoint is rate limited to 10 request per minute. 

### Example


```python
import elio_client
from elio_client.models.post_transcriptions_remove_users_from_recall_account_request import POSTTranscriptionsRemoveUsersFromRecallAccountRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_remove_users_from_recall_account_request = elio_client.POSTTranscriptionsRemoveUsersFromRecallAccountRequest() # POSTTranscriptionsRemoveUsersFromRecallAccountRequest |  (optional)

    try:
        # RemoveUsersFromRecallAccount Remove all users from the token passed in the params. 
        api_instance.p_ost_transcriptions_remove_users_from_recall_account(post_transcriptions_remove_users_from_recall_account_request=post_transcriptions_remove_users_from_recall_account_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_remove_users_from_recall_account: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_remove_users_from_recall_account_request** | [**POSTTranscriptionsRemoveUsersFromRecallAccountRequest**](POSTTranscriptionsRemoveUsersFromRecallAccountRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_transcriptions_update**
> p_ost_transcriptions_update(post_transcriptions_update_request=post_transcriptions_update_request)



### Example


```python
import elio_client
from elio_client.models.post_transcriptions_update_request import POSTTranscriptionsUpdateRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_transcriptions_update_request = elio_client.POSTTranscriptionsUpdateRequest() # POSTTranscriptionsUpdateRequest |  (optional)

    try:
        api_instance.p_ost_transcriptions_update(post_transcriptions_update_request=post_transcriptions_update_request)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_transcriptions_update: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_transcriptions_update_request** | [**POSTTranscriptionsUpdateRequest**](POSTTranscriptionsUpdateRequest.md)|  | [optional] 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_xray_create**
> POSTXrayCreate200Response p_ost_xray_create(post_xray_create_request=post_xray_create_request)



### Example


```python
import elio_client
from elio_client.models.post_xray_create200_response import POSTXrayCreate200Response
from elio_client.models.post_xray_create_request import POSTXrayCreateRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_xray_create_request = elio_client.POSTXrayCreateRequest() # POSTXrayCreateRequest |  (optional)

    try:
        api_response = api_instance.p_ost_xray_create(post_xray_create_request=post_xray_create_request)
        print("The response of DefaultApi->p_ost_xray_create:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_xray_create: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_xray_create_request** | [**POSTXrayCreateRequest**](POSTXrayCreateRequest.md)|  | [optional] 

### Return type

[**POSTXrayCreate200Response**](POSTXrayCreate200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_xray_generate_x_ray_info**
> POSTXrayGenerateXRayInfo200Response p_ost_xray_generate_x_ray_info(post_xray_generate_x_ray_info_request=post_xray_generate_x_ray_info_request)



### Example


```python
import elio_client
from elio_client.models.post_xray_generate_x_ray_info200_response import POSTXrayGenerateXRayInfo200Response
from elio_client.models.post_xray_generate_x_ray_info_request import POSTXrayGenerateXRayInfoRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_xray_generate_x_ray_info_request = elio_client.POSTXrayGenerateXRayInfoRequest() # POSTXrayGenerateXRayInfoRequest |  (optional)

    try:
        api_response = api_instance.p_ost_xray_generate_x_ray_info(post_xray_generate_x_ray_info_request=post_xray_generate_x_ray_info_request)
        print("The response of DefaultApi->p_ost_xray_generate_x_ray_info:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_xray_generate_x_ray_info: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_xray_generate_x_ray_info_request** | [**POSTXrayGenerateXRayInfoRequest**](POSTXrayGenerateXRayInfoRequest.md)|  | [optional] 

### Return type

[**POSTXrayGenerateXRayInfo200Response**](POSTXrayGenerateXRayInfo200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_xray_generate_x_ray_prompt**
> POSTXrayGenerateXRayPrompt200Response p_ost_xray_generate_x_ray_prompt(post_xray_generate_x_ray_prompt_request=post_xray_generate_x_ray_prompt_request)



### Example


```python
import elio_client
from elio_client.models.post_xray_generate_x_ray_prompt200_response import POSTXrayGenerateXRayPrompt200Response
from elio_client.models.post_xray_generate_x_ray_prompt_request import POSTXrayGenerateXRayPromptRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    post_xray_generate_x_ray_prompt_request = elio_client.POSTXrayGenerateXRayPromptRequest() # POSTXrayGenerateXRayPromptRequest |  (optional)

    try:
        api_response = api_instance.p_ost_xray_generate_x_ray_prompt(post_xray_generate_x_ray_prompt_request=post_xray_generate_x_ray_prompt_request)
        print("The response of DefaultApi->p_ost_xray_generate_x_ray_prompt:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_xray_generate_x_ray_prompt: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **post_xray_generate_x_ray_prompt_request** | [**POSTXrayGenerateXRayPromptRequest**](POSTXrayGenerateXRayPromptRequest.md)|  | [optional] 

### Return type

[**POSTXrayGenerateXRayPrompt200Response**](POSTXrayGenerateXRayPrompt200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ost_xray_share_x_ray_as_template**
> POSTXrayCreate200Response p_ost_xray_share_x_ray_as_template(xray_id)



### Example


```python
import elio_client
from elio_client.models.post_xray_create200_response import POSTXrayCreate200Response
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    xray_id = 'xray_id_example' # str | 

    try:
        api_response = api_instance.p_ost_xray_share_x_ray_as_template(xray_id)
        print("The response of DefaultApi->p_ost_xray_share_x_ray_as_template:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ost_xray_share_x_ray_as_template: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **xray_id** | **str**|  | 

### Return type

[**POSTXrayCreate200Response**](POSTXrayCreate200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ut_meetings_add_session_access_control_rules**
> PUTMeetingsAddSessionAccessControlRules200Response p_ut_meetings_add_session_access_control_rules(x_fingerprint, put_meetings_add_session_access_control_rules_request=put_meetings_add_session_access_control_rules_request)

AddSessionAccessControlRules is now deprecated, please use UpdateSessionAccessControlRules (later down in this file) 

### Example


```python
import elio_client
from elio_client.models.put_meetings_add_session_access_control_rules200_response import PUTMeetingsAddSessionAccessControlRules200Response
from elio_client.models.put_meetings_add_session_access_control_rules_request import PUTMeetingsAddSessionAccessControlRulesRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    x_fingerprint = 'x_fingerprint_example' # str | 
    put_meetings_add_session_access_control_rules_request = elio_client.PUTMeetingsAddSessionAccessControlRulesRequest() # PUTMeetingsAddSessionAccessControlRulesRequest |  (optional)

    try:
        # AddSessionAccessControlRules is now deprecated, please use UpdateSessionAccessControlRules (later down in this file) 
        api_response = api_instance.p_ut_meetings_add_session_access_control_rules(x_fingerprint, put_meetings_add_session_access_control_rules_request=put_meetings_add_session_access_control_rules_request)
        print("The response of DefaultApi->p_ut_meetings_add_session_access_control_rules:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ut_meetings_add_session_access_control_rules: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fingerprint** | **str**|  | 
 **put_meetings_add_session_access_control_rules_request** | [**PUTMeetingsAddSessionAccessControlRulesRequest**](PUTMeetingsAddSessionAccessControlRulesRequest.md)|  | [optional] 

### Return type

[**PUTMeetingsAddSessionAccessControlRules200Response**](PUTMeetingsAddSessionAccessControlRules200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ut_meetings_approve_or_denyed_session_access_request**
> PUTMeetingsApproveOrDenyedSessionAccessRequest200Response p_ut_meetings_approve_or_denyed_session_access_request(x_fingerprint, put_meetings_approve_or_denyed_session_access_request_request=put_meetings_approve_or_denyed_session_access_request_request)

ApproveOrDenyedSessionAccessRequest is deprecated and should not be used. 

### Example


```python
import elio_client
from elio_client.models.put_meetings_approve_or_denyed_session_access_request200_response import PUTMeetingsApproveOrDenyedSessionAccessRequest200Response
from elio_client.models.put_meetings_approve_or_denyed_session_access_request_request import PUTMeetingsApproveOrDenyedSessionAccessRequestRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    x_fingerprint = 'x_fingerprint_example' # str | 
    put_meetings_approve_or_denyed_session_access_request_request = elio_client.PUTMeetingsApproveOrDenyedSessionAccessRequestRequest() # PUTMeetingsApproveOrDenyedSessionAccessRequestRequest |  (optional)

    try:
        # ApproveOrDenyedSessionAccessRequest is deprecated and should not be used. 
        api_response = api_instance.p_ut_meetings_approve_or_denyed_session_access_request(x_fingerprint, put_meetings_approve_or_denyed_session_access_request_request=put_meetings_approve_or_denyed_session_access_request_request)
        print("The response of DefaultApi->p_ut_meetings_approve_or_denyed_session_access_request:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ut_meetings_approve_or_denyed_session_access_request: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **x_fingerprint** | **str**|  | 
 **put_meetings_approve_or_denyed_session_access_request_request** | [**PUTMeetingsApproveOrDenyedSessionAccessRequestRequest**](PUTMeetingsApproveOrDenyedSessionAccessRequestRequest.md)|  | [optional] 

### Return type

[**PUTMeetingsApproveOrDenyedSessionAccessRequest200Response**](PUTMeetingsApproveOrDenyedSessionAccessRequest200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ut_meetings_post_session_summary_optimistic**
> p_ut_meetings_post_session_summary_optimistic(session_id)

PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 

202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str | 

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes 
        api_instance.p_ut_meetings_post_session_summary_optimistic(session_id)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ut_meetings_post_session_summary_optimistic: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **session_id** | **str**|  | 

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ut_meetings_update_user_by_id**
> POSTMeetingsGetUserByIDWithRelations200Response p_ut_meetings_update_user_by_id(user_id, put_meetings_update_user_by_id_request=put_meetings_update_user_by_id_request)

UpdateUserByID updates user details by their ID 

### Example


```python
import elio_client
from elio_client.models.post_meetings_get_user_by_id_with_relations200_response import POSTMeetingsGetUserByIDWithRelations200Response
from elio_client.models.put_meetings_update_user_by_id_request import PUTMeetingsUpdateUserByIDRequest
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    user_id = 'user_id_example' # str | 
    put_meetings_update_user_by_id_request = elio_client.PUTMeetingsUpdateUserByIDRequest() # PUTMeetingsUpdateUserByIDRequest |  (optional)

    try:
        # UpdateUserByID updates user details by their ID 
        api_response = api_instance.p_ut_meetings_update_user_by_id(user_id, put_meetings_update_user_by_id_request=put_meetings_update_user_by_id_request)
        print("The response of DefaultApi->p_ut_meetings_update_user_by_id:\n")
        pprint(api_response)
    except Exception as e:
        print("Exception when calling DefaultApi->p_ut_meetings_update_user_by_id: %s\n" % e)
```



### Parameters


Name | Type | Description  | Notes
------------- | ------------- | ------------- | -------------
 **user_id** | **str**|  | 
 **put_meetings_update_user_by_id_request** | [**PUTMeetingsUpdateUserByIDRequest**](PUTMeetingsUpdateUserByIDRequest.md)|  | [optional] 

### Return type

[**POSTMeetingsGetUserByIDWithRelations200Response**](POSTMeetingsGetUserByIDWithRelations200Response.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ut_transcriptions_identity_associated**
> p_ut_transcriptions_identity_associated()

Needed to verify domains for Microsoft OAuth2 integration 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed to verify domains for Microsoft OAuth2 integration 
        api_instance.p_ut_transcriptions_identity_associated()
    except Exception as e:
        print("Exception when calling DefaultApi->p_ut_transcriptions_identity_associated: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **p_ut_transcriptions_microsoft_redirect_of_redirect**
> p_ut_transcriptions_microsoft_redirect_of_redirect()

Needed redirect back to recall 

### Example


```python
import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)


# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)

    try:
        # Needed redirect back to recall 
        api_instance.p_ut_transcriptions_microsoft_redirect_of_redirect()
    except Exception as e:
        print("Exception when calling DefaultApi->p_ut_transcriptions_microsoft_redirect_of_redirect: %s\n" % e)
```



### Parameters

This endpoint does not need any parameter.

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details

| Status code | Description | Response headers |
|-------------|-------------|------------------|
**200** | Success response |  -  |
**0** | Error response |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

