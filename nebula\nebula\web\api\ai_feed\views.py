import traceback
from datetime import datetime
from typing import Annotated, List, Optional

import starlette.status
from fastapi import Depends, Header, HTTPException, Query, Response
from loguru import logger
from starlette import status

from nebula.db.models.ai_feed import AIFeed
from nebula.router import APIRouter
from nebula.services.ai_feed.ai_feed_service import (
    create_ai_feed,
    create_new_action_items_msg_for_ai_feed,
    create_new_insights_msg_for_ai_feed,
    create_new_summary_msg_for_ai_feed,
    fetch_ai_feed_msgs_paginated,
    get_ai_feed_by_id,
    get_catchup_summaries_paginated,
    populate_owner_for_ai_feed_msg_links,
    update_ai_feed_msg_dynamic_fields,
    update_ai_feed_msg_links,
)
from nebula.services.ai_feed.utils import mars_user_dto_to_user_basic
from nebula.services.elio.service import trigger_ai_feed_event
from nebula.services.mars.api_service import mars_get_user
from nebula.services.mars.schema import MarsGetUserResponseUserDTO
from nebula.web.api.ai_feed.schema import (
    CopyAIFeedsRequestDTO,
    CopyAIFeedsResponseDTO,
    EditAIFeedRequestBodyDTO,
    EditAIFeedResponseDTO,
    GetAIFeedPaginatedResponseDTO,
    GetAIFeedResponseDTO,
    GetCatchupSummariesPaginatedResponseDTO,
    PostAIFeedActionItemsRequestDTO,
    PostAIFeedActionItemsResponseDTO,
    PostAIFeedCatchupSummaryRequestDTO,
    PostAIFeedCatchupSummaryResponseDTO,
    PostAIFeedInsightsRequestDTO,
    PostAIFeedInsightsResponseDTO,
    PostAIFeedLinkRequestBodyDTO,
    convert_ai_feed_for_user_id,
    convert_ai_feed_msgs_into_catchup_summary_dtos,
    convert_ai_feed_msgs_into_slack_block_ui,
    patch_ai_feed_json_fields,
)
from nebula.web.api.auth import authorize

router = APIRouter()


@router.get(
    "/catchup-summaries",
    response_model=GetCatchupSummariesPaginatedResponseDTO,
    dependencies=[Depends(authorize)],
)
async def get_catchup_summaries(
    sessionID: str,  # noqa: N803
    sessionRecurrenceID: str,  # noqa: N803
    limit: Annotated[
        int,
        Query(
            ge=1,
            le=100,
        ),
    ] = 100,
    skip: Annotated[int, Query(ge=0)] = 0,
    sortOrder: Annotated[str, Query(max_length=4)] = "desc",
) -> GetCatchupSummariesPaginatedResponseDTO:
    """API to GET catchup summaries paginated.

    :param sessionID: Session ID
    :param sessionRecurrenceID: Session recurrence ID
    :param limit: Limit
    :param skip: Skip
    :param sortOrder: sortOrder
    :return: GetCatchupSummariesPaginatedResponseDTO
    """
    count, ai_feed_msgs = await get_catchup_summaries_paginated(
        session_id=sessionID,
        session_recurrence_id=sessionRecurrenceID,
        limit=limit,
        skip=skip,
        sort=sortOrder,
    )

    return GetCatchupSummariesPaginatedResponseDTO(
        message="Successfully retrieved catchup summaries paginated",
        data=GetCatchupSummariesPaginatedResponseDTO.Data(
            totalCount=count,
            summaries=convert_ai_feed_msgs_into_catchup_summary_dtos(ai_feed_msgs),
        ),
    )


@router.get(
    "/",
    response_model=GetAIFeedPaginatedResponseDTO,
    dependencies=[Depends(authorize)],
)
async def get_ai_feed_messages(
    response: Response,
    sessionID: str,  # noqa: N803
    sessionRecurrenceID: str,  # noqa: N803
    limit: Annotated[int, Query(ge=1, le=200)] = 100,
    skip: Annotated[int, Query(ge=0)] = 0,
    sortOrder: Annotated[str, Query(max_length=4)] = "desc",
    x_user_id: str | None = Header(default=None),
) -> GetAIFeedPaginatedResponseDTO:
    """API to fetch AI Feed messages paginated.

    :param response: response
    :param sessionID: session id
    :param sessionRecurrenceID: session recurrence id
    :param limit: limit
    :param skip: skip
    :param sortOrder: sort order

    :return: GetAIFeedPaginatedResponseDTO
    """
    total_mgs_count, ai_feed_msgs = await fetch_ai_feed_msgs_paginated(
        session_id=sessionID,
        session_recurrence_id=sessionRecurrenceID,
        limit=limit,
        skip=skip,
        sort=sortOrder,
    )

    await populate_owner_for_ai_feed_msg_links(ai_feed_msgs)

    # ai feed msgs are ordered by "end ts", but multiple messages from one batch may share the exact same timestamp
    # we can ensure the ordering by id afterward
    # reply: why isn't this done in other places where we list messages?
    sorted_msgs = sorted(ai_feed_msgs, key=lambda msg: msg["id"])
    if x_user_id is None:
        slack_ui_msgs = convert_ai_feed_msgs_into_slack_block_ui(
            msgs=sorted_msgs,
        )
    else:
        slack_ui_msgs = convert_ai_feed_for_user_id(
            ai_feeds=sorted(ai_feed_msgs, key=lambda msg: msg["id"]),
            user_id=x_user_id,
        )
    response.headers["Content-Type"] = "application/json"

    return GetAIFeedPaginatedResponseDTO(
        message="success",
        data=GetAIFeedPaginatedResponseDTO.Data(
            totalSummaryCount=total_mgs_count,
            messages=slack_ui_msgs,
        ),
    )


@router.post(
    "/copy",
    response_model=CopyAIFeedsResponseDTO,
    dependencies=[Depends(authorize)],
)
async def copy_ai_feeds(
    body: CopyAIFeedsRequestDTO,
):
    try:
        new_ai_feeds: List[AIFeed] = []
        ai_feeds_src = await AIFeed.objects().where(
            AIFeed.session_recurrence_id == body.srcRecurrenceID,
        )

        if len(ai_feeds_src) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="AI Feeds not found for source session",
            )

        for ai_feed in ai_feeds_src:
            feed = create_ai_feed(
                session_id=body.destSessionID,
                session_recurrence_id=body.destRecurrenceID,
                transcription_batch_id=ai_feed.transcription_batch_id,
                msg_type=ai_feed.msg_type,
                lang=ai_feed.lang,
                msg=ai_feed.msg,
                msg_dynamic_fields=ai_feed.msg_dynamic_fields,
                metadata_dynamic_fields=ai_feed.metadata_dynamic_fields,
                start_transcription_id=str(
                    ai_feed.start_transcription_id,
                ),
                start_transcription_time=ai_feed.start_transcription_time,
                end_transcription_id=str(
                    ai_feed.end_transcription_id,
                ),
                end_transcription_time=ai_feed.end_transcription_time,
                duration=ai_feed.duration,
            )
            await feed.save()
            new_ai_feeds.append(patch_ai_feed_json_fields(feed))

        await populate_owner_for_ai_feed_msg_links(new_ai_feeds)

        return CopyAIFeedsResponseDTO(
            message=f"Successfully copied ai feed messages from recurrence {body.srcRecurrenceID} to {body.destRecurrenceID}",
            data=CopyAIFeedsResponseDTO.Data(
                messages=[],
            ),
        )

    except Exception:
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=starlette.status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get(
    "/{ai_feed_id}",
    response_model=GetAIFeedResponseDTO,
    dependencies=[Depends(authorize)],
)
async def get_ai_feed(
    ai_feed_id: int,
    x_user_id: str | None = Header(default=None),
):
    """API to get an AI feed item.

    :param ai_feed_id: AI Feed ID
    :param x_user_id X-User-ID header
    :return: GetAIFeedResponseDTO
    """

    ai_feed = await get_ai_feed_by_id(ai_feed_id)

    if ai_feed is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="AI Feed Item not found",
        )

    msgs = [ai_feed]
    await populate_owner_for_ai_feed_msg_links(msgs)

    try:
        message = convert_ai_feed_for_user_id(msgs, x_user_id)[0]

        return GetAIFeedResponseDTO(
            message="OK",
            data=message,
        )
    except Exception:
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=starlette.status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch(
    "/{ai_feed_id}",
    response_model=EditAIFeedResponseDTO,
    dependencies=[Depends(authorize)],
)
async def edit_ai_feed(
    ai_feed_id: int,
    body: EditAIFeedRequestBodyDTO,
    x_user_id: str | None = Header(default=None),
):
    """API to edit an AI feed item.

    :param ai_feed_id: AI Feed ID
    :param body: EditAIFeedRequestBodyDTO
    :param x_user_id X-User-ID header
    :return: EditAIFeedResponseDTO
    """

    try:
        ai_feed = await get_ai_feed_by_id(ai_feed_id)

        if ai_feed is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI Feed not found",
            )

        new_dynamic_fields = (
            ai_feed.msg_dynamic_fields if ai_feed.msg_dynamic_fields is not None else {}
        )

        if (
            body.action == EditAIFeedRequestBodyDTO.ActionType.completed
            or body.action == EditAIFeedRequestBodyDTO.ActionType.deleted
        ):
            if not isinstance(body.value, bool):
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail='Provided "value" property is not a boolean',
                )

        if body.action == EditAIFeedRequestBodyDTO.ActionType.edited:
            if not isinstance(body.value, str):
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail='Provided "value" property is not a string',
                )

        user_response = await mars_get_user(x_user_id)
        user: Optional[MarsGetUserResponseUserDTO] = user_response.get("user")

        if user_response is None or user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found",
            )

        action = body.action.name.lower()

        new_dynamic_fields[action] = {
            "value": body.value,
            "user": mars_user_dto_to_user_basic(user).dict(),
        }
        if (
            body.action == EditAIFeedRequestBodyDTO.ActionType.deleted
            and body.why is not None
        ):
            new_dynamic_fields[action]["why"] = body.why

        await update_ai_feed_msg_dynamic_fields(
            ai_feed_id,
            new_dynamic_fields,
        )
        ai_feed = await get_ai_feed_by_id(ai_feed_id)

        if ai_feed is None:
            raise ValueError("AI Feed not found after updating")

        msgs = [ai_feed]
        await populate_owner_for_ai_feed_msg_links(msgs)

        await trigger_ai_feed_event(
            session_id=int(ai_feed.session_id),
            recurrence_id=int(ai_feed.session_recurrence_id),
            ev_type="feed_item_ready",
            ai_feed_id=ai_feed.id,
        )

        return EditAIFeedResponseDTO(
            message="AI Feed successfully updated",
            data=convert_ai_feed_for_user_id(msgs, x_user_id)[0],
        )
    except Exception:
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=starlette.status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post(
    "/{ai_feed_id}/links",
    dependencies=[Depends(authorize)],
)
async def post_ai_feed_link(
    ai_feed_id: int,
    body: PostAIFeedLinkRequestBodyDTO,
):
    try:
        ai_feed = await get_ai_feed_by_id(ai_feed_id)

        if ai_feed is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="AI Feed not found",
            )

        links = ai_feed.links if ai_feed.links is not None else []

        current_utc_timestamp = datetime.utcnow()

        links.append(
            {
                "provider_name": body.providerName,
                "action_name": body.actionName,
                "integration_name": body.integrationName,
                "provider_icon": body.providerIcon,
                "provider_icon_background_color": body.providerIconBackgroundColor,
                "provider_category": body.providerCategory,
                "provider_category_label": body.providerCategoryLabel,
                "owner_id": body.ownerId,
                "created_at": current_utc_timestamp.isoformat(),
                "private_message": body.privateMessage,
                "private_link_url": None
                if body.privateLink is None
                else body.privateLink.url,
                "private_link_label": None
                if body.privateLink is None
                else body.privateLink.label,
                "public_message": body.publicMessage,
                "public_link_url": None
                if body.publicLink is None
                else body.publicLink.url,
                "public_link_label": None
                if body.publicLink is None
                else body.publicLink.label,
            }
        )

        await update_ai_feed_msg_links(ai_feed_id=ai_feed_id, updated_links=links)

        # update links
        ai_feed.links = links
        msgs = [ai_feed]
        await populate_owner_for_ai_feed_msg_links(msgs)

        await trigger_ai_feed_event(
            session_id=int(ai_feed.session_id),
            recurrence_id=int(ai_feed.session_recurrence_id),
            ev_type="feed_item_ready",
            ai_feed_id=ai_feed.id,
        )

        return {"message": "AI Feed link successfully added"}

    except Exception:
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=starlette.status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.post(
    "/action-items",
    response_model=PostAIFeedActionItemsResponseDTO,
    dependencies=[Depends(authorize)],
)
async def post_ai_feed_action_items(
    body: PostAIFeedActionItemsRequestDTO,
) -> PostAIFeedActionItemsResponseDTO:
    """API for ray actor to create action items.

    :param body: PostAIFeedActionItemsRequestDTO
    :raises Exception: In case something fails in api request
    :return: PostAIFeedActionItemsResponseDTO
    """
    try:
        if body.actionItems is None or len(body.actionItems) == 0:
            logger.info("No action items found in the response body")
            return PostAIFeedActionItemsResponseDTO(message="success")

        feeds = await create_new_action_items_msg_for_ai_feed(body)

        for item in feeds:
            await trigger_ai_feed_event(
                session_id=int(item.session_id),
                recurrence_id=int(item.session_recurrence_id),
                ev_type="feed_item_ready",
                ai_feed_id=item.id,
            )

        return PostAIFeedActionItemsResponseDTO(message="success")
    except Exception:
        logger.error(
            f"post_ai_feed_action_items:: Failed for batch_id: {body.batchID}, session_id: {body.sessionID}, session_recurrence_id: {body.sessionRecurrenceID}",
        )
        logger.error(traceback.format_exc())
        raise


@router.post(
    "/catchup-summary",
    response_model=PostAIFeedCatchupSummaryResponseDTO,
    dependencies=[Depends(authorize)],
)
async def post_ai_feed_catchup_summary(
    body: PostAIFeedCatchupSummaryRequestDTO,
) -> PostAIFeedCatchupSummaryResponseDTO:
    """API for ray actor to create catchup summary.

    :param body: PostAIFeedCatchupSummaryRequestDTO
    :raises Exception: In case something fails in api request
    :return: PostAIFeedCatchupSummaryResponseDTO
    """
    try:
        if body.summary is None:
            logger.info("No summary found in the response body")
            return PostAIFeedCatchupSummaryResponseDTO(message="success")

        ai_feed_item = await create_new_summary_msg_for_ai_feed(body)
        await trigger_ai_feed_event(
            session_id=int(ai_feed_item.session_id),
            recurrence_id=int(ai_feed_item.session_recurrence_id),
            ev_type="feed_item_ready",
            ai_feed_id=ai_feed_item.id,
        )

        return PostAIFeedCatchupSummaryResponseDTO(message="success")
    except Exception:
        logger.error(
            f"post_ai_feed_catchup_summary:: Failed for batch_id: {body.batchID}, session_id: {body.sessionID}, session_recurrence_id: {body.sessionRecurrenceID}",
        )
        logger.error(traceback.format_exc())
        raise


@router.post(
    "/insights",
    response_model=PostAIFeedInsightsResponseDTO,
    dependencies=[Depends(authorize)],
)
async def post_ai_feed_insights(
    body: PostAIFeedInsightsRequestDTO,
) -> PostAIFeedInsightsResponseDTO:
    """API for ray actor to create catchup summary.

    :param body: PostAIFeedInsightsRequestDTO
    :raises Exception: In case something fails in api request
    :return: PostAIFeedInsightsResponseDTO
    """
    try:
        if not hasattr(body, "insights") or len(body.insights) == 0:
            logger.info("No insights found in the response body")
            return PostAIFeedInsightsResponseDTO(message="success")

        feeds = await create_new_insights_msg_for_ai_feed(body)

        for item in feeds:
            await trigger_ai_feed_event(
                session_id=int(item.session_id),
                recurrence_id=int(item.session_recurrence_id),
                ev_type="feed_item_ready",
                ai_feed_id=item.id,
            )

        return PostAIFeedInsightsResponseDTO(message="success")
    except Exception:
        logger.error(
            f"post_ai_feed_insights:: Failed for batch_id: {body.batchID}, session_id: {body.sessionID}, session_recurrence_id: {body.sessionRecurrenceID}",
        )
        logger.error(traceback.format_exc())
        raise
