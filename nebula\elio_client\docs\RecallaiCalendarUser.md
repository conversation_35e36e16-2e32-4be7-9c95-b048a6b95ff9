# RecallaiCalendarUser


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**connections** | [**List[RecallaiRecallConnection]**](RecallaiRecallConnection.md) |  | 
**external_id** | **str** |  | 
**id** | **str** |  | 
**preferences** | [**RecallaiRecordingPreferences**](RecallaiRecordingPreferences.md) |  | 

## Example

```python
from elio_client.models.recallai_calendar_user import RecallaiCalendarUser

# TODO update the JSON string below
json = "{}"
# create an instance of RecallaiCalendarUser from a JSON string
recallai_calendar_user_instance = RecallaiCalendarUser.from_json(json)
# print the JSON string representation of the object
print(RecallaiCalendarUser.to_json())

# convert the object into a dict
recallai_calendar_user_dict = recallai_calendar_user_instance.to_dict()
# create an instance of Recal<PERSON>CalendarUser from a dict
recallai_calendar_user_from_dict = <PERSON>callaiCalendarUser.from_dict(recallai_calendar_user_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


