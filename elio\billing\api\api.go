package api

import "encore.app/shared"

type SubscriptionPlanConfigDTOAndPaddleProductDTO struct {
	shared.BillingProductDTO
	Config shared.SubscriptionPlanConfigDTO `json:"config"`
}

type ListPlansResponse struct {
	Success bool                                           `json:"success"`
	Message string                                         `json:"message"`
	Data    []SubscriptionPlanConfigDTOAndPaddleProductDTO `json:"data"`
}

type ListProductsRequest struct {
	IncludePrices bool `json:"includePrices"`
}

type ListProductsResponse struct {
	Products []shared.BillingProductDTO `json:"products"`
}

type ListTransactionsByCustomerIDRequest struct {
	CustomerID string    `json:"customerID"`
	Limit      *int      `json:"limit"`
	Status     *[]string `json:"status"`
}

type ListTransactionsByCustomerIDResponse struct {
	Transactions []shared.BillingTransactionDTO `json:"transactions"`
}

type GetInvoiceByTransactionIDRequest struct {
	TransactionID string `json:"transactionID"`
}

type GetInvoiceByTransactionIDResponse struct {
	URL string `json:"url"`
}

type GetSubscriptionUpdatePaymentMethodTransactionRequest struct {
	SubscriptionID string `json:"subscriptionID"`
}

type GetSubscriptionUpdatePaymentMethodTransactionResponse struct {
	Transaction shared.BillingTransactionDTO `json:"transaction"`
}
