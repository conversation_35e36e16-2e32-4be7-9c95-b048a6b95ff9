# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List
from elio_client.models.shared_subscription_plan_config_dto_integrations import SharedSubscriptionPlanConfigDTOIntegrations
from elio_client.models.shared_subscription_plan_config_dto_meetings import SharedSubscriptionPlanConfigDTOMeetings
from elio_client.models.shared_subscription_plan_config_dto_recording import SharedSubscriptionPlanConfigDTORecording
from elio_client.models.shared_subscription_plan_config_dto_stream import SharedSubscriptionPlanConfigDTOStream
from elio_client.models.shared_subscription_plan_config_dto_support import SharedSubscriptionPlanConfigDTOSupport
from elio_client.models.shared_subscription_plan_config_dto_time_limit import SharedSubscriptionPlanConfigDTOTimeLimit
from elio_client.models.shared_subscription_plan_config_dtoai_feed import SharedSubscriptionPlanConfigDTOAiFeed
from typing import Optional, Set
from typing_extensions import Self

class SharedSubscriptionPlanConfigDTO(BaseModel):
    """
    SharedSubscriptionPlanConfigDTO
    """ # noqa: E501
    ai_feed: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="aiFeed")
    bots: SharedSubscriptionPlanConfigDTOAiFeed
    created_at: StrictInt = Field(alias="createdAt")
    crm: SharedSubscriptionPlanConfigDTOAiFeed
    custom_feed_items: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="customFeedItems")
    custom_integrations: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="customIntegrations")
    id: StrictStr
    integrations: SharedSubscriptionPlanConfigDTOIntegrations
    meeting_memory: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="meetingMemory")
    meeting_summary: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="meetingSummary")
    meeting_templates: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="meetingTemplates")
    meeting_workflows: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="meetingWorkflows")
    meetings: SharedSubscriptionPlanConfigDTOMeetings
    model_segregation: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="modelSegregation")
    off_the_record: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="offTheRecord")
    paddle_product_id: StrictStr = Field(alias="paddleProductID")
    paddle_product_name: StrictStr = Field(alias="paddleProductName")
    queue_mode: SharedSubscriptionPlanConfigDTOAiFeed = Field(alias="queueMode")
    recording: SharedSubscriptionPlanConfigDTORecording
    stream: SharedSubscriptionPlanConfigDTOStream
    support: SharedSubscriptionPlanConfigDTOSupport
    time_limit: SharedSubscriptionPlanConfigDTOTimeLimit = Field(alias="timeLimit")
    updated_at: StrictInt = Field(alias="updatedAt")
    __properties: ClassVar[List[str]] = ["aiFeed", "bots", "createdAt", "crm", "customFeedItems", "customIntegrations", "id", "integrations", "meetingMemory", "meetingSummary", "meetingTemplates", "meetingWorkflows", "meetings", "modelSegregation", "offTheRecord", "paddleProductID", "paddleProductName", "queueMode", "recording", "stream", "support", "timeLimit", "updatedAt"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of SharedSubscriptionPlanConfigDTO from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of ai_feed
        if self.ai_feed:
            _dict['aiFeed'] = self.ai_feed.to_dict()
        # override the default output from pydantic by calling `to_dict()` of bots
        if self.bots:
            _dict['bots'] = self.bots.to_dict()
        # override the default output from pydantic by calling `to_dict()` of crm
        if self.crm:
            _dict['crm'] = self.crm.to_dict()
        # override the default output from pydantic by calling `to_dict()` of custom_feed_items
        if self.custom_feed_items:
            _dict['customFeedItems'] = self.custom_feed_items.to_dict()
        # override the default output from pydantic by calling `to_dict()` of custom_integrations
        if self.custom_integrations:
            _dict['customIntegrations'] = self.custom_integrations.to_dict()
        # override the default output from pydantic by calling `to_dict()` of integrations
        if self.integrations:
            _dict['integrations'] = self.integrations.to_dict()
        # override the default output from pydantic by calling `to_dict()` of meeting_memory
        if self.meeting_memory:
            _dict['meetingMemory'] = self.meeting_memory.to_dict()
        # override the default output from pydantic by calling `to_dict()` of meeting_summary
        if self.meeting_summary:
            _dict['meetingSummary'] = self.meeting_summary.to_dict()
        # override the default output from pydantic by calling `to_dict()` of meeting_templates
        if self.meeting_templates:
            _dict['meetingTemplates'] = self.meeting_templates.to_dict()
        # override the default output from pydantic by calling `to_dict()` of meeting_workflows
        if self.meeting_workflows:
            _dict['meetingWorkflows'] = self.meeting_workflows.to_dict()
        # override the default output from pydantic by calling `to_dict()` of meetings
        if self.meetings:
            _dict['meetings'] = self.meetings.to_dict()
        # override the default output from pydantic by calling `to_dict()` of model_segregation
        if self.model_segregation:
            _dict['modelSegregation'] = self.model_segregation.to_dict()
        # override the default output from pydantic by calling `to_dict()` of off_the_record
        if self.off_the_record:
            _dict['offTheRecord'] = self.off_the_record.to_dict()
        # override the default output from pydantic by calling `to_dict()` of queue_mode
        if self.queue_mode:
            _dict['queueMode'] = self.queue_mode.to_dict()
        # override the default output from pydantic by calling `to_dict()` of recording
        if self.recording:
            _dict['recording'] = self.recording.to_dict()
        # override the default output from pydantic by calling `to_dict()` of stream
        if self.stream:
            _dict['stream'] = self.stream.to_dict()
        # override the default output from pydantic by calling `to_dict()` of support
        if self.support:
            _dict['support'] = self.support.to_dict()
        # override the default output from pydantic by calling `to_dict()` of time_limit
        if self.time_limit:
            _dict['timeLimit'] = self.time_limit.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of SharedSubscriptionPlanConfigDTO from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "aiFeed": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["aiFeed"]) if obj.get("aiFeed") is not None else None,
            "bots": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["bots"]) if obj.get("bots") is not None else None,
            "createdAt": obj.get("createdAt"),
            "crm": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["crm"]) if obj.get("crm") is not None else None,
            "customFeedItems": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["customFeedItems"]) if obj.get("customFeedItems") is not None else None,
            "customIntegrations": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["customIntegrations"]) if obj.get("customIntegrations") is not None else None,
            "id": obj.get("id"),
            "integrations": SharedSubscriptionPlanConfigDTOIntegrations.from_dict(obj["integrations"]) if obj.get("integrations") is not None else None,
            "meetingMemory": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["meetingMemory"]) if obj.get("meetingMemory") is not None else None,
            "meetingSummary": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["meetingSummary"]) if obj.get("meetingSummary") is not None else None,
            "meetingTemplates": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["meetingTemplates"]) if obj.get("meetingTemplates") is not None else None,
            "meetingWorkflows": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["meetingWorkflows"]) if obj.get("meetingWorkflows") is not None else None,
            "meetings": SharedSubscriptionPlanConfigDTOMeetings.from_dict(obj["meetings"]) if obj.get("meetings") is not None else None,
            "modelSegregation": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["modelSegregation"]) if obj.get("modelSegregation") is not None else None,
            "offTheRecord": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["offTheRecord"]) if obj.get("offTheRecord") is not None else None,
            "paddleProductID": obj.get("paddleProductID"),
            "paddleProductName": obj.get("paddleProductName"),
            "queueMode": SharedSubscriptionPlanConfigDTOAiFeed.from_dict(obj["queueMode"]) if obj.get("queueMode") is not None else None,
            "recording": SharedSubscriptionPlanConfigDTORecording.from_dict(obj["recording"]) if obj.get("recording") is not None else None,
            "stream": SharedSubscriptionPlanConfigDTOStream.from_dict(obj["stream"]) if obj.get("stream") is not None else None,
            "support": SharedSubscriptionPlanConfigDTOSupport.from_dict(obj["support"]) if obj.get("support") is not None else None,
            "timeLimit": SharedSubscriptionPlanConfigDTOTimeLimit.from_dict(obj["timeLimit"]) if obj.get("timeLimit") is not None else None,
            "updatedAt": obj.get("updatedAt")
        })
        return _obj


