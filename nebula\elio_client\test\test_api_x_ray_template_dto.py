# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_x_ray_template_dto import ApiXRayTemplateDTO

class TestApiXRayTemplateDTO(unittest.TestCase):
    """ApiXRayTemplateDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiXRayTemplateDTO:
        """Test ApiXRayTemplateDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiXRayTemplateDTO`
        """
        model = ApiXRayTemplateDTO()
        if include_optional:
            return ApiXRayTemplateDTO(
                created_at = 56,
                description = '',
                icon = '',
                id = 56,
                owner = elio_client.models.shared/user_dto.shared.UserDTO(
                    about = '', 
                    apple_id = '', 
                    avatar = '', 
                    created_at = 56, 
                    customer_id = '', 
                    email = '', 
                    fingerprint = elio_client.models.the_fingerprint_of_the_user
.the fingerprint of the user
(), 
                    first_name = '', 
                    google_id = '', 
                    id = '', 
                    is_test_user = True, 
                    last_name = '', 
                    lobbies = [
                        elio_client.models.shared/lobby_dto.shared.LobbyDTO(
                            is_active = True, 
                            lobby_id = '', 
                            slug = '', )
                        ], 
                    marketing_opt_in = True, 
                    nft_avatar = elio_client.models.shared/user_crypto_token.shared.UserCryptoToken(
                        blockchain = 1.337, 
                        created_at = 56, 
                        details = elio_client.models.the_metadata_of_the_token
.the metadata of the token
(), 
                        id = '', 
                        purpose_type = '', 
                        token = '', 
                        updated_at = 56, 
                        url = '', 
                        user_id = '', 
                        wallet_id = '', ), 
                    notification_settings = elio_client.models.shared/notification_settings.shared.NotificationSettings(
                        is_email_notification_enabled_global = True, 
                        is_push_notification_enabled_global = True, ), 
                    onboarding = elio_client.models.shared/onboarding_flags.shared.OnboardingFlags(
                        0 = True, 
                        1 = True, 
                        2 = True, 
                        3 = True, 
                        4 = True, 
                        5 = True, 
                        6 = '', ), 
                    role_ids = [
                        ''
                        ], 
                    sign_up_timestamp = 56, 
                    social = [
                        elio_client.models.shared/user_social_dto.shared.UserSocialDTO(
                            avatar = '', 
                            bio = '', 
                            first_name = '', 
                            full_name = '', 
                            last_name = '', 
                            platform = '', 
                            url = '', 
                            user_name = '', )
                        ], 
                    subscription_plan = elio_client.models.shared/user_subscription_plan_dto.shared.UserSubscriptionPlanDTO(
                        address_id = '', 
                        billing_cycle = elio_client.models.shared/billing_cycle_dto.shared.BillingCycleDTO(
                            frequency = 1.337, 
                            interval = '', ), 
                        billing_details = elio_client.models.shared_user_subscription_plan_dto_billing_details.shared_UserSubscriptionPlanDTO_billingDetails(
                            additional_information = '', 
                            enable_checkout = True, 
                            purchase_order_minimum = '', ), 
                        business_id = '', 
                        canceled_at = '', 
                        collection_mode = '', 
                        created_at = 56, 
                        currency_code = '', 
                        current_billing_period = elio_client.models.shared_subscription_item_dto_trial_dates.shared_SubscriptionItemDTO_trialDates(
                            ends_at = '', 
                            starts_at = '', ), 
                        custom_data = elio_client.models.custom_data.customData(), 
                        customer_id = '', 
                        first_billed_at = '', 
                        id = '', 
                        items = [
                            elio_client.models.shared/subscription_item_dto.shared.SubscriptionItemDTO(
                                created_at = '', 
                                custom_data = elio_client.models.custom_data.customData(), 
                                next_billed_at = '', 
                                previously_billed_at = '', 
                                price = elio_client.models.shared/price_dto.shared.PriceDTO(
                                    id = '', 
                                    product_id = '', 
                                    quantity = elio_client.models.shared/price_quantity_dto.shared.PriceQuantityDTO(
                                        maximum = 1.337, 
                                        minimum = 1.337, ), 
                                    status = '', 
                                    tax_mode = '', 
                                    trial_period = elio_client.models.shared/price_dto_trial_period.shared.PriceDTOTrialPeriod(
                                        frequency = 1.337, 
                                        interval = '', ), 
                                    unit_price = elio_client.models.shared/unit_price_dto.shared.UnitPriceDTO(
                                        amount = '', 
                                        currency_code = '', ), 
                                    unit_price_overrides = [
                                        elio_client.models.shared/unit_price_override_dto.shared.UnitPriceOverrideDTO(
                                            country_codes = [
                                                ''
                                                ], )
                                        ], ), 
                                quantity = 1.337, 
                                recurring = True, 
                                status = '', 
                                trial_dates = elio_client.models.shared_subscription_item_dto_trial_dates.shared_SubscriptionItemDTO_trialDates(
                                    ends_at = '', 
                                    starts_at = '', ), 
                                updated_at = '', )
                            ], 
                        management_urls = elio_client.models.shared_user_subscription_plan_dto_management_urls.shared_UserSubscriptionPlanDTO_managementUrls(
                            cancel = '', 
                            update_payment_method = '', ), 
                        next_billed_at = '', 
                        paddle_subscription_id = '', 
                        paused_at = '', 
                        plan_config = elio_client.models.shared/subscription_plan_config_dto.shared.SubscriptionPlanConfigDTO(
                            ai_feed = elio_client.models.shared_subscription_plan_config_dto_ai_feed.shared_SubscriptionPlanConfigDTO_aiFeed(
                                enabled = True, ), 
                            bots = elio_client.models.shared_subscription_plan_config_dto_ai_feed.shared_SubscriptionPlanConfigDTO_aiFeed(
                                enabled = True, ), 
                            created_at = 56, 
                            crm = , 
                            custom_feed_items = , 
                            custom_integrations = , 
                            id = '', 
                            integrations = elio_client.models.shared_subscription_plan_config_dto_integrations.shared_SubscriptionPlanConfigDTO_integrations(
                                apps = [
                                    ''
                                    ], 
                                enabled = True, ), 
                            meeting_memory = , 
                            meeting_summary = , 
                            meeting_templates = , 
                            meeting_workflows = , 
                            meetings = elio_client.models.shared_subscription_plan_config_dto_meetings.shared_SubscriptionPlanConfigDTO_meetings(
                                enabled = True, 
                                max = 1.337, ), 
                            model_segregation = , 
                            off_the_record = , 
                            paddle_product_id = '', 
                            paddle_product_name = '', 
                            queue_mode = , 
                            recording = elio_client.models.shared_subscription_plan_config_dto_recording.shared_SubscriptionPlanConfigDTO_recording(
                                enabled = True, 
                                local = True, ), 
                            stream = elio_client.models.shared_subscription_plan_config_dto_stream.shared_SubscriptionPlanConfigDTO_stream(
                                enabled = True, 
                                quality = 1.337, ), 
                            support = elio_client.models.shared_subscription_plan_config_dto_support.shared_SubscriptionPlanConfigDTO_support(
                                enabled = True, 
                                type = '', ), 
                            time_limit = elio_client.models.shared_subscription_plan_config_dto_time_limit.shared_SubscriptionPlanConfigDTO_timeLimit(
                                enabled = True, 
                                max = 56, ), 
                            updated_at = 56, ), 
                        plan_config_overrides = elio_client.models.plan_config_overrides.planConfigOverrides(), 
                        scheduled_change = elio_client.models.shared_user_subscription_plan_dto_scheduled_change.shared_UserSubscriptionPlanDTO_scheduledChange(
                            action = '', 
                            effective_at = '', 
                            resume_at = '', ), 
                        started_at = '', 
                        status = '', 
                        trial_ends_at = 56, 
                        updated_at = 56, 
                        user_id = '', ), 
                    team = elio_client.models.shared/team_with_inline_relations_dto.shared.TeamWithInlineRelationsDTO(
                        created_at = 56, 
                        domains = [
                            ''
                            ], 
                        id = '', 
                        members_count = 56, 
                        name = '', 
                        role = '', 
                        updated_at = 56, ), 
                    timezone = '', 
                    updated_at = 56, ),
                owner_id = 56,
                prompt = '',
                short_summary = '',
                title = '',
                type = '',
                updated_at = 56
            )
        else:
            return ApiXRayTemplateDTO(
                created_at = 56,
                description = '',
                icon = '',
                id = 56,
                owner = elio_client.models.shared/user_dto.shared.UserDTO(
                    about = '', 
                    apple_id = '', 
                    avatar = '', 
                    created_at = 56, 
                    customer_id = '', 
                    email = '', 
                    fingerprint = elio_client.models.the_fingerprint_of_the_user
.the fingerprint of the user
(), 
                    first_name = '', 
                    google_id = '', 
                    id = '', 
                    is_test_user = True, 
                    last_name = '', 
                    lobbies = [
                        elio_client.models.shared/lobby_dto.shared.LobbyDTO(
                            is_active = True, 
                            lobby_id = '', 
                            slug = '', )
                        ], 
                    marketing_opt_in = True, 
                    nft_avatar = elio_client.models.shared/user_crypto_token.shared.UserCryptoToken(
                        blockchain = 1.337, 
                        created_at = 56, 
                        details = elio_client.models.the_metadata_of_the_token
.the metadata of the token
(), 
                        id = '', 
                        purpose_type = '', 
                        token = '', 
                        updated_at = 56, 
                        url = '', 
                        user_id = '', 
                        wallet_id = '', ), 
                    notification_settings = elio_client.models.shared/notification_settings.shared.NotificationSettings(
                        is_email_notification_enabled_global = True, 
                        is_push_notification_enabled_global = True, ), 
                    onboarding = elio_client.models.shared/onboarding_flags.shared.OnboardingFlags(
                        0 = True, 
                        1 = True, 
                        2 = True, 
                        3 = True, 
                        4 = True, 
                        5 = True, 
                        6 = '', ), 
                    role_ids = [
                        ''
                        ], 
                    sign_up_timestamp = 56, 
                    social = [
                        elio_client.models.shared/user_social_dto.shared.UserSocialDTO(
                            avatar = '', 
                            bio = '', 
                            first_name = '', 
                            full_name = '', 
                            last_name = '', 
                            platform = '', 
                            url = '', 
                            user_name = '', )
                        ], 
                    subscription_plan = elio_client.models.shared/user_subscription_plan_dto.shared.UserSubscriptionPlanDTO(
                        address_id = '', 
                        billing_cycle = elio_client.models.shared/billing_cycle_dto.shared.BillingCycleDTO(
                            frequency = 1.337, 
                            interval = '', ), 
                        billing_details = elio_client.models.shared_user_subscription_plan_dto_billing_details.shared_UserSubscriptionPlanDTO_billingDetails(
                            additional_information = '', 
                            enable_checkout = True, 
                            purchase_order_minimum = '', ), 
                        business_id = '', 
                        canceled_at = '', 
                        collection_mode = '', 
                        created_at = 56, 
                        currency_code = '', 
                        current_billing_period = elio_client.models.shared_subscription_item_dto_trial_dates.shared_SubscriptionItemDTO_trialDates(
                            ends_at = '', 
                            starts_at = '', ), 
                        custom_data = elio_client.models.custom_data.customData(), 
                        customer_id = '', 
                        first_billed_at = '', 
                        id = '', 
                        items = [
                            elio_client.models.shared/subscription_item_dto.shared.SubscriptionItemDTO(
                                created_at = '', 
                                custom_data = elio_client.models.custom_data.customData(), 
                                next_billed_at = '', 
                                previously_billed_at = '', 
                                price = elio_client.models.shared/price_dto.shared.PriceDTO(
                                    id = '', 
                                    product_id = '', 
                                    quantity = elio_client.models.shared/price_quantity_dto.shared.PriceQuantityDTO(
                                        maximum = 1.337, 
                                        minimum = 1.337, ), 
                                    status = '', 
                                    tax_mode = '', 
                                    trial_period = elio_client.models.shared/price_dto_trial_period.shared.PriceDTOTrialPeriod(
                                        frequency = 1.337, 
                                        interval = '', ), 
                                    unit_price = elio_client.models.shared/unit_price_dto.shared.UnitPriceDTO(
                                        amount = '', 
                                        currency_code = '', ), 
                                    unit_price_overrides = [
                                        elio_client.models.shared/unit_price_override_dto.shared.UnitPriceOverrideDTO(
                                            country_codes = [
                                                ''
                                                ], )
                                        ], ), 
                                quantity = 1.337, 
                                recurring = True, 
                                status = '', 
                                trial_dates = elio_client.models.shared_subscription_item_dto_trial_dates.shared_SubscriptionItemDTO_trialDates(
                                    ends_at = '', 
                                    starts_at = '', ), 
                                updated_at = '', )
                            ], 
                        management_urls = elio_client.models.shared_user_subscription_plan_dto_management_urls.shared_UserSubscriptionPlanDTO_managementUrls(
                            cancel = '', 
                            update_payment_method = '', ), 
                        next_billed_at = '', 
                        paddle_subscription_id = '', 
                        paused_at = '', 
                        plan_config = elio_client.models.shared/subscription_plan_config_dto.shared.SubscriptionPlanConfigDTO(
                            ai_feed = elio_client.models.shared_subscription_plan_config_dto_ai_feed.shared_SubscriptionPlanConfigDTO_aiFeed(
                                enabled = True, ), 
                            bots = elio_client.models.shared_subscription_plan_config_dto_ai_feed.shared_SubscriptionPlanConfigDTO_aiFeed(
                                enabled = True, ), 
                            created_at = 56, 
                            crm = , 
                            custom_feed_items = , 
                            custom_integrations = , 
                            id = '', 
                            integrations = elio_client.models.shared_subscription_plan_config_dto_integrations.shared_SubscriptionPlanConfigDTO_integrations(
                                apps = [
                                    ''
                                    ], 
                                enabled = True, ), 
                            meeting_memory = , 
                            meeting_summary = , 
                            meeting_templates = , 
                            meeting_workflows = , 
                            meetings = elio_client.models.shared_subscription_plan_config_dto_meetings.shared_SubscriptionPlanConfigDTO_meetings(
                                enabled = True, 
                                max = 1.337, ), 
                            model_segregation = , 
                            off_the_record = , 
                            paddle_product_id = '', 
                            paddle_product_name = '', 
                            queue_mode = , 
                            recording = elio_client.models.shared_subscription_plan_config_dto_recording.shared_SubscriptionPlanConfigDTO_recording(
                                enabled = True, 
                                local = True, ), 
                            stream = elio_client.models.shared_subscription_plan_config_dto_stream.shared_SubscriptionPlanConfigDTO_stream(
                                enabled = True, 
                                quality = 1.337, ), 
                            support = elio_client.models.shared_subscription_plan_config_dto_support.shared_SubscriptionPlanConfigDTO_support(
                                enabled = True, 
                                type = '', ), 
                            time_limit = elio_client.models.shared_subscription_plan_config_dto_time_limit.shared_SubscriptionPlanConfigDTO_timeLimit(
                                enabled = True, 
                                max = 56, ), 
                            updated_at = 56, ), 
                        plan_config_overrides = elio_client.models.plan_config_overrides.planConfigOverrides(), 
                        scheduled_change = elio_client.models.shared_user_subscription_plan_dto_scheduled_change.shared_UserSubscriptionPlanDTO_scheduledChange(
                            action = '', 
                            effective_at = '', 
                            resume_at = '', ), 
                        started_at = '', 
                        status = '', 
                        trial_ends_at = 56, 
                        updated_at = 56, 
                        user_id = '', ), 
                    team = elio_client.models.shared/team_with_inline_relations_dto.shared.TeamWithInlineRelationsDTO(
                        created_at = 56, 
                        domains = [
                            ''
                            ], 
                        id = '', 
                        members_count = 56, 
                        name = '', 
                        role = '', 
                        updated_at = 56, ), 
                    timezone = '', 
                    updated_at = 56, ),
                owner_id = 56,
                prompt = '',
                short_summary = '',
                title = '',
                type = '',
                updated_at = 56,
        )
        """

    def testApiXRayTemplateDTO(self):
        """Test ApiXRayTemplateDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
