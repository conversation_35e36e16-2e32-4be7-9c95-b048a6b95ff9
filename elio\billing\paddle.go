package billing

import (
	"context"
	"encoding/json"
	"encore.app/billing/api"
	"encore.app/shared"
	"fmt"
	paddleSDK "github.com/PaddleHQ/paddle-go-sdk/v3"
	"github.com/samber/lo"
	"net/http"
)

//encore:api private
func (p *Billing) ListProducts(ctx context.Context, req api.ListProductsRequest) (*api.ListProductsResponse, error) {
	listProductsRes, err := p.paddleClient.ListProducts(ctx, &paddleSDK.ListProductsRequest{
		IncludePrices: req.IncludePrices,
	})
	if err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	products := make([]shared.BillingProductDTO, 0)
	err = listProductsRes.Iter(ctx, func(v *paddleSDK.Product) (bool, error) {
		prices := lo.Map(v.Prices, func(price paddleSDK.Price, _ int) shared.PriceDTO {
			priceDTO := shared.PriceDTO{
				Id:        lo.ToPtr(price.ID),
				ProductID: lo.ToPtr(price.ProductID),
				Status:    lo.ToPtr(shared.PriceDTOStatus(price.Status)),
				TaxMode:   lo.ToPtr(shared.PriceDTOTaxMode(price.TaxMode)),
				UnitPrice: lo.ToPtr(shared.UnitPriceDTO{
					Amount:       price.UnitPrice.Amount,
					CurrencyCode: string(price.UnitPrice.CurrencyCode),
				}),
				UnitPriceOverrides: lo.ToPtr(
					lo.Map(price.UnitPriceOverrides, func(override paddleSDK.UnitPriceOverride, _ int) shared.UnitPriceOverrideDTO {
						return shared.UnitPriceOverrideDTO{
							CountryCodes: lo.Map(override.CountryCodes, func(countryCode paddleSDK.CountryCode, _ int) string {
								return string(countryCode)
							}),
							UnitPrice: shared.UnitPriceDTO{
								Amount:       override.UnitPrice.Amount,
								CurrencyCode: string(override.UnitPrice.CurrencyCode),
							},
						}
					}),
				),
			}

			if price.BillingCycle != nil {
				priceDTO.BillingCycle = &shared.BillingCycleDTO{
					Frequency: lo.ToPtr(float32(price.BillingCycle.Frequency)),
					Interval:  lo.ToPtr(string(price.BillingCycle.Interval)),
				}
			}

			if price.TrialPeriod != nil {
				priceDTO.TrialPeriod = &shared.PriceDTOTrialPeriod{
					Frequency: lo.ToPtr(float32(price.TrialPeriod.Frequency)),
					Interval:  lo.ToPtr(string(price.TrialPeriod.Interval)),
				}
			}

			return priceDTO
		})

		product := shared.BillingProductDTO{
			ID:          v.ID,
			Name:        v.Name,
			Description: v.Description,
			Status:      string(v.Status),
			Prices:      prices,
		}
		products = append(products, product)

		return true, nil
	})
	if err != nil {
		return nil, err
	}

	return &api.ListProductsResponse{
		Products: products,
	}, nil
}

//encore:api private
func (p *Billing) ListTransactionsByCustomerID(ctx context.Context, req api.ListTransactionsByCustomerIDRequest) (*api.ListTransactionsByCustomerIDResponse,
	error) {
	listTransactionsReq := paddleSDK.ListTransactionsRequest{
		CustomerID: []string{req.CustomerID},
		PerPage:    req.Limit,
	}

	if req.Status != nil {
		listTransactionsReq.Status = *req.Status
	}

	listTransactionsRes, err := p.paddleClient.ListTransactions(ctx, &listTransactionsReq)
	if err != nil {
		err = fmt.Errorf("error listing transactions by customer id: %w", err)
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	transactionDTOs := make([]shared.BillingTransactionDTO, 0)
	err = listTransactionsRes.Iter(ctx, func(t *paddleSDK.Transaction) (bool, error) {
		tDTO := shared.BillingTransactionDTO{
			Payments: lo.Map(t.Payments, func(p paddleSDK.TransactionPaymentAttempt, _ int) shared.BillingTransactionPaymentAttemptDTO {
				return shared.BillingTransactionPaymentAttemptDTO{
					MethodDetails: shared.BillingPaymentMethodDetailsDTO{
						Type: string(p.MethodDetails.Type),
						Card: &shared.BillingCard{
							Card: lo.FromPtr(p.MethodDetails.Card),
						},
					},
					TransactionPaymentAttempt: p,
				}
			}),
			Items: lo.Map(t.Items, func(i paddleSDK.TransactionItem, _ int) shared.BillingTransactionItemDTO {
				return shared.BillingTransactionItemDTO{
					Price: shared.BillingPriceDTO{
						Price: i.Price,
					},
					TransactionItem: i,
				}
			}),
			Transaction: *t,
		}

		if t.CustomData != nil {
			customDataJson, err := json.Marshal(t.CustomData)
			if err != nil {
				err = fmt.Errorf("error marshalling custom data: %w", err)
				return false, err
			}
			tDTO.CustomData = customDataJson
		}

		transactionDTOs = append(transactionDTOs, tDTO)
		return true, nil
	})
	if err != nil {
		err = fmt.Errorf("error iterating transactions: %w", err)
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	return &api.ListTransactionsByCustomerIDResponse{
		Transactions: transactionDTOs,
	}, nil
}

//encore:api private
func (p *Billing) GetInvoiceByTransactionID(ctx context.Context, req api.GetInvoiceByTransactionIDRequest) (*api.GetInvoiceByTransactionIDResponse, error) {
	getInvoiceByTransactionIDRes, err := p.paddleClient.GetTransactionInvoice(ctx, &paddleSDK.GetTransactionInvoiceRequest{
		TransactionID: req.TransactionID,
	})
	if err != nil {
		err = fmt.Errorf("error getting invoice by transaction id: %w", err)
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	url := getInvoiceByTransactionIDRes.URL

	return &api.GetInvoiceByTransactionIDResponse{
		URL: url,
	}, nil
}

//encore:api private
func (p *Billing) GetSubscriptionUpdatePaymentMethodTransaction(ctx context.Context, req api.GetSubscriptionUpdatePaymentMethodTransactionRequest) (*api.GetSubscriptionUpdatePaymentMethodTransactionResponse, error) {
	updatePaymentMethodTransactionRes, err := p.paddleClient.GetSubscriptionUpdatePaymentMethodTransaction(
		ctx,
		&paddleSDK.GetSubscriptionUpdatePaymentMethodTransactionRequest{
			SubscriptionID: req.SubscriptionID,
		},
	)
	if err != nil {
		return nil, err
	}

	transactionDTO := shared.BillingTransactionDTO{
		Transaction: *updatePaymentMethodTransactionRes,
	}

	return &api.GetSubscriptionUpdatePaymentMethodTransactionResponse{
		Transaction: transactionDTO,
	}, nil
}
