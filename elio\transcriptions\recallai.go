// Recall.ai handles our automated bots
package transcriptions

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"

	"encore.app/pkg/preview"

	"encore.app/meetings"
	mapi "encore.app/meetings/api"
	"encore.app/pkg/recallai"
	"encore.app/shared"
	"encore.app/transcriptions/api"
	"encore.app/wormhole"
	wormhole_api "encore.app/wormhole/api"
	luxor_api "encore.app/wormhole/services/luxor-api"
	"encore.dev"
	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/cron"
	"encore.dev/rlog"
	"encore.dev/storage/cache"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/google/go-cmp/cmp"
	"github.com/samber/lo"
	"github.com/tidwall/sjson"
)

type OngoingBotSession struct {
	SessionID    string
	RecurrenceID string
	UserID       string
	Title        string
	About        string
	StartTimeISO string
}

var ongoingBotSessions = cache.NewStructKeyspace[string, OngoingBotSession](shared.PrimaryCluster, cache.KeyspaceConfig{
	KeyPattern:    "botSessions/:key",
	DefaultExpiry: cache.ExpireIn(time.Hour * 12),
})

var recallCalendarUserTokens = cache.NewStringKeyspace[string](shared.PrimaryCluster, cache.KeyspaceConfig{
	KeyPattern:    "recallCalendarUserTokens/:key",
	DefaultExpiry: cache.ExpireIn(time.Hour * 23), // Expire in 23 hours, since the token is valid for a day starting at creation time. This way we don't have to deal with expiry.
})

var _ = cron.NewJob("cleanup-recall-users", cron.JobConfig{
	Title:    "Cleanup Recall Users",
	Every:    6 * cron.Hour,
	Endpoint: CleanupRecallUsers,
})

// RemoveUsersFromRecallAccountParams Removes all users associated with the Recall RecallCustomAPIKey token.
type RemoveUsersFromRecallAccountParams struct {
	RecallCustomAPIKey string   `json:"recallCustomAPIKey"` // RecallCustomAPIKey valid Recall API key
	Exceptions         []string `json:"exceptions"`         // Exceptions List of external user IDs to exclude from deletion
}

func (req RemoveUsersFromRecallAccountParams) Validate() error {
	if req.RecallCustomAPIKey == "" {
		return fmt.Errorf("recallCustomAPIKey is required")
	}
	return nil
}

// RemoveUsersFromRecallAccount Remove all users from the token passed in the params.
// We need to take into account that the delete user endpoint is rate limited to 10 request per minute.
//
//encore:api private
func (t *Transcriptions) RemoveUsersFromRecallAccount(ctx context.Context, params RemoveUsersFromRecallAccountParams) error {
	recallSDK := recallai.NewUSEastRecallSDK(params.RecallCustomAPIKey)
	exceptions := params.Exceptions

	recallUsers, err := recallSDK.ListUsers()
	if err != nil {
		return errs.Wrap(err, "Error while listing users from Recall")
	}

	deleteCalls := 0
	for _, user := range recallUsers {
		if lo.Contains(exceptions, user.ExternalId) {
			continue
		}

		res, err := recallSDK.GetCalendarAuthToken(recallai.CalendarAuthTokenParams{
			UserId: user.ExternalId,
		})
		if err != nil {
			return errs.Wrap(err, "Error while getting calendar auth token from Recall")
		}

		if err = recallSDK.DeleteUser(res.Token); err == nil {
			return errs.Wrap(err, "Error while deleting user from Recall")
		}
		deleteCalls++

		if deleteCalls >= 10 {
			break
		}
	}

	return nil
}

// getRecallUserToken is a utility that wraps the logic of caching a user token for re-usability accross the many recall endpoints.
// It accepts a context and the Rumi user id of the user in question. It will return the token and nil or nil and the error that occured.
func (t *Transcriptions) getRecallUserToken(ctx context.Context, userID string) (string, error) {
	var userToken string
	userToken, err := recallCalendarUserTokens.Get(ctx, userID)

	if err != nil {
		// If the token is not in the cache, we need to get it from Recall
		if errors.Is(err, cache.Miss) {
			res, innerErr := t.RecallSDK.GetCalendarAuthToken(recallai.CalendarAuthTokenParams{
				UserId: userID,
			})
			if innerErr != nil {
				rlog.Error("Error while getting calendar auth token from Recall", "err", innerErr)
				return "", innerErr
			}

			userToken = res.Token
			innerErr = recallCalendarUserTokens.Set(ctx, userID, userToken)
			if innerErr != nil {
				rlog.Error("Error while setting calendar auth token in cache", "err", innerErr)
				return "", innerErr
			}
		} else {
			rlog.Error("Error while getting calendar auth token from cache", "err", err)
			return "", err
		}
	}

	return userToken, nil
}

// CleanupRecallUsers is a cron job that removes users from Recall that have no connections.
// This prevents us from having tons of users with no connections on the recall side.
//
//encore:api private
func (t *Transcriptions) CleanupRecallUsers(ctx context.Context) error {
	// List all users in Recall, remove each user with no connections.
	// We need to take into account that the delete user endpoint is rate limited to 10 request per minute.
	recallUsers, err := t.RecallSDK.ListUsers()
	if err != nil {
		return err
	}

	// For each user, check if they have any connections. If not, delete the user.
	// We only delete 10 users at a time to avoid rate limiting.
	deleteCalls := 0
	for _, user := range recallUsers {
		connections := lo.Filter(user.Connections, func(connection recallai.RecallConnection, _ int) bool {
			return connection.Connected
		})

		if len(connections) == 0 {
			// Remove the user from Recall
			token, err := t.getRecallUserToken(ctx, user.ExternalId)
			if err != nil {
				return err
			}
			err = t.RecallSDK.DeleteUser(token)
			if err != nil {
				return err
			}
			deleteCalls++
		}
		if deleteCalls >= 10 {
			break
		}
	}

	return nil
}

var meetingType = shared.SessionDTORecallBot

// parseBotMeetingTitle is a utility that parses the meeting title from the bot response.
// It accepts a bot response and returns a string.
// This is used as a temporary title until we can apply the LLM generated one after the session.
func parseBotMeetingTitle(botResponse *recallai.BotResponse) string {
	// Depending on the origin platform, the metadata.title will be either null or a string
	// If it's null, we should use the non-host names to generate a title
	botBranding := "RumiBot"
	title := botResponse.MeetingMetadata.Title // Meeting metadata is normally null
	if title == "" {
		// If we didn't get the title, we can say meeting with the non-host names
		nonHostNames := lo.Reduce(botResponse.MeetingParticipants, func(acc []string, p recallai.MeetingParticipant, _ int) []string {
			// Skip the host, so we don't title: "meeting with myself!"
			if p.IsHost {
				return acc
			}
			return append(acc, p.Name)
		}, []string{})

		if len(nonHostNames) > 0 {
			title = fmt.Sprintf("%s session with %s", botBranding, strings.Join(nonHostNames[:], ", "))
		} else {
			title = fmt.Sprintf("%s session", botBranding)
		}
	} else {
		title = fmt.Sprintf("%s session: %s", botBranding, title)
	}

	return title
}

//encore:api public path=/v1.0/transcriptions/bot/webhook method=POST
func (t *Transcriptions) RecallTranscriptionsWebhook(ctx context.Context, request api.RecallTranscriptionsWebhookRequest) error {
	if request.Token != Config.RecallAiWebhookToken() {
		return errs.B().Code(errs.Unauthenticated).Err()
	}

	event := request.Event
	STATUS_CHANGE := "bot.status_change"

	// Recall only sends the status code in the status_change event
	action := ""
	if event == STATUS_CHANGE {
		action = request.Data.Status.Code
	}

	switch {
	// "in_call_recording" status of the "bot.status_change" event is the last status event before we receive Transcripts
	// We should create the session here, since we know the bot was successfull, joining and setting up.
	case event == STATUS_CHANGE && action == "in_call_recording":
		// Check if we already created a session for this:
		botSession, err := ongoingBotSessions.Get(ctx, request.Data.BotID)
		if !errors.Is(err, cache.Miss) {
			if err == nil {
				rlog.Debug("Bot session already ongoing.", "Bot id", request.Data.BotID)
				return nil
			}
			return err
		}

		userToken, err := t.getRecallUserToken(ctx, botSession.UserID)
		if err != nil {
			return err
		}

		// Get the scheduled bot details
		bot, err := t.RecallSDK.RetrieveBot(ctx, request.Data.BotID, userToken)
		if err != nil {
			rlog.Error("Error while retrieving bot", "err", err)
			return err
		}

		title := parseBotMeetingTitle(bot)
		if baseURL := preview.ExtractPreviewURL(title); baseURL != "" && encore.Meta().Environment.Type != encore.EnvEphemeral {
			preview.RecallPreviewWebhook(secrets.ElioUser, secrets.ElioPassword, request.Data.BotID, baseURL)
			return api.EphemeralEnvOnly
		}
		userID, accessRules := extractAccessRulesFromBot(ctx, bot)
		startTimeStr := bot.CalendarMeetings[0].StartTime // this is a datetime, we need timeUnix
		startTime, err := time.Parse(time.RFC3339, startTimeStr)
		if err != nil {
			return err
		}

		// The frontend expects the start timestamp to be in seconds, not milliseconds
		startTimeUnix := startTime.Unix()
		meetingUrl := t.RecallSDK.GetMeetingURL(bot)

		r := mapi.CreateSessionRequest{
			Title:  title,
			About:  meetingUrl,
			UserID: userID,
			Settings: json.RawMessage(`{
				"enableRecording":true,
				"summaAI":true,
				"preferredLanguages":["en-US"],
				"isRecall":true,
				"postSessionSummary": {"recipients": {"email": "everyone"}}
			}`), // isRecall is used to identify if the session was created by a recall bot
			StartTimestamp:    startTimeUnix,
			SessionState:      string(shared.SessionStateActive),
			ViewerAccessRules: accessRules,
			MeetingType:       meetingType,
		}

		session, err := meetings.CreateSessionWithUser(ctx, &r)
		if err != nil {
			return err
		}

		err = ongoingBotSessions.SetIfNotExists(ctx, request.Data.BotID, OngoingBotSession{
			SessionID:    session.Data.Session.SessionID,
			RecurrenceID: session.Data.Session.SessionRecurrenceID,
			UserID:       userID,
			Title:        title,
			About:        meetingUrl,
			StartTimeISO: request.Data.Status.CreatedAt,
		})
		if err != nil {
			if errors.Is(err, cache.KeyExists) {
				// We already created a session??
				rlog.Warn("Trying to duplicate a bot session.", "Bot id", request.Data.BotID)
			}
			return err
		}

	// "done" status change event signals that we are done receiving transcripts. We now can trigger summaries
	case event == STATUS_CHANGE && action == "done":
		// The bot is done with transcripts, remove them from the ongoing meetings
		sessionIDs, err := ongoingBotSessions.GetAndDelete(ctx, request.Data.BotID)
		if err != nil {
			return err
		}

		// Update the session state to concluded:
		_, err = meetings.UpdateSession(ctx, sessionIDs.SessionID, sessionIDs.RecurrenceID, &mapi.UpdateSessionRequest{
			State: lo.ToPtr(shared.SessionStateEnded),
		})
		if err != nil {
			return err
		}

		// Trigger the last batch
		t.CreateEndOfSessionTranscriptsBatch(ctx, api.CreateEndOfSessionTranscriptsBatchRequest{
			SessionID:    sessionIDs.SessionID,
			RecurrenceID: sessionIDs.RecurrenceID,
			MeetingType:  string(meetingType),
		})

		// Token and bot retrieval
		userToken, err := t.getRecallUserToken(ctx, sessionIDs.UserID)
		if err != nil {
			return err
		}
		bot, err := t.RecallSDK.RetrieveBot(ctx, request.Data.BotID, userToken)
		if err != nil {
			rlog.Error("Error while retrieving bot", "err", err)
			return err
		}
		// Process the bot recording if we have video url
		if bot.VideoUrl != "" {
			err = ProcessBotRecording(ctx, api.RecallProcessRecordingRequest{
				VideoURL:     bot.VideoUrl,
				SessionID:    sessionIDs.SessionID,
				RecurrenceID: sessionIDs.RecurrenceID,
				BotID:        request.Data.BotID,
			})
			if err != nil {
				rlog.Error("Error while processing bot recording", "err", err)
			}
		}

	// Then we start seeing incoming "bot.transcription" events
	// We should create the LiveTranscription rows here, and check if there are enough for a batch (like we do with deepgram)
	case event == "bot.transcription":
		ongoing, err := ongoingBotSessions.Get(ctx, request.Data.BotID)
		if err != nil {
			return err
		}

		// It is possible to receive a transcript event without a transcript all together
		// or maybe without a speaker or speakerID.
		// See: https://docs.recall.ai/docs/real-time-transcription#events
		if request.Data.Transcript == nil {
			rlog.Debug("Recall transcript is empty", "botID", request.Data.BotID, "data", request.Data)
			return nil
		}

		speakerName := "unknown speaker"
		speakerUID := ""
		if request.Data.Transcript.Speaker != nil {
			speakerName = *request.Data.Transcript.Speaker
		}
		if request.Data.Transcript.SpeakerID != nil {
			speakerUID = fmt.Sprintf("%d", *request.Data.Transcript.SpeakerID)
		}
		if request.Data.Transcript.TranscriptionProviderSpeaker != nil {
			speakerName = fmt.Sprintf("Speaker %s", *request.Data.Transcript.TranscriptionProviderSpeaker)
			speakerUID = *request.Data.Transcript.TranscriptionProviderSpeaker
		}
		if speakerUID == "" {
			if request.Data.Transcript.OriginalTranscriptID != 0 {
				transcriptId := request.Data.Transcript.OriginalTranscriptID
				speakerUID = fmt.Sprintf("%s-%d", ongoing.RecurrenceID, transcriptId)
			} else {
				speakerUID = fmt.Sprintf("%s-0", ongoing.RecurrenceID)
			}
		}

		rlog.Debug("Incoming transcript recall sentence", "speakerUID", speakerUID, "speakerName", speakerName, "IsFinal", request.Data.Transcript.IsFinal, "transcriptId", request.Data.Transcript.OriginalTranscriptID)

		// map words and return text and join them
		sentence := api.LiveTranscriptWord{
			Text: strings.Join(lo.Map(request.Data.Transcript.Words, func(word api.RecallWord, idx int) string {
				return word.Text
			}), " "),
			Final: request.Data.Transcript.IsFinal,
			Speaker: api.HeardSpeaker{
				SpeakerName: speakerName,
				SpeakerUID:  speakerUID,
			},
		}

		liveTranscriptMsg := api.LiveTranscriptionMessage{
			User: &api.LiveTranscriptionMessageUser{
				ID:  ongoing.UserID,
				Uid: shared.ParseInt(ongoing.UserID),
			},
			Session: api.LiveTranscriptionSession{
				RecurrenceID: ongoing.RecurrenceID,
				ID:           ongoing.SessionID,
				MeetingType:  meetingType,
			},
			Payload: api.LiveTranscriptionPayload{
				Words:   []api.LiveTranscriptWord{sentence},
				Time:    time.Now().UnixMilli(),
				Culture: "en-US",
			},
			DataSource: "recall.ai",
		}

		canBatch, err := t.Storage.StoreTranscript(ctx, liveTranscriptMsg)
		if err != nil {
			return err
		}
		if canBatch {
			// Trigger the last batch
			batch, err := t.Storage.CreateTranscriptionsBatch(ongoing.SessionID, ongoing.RecurrenceID, meetingType)
			if err != nil {
				return err
			}
			if batch != nil && encore.Meta().Environment.Type != encore.EnvTest {
				batch.IsLastBatch = false
				err = t.Storage.PublishBatchToKafka(batch)
				if err != nil {
					return err
				}
			}
		}

	case event == "bot.output_log":
		// Something went wrong during the transcription process over at Recall
		level := request.Data.Log.Level
		message := request.Data.Log.Message
		createdAt := request.Data.Log.CreatedAt
		outputId := request.Data.Log.OutputId
		rlog.Error("Recall bot output log", "level", level, "message", message, "createdAt", createdAt, "outputId", outputId)
	}

	return nil
}

func extractAccessRulesFromBot(ctx context.Context, bot *recallai.BotResponse) (string, []mapi.SessionAccessRulesDTO) {
	participantsUserIDs := lo.Map(bot.CalendarMeetings, func(meeting recallai.CalendarMeeting, idx int) string {
		return meeting.CalendarUser.ExternalId
	})
	// Pop host off slice
	hostID := participantsUserIDs[0]
	participantsUserIDs = participantsUserIDs[1:]
	participantEmails := []mapi.SessionAccessRulesDTO{}
	lo.ForEach(participantsUserIDs, func(userID string, idx int) {
		user, err := meetings.GetUserByID(ctx, &mapi.GetUserByIDRequest{
			UserID: userID,
		})
		if err != nil || user.Email == nil {
			return
		}
		participantEmails = append(participantEmails, mapi.SessionAccessRulesDTO{
			Type:  "email",
			Value: *user.Email,
		})
	})
	return hostID, participantEmails
}

// GenerateBotAuthURL Generate a URL for the user to authenticate with Google Calendar.
//
//encore:api auth method=GET path=/v1.0/transcriptions/bot/google-auth
func (t *Transcriptions) GenerateBotGoogleAuthURL(ctx context.Context) (*api.GenerateBotAuthURLResponse, error) {
	// Generate the user token:
	userID, _ := auth.UserID()
	authToken, err := t.RecallSDK.GetCalendarAuthToken(recallai.CalendarAuthTokenParams{
		UserId: string(userID),
	})
	recallCalendarUserTokens.Set(ctx, string(userID), authToken.Token)

	if err != nil {
		return nil, err
	}
	state := "{}"
	state, _ = sjson.Set(state, "recall_calendar_auth_token", authToken.Token)
	state, _ = sjson.Set(state, "google_oauth_redirect_url", t.RecallSDK.GoogleCallbackURL())

	frontendUrl := Config.FrontendURL() + "/dashboard/integrations"
	state, _ = sjson.Set(state, "success_url", frontendUrl)
	state, _ = sjson.Set(state, "error_url", frontendUrl)

	authURL, _ := url.Parse("https://accounts.google.com/o/oauth2/v2/auth")
	q := authURL.Query()
	q.Add("scope", "https://www.googleapis.com/auth/calendar.events.readonly https://www.googleapis.com/auth/userinfo.email")
	q.Add("access_type", "offline")
	q.Add("prompt", "consent")
	q.Add("include_granted_scopes", "true")
	q.Add("response_type", "code")
	q.Add("state", state)
	q.Add("redirect_uri", t.RecallSDK.GoogleCallbackURL())
	q.Add("client_id", Config.GoogleOAuth2ClientID())
	authURL.RawQuery = q.Encode()
	return &api.GenerateBotAuthURLResponse{
		AuthURL: authURL.String(),
	}, nil
}

// GenerateBotAuthURL Generate a URL for the user to authenticate with Microsoft Calendar.
//
//encore:api auth method=GET path=/v1.0/transcriptions/bot/microsoft-auth
func (t *Transcriptions) GenerateBotMicrosoftAuthURL(ctx context.Context) (*api.GenerateBotAuthURLResponse, error) {
	// Generate the user token:
	userID, _ := auth.UserID()
	authToken, err := t.RecallSDK.GetCalendarAuthToken(recallai.CalendarAuthTokenParams{
		UserId: string(userID),
	})
	recallCalendarUserTokens.Set(ctx, string(userID), authToken.Token)
	if err != nil {
		return nil, err
	}

	redirectURI := fmt.Sprintf("%s/v1.0/calendar/ms_oauth_callback", encore.Meta().APIBaseURL.String())
	state := "{}"
	state, _ = sjson.Set(state, "recall_calendar_auth_token", authToken.Token)
	state, _ = sjson.Set(state, "ms_oauth_redirect_url", redirectURI)

	frontendUrl := Config.FrontendURL() + "/dashboard/integrations"
	state, _ = sjson.Set(state, "success_url", frontendUrl)
	state, _ = sjson.Set(state, "error_url", frontendUrl)

	authURL, _ := url.Parse("https://login.microsoftonline.com/common/oauth2/v2.0/authorize")
	q := authURL.Query()
	q.Add("scope", "offline_access openid email https://graph.microsoft.com/Calendars.Read")
	q.Add("response_mode", "query")
	q.Add("response_type", "code")
	q.Add("state", state)
	q.Add("redirect_uri", redirectURI)
	q.Add("client_id", Config.MicrosoftOAuth2ClientID())
	authURL.RawQuery = q.Encode()
	return &api.GenerateBotAuthURLResponse{
		AuthURL: authURL.String(),
	}, nil
}

// Disconnect a user's calendar platform from our Rumi bots recall.ai account
//
//encore:api auth method=POST path=/v1.0/transcriptions/bot/disconnect
func (t *Transcriptions) DisconnectCalendar(ctx context.Context, params recallai.DisconnectCalendarPlatformParams) error {
	userID, _ := auth.UserID()
	userToken, err := t.getRecallUserToken(ctx, string(userID))
	if err != nil {
		return err
	}
	err = t.RecallSDK.DisconnectCalendarPlatform(params, userToken)
	if err != nil {
		rlog.Error("Error while disconnecting calendar platform", "err", err)
	}
	return err
}

//encore:api auth method=GET path=/v1.0/transcriptions/bot/calendar-user
func (t *Transcriptions) GetCalendarUser(ctx context.Context) (*recallai.CalendarUser, error) {
	userID, _ := auth.UserID()
	userToken, err := t.getRecallUserToken(ctx, string(userID))
	if err != nil {
		return nil, err
	}
	// The user related to the RecallToken
	user, err := t.RecallSDK.GetCalendarUser(userToken)
	if err != nil {
		// Check if http status is 403 or 404 to handle the case where we don't recongize the user
		errorMessage := err.Error()
		if strings.Contains(errorMessage, "403") || strings.Contains(errorMessage, "404") {
			return nil, nil
		}

		rlog.Error("Error while getting calendar user", "err", err)
		return nil, err
	}

	// Set default preferences if needed:
	defaultPreferences := t.RecallSDK.GetDefaultPreferences()
	defaultPreferences.BotName = "RumiBot"
	if !cmp.Equal(user.Preferences, defaultPreferences) {
		err = t.RecallSDK.UpdateRecordingPreferences(userToken, defaultPreferences)
		if err != nil {
			rlog.Error("Error while updating recording preferences", "err", err)
		}
	}

	return user, nil
}

type RecallVideoReadyJSON struct {
	SourceVideo         string `json:"sourceVideo"`
	DestinationFilePath string `json:"destinationFilePath"`
}

//encore:api private
func ProcessBotRecording(ctx context.Context, request api.RecallProcessRecordingRequest) error {
	// Download the url and save it to s3
	region := Config.AWSRegion()
	accessKey := secrets.AWSAccessKey
	secret := secrets.AWSSecretKey
	bucket := Config.AWSBucketName()
	now := time.Now()
	filename := fmt.Sprintf("%d/%d/%d/%s/recall-%s.mp4", now.Year(), now.Month(), now.Day(), request.RecurrenceID, request.BotID)
	// Lambda will download this json file and upload the video to the destination path
	readyFilename := fmt.Sprintf("recording-ready-%s", request.BotID)

	s3session, err := session.NewSession(&aws.Config{
		Region:      aws.String(region),
		Credentials: credentials.NewStaticCredentials(accessKey, secret, ""),
	})
	if err != nil {
		rlog.Error("Error while creating s3 session", "err", err)
		return err
	}
	uploader := s3manager.NewUploader(s3session)
	meta := RecallVideoReadyJSON{
		SourceVideo:         request.VideoURL,
		DestinationFilePath: filename,
	}
	bytes, _ := json.Marshal(meta)

	_, err = uploader.Upload(&s3manager.UploadInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(readyFilename),
		Body:   strings.NewReader(string(bytes)),
	})
	if err != nil {
		rlog.Error("Error while uploading video to s3", "err", err)
		return err
	}

	// Update the session with the new recording
	_, err = wormhole.UpsertRecording(ctx, &wormhole_api.UpsertRecordingRequest{
		UpsertRecordingDTO: luxor_api.UpsertRecordingDTO{
			SessionID:           request.SessionID,
			SessionRecurrenceID: &request.RecurrenceID,
			PlaylistLandscape:   &filename,
		},
	})
	if err != nil {
		rlog.Error("Error while upserting recording", "err", err)
		return err
	}

	return nil
}

//encore:api auth method=POST path=/v1.0/transcriptions/bot/create-for-meeting
func (t *Transcriptions) CreateBotForMeeting(ctx context.Context, req api.CreateBotRequest) (*recallai.BotResponse, error) {
	userID, _ := auth.UserID()
	userToken, err := t.getRecallUserToken(ctx, string(userID))
	if err != nil {
		rlog.Error("Error while getting recall user token", "err", err)
		return nil, err
	}

	// Some values are mutable and stored in our config.
	deepgramModel := Config.DeepgramModel()
	replacements := Config.Vocabulary()
	endpointing, err := strconv.Atoi(Config.EndpointMS())
	if err != nil {
		rlog.Error("Invalid enpoint ms value in transcriptions config", "err", err)
		return nil, err
	}

	bot, err := t.RecallSDK.CreateBot(ctx, userToken, recallai.CreateBotParams{
		MeetingURL: req.MeetingLink,
		RealTimeTranscription: recallai.RealTimeTranscriptionConfig{
			DestinationUrl:      Config.RecallWebhookURL(),
			EnhancedDiarization: true,
		},
		TranscriptionOptions: recallai.TranscriptionOptions{
			Provider: "deepgram",
			Deepgram: recallai.DeepgramOptions{
				Punctuate:      false,
				Encoding:       "linear16",
				SampleRate:     96000,
				Model:          deepgramModel,
				Language:       "en-US",
				SmartFormat:    true,
				InterimResults: false,
				Channels:       1,
				VadEvents:      false,
				Endpointing:    endpointing,
				FillerWords:    true,
				Replace:        replacements,
			},
		},
		GoogleMeet: recallai.GoogleMeetOptions{
			GoogleLoginGroupId: Config.GoogleLoginGroupId(),
			LoginRequired:      true,
		},
	})
	if err != nil {
		rlog.Error("Error while creating bot", "err", err)
		return nil, err
	}

	rlog.Debug("Bot created", "bot", bot)

	return bot, nil
}
