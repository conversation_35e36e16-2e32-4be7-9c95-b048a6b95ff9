# ApiXRayDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**alert_channels** | **Dict[str, bool]** |  | 
**created_at** | **int** |  | 
**current_commit** | [**ApiXRayDocCommit**](ApiXRayDocCommit.md) |  | 
**current_commit_id** | **int** |  | 
**description** | **str** |  | 
**frequency** | **str** |  | 
**icon** | **str** |  | 
**id** | **int** |  | 
**is_active** | **bool** |  | 
**last_digest_at** | **int** |  | 
**owner_id** | **int** |  | 
**prompt** | **str** |  | 
**scope** | **str** |  | 
**short_summary** | **str** |  | 
**title** | **str** |  | 
**type** | **str** |  | 
**unread_notifications_count** | **int** |  | 
**updated_at** | **int** |  | 
**visibility** | **str** |  | 

## Example

```python
from elio_client.models.api_x_ray_dto import ApiXRayDTO

# TODO update the JSON string below
json = "{}"
# create an instance of ApiXRayDTO from a JSON string
api_x_ray_dto_instance = ApiXRayDTO.from_json(json)
# print the JSON string representation of the object
print(ApiXRayDTO.to_json())

# convert the object into a dict
api_x_ray_dto_dict = api_x_ray_dto_instance.to_dict()
# create an instance of ApiXRayDTO from a dict
api_x_ray_dto_from_dict = ApiXRayDTO.from_dict(api_x_ray_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


