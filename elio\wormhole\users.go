package wormhole

import (
	"context"
	"net/http"

	"encore.app/shared"
	mars_api "encore.app/wormhole/services/mars-api"
	"encore.dev/beta/auth"
	"encore.dev/rlog"
)

//encore:api auth method=DELETE path=/v1.0/users/me
func (c Wormhole) DeleteUserByID(ctx context.Context) error {
	userID, _ := auth.UserID()

	// Step 1: Fetch user
	getUserResponse, err := c.MarsClient.UserControllerGetUser(ctx, &mars_api.UserControllerGetUserParams{UserID: (*string)(&userID)})
	if err != nil {
		return err
	}
	if getUserResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		err = errResp.Map(getUserResponse)
		if err != nil {
			return err
		}
		return shared.HttpResponseError(errResp.Error.Message, errResp.Error.ErrCode, errResp.StatusCode)
	}

	userBodyJson, err := mars_api.ParseUserControllerGetUserResponse(getUserResponse)
	if err != nil {
		rlog.Debug("Response parsing error", "err", err)
		return err
	}

	user := userBodyJson.JSON200.User

	// Step 2: Send Slack message to channel #account-deletion-requests, notifying the team of the deletion req
	err = c.SlackService.PostAccountDeletionRequest(user.Email, user.UserID)

	if err != nil {
		return shared.HttpResponseError(err.Error(), 2000, 500)
	}

	return nil
}
