import asyncio
import time
import traceback
from typing import Optional

from temporalio import activity

from elio_client import POSTCommsMeetingMetadataReadyRequest
from elio_client import ApiMeetingMetadata as ElioApiMeetingMetadata
from nebula.services.elio.service import (
    get_concentrated_transcripts,
    get_session_by_id,
    get_session_participants,
    trigger_elio_meeting_metadata_ready,
)
from nebula.db.models.meeting_metadata import MeetingMetadata
from nebula.services.meeting_metadata.meeting_metadata_service import (
    extract_metadata_with_llm,
    SessionData,
)
from nebula.shared.transcriptions import get_all_transcriptions_with_timestamps
from nebula.temporal.types import (
    ExtractMetadataInput,
    MetadataWorkflowParams,
    TranscriptionBatchesResult,
    SessionDataModel,
    MetadataRecordResult,
    ProcessedTranscriptResult,
    CreateMetadataInput,
    CreateElioUpdateInput,
)
from nebula.services.meeting_metadata.models import MeetingMetadataExtraction

class MetadataActivities:
    @activity.defn
    async def get_transcription_batches(
        self, session_info: MetadataWorkflowParams
    ) -> TranscriptionBatchesResult:
        """
        Activity to retrieve transcription batches for a session.

        Args:
            session_info: Session identification information

        Returns:
            TranscriptionBatchesResult containing transcription batches
        """
        try:
            transcripts = await asyncio.to_thread(
                get_concentrated_transcripts,
                session_info.session_id, session_info.recurrence_id
            )

            activity.logger.info(
                f"Retrieved transcript batches for session {session_info.session_id}"
            )

            return TranscriptionBatchesResult(
                transcription_batches=list(transcripts) if transcripts else []
            )

        except Exception as e:
            activity.logger.error(
                f"Error retrieving transcription batches: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def get_session_data(
        self, session_info: MetadataWorkflowParams
    ) -> SessionDataModel:
        """
        Activity to retrieve session data.

        Args:
            session_info: Session identification information

        Returns:
            SessionDataModel containing meeting information
        """
        try:
            # Get session
            session = await asyncio.to_thread(
                get_session_by_id,
                session_info.session_id, session_info.recurrence_id
            )

            # Get participants
            participants = await asyncio.to_thread(
                get_session_participants,
                session_info.session_id, session_info.recurrence_id
            )

            # Get participant names
            participant_names = []
            for participant in participants:
                if participant.joined is False:
                    continue

                fullname = (
                    f"{participant.user.first_name} {participant.user.last_name}"
                    if participant.user is not None
                    and participant.user.first_name
                    and participant.user.last_name
                    else None
                )

                if fullname:
                    participant_names.append(fullname)

            # Default meeting type if none available
            default_meeting_type = "Meeting"

            # Create session data model
            session_data = SessionDataModel(
                meeting_id=f"{session_info.session_id}_{session_info.recurrence_id}",
                title=session.session_title,
                participants=participant_names,
                type=session.user_meeting_type.title
                if session.user_meeting_type
                else default_meeting_type,
            )

            activity.logger.info(
                f"Retrieved session data for {session_info.session_id}: "
                f"title='{session_data.title}', "
                f"participants={len(session_data.participants)}"
            )

            return session_data

        except Exception as e:
            activity.logger.error(
                f"Error retrieving session data: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def create_metadata_record(
        self, input: CreateMetadataInput
    ) -> MetadataRecordResult:
        """
        Activity to create a new metadata record in the database.

        Args:
            input: CreateMetadataInput containing session info and extracted metadata

        Returns:
            MetadataRecordResult containing the created metadata ID
        """
        try:
            meeting_metadata = MeetingMetadata(
                session_id=input.session_info.session_id,
                session_recurrence_id=input.session_info.recurrence_id,
                metadata=input.metadata.model_dump(),
            )
            await meeting_metadata.save()

            activity.logger.info(
                f"Created metadata record with ID {meeting_metadata.id} "
                f"for session {input.session_info.session_id}"
            )

            return MetadataRecordResult(metadata_id=meeting_metadata.id)

        except Exception as e:
            activity.logger.error(
                f"Error creating metadata record: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def process_transcript(
        self, transcript_batches: TranscriptionBatchesResult
    ) -> ProcessedTranscriptResult:
        """
        Activity to process transcript batches into a single transcript.

        Args:
            transcript_batches: Transcription batches to process

        Returns:
            ProcessedTranscriptResult containing the processed transcript
        """
        try:
            # Get full transcript text
            transcripts, locale = get_all_transcriptions_with_timestamps(
                transcript_batches.transcription_batches
            )
            transcript = "\n".join(transcripts)

            activity.logger.info(
                f"Processed transcript: {len(transcript)} characters, locale={locale}"
            )

            return ProcessedTranscriptResult(
                transcript=transcript, locale=locale or "en-US"
            )

        except Exception as e:
            activity.logger.error(
                f"Error processing transcript: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def extract_metadata(
        self, input: ExtractMetadataInput
    ) -> MeetingMetadataExtraction:
        """
        Activity to extract metadata from transcript using LLM.

        Args:
            transcript_data: Processed transcript data
            session_data: Session data model

        Returns:
            MeetingMetadataExtraction containing the extracted metadata
        """
        try:
            session_data: SessionData = {
                "meeting_id": input.session_data.meeting_id,
                "title": input.session_data.title,
                "participants": input.session_data.participants,
                "type": input.session_data.type,
            }

            # Generate metadata using LLM
            start_time = time.time()
            metadata_model = await extract_metadata_with_llm(
                input.transcript, session_data
            )
            processing_time = time.time() - start_time

            activity.logger.info(
                f"Extracted metadata for meeting {input.session_data.meeting_id} "
                f"in {processing_time:.2f} seconds"
            )

            return metadata_model

        except Exception as e:
            activity.logger.error(
                f"Error extracting metadata: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def get_metadata_record(self, metadata_id: int) -> Optional[MeetingMetadata]:
        """
        Activity to retrieve a metadata record from the database.

        Args:
            metadata_id: ID of the metadata record

        Returns:
            Optional[MeetingMetadata] containing the metadata record if found
        """
        try:
            meeting_metadata = await MeetingMetadata.objects().get(
                MeetingMetadata.id == metadata_id,
            )

            if not meeting_metadata:
                activity.logger.error(
                    f"Metadata record with ID {metadata_id} not found"
                )
                return None

            activity.logger.info(f"Retrieved metadata record with ID {metadata_id}")
            return meeting_metadata

        except Exception as e:
            activity.logger.error(
                f"Error retrieving metadata record: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def update_elio_about_metadata_creation(self, update: CreateElioUpdateInput) -> None:
        """
        Activity to update Elio about the metadata creation.

        Args:
            update: Session identification information

        Returns:
            None
        """
        # Parse metadata into POSTCommsMeetingMetadataReadyRequest model
        try:
            update_metadata = update.metadata
            metadata_request = POSTCommsMeetingMetadataReadyRequest(
                metadata=ElioApiMeetingMetadata(
                    key_decisions=update_metadata.key_decisions,
                    keywords=update_metadata.keywords,
                    meeting_id=update_metadata.meeting_id if update_metadata.meeting_id is not None else "-",
                    participants=update_metadata.participants,
                    short_summary=update_metadata.short_summary if update_metadata.short_summary is not None else "-",
                    title=update_metadata.title if update_metadata.title != "" else "-",
                    topics=update_metadata.topics,
                    type=update_metadata.type if update_metadata.type != "" else "-",
                ),
                sessionID=update.session_info.session_id, # Gotcha aliased field
                sessionRecurrenceID=update.session_info.recurrence_id, # Gotcha aliased field
            )
        except Exception as e:
            activity.logger.error(f"Failed to parse metadata into request model: {str(e)}")
            raise

        try:
            await asyncio.to_thread(trigger_elio_meeting_metadata_ready, metadata_request)
        except Exception as e:
            activity.logger.error(f"Error updating Elio about metadata creation: {str(e)}\n{traceback.format_exc()}")
            raise
