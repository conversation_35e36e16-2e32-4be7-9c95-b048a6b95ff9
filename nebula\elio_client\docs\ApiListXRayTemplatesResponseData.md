# ApiListXRayTemplatesResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**has_more** | **bool** |  | 
**templates** | [**List[ApiXRayTemplateDTO]**](ApiXRayTemplateDTO.md) |  | 
**total_count** | **int** |  | 

## Example

```python
from elio_client.models.api_list_x_ray_templates_response_data import ApiListXRayTemplatesResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiListXRayTemplatesResponseData from a JSON string
api_list_x_ray_templates_response_data_instance = ApiListXRayTemplatesResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiListXRayTemplatesResponseData.to_json())

# convert the object into a dict
api_list_x_ray_templates_response_data_dict = api_list_x_ray_templates_response_data_instance.to_dict()
# create an instance of ApiListXRayTemplatesResponseData from a dict
api_list_x_ray_templates_response_data_from_dict = ApiListXRayTemplatesResponseData.from_dict(api_list_x_ray_templates_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


