"""Schema models for meeting suggestions generation."""

from typing import List
from pydantic import BaseModel, Field


class MeetingSuggestionItem(BaseModel):
    """Individual meeting suggestion item."""
    
    category: str = Field(description="Category of the suggestion (self_review, action_items, content_feedback)")
    prompt: str = Field(description="The suggestion prompt text for the user")


class UserMeetingSuggestionItem(BaseModel):
    """Meeting suggestion item with user information."""
    
    user_id: str = Field(description="User ID this suggestion is for")
    category: str = Field(description="Category of the suggestion (self_review, action_items, content_feedback)")
    prompt: str = Field(description="The suggestion prompt text for the user")


class MeetingSuggestionsExtraction(BaseModel):
    """Complete set of meeting suggestions for the end screen."""
    
    suggestions: List[MeetingSuggestionItem] = Field(
        description="List of exactly 3 suggestion items for the meeting end screen"
    )


class AllUsersMeetingSuggestionsExtraction(BaseModel):
    """Complete set of meeting suggestions for all users."""
    
    user_suggestions: List[UserMeetingSuggestionItem] = Field(
        description="List of suggestions for all participants, 3 per user"
    )


class ParticipantInfo(BaseModel):
    """Information about a meeting participant."""
    
    user_id: str = Field(description="User ID")
    name: str = Field(description="User's full name")


class MeetingSuggestionsGenerationData(BaseModel):
    """Data structure for meeting suggestions generation input."""
    
    title: str = Field(description="Meeting title")
    date: str = Field(description="Meeting date")
    user_name: str = Field(description="Local user name")
    transcript: str = Field(description="Meeting transcript highlights")


class AllUsersMeetingSuggestionsGenerationData(BaseModel):
    """Data structure for generating suggestions for all participants."""
    
    title: str = Field(description="Meeting title")
    date: str = Field(description="Meeting date")
    participants: List[ParticipantInfo] = Field(description="All meeting participants")
    transcript: str = Field(description="Meeting transcript highlights") 