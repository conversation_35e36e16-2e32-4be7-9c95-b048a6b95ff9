# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_mark_x_ray_notifications_seen_response_data import ApiMarkXRayNotificationsSeenResponseData

class TestApiMarkXRayNotificationsSeenResponseData(unittest.TestCase):
    """ApiMarkXRayNotificationsSeenResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiMarkXRayNotificationsSeenResponseData:
        """Test ApiMarkXRayNotificationsSeenResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiMarkXRayNotificationsSeenResponseData`
        """
        model = ApiMarkXRayNotificationsSeenResponseData()
        if include_optional:
            return ApiMarkXRayNotificationsSeenResponseData(
                marked_count = 56
            )
        else:
            return ApiMarkXRayNotificationsSeenResponseData(
                marked_count = 56,
        )
        """

    def testApiMarkXRayNotificationsSeenResponseData(self):
        """Test ApiMarkXRayNotificationsSeenResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
