# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_meetings_get_user_payment_method_details200_response import GETMeetingsGetUserPaymentMethodDetails200Response

class TestGETMeetingsGetUserPaymentMethodDetails200Response(unittest.TestCase):
    """GETMeetingsGetUserPaymentMethodDetails200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETMeetingsGetUserPaymentMethodDetails200Response:
        """Test GETMeetingsGetUserPaymentMethodDetails200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETMeetingsGetUserPaymentMethodDetails200Response`
        """
        model = GETMeetingsGetUserPaymentMethodDetails200Response()
        if include_optional:
            return GETMeetingsGetUserPaymentMethodDetails200Response(
                data = elio_client.models.api/get_user_payment_method_details_response_data.api.GetUserPaymentMethodDetailsResponseData(
                    last_payment = elio_client.models.shared/billing_payment_result_dto.shared.BillingPaymentResultDTO(
                        error_code = '', 
                        status = '', ), ),
                message = '',
                success = True
            )
        else:
            return GETMeetingsGetUserPaymentMethodDetails200Response(
                data = elio_client.models.api/get_user_payment_method_details_response_data.api.GetUserPaymentMethodDetailsResponseData(
                    last_payment = elio_client.models.shared/billing_payment_result_dto.shared.BillingPaymentResultDTO(
                        error_code = '', 
                        status = '', ), ),
                message = '',
                success = True,
        )
        """

    def testGETMeetingsGetUserPaymentMethodDetails200Response(self):
        """Test GETMeetingsGetUserPaymentMethodDetails200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
