# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_xray_generate_x_ray_prompt200_response import POSTXrayGenerateXRayPrompt200Response

class TestPOSTXrayGenerateXRayPrompt200Response(unittest.TestCase):
    """POSTXrayGenerateXRayPrompt200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTXrayGenerateXRayPrompt200Response:
        """Test POSTXrayGenerateXRayPrompt200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTXrayGenerateXRayPrompt200Response`
        """
        model = POSTXrayGenerateXRayPrompt200Response()
        if include_optional:
            return POSTXrayGenerateXRayPrompt200Response(
                data = elio_client.models.api/generate_x_ray_prompt_response_data.api.GenerateXRayPromptResponseData(
                    prompt = '', 
                    type = '', ),
                message = '',
                success = True
            )
        else:
            return POSTXrayGenerateXRayPrompt200Response(
                data = elio_client.models.api/generate_x_ray_prompt_response_data.api.GenerateXRayPromptResponseData(
                    prompt = '', 
                    type = '', ),
                message = '',
                success = True,
        )
        """

    def testPOSTXrayGenerateXRayPrompt200Response(self):
        """Test POSTXrayGenerateXRayPrompt200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
