from temporalio import workflow
from datetime import timedelta

with workflow.unsafe.imports_passed_through():
    from nebula.temporal.activities.typesense_act import TypesenseActivities
    from nebula.temporal.activities.metadata_act import MetadataActivities
    from nebula.temporal.types import (
        TypesenseIndexingWorkflowParams,
        TypesenseIndexingWorkflowResult,
        GetRecurrenceDataInput,
        ProcessTranscriptsForTypesenseInput,
        IndexMeetingInTypesenseInput,
        MetadataWorkflowParams,
    )


@workflow.defn(name="TypesenseIndexing")
class TypesenseIndexingWorkflow:
    """Workflow for typesense indexing of meeting transcripts"""

    @workflow.run
    async def run(
        self, params: TypesenseIndexingWorkflowParams
    ) -> TypesenseIndexingWorkflowResult:
        """
        Run the typesense indexing workflow.

        Args:
            params: TypesenseIndexingWorkflowParams containing session_id and recurrence_id

        Returns:
            TypesenseIndexingWorkflowResult containing the results of indexing
        """
        workflow.logger.info(
            f"Starting typesense indexing workflow for session {params.session_id}, "
            f"recurrence {params.recurrence_id}"
        )

        start_time = workflow.now().timestamp()

        # Step 1: Get transcription batches
        transcription_result = await workflow.execute_activity(  # type: ignore
            MetadataActivities.get_transcription_batches,  # type: ignore
            MetadataWorkflowParams(
                session_id=params.session_id,
                recurrence_id=params.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=60),
        )

        if len(transcription_result.transcription_batches) == 0:
            workflow.logger.warn(
                f"No transcription batches found for session {params.session_id}, "
                f"recurrence {params.recurrence_id}"
            )
            return TypesenseIndexingWorkflowResult(
                done=True,
                message="No transcription batches found",
                indexed_documents=0,
                processing_time_seconds=0,
            )

        # Step 2: Get recurrence data for indexing
        recurrence_data = await workflow.execute_activity(  # type: ignore
            TypesenseActivities.get_recurrence_data,  # type: ignore
            GetRecurrenceDataInput(
                session_id=params.session_id,
                recurrence_id=params.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 3: Process transcripts for typesense format
        processed_transcripts = await workflow.execute_activity(  # type: ignore
            TypesenseActivities.process_transcripts_for_typesense,  # type: ignore
            ProcessTranscriptsForTypesenseInput(
                transcripts=list(transcription_result.transcription_batches),
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 4: Index the meeting in Typesense
        indexing_result = await workflow.execute_activity(  # type: ignore
            TypesenseActivities.index_meeting_in_typesense,  # type: ignore
            IndexMeetingInTypesenseInput(
                recurrence_data=recurrence_data,
                processed_transcripts=processed_transcripts,
            ),
            start_to_close_timeout=timedelta(minutes=3),
        )

        processing_time = workflow.now().timestamp() - start_time

        workflow.logger.info(
            f"Completed typesense indexing for session {params.session_id}, "
            f"recurrence {params.recurrence_id} in {processing_time:.2f} seconds"
        )

        return TypesenseIndexingWorkflowResult(
            done=True,
            message="Successfully indexed meeting in Typesense",
            indexed_documents=indexing_result.indexed_documents,
            processing_time_seconds=processing_time,
        ) 