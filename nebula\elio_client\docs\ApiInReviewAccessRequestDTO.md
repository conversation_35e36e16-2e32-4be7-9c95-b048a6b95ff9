# ApiInReviewAccessRequestDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**access_type** | **str** |  | 
**created_at** | **int** |  | 
**id** | **str** |  | 
**request_message** | **str** |  | 
**restriction_status** | **str** |  | 
**session_id** | **str** |  | 
**session_recurrence_id** | **str** |  | 
**type** | **str** |  | 
**updated_at** | **int** |  | 
**user** | [**ApiInReviewAccessRequestDTOUser**](ApiInReviewAccessRequestDTOUser.md) |  | 
**value** | **str** |  | 

## Example

```python
from elio_client.models.api_in_review_access_request_dto import ApiInReviewAccessRequestDTO

# TODO update the JSON string below
json = "{}"
# create an instance of ApiInReviewAccessRequestDTO from a JSON string
api_in_review_access_request_dto_instance = ApiInReviewAccessRequestDTO.from_json(json)
# print the JSON string representation of the object
print(ApiInReviewAccessRequestDTO.to_json())

# convert the object into a dict
api_in_review_access_request_dto_dict = api_in_review_access_request_dto_instance.to_dict()
# create an instance of ApiInReviewAccessRequestDTO from a dict
api_in_review_access_request_dto_from_dict = ApiInReviewAccessRequestDTO.from_dict(api_in_review_access_request_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


