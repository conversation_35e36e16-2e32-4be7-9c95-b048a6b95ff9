# coding: utf-8

# flake8: noqa

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


__version__ = "1.0.0"

# import apis into sdk package
from elio_client.api.default_api import DefaultApi

# import ApiClient
from elio_client.api_response import ApiResponse
from elio_client.api_client import ApiClient
from elio_client.configuration import Configuration
from elio_client.exceptions import OpenApiException
from elio_client.exceptions import ApiTypeError
from elio_client.exceptions import ApiValueError
from elio_client.exceptions import ApiKeyError
from elio_client.exceptions import ApiAttributeError
from elio_client.exceptions import ApiException

# import models into sdk package
from elio_client.models.api_error import APIError
from elio_client.models.api_access_control_item import ApiAccessControlItem
from elio_client.models.api_generate_x_ray_info_response_data import ApiGenerateXRayInfoResponseData
from elio_client.models.api_generate_x_ray_prompt_response_data import ApiGenerateXRayPromptResponseData
from elio_client.models.api_get_user_payment_method_details_response_data import ApiGetUserPaymentMethodDetailsResponseData
from elio_client.models.api_get_x_ray_notifications_response_data import ApiGetXRayNotificationsResponseData
from elio_client.models.api_heard_speaker import ApiHeardSpeaker
from elio_client.models.api_identified_speaker import ApiIdentifiedSpeaker
from elio_client.models.api_in_review_access_request_dto import ApiInReviewAccessRequestDTO
from elio_client.models.api_in_review_access_request_dto_user import ApiInReviewAccessRequestDTOUser
from elio_client.models.api_list_sessions_response_data import ApiListSessionsResponseData
from elio_client.models.api_list_user_transactions_response_data import ApiListUserTransactionsResponseData
from elio_client.models.api_list_x_ray_templates_response_data import ApiListXRayTemplatesResponseData
from elio_client.models.api_list_x_rays_response_data import ApiListXRaysResponseData
from elio_client.models.api_mark_x_ray_notifications_seen_response_data import ApiMarkXRayNotificationsSeenResponseData
from elio_client.models.api_meeting_metadata import ApiMeetingMetadata
from elio_client.models.api_meeting_suggestion_dto import ApiMeetingSuggestionDTO
from elio_client.models.api_meeting_suggestion_input import ApiMeetingSuggestionInput
from elio_client.models.api_recall_search_hit import ApiRecallSearchHit
from elio_client.models.api_recall_transcriptions_webhook_data import ApiRecallTranscriptionsWebhookData
from elio_client.models.api_recall_transcriptions_webhook_log import ApiRecallTranscriptionsWebhookLog
from elio_client.models.api_recall_transcriptions_webhook_search import ApiRecallTranscriptionsWebhookSearch
from elio_client.models.api_recall_transcriptions_webhook_status import ApiRecallTranscriptionsWebhookStatus
from elio_client.models.api_recall_transcriptions_webhook_transcript import ApiRecallTranscriptionsWebhookTranscript
from elio_client.models.api_recall_word import ApiRecallWord
from elio_client.models.api_session_access_dto import ApiSessionAccessDTO
from elio_client.models.api_session_access_rules_dto import ApiSessionAccessRulesDTO
from elio_client.models.api_session_default_guest import ApiSessionDefaultGuest
from elio_client.models.api_session_default_user import ApiSessionDefaultUser
from elio_client.models.api_session_default_users_dto import ApiSessionDefaultUsersDTO
from elio_client.models.api_session_guest import ApiSessionGuest
from elio_client.models.api_session_request_count import ApiSessionRequestCount
from elio_client.models.api_session_user import ApiSessionUser
from elio_client.models.api_session_user_dto import ApiSessionUserDTO
from elio_client.models.api_speaker_snippet import ApiSpeakerSnippet
from elio_client.models.api_suggestion_response_dto import ApiSuggestionResponseDTO
from elio_client.models.api_update_access_rule import ApiUpdateAccessRule
from elio_client.models.api_update_user_request_data import ApiUpdateUserRequestData
from elio_client.models.api_user_meeting_type_request_dto import ApiUserMeetingTypeRequestDTO
from elio_client.models.api_user_summary_dto import ApiUserSummaryDTO
from elio_client.models.api_x_ray_dto import ApiXRayDTO
from elio_client.models.api_x_ray_doc_commit import ApiXRayDocCommit
from elio_client.models.api_x_ray_notification_dto import ApiXRayNotificationDTO
from elio_client.models.api_x_ray_template_dto import ApiXRayTemplateDTO
from elio_client.models.delete_xray_delete_x_ray200_response import DELETEXrayDeleteXRay200Response
from elio_client.models.data_is_the_response_data import DataIsTheResponseData
from elio_client.models.get_comms_post_session_summary_ready200_response import GETCommsPostSessionSummaryReady200Response
from elio_client.models.get_meetings_get_all_access_requests200_response import GETMeetingsGetAllAccessRequests200Response
from elio_client.models.get_meetings_get_all_access_requests200_response_data import GETMeetingsGetAllAccessRequests200ResponseData
from elio_client.models.get_meetings_get_chat_token200_response import GETMeetingsGetChatToken200Response
from elio_client.models.get_meetings_get_latest_recurrence_compact200_response import GETMeetingsGetLatestRecurrenceCompact200Response
from elio_client.models.get_meetings_get_meeting_suggestions_by_session200_response import GETMeetingsGetMeetingSuggestionsBySession200Response
from elio_client.models.get_meetings_get_meeting_suggestions_by_user_internal200_response import GETMeetingsGetMeetingSuggestionsByUserInternal200Response
from elio_client.models.get_meetings_get_meeting_suggestions_by_user_internal200_response_data import GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData
from elio_client.models.get_meetings_get_past_session_recurrences200_response import GETMeetingsGetPastSessionRecurrences200Response
from elio_client.models.get_meetings_get_session_recurrences_by_id200_response import GETMeetingsGetSessionRecurrencesById200Response
from elio_client.models.get_meetings_get_session_users200_response import GETMeetingsGetSessionUsers200Response
from elio_client.models.get_meetings_get_session_users_by_session_id200_response import GETMeetingsGetSessionUsersBySessionID200Response
from elio_client.models.get_meetings_get_update_user_payment_method_transaction200_response import GETMeetingsGetUpdateUserPaymentMethodTransaction200Response
from elio_client.models.get_meetings_get_user_payment_method_details200_response import GETMeetingsGetUserPaymentMethodDetails200Response
from elio_client.models.get_meetings_get_user_plan_by_user_id200_response import GETMeetingsGetUserPlanByUserID200Response
from elio_client.models.get_meetings_list_user_transactions200_response import GETMeetingsListUserTransactions200Response
from elio_client.models.get_meetings_list_users200_response import GETMeetingsListUsers200Response
from elio_client.models.get_transcriptions_fetch_heard_speakers200_response import GETTranscriptionsFetchHeardSpeakers200Response
from elio_client.models.get_transcriptions_generate_bot_google_auth_url200_response import GETTranscriptionsGenerateBotGoogleAuthURL200Response
from elio_client.models.get_transcriptions_get_batch_ids200_response import GETTranscriptionsGetBatchIDs200Response
from elio_client.models.get_transcriptions_get_batch_ids200_response_data import GETTranscriptionsGetBatchIDs200ResponseData
from elio_client.models.get_transcriptions_get_calendar_user200_response import GETTranscriptionsGetCalendarUser200Response
from elio_client.models.get_transcriptions_get_speakers_as_session_guests_by_session_id200_response import GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response
from elio_client.models.get_transcriptions_get_transcription_batch200_response import GETTranscriptionsGetTranscriptionBatch200Response
from elio_client.models.get_transcriptions_get_transcription_batch200_response_data import GETTranscriptionsGetTranscriptionBatch200ResponseData
from elio_client.models.get_xray_get_x_ray_notifications200_response import GETXrayGetXRayNotifications200Response
from elio_client.models.get_xray_get_x_ray_template200_response import GETXrayGetXRayTemplate200Response
from elio_client.models.get_xray_list_x_ray_templates200_response import GETXrayListXRayTemplates200Response
from elio_client.models.get_xray_list_x_rays200_response import GETXrayListXRays200Response
from elio_client.models.mars_api_user_social import MarsApiUserSocial
from elio_client.models.patch_meetings_recur_session_no_request200_response import PATCHMeetingsRecurSessionNoRequest200Response
from elio_client.models.patch_meetings_recur_session_request import PATCHMeetingsRecurSessionRequest
from elio_client.models.patch_meetings_update_session200_response import PATCHMeetingsUpdateSession200Response
from elio_client.models.patch_meetings_update_session_request import PATCHMeetingsUpdateSessionRequest
from elio_client.models.patch_meetings_update_session_request_user_meeting_type import PATCHMeetingsUpdateSessionRequestUserMeetingType
from elio_client.models.patch_xray_mark_x_ray_notifications_seen200_response import PATCHXrayMarkXRayNotificationsSeen200Response
from elio_client.models.patch_xray_update_x_ray_request import PATCHXrayUpdateXRayRequest
from elio_client.models.post_comms_meeting_metadata_ready_request import POSTCommsMeetingMetadataReadyRequest
from elio_client.models.post_comms_trigger_guest_waiting_in_lobby_notification_request import POSTCommsTriggerGuestWaitingInLobbyNotificationRequest
from elio_client.models.post_meetings_ai_feed_event_request import POSTMeetingsAIFeedEventRequest
from elio_client.models.post_meetings_count_in_review_requests200_response import POSTMeetingsCountInReviewRequests200Response
from elio_client.models.post_meetings_count_in_review_requests_request import POSTMeetingsCountInReviewRequestsRequest
from elio_client.models.post_meetings_create_access_request_request import POSTMeetingsCreateAccessRequestRequest
from elio_client.models.post_meetings_create_session_with_user200_response import POSTMeetingsCreateSessionWithUser200Response
from elio_client.models.post_meetings_create_session_with_user200_response_data import POSTMeetingsCreateSessionWithUser200ResponseData
from elio_client.models.post_meetings_create_session_with_user_request import POSTMeetingsCreateSessionWithUserRequest
from elio_client.models.post_meetings_create_user_request import POSTMeetingsCreateUserRequest
from elio_client.models.post_meetings_get_lobby_guests200_response import POSTMeetingsGetLobbyGuests200Response
from elio_client.models.post_meetings_get_session_access_rules200_response import POSTMeetingsGetSessionAccessRules200Response
from elio_client.models.post_meetings_get_user_by_id200_response import POSTMeetingsGetUserByID200Response
from elio_client.models.post_meetings_get_user_by_id_request import POSTMeetingsGetUserByIDRequest
from elio_client.models.post_meetings_get_user_by_id_with_relations200_response import POSTMeetingsGetUserByIDWithRelations200Response
from elio_client.models.post_meetings_get_user_by_id_with_relations200_response_data import POSTMeetingsGetUserByIDWithRelations200ResponseData
from elio_client.models.post_meetings_join_lobby_as_guest_request import POSTMeetingsJoinLobbyAsGuestRequest
from elio_client.models.post_meetings_list_sessions_by_lobby_id_request import POSTMeetingsListSessionsByLobbyIDRequest
from elio_client.models.post_meetings_login_guest_user_with_session200_response import POSTMeetingsLoginGuestUserWithSession200Response
from elio_client.models.post_meetings_login_guest_user_with_session_request import POSTMeetingsLoginGuestUserWithSessionRequest
from elio_client.models.post_meetings_remove_guest_from_lobby_request import POSTMeetingsRemoveGuestFromLobbyRequest
from elio_client.models.post_meetings_revoke_access_session_user_request import POSTMeetingsRevokeAccessSessionUserRequest
from elio_client.models.post_meetings_update_session_access_control_rules_request import POSTMeetingsUpdateSessionAccessControlRulesRequest
from elio_client.models.post_meetings_upsert_meeting_suggestions200_response import POSTMeetingsUpsertMeetingSuggestions200Response
from elio_client.models.post_meetings_upsert_meeting_suggestions_request import POSTMeetingsUpsertMeetingSuggestionsRequest
from elio_client.models.post_meetings_upsert_session_user_request import POSTMeetingsUpsertSessionUserRequest
from elio_client.models.post_transcriptions_create_bot_for_meeting200_response import POSTTranscriptionsCreateBotForMeeting200Response
from elio_client.models.post_transcriptions_create_bot_for_meeting_request import POSTTranscriptionsCreateBotForMeetingRequest
from elio_client.models.post_transcriptions_disconnect_calendar_request import POSTTranscriptionsDisconnectCalendarRequest
from elio_client.models.post_transcriptions_generate_audio_ingress_key200_response import POSTTranscriptionsGenerateAudioIngressKey200Response
from elio_client.models.post_transcriptions_generate_audio_ingress_key_request import POSTTranscriptionsGenerateAudioIngressKeyRequest
from elio_client.models.post_transcriptions_identify_heard_speakers_request import POSTTranscriptionsIdentifyHeardSpeakersRequest
from elio_client.models.post_transcriptions_process_bot_recording_request import POSTTranscriptionsProcessBotRecordingRequest
from elio_client.models.post_transcriptions_recall_transcriptions_webhook_request import POSTTranscriptionsRecallTranscriptionsWebhookRequest
from elio_client.models.post_transcriptions_remove_users_from_recall_account_request import POSTTranscriptionsRemoveUsersFromRecallAccountRequest
from elio_client.models.post_transcriptions_update_request import POSTTranscriptionsUpdateRequest
from elio_client.models.post_xray_create200_response import POSTXrayCreate200Response
from elio_client.models.post_xray_create_request import POSTXrayCreateRequest
from elio_client.models.post_xray_generate_x_ray_info200_response import POSTXrayGenerateXRayInfo200Response
from elio_client.models.post_xray_generate_x_ray_info_request import POSTXrayGenerateXRayInfoRequest
from elio_client.models.post_xray_generate_x_ray_prompt200_response import POSTXrayGenerateXRayPrompt200Response
from elio_client.models.post_xray_generate_x_ray_prompt_request import POSTXrayGenerateXRayPromptRequest
from elio_client.models.put_meetings_add_session_access_control_rules200_response import PUTMeetingsAddSessionAccessControlRules200Response
from elio_client.models.put_meetings_add_session_access_control_rules200_response_data import PUTMeetingsAddSessionAccessControlRules200ResponseData
from elio_client.models.put_meetings_add_session_access_control_rules_request import PUTMeetingsAddSessionAccessControlRulesRequest
from elio_client.models.put_meetings_approve_or_denyed_session_access_request200_response import PUTMeetingsApproveOrDenyedSessionAccessRequest200Response
from elio_client.models.put_meetings_approve_or_denyed_session_access_request_request import PUTMeetingsApproveOrDenyedSessionAccessRequestRequest
from elio_client.models.put_meetings_update_user_by_id_request import PUTMeetingsUpdateUserByIDRequest
from elio_client.models.recallai_bot_meeting_link import RecallaiBotMeetingLink
from elio_client.models.recallai_calendar_meeting import RecallaiCalendarMeeting
from elio_client.models.recallai_calendar_user import RecallaiCalendarUser
from elio_client.models.recallai_meeting_metadata import RecallaiMeetingMetadata
from elio_client.models.recallai_meeting_participant import RecallaiMeetingParticipant
from elio_client.models.recallai_recall_connection import RecallaiRecallConnection
from elio_client.models.recallai_recording_preferences import RecallaiRecordingPreferences
from elio_client.models.shared_billing_cycle_dto import SharedBillingCycleDTO
from elio_client.models.shared_billing_payment_method_details_dto import SharedBillingPaymentMethodDetailsDTO
from elio_client.models.shared_billing_payment_result_dto import SharedBillingPaymentResultDTO
from elio_client.models.shared_billing_transaction_dto import SharedBillingTransactionDTO
from elio_client.models.shared_billing_transaction_item_dto import SharedBillingTransactionItemDTO
from elio_client.models.shared_billing_transaction_payment_attempt_dto import SharedBillingTransactionPaymentAttemptDTO
from elio_client.models.shared_guest_dto import SharedGuestDTO
from elio_client.models.shared_lobby_dto import SharedLobbyDTO
from elio_client.models.shared_lobby_participant_dto import SharedLobbyParticipantDTO
from elio_client.models.shared_notification_settings import SharedNotificationSettings
from elio_client.models.shared_offset_pagination_response import SharedOffsetPaginationResponse
from elio_client.models.shared_onboarding_flags import SharedOnboardingFlags
from elio_client.models.shared_participant_metadata import SharedParticipantMetadata
from elio_client.models.shared_price_dto import SharedPriceDTO
from elio_client.models.shared_price_dto_trial_period import SharedPriceDTOTrialPeriod
from elio_client.models.shared_price_quantity_dto import SharedPriceQuantityDTO
from elio_client.models.shared_session_access_rule_dto import SharedSessionAccessRuleDTO
from elio_client.models.shared_session_dto import SharedSessionDTO
from elio_client.models.shared_session_integrations_dto import SharedSessionIntegrationsDTO
from elio_client.models.shared_session_subscription_plan_dto import SharedSessionSubscriptionPlanDTO
from elio_client.models.shared_state_updated_at import SharedStateUpdatedAt
from elio_client.models.shared_subscription_item_dto import SharedSubscriptionItemDTO
from elio_client.models.shared_subscription_item_dto_trial_dates import SharedSubscriptionItemDTOTrialDates
from elio_client.models.shared_subscription_plan_config_dto import SharedSubscriptionPlanConfigDTO
from elio_client.models.shared_subscription_plan_config_dtoai_feed import SharedSubscriptionPlanConfigDTOAiFeed
from elio_client.models.shared_subscription_plan_config_dto_integrations import SharedSubscriptionPlanConfigDTOIntegrations
from elio_client.models.shared_subscription_plan_config_dto_meetings import SharedSubscriptionPlanConfigDTOMeetings
from elio_client.models.shared_subscription_plan_config_dto_recording import SharedSubscriptionPlanConfigDTORecording
from elio_client.models.shared_subscription_plan_config_dto_stream import SharedSubscriptionPlanConfigDTOStream
from elio_client.models.shared_subscription_plan_config_dto_support import SharedSubscriptionPlanConfigDTOSupport
from elio_client.models.shared_subscription_plan_config_dto_time_limit import SharedSubscriptionPlanConfigDTOTimeLimit
from elio_client.models.shared_team_with_inline_relations_dto import SharedTeamWithInlineRelationsDTO
from elio_client.models.shared_transcription_dto import SharedTranscriptionDTO
from elio_client.models.shared_unit_price_dto import SharedUnitPriceDTO
from elio_client.models.shared_unit_price_override_dto import SharedUnitPriceOverrideDTO
from elio_client.models.shared_user_crypto_token import SharedUserCryptoToken
from elio_client.models.shared_user_dto import SharedUserDTO
from elio_client.models.shared_user_meeting_type_dto import SharedUserMeetingTypeDTO
from elio_client.models.shared_user_social_dto import SharedUserSocialDTO
from elio_client.models.shared_user_subscription_plan_dto import SharedUserSubscriptionPlanDTO
from elio_client.models.shared_user_subscription_plan_dto_billing_details import SharedUserSubscriptionPlanDTOBillingDetails
from elio_client.models.shared_user_subscription_plan_dto_management_urls import SharedUserSubscriptionPlanDTOManagementUrls
from elio_client.models.shared_user_subscription_plan_dto_scheduled_change import SharedUserSubscriptionPlanDTOScheduledChange
from elio_client.models.the_data_returned_by_the_api import TheDataReturnedByTheAPI
from elio_client.models.the_response_data import TheResponseData
