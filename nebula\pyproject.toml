[tool.poetry]
name = "nebula"
version = "0.1.0"
description = "service for ai features for waitroom"
authors = []
maintainers = []
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.12"
uvicorn = { version = "^0.17.0", extras = ["standard"] }
pydantic = "2.10.4"
pydantic-settings = "^2.2.1"
fastapi = "^0.110.2"
yarl = "^1.18.3"
ujson = "^5.1.0"
piccolo = { extras = ["postgres"], version = "^1.22.0" }
aiofiles = "^0.8.0"
httptools = "^0.6.4"
loguru = "^0.7.3"
aiokafka = "^0.11.0"
botocore = "^1.29.158"
boto3 = "^1.26.158"
connexion = { extras = ["swagger-ui"], version = "^3.1.0" }
simplejson = "^3.19.1"
requests = "^2.31.0"
markdown = "^3.4.4"
types-markdown = "^********"
psycopg2-binary = "^2.9.9"
openai = "^1.74.0"
tiktoken = "^0.8.0"
langchain = "^0.3.8"
langchain-openai = "^0.2.11"
nltk = "^3.9"
backoff = "^2.2.1"
typesense = "^0.19.0"
modal = "^0.72.14"
python-dotenv = "^1.0.1"
aiopg = "^1.4.0"
dateparser = "^1.2.0"
matplotlib = "^3.9.1"
seaborn = "^0.13.2"
anthropic = "^0.30.1"
scikit-learn = "^1.6.0"
opentelemetry-distro = "^0.46b0"
opentelemetry-exporter-otlp = "^1.25.0"
opentelemetry-instrumentation-aiohttp-client = "^0.46b0"
textblob = "^0.18.0.post0"
cohere = "^5.5.8"
humanize = "^4.11.0"
sse-starlette = "^2.1.3"
faker = "^30.0.0"
arrow = "^1.3.0"
pytest-asyncio = "^0.25.2"
temporalio = "^1.12.0"
cryptography = "^45.0.3"

[tool.poetry.group.dev.dependencies]
anyio = "^3.6.1"
autoflake = "^1.4"
httpx = "^0.28.1"
isort = "^5.10.1"
langchain-experimental = "^0.3.4"
pyright = "1.1.356"
pytest = "^8.3"
pytest-cov = "^3.0.0"
pytest-env = "^0.6.2"
ruff = "^0.9.10"
sentence-transformers = "^3.3.1"
sseclient = "^0.0.27"
text-generation = "^0.7.0"
types-markdown = "^********"
types-requests = "^********"
types-simplejson = "^********"
wemake-python-styleguide = "^0.16.1"
yesqa = "^1.3.0"

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "elio_client",
]

# Same as Black.
line-length = 88
indent-width = 4

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["E4", "E7", "E9", "F"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
# docstring-code-format = true

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
# docstring-code-line-length = "dynamic"

[tool.pytest.ini_options]
filterwarnings = [
    "error",
    "ignore::DeprecationWarning",
    "ignore:.*unclosed.*:ResourceWarning",
    "ignore:Please use `import python_multipart` instead.:PendingDeprecationWarning",
]
testpaths = ["nebula/tests"]
log_cli = true
markers = [
    "integration: marks tests as integration tests (deselect with '-m \"not integration\"')",
    "unit: marks tests as unit tests",
]


[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
