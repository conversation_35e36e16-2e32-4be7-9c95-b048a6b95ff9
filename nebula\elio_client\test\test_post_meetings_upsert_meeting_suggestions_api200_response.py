# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_meetings_upsert_meeting_suggestions_api200_response import POSTMeetingsUpsertMeetingSuggestionsAPI200Response

class TestPOSTMeetingsUpsertMeetingSuggestionsAPI200Response(unittest.TestCase):
    """POSTMeetingsUpsertMeetingSuggestionsAPI200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTMeetingsUpsertMeetingSuggestionsAPI200Response:
        """Test POSTMeetingsUpsertMeetingSuggestionsAPI200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTMeetingsUpsertMeetingSuggestionsAPI200Response`
        """
        model = POSTMeetingsUpsertMeetingSuggestionsAPI200Response()
        if include_optional:
            return POSTMeetingsUpsertMeetingSuggestionsAPI200Response(
                count = 56,
                message = '',
                success = True
            )
        else:
            return POSTMeetingsUpsertMeetingSuggestionsAPI200Response(
                count = 56,
                message = '',
                success = True,
        )
        """

    def testPOSTMeetingsUpsertMeetingSuggestionsAPI200Response(self):
        """Test POSTMeetingsUpsertMeetingSuggestionsAPI200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
