from temporalio import workflow
from datetime import timedelta, datetime


with workflow.unsafe.imports_passed_through():
    from nebula.temporal.activities.meeting_suggestions_act import MeetingSuggestionsActivities
    from nebula.temporal.activities.metadata_act import MetadataActivities
    from nebula.temporal.types import (
        MeetingSuggestionsWorkflowParams,
        MeetingSuggestionsWorkflowResult,
        ExtractAllUsersMeetingSuggestionsInput,
        CreateAllUsersMeetingSuggestionsInput,
    )


@workflow.defn(name="MeetingSuggestionsGeneration")
class MeetingSuggestionsWorkflow:
    """Workflow for meeting suggestions generation from meeting transcripts"""

    @workflow.run
    async def run(
        self, session_info: MeetingSuggestionsWorkflowParams
    ) -> MeetingSuggestionsWorkflowResult:
        """
        Run the meeting suggestions generation workflow.

        Args:
            session_info: Session identification information containing session_id and recurrence_id

        Returns:
            MeetingSuggestionsWorkflowResult containing the results of meeting suggestions generation
        """
        # Step 1: Get transcription batches (using shared activity)
        transcription_result = await workflow.execute_activity(  # type: ignore
            MetadataActivities.get_transcription_batches,  # type: ignore
            session_info,
            start_to_close_timeout=timedelta(seconds=120),
        )

        if len(transcription_result.transcription_batches) == 0:
            workflow.logger.warn(
                f"No transcription batches found for session {session_info.session_id}, "
                f"recurrence {session_info.recurrence_id}"
            )
            return MeetingSuggestionsWorkflowResult(
                done=True,
                message="No transcription batches found",
            )

        # Step 2: Get session data (using shared activity)
        session_data = await workflow.execute_activity(  # type: ignore
            MetadataActivities.get_session_data,  # type: ignore
            session_info,
            start_to_close_timeout=timedelta(seconds=10),
        )

        # Step 3: Process transcript with user IDs
        transcript_with_users = await workflow.execute_activity(  # type: ignore
            MeetingSuggestionsActivities.process_transcript_with_user_ids,  # type: ignore
            transcription_result,
            start_to_close_timeout=timedelta(seconds=30),
        )

        if len(transcript_with_users.transcript) <= 500:
            workflow.logger.info(
                f"Session {session_info.session_id} - skipping meeting suggestions generation "
                f"(transcript too short: {len(transcript_with_users.transcript)} tokens)"
            )
            return MeetingSuggestionsWorkflowResult(
                done=True,
                message="Transcript too short",
            )

        if len(transcript_with_users.participants) == 0:
            workflow.logger.warn(
                f"No participants with user IDs found in transcript for session {session_info.session_id}, "
                f"recurrence {session_info.recurrence_id}"
            )
            return MeetingSuggestionsWorkflowResult(
                done=True,
                message="No participants with user IDs found in transcript",
            )

        # Step 4: Extract meeting suggestions for ALL users in SINGLE LLM call (NEW APPROACH)
        start_time = workflow.now().timestamp()
        
        # Format date from current timestamp  
        meeting_date = datetime.fromtimestamp(start_time).strftime("%Y-%m-%d")
        
        suggestions = await workflow.execute_activity(  # type: ignore
            MeetingSuggestionsActivities.extract_all_users_meeting_suggestions,  # type: ignore
            ExtractAllUsersMeetingSuggestionsInput(
                title=session_data.title,
                date=meeting_date,
                transcript=transcript_with_users.transcript,
                participants=transcript_with_users.participants,
            ),
            start_to_close_timeout=timedelta(minutes=5),  # Longer timeout for single large call
        )

        processing_time = workflow.now().timestamp() - start_time

        # Check if suggestions are empty or invalid
        if not suggestions or not suggestions.user_suggestions or len(suggestions.user_suggestions) == 0:
            workflow.logger.info(
                f"No meeting suggestions extracted for session {session_info.session_id}, "
                f"recurrence {session_info.recurrence_id}"
            )
            return MeetingSuggestionsWorkflowResult(
                done=False,
                message="Meeting suggestions extraction failed - AI returned empty results",
            )

        # Step 5: Create meeting suggestions records for all users
        suggestions_record = await workflow.execute_activity(  # type: ignore
            MeetingSuggestionsActivities.create_all_users_meeting_suggestions_record,  # type: ignore
            CreateAllUsersMeetingSuggestionsInput(
                session_info=session_info,
                suggestions=suggestions,
            ),
            start_to_close_timeout=timedelta(seconds=30),  # More time for multiple users
        )

        workflow.logger.info(
            f"Completed meeting suggestions generation: {suggestions_record.total_records_created} records "
            f"for {suggestions_record.participant_count} participants in session {session_info.session_id}, "
            f"recurrence {session_info.recurrence_id} in {processing_time:.2f} seconds"
        )

        return MeetingSuggestionsWorkflowResult(
            done=True,
            message=f"Successfully generated {suggestions_record.total_records_created} meeting suggestions for {suggestions_record.participant_count} participants",
            processing_time_seconds=processing_time,
        ) 