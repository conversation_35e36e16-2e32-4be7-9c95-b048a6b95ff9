# ApiUpdateUserRequestData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**about** | **str** |  | [optional] 
**avatar** | **str** |  | [optional] 
**first_name** | **str** |  | [optional] 
**last_name** | **str** |  | [optional] 
**onboarding** | [**SharedOnboardingFlags**](SharedOnboardingFlags.md) |  | [optional] 
**social** | [**List[SharedUserSocialDTO]**](SharedUserSocialDTO.md) |  | [optional] 

## Example

```python
from elio_client.models.api_update_user_request_data import ApiUpdateUserRequestData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiUpdateUserRequestData from a JSON string
api_update_user_request_data_instance = ApiUpdateUserRequestData.from_json(json)
# print the JSON string representation of the object
print(ApiUpdateUserRequestData.to_json())

# convert the object into a dict
api_update_user_request_data_dict = api_update_user_request_data_instance.to_dict()
# create an instance of ApiUpdateUserRequestData from a dict
api_update_user_request_data_from_dict = ApiUpdateUserRequestData.from_dict(api_update_user_request_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


