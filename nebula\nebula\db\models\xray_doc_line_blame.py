from nebula.db.models.xray import XRay
from nebula.db.models.xray_doc_commit import XRayDocumentCommit
from piccolo.columns import BigSerial, Text, Timestamp, BigInt, Integer
from piccolo.columns.column_types import ForeignKey
from piccolo.columns.defaults.timestamp import TimestampNow
from piccolo.table import Table


class XRayDocumentLineBlame(Table, tablename="xray_doc_line_blames"):
    """
    Stores line-level blame information for each line in an XRay document.
    Tracks who last modified each line and when.
    """

    id = BigSerial(primary_key=True)
    xray_id = ForeignKey(references=XRay)
    commit_id = ForeignKey(references=XRayDocumentCommit)
    line_number = Integer(null=False)
    content = Text(null=False)  # Content of the line
    author_id = BigInt(
        null=True
    )  # Null if AI made the change. V1 does not allow the user to edit the document, V2 will have this functionality
    created_at = Timestamp(default=TimestampNow())

    class Meta:
        """Meta configuration for the LineBlame model."""

        indexes = (
            # Compound index for efficient retrieval of lines for a document
            (("xray_id", "line_number"), True),
        )
