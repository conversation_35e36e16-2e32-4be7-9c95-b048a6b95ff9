package shared

import (
	"embed"
	"encoding/json"
	"fmt"
	"github.com/stretchr/testify/assert"
	"os"
	"strings"
	"testing"

	_ "embed"

	encore "encore.dev"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"github.com/jmoiron/sqlx"
)

// A re-usable way to print data structures in a more readable way
// PP stands for PrettyPrint
func PP(something any) {
	j, _ := json.MarshalIndent(something, "", "\t")
	fmt.Println("-------------- PP --------------")
	fmt.Println(string(j))
	fmt.Println("--------------------------------")
}

func SeedDBTestDataFromFixture(db *sqldb.Database, fixture string) {
	// This function will wrap an encore db reference in a sqlx db reference
	// before calling the seed logic.
	sqlxDB := sqlx.NewDb(db.<PERSON>dl<PERSON>(), "pgx")
	SeedDBTestDataFromFixtureX(sqlxDB, fixture)
}

func SeedDBTestDataFromFixtureX(db *sqlx.DB, fixture string) {
	if encore.Meta().Environment.Type == "test" {
		result, err := db.Exec(fixture)
		if err != nil {
			msg := "failed to seed from fixtures"
			rlog.Error(msg, "err", err)
			panic(msg)
		}
		affected, err := result.RowsAffected()
		if err != nil {
			msg := "failed to get rows affected"
			rlog.Error(msg, "err", err)
			panic(msg)
		}

		if affected == 0 {
			rlog.Warn("no rows seeded", "fixture", fixture)
		}
	}
}

// Because go:embed doesn't allow embedding files from parent directories, see here:
// https://stackoverflow.com/questions/74539809/embedding-files-via-relative-paths-in-go
// This shared functionality is to expose all the fixture files for tests in the elio project.

// read all sql files in the test_data directory:
//
//go:embed test-data/*.sql
var testDataFiles embed.FS

// PopulateAllTestFixtures returns a map of all the fixture files in the test-data directory.
func PopulateAllTestFixtures() map[string]string {
	var fixturesMap = map[string]string{}

	files, err := testDataFiles.ReadDir("test-data")
	if err != nil {
		panic(err)
	}

	for _, file := range files {
		content, err := testDataFiles.ReadFile("test-data/" + file.Name())
		if err != nil {
			panic(err)
		}
		fixturesMap[file.Name()] = string(content)
	}

	return fixturesMap
}

// GetFixtureByName returns the content of a fixture file by name, and is in general more efficient than PopulateAllTestFixtures,
// when fetching a single fixture. The arg `fixtureName` is the relative path from the shared/test-data directory.
func GetFixtureByName(fixtureName string) string {
	var fixtureContent string

	files, err := testDataFiles.ReadDir("test-data")
	if err != nil {
		panic(err)
	}
	for _, file := range files {
		if file.Name() == fixtureName {
			content, err := testDataFiles.ReadFile("test-data/" + file.Name())
			if err != nil {
				panic(err)
			}
			fixtureContent = string(content)
			break
		}
	}

	return fixtureContent
}

func AssertEqualJSON(t *testing.T, expected, actual []byte) {
	assert.Equal(t, strings.TrimSpace(string(expected)), strings.TrimSpace(string(actual)))
}

func MarshalResponse(t *testing.T, response any) []byte {
	resJSON, err := json.MarshalIndent(response, "", "  ")
	if err != nil {
		t.Fatal(err)
	}
	return resJSON
}

func MarshalAndSaveJSONToFile(fileName string, data any) error {
	jsonFile, err := os.Create(fileName)
	if err != nil {
		return err
	}
	defer jsonFile.Close()

	byteValue, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return err
	}

	_, err = jsonFile.Write(byteValue)
	if err != nil {
		return err
	}

	return nil
}
