from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from starlette import status
from typing import Dict, Any, cast

from nebula.services.meeting_metadata.meeting_metadata_service import (
    get_meeting_metadata_by_id,
    get_meeting_metadata_by_session_ids,
)
from nebula.web.api.auth import authorize
from nebula.web.api.meeting_metadata.schema import (
    MeetingMetadataResponse,
)
from nebula.web.api.meeting_metadata.utils import convert_metadata_to_dto

router = APIRouter()


@router.get(
    "/{metadata_id}",
    response_model=MeetingMetadataResponse,
    dependencies=[Depends(authorize)],
)
async def get_metadata_by_id(metadata_id: int) -> JSONResponse:
    """
    Get meeting metadata by ID.

    :param metadata_id: The ID of the metadata record
    :return: JSONResponse with metadata
    """
    metadata = await get_meeting_metadata_by_id(metadata_id)

    if not metadata:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"message": "Meeting metadata not found"},
        )

    # Convert the raw metadata dict to a MeetingMetadataDTO
    metadata_dto = None
    if metadata.metadata and isinstance(metadata.metadata, dict):
        metadata_dict = cast(Dict[str, Any], metadata.metadata)
        metadata_dto = convert_metadata_to_dto(metadata_dict)

    # Create response with proper handling of optional metadata
    response_data = {
        "message": "Successfully retrieved meeting metadata",
        "data": {
            "id": metadata.id,
            "session_id": metadata.session_id,
            "session_recurrence_id": metadata.session_recurrence_id,
            "metadata": metadata_dto.dict() if metadata_dto else None,
            "created_at": metadata.created_at.isoformat(),
            "updated_at": metadata.updated_at.isoformat(),
        },
    }

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=response_data,
    )


@router.get(
    "/by-session/{session_id}/{recurrence_id}",
    response_model=MeetingMetadataResponse,
    dependencies=[Depends(authorize)],
)
async def get_metadata_by_session_id(
    session_id: str,
    recurrence_id: str,
) -> JSONResponse:
    """
    Get meeting metadata by session ID and recurrence ID.

    :param session_id: The session ID
    :param recurrence_id: The session recurrence ID
    :return: JSONResponse with metadata
    """
    metadata = await get_meeting_metadata_by_session_ids(session_id, recurrence_id)

    if not metadata:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"message": "Meeting metadata not found"},
        )

    # Convert the raw metadata dict to a MeetingMetadataDTO
    metadata_dto = None
    if metadata.metadata and isinstance(metadata.metadata, dict):
        metadata_dict = cast(Dict[str, Any], metadata.metadata)
        metadata_dto = convert_metadata_to_dto(metadata_dict)

    # Create response with proper handling of optional metadata
    response_data = {
        "message": "Successfully retrieved meeting metadata",
        "data": {
            "id": metadata.id,
            "session_id": metadata.session_id,
            "session_recurrence_id": metadata.session_recurrence_id,
            "metadata": metadata_dto.dict() if metadata_dto else None,
            "created_at": metadata.created_at.isoformat(),
            "updated_at": metadata.updated_at.isoformat(),
        },
    }

    return JSONResponse(
        status_code=status.HTTP_200_OK,
        content=response_data,
    )
