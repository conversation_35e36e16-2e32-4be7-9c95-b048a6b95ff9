import os
import logging
import sys

import pytest
import pytest_asyncio
from aiopg import create_pool
from piccolo.apps.migrations.commands.forwards import run_forwards

from elio_client.models.shared_session_dto import SharedSessionDTO

from nebula.settings import settings
from piccolo_conf_test import TEST_SCHEMA
from elio_client.models.api_session_user_dto import ApiSessionUserDTO

logging.basicConfig(
    level=logging.DEBUG,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    stream=sys.stderr,
    force=True,
)
logger = logging.getLogger(__name__)

os.environ["PICCOLO_CONF"] = "piccolo_conf_test"


db_dsn = f"dbname={settings.postgres_db} user={settings.postgres_user} password={settings.postgres_pass} host={settings.postgres_host} port={settings.postgres_port}"


@pytest.fixture(scope="session")
def event_loop_policy():
    import asyncio

    return asyncio.get_event_loop_policy()


@pytest.fixture(scope="session")
def event_loop(event_loop_policy):
    loop = event_loop_policy.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def setup_test_schema():
    try:
        async with create_pool(db_dsn) as pool:
            async with pool.acquire() as conn:
                async with conn.cursor() as cur:
                    await cur.execute(f"CREATE SCHEMA IF NOT EXISTS {TEST_SCHEMA}")
                    await cur.execute(f"SET search_path TO {TEST_SCHEMA}, public")
                    await cur.execute(
                        "SELECT schema_name FROM information_schema.schemata WHERE schema_name = %s",
                        (TEST_SCHEMA,),
                    )
                    result = await cur.fetchone()
    except Exception as e:
        logger.error(f"Error in schema creation: {e}")
        raise

    try:
        from piccolo_conf_test import DB

        await run_forwards("all")
        yield TEST_SCHEMA
    finally:
        try:
            async with create_pool(db_dsn) as pool:
                async with pool.acquire() as conn:
                    async with conn.cursor() as cur:
                        await cur.execute(
                            f"DROP SCHEMA IF EXISTS {TEST_SCHEMA} CASCADE"
                        )
        except Exception as e:
            logger.error(f"Error during teardown: {e}")


@pytest_asyncio.fixture(scope="function")
async def setup_test_db(setup_test_schema):
    yield db_dsn


@pytest_asyncio.fixture(scope="function")
async def nebula_db_pool(setup_test_schema):
    try:
        pool = await create_pool(db_dsn)
        async with pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute(f"SET search_path TO {TEST_SCHEMA}, public")
        yield pool
    except Exception as e:
        logger.error(f"NEBULA_DB_POOL: Exception occurred: {e}")
        raise
    finally:
        if "pool" in locals():
            pool.close()
            await pool.wait_closed()


@pytest.fixture
def ended_session():
    session = SharedSessionDTO.from_json(
        """
        {
            "about": "",
            "avatar": null,
            "branchURL": "https://rumiaidev.app.link/45e6HUNCERb",
            "cover": null,
            "createTimestamp": 1741710722,
            "endTimestamp": 1741710845,
            "featured": false,
            "hasPastRecordings": false,
            "hosts": [],
            "isLiveStreamEnabled": false,
            "isRecordingProcessed": false,
            "ogMetadata": {},
            "primaryHostUserID": "3",
            "publicURL": "https://develop.rumi.ai/sessions/1",
            "isViewerAccessRestricted": true,
            "dataVisibility": "participant-only",
            "sessionCreatorUserID": "3",
            "sessionID": "1",
            "sessionRecurrenceID": "2",
            "sessionSettings": {
                "aiFeedVisibility": "all",
                "enableRecording": false,
                "postSessionSummaries": {
                    "recipients": {
                        "email": "everyone"
                    }
                },
                "preferredLanguages": [
                    "en-US"
                ],
                "recurring": 0,
                "summaAI": true
            },
            "sessionState": "ended",
            "sessionTags": null,
            "sessionTitle": "Test session",
            "startTimestamp": 1741710760,
            "stateUpdatedAt": [
                {
                    "state": "scheduled",
                    "updatedAt": 1741710722
                },
                {
                    "state": "active",
                    "updatedAt": 1741710760
                },
                {
                    "state": "ended",
                    "updatedAt": 1741710845
                }
            ],
            "updatedAt": 1741710845,
            "rowversion": 4,
            "accessStatus": "locked",
            "meetingType": "party-mode",
            "userMeetingType": {
                "description": "Company-wide gatherings for sharing high-level updates, recognizing achievements, and reinforcing company culture.",
                "id": "929724948262422490",
                "promptTemplate": "",
                "title": "all_hands"
            }
        }
        """
    )
    return session


@pytest.fixture
def session_user():
    user = ApiSessionUserDTO.from_json(
        """
        {
            "sessionUserID": "1237132806325274364",
            "joined": true,
            "isViewerAccessRevoked": false,
            "roleIDs": [
                "host",
                "session_producer",
                "viewer"
            ],
            "user": {
                "userID": "933286024707048692",
                "email": "<EMAIL>",
                "firstName": "Manuel",
                "lastName": "Maldonado"
            }
        }
        """
    )
    return user


@pytest.fixture
def guest_session_user():
    guest = ApiSessionUserDTO.from_json(
        """
        {
            "sessionUserID": "1237716020227999534",
            "joined": true,
            "isViewerAccessRevoked": false,
            "roleIDs": [
                "viewer",
                "guest"
            ],
            "guest": {
                "surrogateID": "1237716017266820908",
                "fullName": "Manny",
                "email": "<EMAIL>"
            }
        }
        """
    )
    return guest
