package meetings

import (
	"context"
	"crypto/tls"
	"net/http"
	"strings"
	"time"

	"encore.app/pkg/featureflags"
	"encore.app/pkg/notify"
	"encore.app/pkg/postmark"
	"encore.app/pkg/preview"
	"encore.app/shared"
	encore "encore.dev"
	"encore.dev/rlog"

	nebula_api "encore.app/wormhole/services/nebula-api"

	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl/scram"

	config2 "encore.app/meetings/config"
	"encore.app/meetings/contracts"
	"encore.app/meetings/database"
	"encore.app/meetings/services"
	"encore.dev/config"
	"encore.dev/storage/sqldb"
	"github.com/jmoiron/sqlx"
)

var secrets struct {
	NebulaUser          string
	NebulaPassword      string
	KafkaBrokers        string
	KafkaPassword       string
	KafkaUsername       string
	BranchKey           string
	BranchSecret        string
	ElioUser            string
	ElioPassword        string
	PostmarkServerToken string
}

// encore:service
type Meetings struct {
	MeetingType   contracts.MeetingTypeService
	BranchLink    contracts.BranchLinkService
	KafkaProducer contracts.KafkaService
	Nebula        contracts.NebulaService
	Notify        notify.Client
}

var namedMarsDB = sqldb.NewDatabase("mars", sqldb.DatabaseConfig{
	Migrations: "./migrations",
})

var MarsDB = sqlx.NewDb(namedMarsDB.Stdlib(), "pgx")

var Config = config.Load[*config2.MeetingsConfig]()

func initMeetings() (*Meetings, error) {
	meetings := &Meetings{
		MeetingType: &services.MeetingTypeService{},
		BranchLink: &services.BranchLinkService{
			Client: &http.Client{
				Timeout: 5 * time.Second,
			},
			APIKey: secrets.BranchKey,
			Secret: secrets.BranchSecret,
		},
		KafkaProducer: &services.KafkaService{},
		Nebula: &services.NebulaService{
			NebulaClient: nebula_api.CreateNebulaClient(Config.Nebula.HostUrl(), secrets.NebulaUser, secrets.NebulaPassword),
		},
	}
	var err error
	if meetings.Notify, err = notify.NewNotifyClient(postmark.NewPostmarkClient(secrets.PostmarkServerToken)); err != nil {
		return nil, err
	}
	return meetings, meetings.Initialize()
}

func (meetings *Meetings) initKafka() {
	if Config.Kafka.TopicSessionsUpdates() == "" {
		rlog.Info("Kafka Topic Sessions Updates Disabled")
		return
	}
	// env := encore.Meta().Environment.Name
	brokers := strings.Split(secrets.KafkaBrokers, ",")
	// groupID := fmt.Sprintf("%s-%s", "hubble", env)
	isTlsEnabled := Config.Kafka.TLS()
	sasl := Config.Kafka.SASL()

	dialer := &kafka.Dialer{
		Timeout:   10 * time.Second,
		DualStack: true,
	}

	if isTlsEnabled {
		dialer.TLS = &tls.Config{}
	}

	if sasl {
		username := secrets.KafkaUsername
		password := secrets.KafkaPassword
		dialer.SASLMechanism = nil
		if username != "" || password != "" {
			mechanism, err := scram.Mechanism(scram.SHA512, username, password)
			if err != nil {
				panic(err)
			}

			dialer.SASLMechanism = mechanism
		}
	}

	topicSessionsUpdates := Config.Kafka.TopicSessionsUpdates()
	prid := shared.FormatInt(preview.ExtractPRFromURL(encore.Meta().APIBaseURL.Host))
	topicSessionsUpdates = strings.ReplaceAll(topicSessionsUpdates, ":prid", prid)

	writer := kafka.NewWriter(kafka.WriterConfig{
		Brokers:      brokers,
		Topic:        topicSessionsUpdates,
		Dialer:       dialer,
		RequiredAcks: 1,
		Async:        false,
	})
	meetings.KafkaProducer = &services.KafkaService{Kafka: writer}
}

func (meetings *Meetings) Initialize() error {
	var err error
	HasOverrideConfig := Config.MarsPostgresOverride() != ""
	if HasOverrideConfig {
		if MarsDB, err = sqlx.Connect("pgx", Config.MarsPostgresOverride()); err != nil {
			return err
		}
	}

	ctx := context.Background()
	if err = database.Statements.ValidateAll(ctx, MarsDB, "mars"); err != nil {
		return err
	}
	meetings.initKafka()
	return nil
}

// Shutdown gracefully closes resources during service shutdown
func (meetings *Meetings) Shutdown(force context.Context) {
	// Close PostHog feature flags client to flush buffered events
	featureflags.Close()
}
