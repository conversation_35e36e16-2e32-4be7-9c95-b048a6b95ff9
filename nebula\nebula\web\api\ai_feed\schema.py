import json
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel

from nebula.db.models.ai_feed import AIFeed
from nebula.services.ai_feed.models import (
    UserBasic,
    PostAIFeedActionItemsRequestDTO,
    PostAIFeedCatchupSummaryRequestDTO,
    PostAIFeedInsightsRequestDTO,
    patch_ai_feed_json_fields,
)


class LabeledURL(BaseModel):
    label: str
    url: str


class LinkPrivate(BaseModel):
    """Link schema, connects to external providers like Linear, Asana..."""

    """ For the owner of this Link """

    # Name of the Provider eg asana
    providerName: str

    # Name of the Integration eg asana-demo-integration
    integrationName: str

    providerIcon: str

    providerIconBackgroundColor: Optional[str]

    providerCategory: str

    providerCategoryLabel: str

    # Name of the Action on Nango that sent the data to the provider
    actionName: str

    message: Optional[str]

    link: Optional[LabeledURL]


class Link(BaseModel):
    """Link schema, connects to external providers like Linear, Asana..."""

    """ For public links """
    owner: Optional[UserBasic]

    # Owner who should see the private object or not
    ownerId: Optional[str]

    createdAt: str

    private: Optional[LinkPrivate]

    message: Optional[str]

    link: Optional[LabeledURL]


class AIFeedSlackBlockUIMessage(BaseModel):
    """Slack Blocks UI message for AI Feed."""

    # optional because "loading-summary" is not going to have an id, some future msgs too
    id: Optional[int]

    # type of the AI feed message (action item, catchup summary, insight)
    text: str

    links: Optional[List[Link]]

    class Block(BaseModel):
        """Slack Block Schema."""

        class BlockText(BaseModel):
            """Slack Block Text Schema."""

            type: str
            text: str

        # actual text of the message
        text: BlockText

        class BlockType(str, Enum):  # noqa: WPS600
            """Slack Block Type."""

            section = "section"
            divider = "divider"

        type: BlockType

    blocks: Optional[List[Block]]

    class Timestamp(BaseModel):
        """Slack Block Timestamp Schema."""

        start: str
        end: Optional[str]

    ts: Optional[Timestamp]
    context: Optional[str]

    class Completed(BaseModel):
        value: bool
        user: UserBasic

    class Deleted(BaseModel):
        value: bool
        user: UserBasic
        why: Optional[str] = None

    class Edited(BaseModel):
        value: str
        user: UserBasic

    completed: Optional[Completed]
    edited: Optional[Edited]
    deleted: Optional[Deleted]

    def to_json(self) -> str:
        """Convert to json.

        :return: json string
        """
        return json.dumps(
            self,
            default=lambda o: o.__dict__,  # noqa: WPS111
            sort_keys=True,
            indent=4,
        )


class GetAIFeedPaginatedResponseDTO(BaseModel):
    """Get AI Feed Paginated Response DTO."""

    message: str

    class Data(BaseModel):
        """Get AI Feed Paginated Response DTO Data."""

        totalSummaryCount: int
        messages: List[AIFeedSlackBlockUIMessage]

    data: Data


class PostAIFeedActionItemsResponseDTO(BaseModel):
    """Post AI Feed Action Items Response DTO."""

    message: str


class EditAIFeedResponseDTO(BaseModel):
    """Edit AI Feed Response DTO."""

    message: str
    data: AIFeedSlackBlockUIMessage


class EditAIFeedRequestBodyDTO(BaseModel):
    """Edit AI Feed Request Body DTO."""

    class ActionType(str, Enum):  # noqa: WPS600
        """Edit AI Feed Dynamic Request ActionType"""

        completed = "complete"
        edited = "edit"
        deleted = "delete"

    sessionID: str
    sessionRecurrenceID: str
    action: ActionType
    # bool in case action is "complete" or "delete", str in case of "edited"
    value: bool | str
    why: Optional[str] = None


class GetAIFeedResponseDTO(BaseModel):
    """Get AI Feed Response DTO."""

    message: str
    data: AIFeedSlackBlockUIMessage


class PostAIFeedLinkDTO(BaseModel):
    """Post AI Feed Link DTO."""

    label: str
    url: str


class PostAIFeedLinkRequestBodyDTO(BaseModel):
    """Post AI Feed Link Request DTO."""

    # Who created this
    ownerId: str
    # Name of the Provider eg asana
    providerName: str
    # Name of the Integration eg asana-demo-integration
    integrationName: str
    # Name of the Action on Nango that sent the data to the provider
    actionName: str
    # A Private URL and label that links to the newly created item on the provider's domain
    # should only be seen by owner
    privateLink: Optional[PostAIFeedLinkDTO] = None
    # A Public URL and label that links to the newly created item on the provider's domain
    # should be seen by everyone
    publicLink: Optional[PostAIFeedLinkDTO] = None
    # A Private message that links to the newly created item on the provider's domain
    # should only be seen by owner
    privateMessage: Optional[str] = None
    # A Public message that links to the newly created item on the provider's domain
    # should be seen by everyone
    publicMessage: Optional[str] = None

    # Icon url of the provider
    providerIcon: str
    providerIconBackgroundColor: Optional[str] = None

    # Category of the provider
    providerCategory: str
    providerCategoryLabel: str


class CopyAIFeedsRequestDTO(BaseModel):
    srcRecurrenceID: str
    destSessionID: str
    destRecurrenceID: str


class GetAIFeedCatchupSummaryDTO(BaseModel):
    """Post AI Feed Action Items Request DTO."""

    sessionID: str
    sessionRecurrenceID: str
    batchID: str
    lang: Optional[str]

    class Metadata(BaseModel):
        """Batch metadata schema."""

        firstTranscriptionID: int
        firstTimeUnix: int
        lastTranscriptionID: int
        lastTimeUnix: int
        batchDuration: int

    batchMeta: Metadata

    summary: str
    context: Optional[str]

    def to_json(self) -> str:
        """Convert to json.

        :return: json string
        """
        return json.dumps(
            self,
            default=lambda o: o.__dict__,  # noqa: WPS111
            sort_keys=True,
            indent=4,
        )


class PostAIFeedCatchupSummaryResponseDTO(BaseModel):
    """Post AI Feed Catchup Summary Response DTO."""

    message: str


class PostAIFeedInsightsResponseDTO(BaseModel):
    """Post AI Feed Insights Response DTO."""

    message: str


class GetCatchupSummariesPaginatedResponseDTO(BaseModel):
    """Get Catchup Summaries Paginated Response DTO."""

    message: str

    class Data(BaseModel):
        """Get Catchup Summaries Paginated Response DTO Data."""

        totalCount: int
        summaries: List[GetAIFeedCatchupSummaryDTO]

    data: Data


class CopyAIFeedsResponseDTO(BaseModel):
    """Copy AI Feeds Response DTO."""

    message: str

    class Data(BaseModel):
        """Copy AI Feeds DTO Data."""

        messages: List[AIFeedSlackBlockUIMessage]

    data: Data


# patch_ai_feed_json_fields is now imported from nebula.services.ai_feed.models


def convert_ai_feed_msgs_into_slack_block_ui(
    msgs: List[AIFeed],
) -> list[AIFeedSlackBlockUIMessage]:
    """Convert AI Feed msgs into Slack Block UI.

    :param msgs: msgs
    :return: list[AIFeedSlackBlockUIMessage]
    """
    slack_ui_msgs: list[AIFeedSlackBlockUIMessage] = []

    for msg in msgs:
        blocks: list[AIFeedSlackBlockUIMessage.Block] = [
            AIFeedSlackBlockUIMessage.Block(
                text=AIFeedSlackBlockUIMessage.Block.BlockText(
                    type="mrkdwn",
                    text=msg["msg"],
                ),
                type=AIFeedSlackBlockUIMessage.Block.BlockType.section,
            ),
        ]
        if msg["msg_type"] == AIFeed.AIFeedMsgType.ActionItem:
            blocks.append(
                AIFeedSlackBlockUIMessage.Block(
                    text=AIFeedSlackBlockUIMessage.Block.BlockText(
                        type="mrkdwn",
                        text=f"Assignee: {msg['msg_dynamic_fields']['assignee']}",  # noqa: WPS237 E501
                    ),
                    type=AIFeedSlackBlockUIMessage.Block.BlockType.section,
                ),
            )

        # TODO: We should instead try to use dict.get() for this case:
        try:
            ctx = msg["msg_dynamic_fields"]["context"]
        except (KeyError, TypeError):
            ctx = None

        try:
            completed = msg["msg_dynamic_fields"]["completed"]
        except (KeyError, TypeError):
            completed = None

        try:
            edited = msg["msg_dynamic_fields"]["edited"]
        except (KeyError, TypeError):
            edited = None

        try:
            deleted = msg["msg_dynamic_fields"]["deleted"]
        except (KeyError, TypeError):
            deleted = None

        def link_fields(link):
            ownerId = None
            try:
                ownerId = link["owner_id"]
            except (KeyError, TypeError):
                pass

            providerIcon = None
            try:
                providerIcon = link["provider_icon"]
            except (KeyError, TypeError):
                pass

            providerIconBackgroundColor = None
            try:
                providerIconBackgroundColor = link["provider_icon_background_color"]
            except (KeyError, TypeError):
                pass

            return Link(
                owner=link.get("owner"),
                ownerId=ownerId,
                createdAt=link["created_at"],
                private=LinkPrivate(
                    integrationName=link["integration_name"],
                    providerIcon=providerIcon,
                    providerIconBackgroundColor=providerIconBackgroundColor,
                    providerCategory=link["provider_category"],
                    providerCategoryLabel=link["provider_category_label"],
                    providerName=link["provider_name"],
                    actionName=link["action_name"],
                    message=link["private_message"],
                    link=None
                    if link.get("private_link_label") is None
                    else {
                        "label": link["private_link_label"],
                        "url": link["private_link_url"],
                    },
                ),
                message=link["public_message"],
                link=None
                if link.get("public_link_label") is None
                else {
                    "label": link["public_link_label"],
                    "url": link["public_link_url"],
                },
            )

        slack_msg = AIFeedSlackBlockUIMessage(
            id=msg["id"],
            text=msg["msg_type"],
            links=list(map(link_fields, msg["links"])) if msg["links"] else None,
            blocks=blocks,
            ts=AIFeedSlackBlockUIMessage.Timestamp(
                start=str(msg["start_transcription_time"]),
                end=str(msg["end_transcription_time"]),
            ),
            context=ctx,
            completed=completed,
            edited=edited,
            deleted=deleted,
        )
        slack_ui_msgs.append(slack_msg)

    return slack_ui_msgs


def convert_ai_feed_for_user_id(
    ai_feeds: List[AIFeed],
    user_id: str | None,
) -> list[AIFeedSlackBlockUIMessage]:
    """Convert AI Feed msgs into Slack Block UI but filter the links based on the passed user ID

    :param msgs: msgs
    :param user_id: user_id
    :return: list[AIFeedSlackBlockUIMessage]
    """
    msgs = convert_ai_feed_msgs_into_slack_block_ui(ai_feeds)

    # filter links that the user doesn't own
    def hide_private_link_fields(link: Link):
        if link.ownerId != user_id:
            link.private = None
        return link

    def filter_messages(msg: AIFeedSlackBlockUIMessage):
        if msg.links is not None:
            msg.links = list(map(hide_private_link_fields, msg.links))
        return msg

    return list(map(filter_messages, msgs))


def convert_ai_feed_msgs_into_catchup_summary_dtos(
    ai_feed_msgs: list[AIFeed],
) -> list[GetAIFeedCatchupSummaryDTO]:
    """
    Use to convert ai feed messages as they come from db to a list of catchup summary DTOs.
    :param ai_feed_msgs: List of AI feed obj instances
    :return: List of GetAIFeedCatchupSummaryDTO objs
    """

    catchup_summary_dtos: list[GetAIFeedCatchupSummaryDTO] = []

    for ai_feed in ai_feed_msgs:
        dynamic_fields = (
            json.loads(ai_feed.msg_dynamic_fields)
            if ai_feed.msg_dynamic_fields
            else None
        )

        ctx = dynamic_fields["context"] if dynamic_fields else None

        catchup_summary_dtos.append(
            GetAIFeedCatchupSummaryDTO(
                sessionID=ai_feed.session_id,
                sessionRecurrenceID=ai_feed.session_recurrence_id,
                batchID=ai_feed.transcription_batch_id,
                lang=ai_feed.lang,
                batchMeta=GetAIFeedCatchupSummaryDTO.Metadata(
                    firstTranscriptionID=ai_feed.start_transcription_id,
                    firstTimeUnix=ai_feed.start_transcription_time,
                    lastTranscriptionID=ai_feed.end_transcription_id,
                    lastTimeUnix=ai_feed.end_transcription_time,
                    batchDuration=ai_feed.duration,
                ),
                summary=ai_feed.msg,
                context=ctx,
            ),
        )

    return catchup_summary_dtos


def generate_slack_block_ui_message_for_loading() -> AIFeedSlackBlockUIMessage:
    """Generate block for loading.

    :return: AIFeedSlackBlockUIMessage
    """
    return AIFeedSlackBlockUIMessage(
        text="loading-summary",
    )
