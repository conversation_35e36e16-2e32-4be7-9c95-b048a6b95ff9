package meetings

import (
	"context"
	"encore.app/hubble"
	hubble_api "encore.app/hubble/api"
	"encore.app/meetings/api"
	"encore.app/shared"
)

//encore:api public path=/v1.0/sessions/guest method=POST
func (meetings *Meetings) LoginGuestUserWithSession(ctx context.Context, req *api.LoginSessionGuestUserRequest) (*api.LoginSessionGuestUserResponse, error) {
	session, err := GetSessionByID(ctx, &api.GetSessionByIDRequest{SessionID: req.SessionID})

	if err != nil {
		return nil, err
	}

	guestResponse, err := hubble.LoginGuestUser(ctx, &hubble_api.LoginGuestUserRequest{
		Email:    req.Email,
		FullName: req.FullName,
	})

	if err != nil {
		return nil, err
	}

	// Create access request/rule
	response := &api.LoginSessionGuestUserResponse{}
	guestDTO := guestResponse.Data.Guest
	if createFrictionlessAccessRules(ctx, shared.FormatInt(guestDTO.SurrogateID), session.Session, true, &guestDTO) != nil {
		response.Code = shared.GuestUserCreatedWithGrantedAccess
		if session.Session.AccessStatus == shared.SessionDTOAccessStatusLocked {
			response.Code = shared.GuestUserCreatedWithInReviewRequest
		}
	} else {
		response.Code = shared.GuestUSerCreatedWithoutAccessRequest
	}
	response.Message = shared.ErrorMessages[response.Code]
	response.Data.AuthToken = guestResponse.Data.AuthToken
	response.Data.RefreshToken = guestResponse.Data.RefreshToken
	return response, nil
}
