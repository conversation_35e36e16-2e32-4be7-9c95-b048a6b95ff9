package api

import (
	"encoding/json"
	"encore.app/shared"
	"fmt"
	"github.com/samber/lo"
)

type GetMyUserResponse struct {
	Data struct {
		User shared.UserDTO `json:"user"`
	} `json:"data"`
}

type CreateUserRequest struct {
	UserEmail        string   `json:"userEmail" validate:"required,email"`
	UserFirstName    string   `json:"userFirstName" validate:"required"`
	UserLastName     string   `json:"userLastName" validate:"required"`
	RoleIDs          []string `json:"roleIDs" validate:"required"`
	Fingerprint      *string  `json:"fingerprint" encore:"optional"`
	Timezone         *string  `json:"timezone" encore:"optional"`
	Avatar           *string  `json:"avatar" encore:"optional" validate:"omitempty,http_url"`
	About            *string  `json:"about" encore:"optional" validate:"omitempty,max=5000"`
	MarketingOptIn   *bool    `json:"marketingOptIn" encore:"optional"`
	PaddleCustomerID *string  `json:"paddleCustomerID" encore:"optional"`
}

func (request CreateUserRequest) Validate() error {
	err := shared.Validate.Struct(request)
	if err != nil {
		return err
	}

	return nil
}

type CreateUserResponse struct {
	UserID string `json:"userID"`
}

type ListUsersRequest struct {
	Limit          int    `query:"limit" encore:"optional" validate:"omitempty,min=0,max=500"`
	Offset         int    `query:"offset" encore:"optional" validate:"omitempty,min=0"`
	OrderBy        string `query:"orderBy" encore:"optional"`
	OrderDirection string `query:"orderDirection" encore:"optional" validate:"omitempty,oneof=ASC DESC"`
	Email          string `query:"email" encore:"optional" validate:"omitempty"`
}

func (request ListUsersRequest) ParsePaginationParams() (*shared.OffsetPaginationParams, error) {
	limit, _ := lo.Coalesce(request.Limit, 20)
	offset, _ := lo.Coalesce(request.Offset, 0)
	orderBy, _ := lo.Coalesce(request.OrderBy, "id")
	orderDirection, _ := lo.Coalesce(request.OrderDirection, "DESC")

	return &shared.OffsetPaginationParams{
		Limit:          limit,
		Offset:         offset,
		OrderBy:        orderBy,
		OrderDirection: orderDirection,
	}, nil
}

func (request ListUsersRequest) Validate() error {
	wrapErr := func(err error) error {
		return fmt.Errorf("failed to validate list users request: %w", err)
	}

	err := shared.Validate.Struct(request)
	if err != nil {
		return wrapErr(err)
	}

	return nil
}

type ListUsersResponse struct {
	OffsetPaginationResponse shared.OffsetPaginationResponse `json:"offsetPagination"`
	Users                    []shared.UserDTO                `json:"users"`
}

type GetUserPlanByUserIDResponse struct {
	Success bool                           `json:"success"`
	Message string                         `json:"message"`
	Data    shared.UserSubscriptionPlanDTO `json:"data"`
}

type GetUserPaymentMethodDetailsResponseData struct {
	LastPayment             *shared.BillingPaymentResultDTO       `json:"lastPayment"`
	PaymentMethodDetailsDTO shared.BillingPaymentMethodDetailsDTO `json:"-"` // won't be included in JSON
}

// MarshalJSON implements custom JSON marshaling
func (g GetUserPaymentMethodDetailsResponseData) MarshalJSON() ([]byte, error) {
	type Alias GetUserPaymentMethodDetailsResponseData
	return json.Marshal(struct {
		*Alias
		Type string              `json:"type"`
		Card *shared.BillingCard `json:"card"`
	}{
		Alias: (*Alias)(&g),
		Type:  g.PaymentMethodDetailsDTO.Type,
		Card:  g.PaymentMethodDetailsDTO.Card,
	})
}

func (g *GetUserPaymentMethodDetailsResponseData) UnmarshalJSON(data []byte) error {
	type Alias GetUserPaymentMethodDetailsResponseData
	aux := struct {
		*Alias
		Type string              `json:"type"`
		Card *shared.BillingCard `json:"card,omitempty"`
	}{
		Alias: (*Alias)(g),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	g.PaymentMethodDetailsDTO.Type = aux.Type
	g.PaymentMethodDetailsDTO.Card = aux.Card

	return nil
}

type GetUserPaymentMethodDetailsResponse struct {
	Success bool                                    `json:"success"`
	Message string                                  `json:"message"`
	Data    GetUserPaymentMethodDetailsResponseData `json:"data"`
}

type ListUserTransactionsRequest struct {
	Limit           int    `query:"limit" encore:"optional" validate:"omitempty,min=0,max=500"`
	Cursor          string `query:"cursor" encore:"optional" validate:"omitempty,max=100"`
	IncludeInvoices bool   `query:"includeInvoices" encore:"optional" validate:"omitempty"`
}

func (request ListUserTransactionsRequest) Validate() error {
	err := shared.Validate.Struct(request)
	if err != nil {
		return err
	}

	return nil
}

type ListUserTransactionsResponse struct {
	Success bool                               `json:"success"`
	Message string                             `json:"message"`
	Data    []ListUserTransactionsResponseData `json:"data"`
}

type ListUserTransactionsResponseData struct {
	Transaction shared.BillingTransactionDTO `json:"transaction"`
	InvoiceURL  string                       `json:"invoiceUrl"`
}

type GetUpdateUserPaymentMethodTransactionResponse struct {
	Success bool                         `json:"success"`
	Message string                       `json:"message"`
	Data    shared.BillingTransactionDTO `json:"data"`
}

type UpdateUserRequestData struct {
	// Empty string represents a nil value
	Avatar     *string                 `json:"avatar,omitempty" encore:"optional"`
	Onboarding *shared.OnboardingFlags `json:"onboarding,omitempty" encore:"optional"`
	FirstName  *string                 `json:"firstName,omitempty" encore:"optional"`
	LastName   *string                 `json:"lastName,omitempty" encore:"optional"`
	About      *string                 `json:"about,omitempty" encore:"optional"`
	Social     *shared.Socials         `json:"social,omitempty" encore:"optional"`
}

type UpdateUserRequest struct {
	UserData UpdateUserRequestData `json:"userData"`
}

type UpdateUserResponse struct {
	Data struct {
		User shared.UserDTO `json:"user"`
	} `json:"data"`
}
