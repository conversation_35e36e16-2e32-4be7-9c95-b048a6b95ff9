from enum import Enum

from piccolo.columns import (
    BigSerial,
    Text,
    Timestamp,
    Boolean,
    BigInt,
    JSONB,
    Timestamptz,
)
from piccolo.columns.column_types import ForeignKey
from piccolo.columns.defaults.timestamp import TimestampNow
from piccolo.columns.indexes import IndexMethod
from piccolo.columns.reference import LazyTableReference
from piccolo.table import Table


class XRay(Table, tablename="xrays"):
    """
    Represents an X-Ray - user's intent to capture information from meetings.

    There are three types of X-Rays:
    - Build: Original xray type - edits and updates content - "fusion"
    - Monitor: Tracks specific facts/events, only appends updates to the document. The "document" itself is not shown on the frontend, only notifications
    - Digest: Provides periodic summaries

    Users can create an X-Ray from scratch, use a <PERSON>umi suggested template (generic ones), or receive one from someone else - a team member or <PERSON><PERSON>'s GTM team.
    """

    id = BigSerial(primary_key=True)
    owner_id = BigInt(null=False)

    current_commit_id = ForeignKey(
        LazyTableReference(
            table_class_name="XRayDocumentCommit",
            app_name="nebula",
        ),
        null=True,
    )

    title = Text(null=False)  # LLM generated initially, editable by the user
    description = Text(null=False)  # User describes what the X-Ray is about

    prompt = Text(null=False)  # LLM generated from user's xray description
    icon = Text(null=False)  # LLM generated
    short_summary = Text(
        null=True
    )  # LLM generated, taking from title, description and prompt into account
    alert_channels = JSONB(null=False, default={"notifyAuthorEmail": False})

    is_active = Boolean(null=False, default=True)
    created_at = Timestamp(default=TimestampNow())
    updated_at = Timestamp(default=TimestampNow())

    class Visibility(str, Enum):
        User = "user"
        Team = "team"

    class XRayType(str, Enum):
        Build = "build"  # Original xray type - edits and updates content - "fusion"
        Monitor = "monitor"  # Tracks specific facts/events, only appends updates to the document. The document itself is not shown on the frontend, only notifications
        Digest = "digest"  # Provides periodic summaries

    class Scope(str, Enum):
        Personal = "personal"  # User's own meetings only
        Team = "team"  # Team visble meetings only
        All = "all"  # team visible + personal

    # V2 field, not used yet
    visibility = Text(
        choices=Visibility,
        null=False,
        default=Visibility.User,
        index=True,
        index_method=IndexMethod.btree,
    )

    xray_type = Text(
        choices=XRayType,
        null=False,
        default=XRayType.Build,
        index=True,
        index_method=IndexMethod.btree,
    )

    # V1 does not allow the user to choose, so this X-Ray will include all meetings (personal + team visible)
    scope = Text(
        choices=Scope,
        null=False,
        default=Scope.All,
        index=True,
        index_method=IndexMethod.btree,
    )

    # Digest-specific fields
    timezone = Text(
        null=True,
        help_text="Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC'). Required for digest type X-Rays.",
    )

    last_digest_at = Timestamptz(
        null=True,
        index=True,
        index_method=IndexMethod.btree,
        help_text="Timestamp when the digest was last generated. Used for scheduling next digest.",
    )
    frequency = Text(
        null=True,
        help_text="Cron expression for digest scheduling",
    )
