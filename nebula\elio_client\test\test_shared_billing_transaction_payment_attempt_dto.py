# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.shared_billing_transaction_payment_attempt_dto import SharedBillingTransactionPaymentAttemptDTO

class TestSharedBillingTransactionPaymentAttemptDTO(unittest.TestCase):
    """SharedBillingTransactionPaymentAttemptDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SharedBillingTransactionPaymentAttemptDTO:
        """Test SharedBillingTransactionPaymentAttemptDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SharedBillingTransactionPaymentAttemptDTO`
        """
        model = SharedBillingTransactionPaymentAttemptDTO()
        if include_optional:
            return SharedBillingTransactionPaymentAttemptDTO(
                method_details = elio_client.models.shared/billing_payment_method_details_dto.shared.BillingPaymentMethodDetailsDTO(
                    card = elio_client.models.shared/billing_card.shared.BillingCard(), 
                    type = '', )
            )
        else:
            return SharedBillingTransactionPaymentAttemptDTO(
                method_details = elio_client.models.shared/billing_payment_method_details_dto.shared.BillingPaymentMethodDetailsDTO(
                    card = elio_client.models.shared/billing_card.shared.BillingCard(), 
                    type = '', ),
        )
        """

    def testSharedBillingTransactionPaymentAttemptDTO(self):
        """Test SharedBillingTransactionPaymentAttemptDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
