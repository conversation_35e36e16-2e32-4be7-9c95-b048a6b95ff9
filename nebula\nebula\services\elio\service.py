from typing import Literal, List

from elio_client import SharedSessionDTO, ApiSessionUserDTO
from elio_client.api_client import ApiClient
from elio_client.api.default_api import DefaultApi
from elio_client.configuration import Configuration
from elio_client.models.shared_transcription_dto import SharedTranscriptionDTO
from elio_client.models.post_meetings_get_user_by_id_request import (
    POSTMeetingsGetUserByIDRequest,
)
from elio_client.models.post_meetings_get_user_by_id200_response import (
    POSTMeetingsGetUserByID200Response,
)
import aiohttp
from aiohttp import BasicAuth, ClientSession
from loguru import logger


from elio_client.models.api_session_guest import ApiSessionGuest
from nebula.settings import settings
from elio_client.models.post_meetings_upsert_meeting_suggestions_request import (
    POSTMeetingsUpsertMeetingSuggestionsRequest,
)
from elio_client.models.api_meeting_suggestion_input import ApiMeetingSuggestionInput

AIFeedEvent = (
    Literal["working_started"]
    | Literal["working_ended"]
    | Literal["posting_started"]
    | Literal["posting_ended"]
    | Literal["editing_started"]
    | Literal["editing_ended"]
    | Literal["feed_item_ready"]
)

elio_client = DefaultApi(
    ApiClient(
        Configuration(host=settings.elio_base_url),
        header_name="Authorization",
        header_value=settings.elio_basic_auth_header,
    )
)


async def trigger_ai_feed_event(
    session_id: int, recurrence_id: int, ev_type: AIFeedEvent, ai_feed_id=0
):
    try:
        async with ClientSession() as session:
            url = f"{settings.elio_base_url}/v1.0/ai-feed/event"
            logger.info("triggering ai feed event in elio")
            response = await session.post(
                url,
                auth=BasicAuth(
                    settings.luxor_basic_auth_username,
                    settings.luxor_basic_auth_password,
                ),
                json={
                    "sessionID": str(session_id),
                    "sessionRecurrenceID": str(recurrence_id),
                    "eventType": ev_type,
                    "aiFeedID": ai_feed_id,
                },
            )
            # Explicitly check response status
            if response.status == 200:
                response_json = await response.json()
                logger.info(
                    "[trigger_ai_feed_event] success. status code {}. response: {}",
                    response.status,
                    response_json,
                )
                return response_json
            else:
                logger.error(
                    "[trigger_ai_feed_event] unexpected response status: {}. Body: {}",
                    response.status,
                    await response.text(),
                )
                response.raise_for_status()
    except aiohttp.ClientResponseError as e:
        logger.error(
            "[trigger_ai_feed_event] client response error: {} - Status code: {}",
            e.message,
            e.status,
        )
    except aiohttp.ClientConnectionError:
        logger.error("[trigger_ai_feed_event] failed to trigger ai feed event")
    except Exception as e:
        logger.error("[trigger_ai_feed_event] an unexpected error occurred: {}", str(e))


"""
    Combine all known transcription batches into a single dict of transcription objects
    :param session_id: The session ID
    :param recurrence_id: The recurrence ID
    :return: A list of transcriptions
"""


def get_concentrated_transcripts(
    session_id, recurrence_id
) -> list[SharedTranscriptionDTO]:
    batches = elio_client.g_et_transcriptions_get_batch_ids(session_id, recurrence_id)
    combined_transcript: list[SharedTranscriptionDTO] = list()
    for batch_id in batches.data.data:
        combined_transcript.extend(
            get_transactions_for_batch(batch_id, session_id, recurrence_id)
        )
    return combined_transcript


"""
    Get all transcriptions for specific batch
    :param batch_id: The batch ID
    :param session_id: The session ID
    :param recurrence_id: The recurrence ID
    :return: A list of transcriptions
"""


def get_transactions_for_batch(
    batch_id, session_id, recurrence_id
) -> list[SharedTranscriptionDTO]:
    response = elio_client.g_et_transcriptions_get_transcription_batch(
        batch_id, session_id, recurrence_id
    )
    return response.data.transcriptions


def get_session_by_id(session_id, recurrence_id) -> SharedSessionDTO:
    response = elio_client.g_et_meetings_get_session_by_id(session_id, recurrence_id)
    return response.session


def get_session_participants(
    session_id: str, recurrence_id: str
) -> list[ApiSessionUserDTO]:
    response = elio_client.g_et_meetings_get_session_users_by_session_id(
        session_id, recurrence_id
    )
    return response.session_users


def get_session_speakers_as_guests(session_id, recurrence_id) -> list[ApiSessionGuest]:
    response = (
        elio_client.g_et_transcriptions_get_speakers_as_session_guests_by_session_id(
            session_id, recurrence_id
        )
    )
    return [
        ApiSessionGuest(
            fullName=guest.full_name if guest.full_name is not None else "",
            email=guest.email if guest.email is not None else "",
            surrogateID=str(guest.surrogate_id),
        )
        for guest in response.guests
    ]


def get_user_by_id(user_id: str) -> POSTMeetingsGetUserByID200Response:
    """Get user information by user ID"""
    request = POSTMeetingsGetUserByIDRequest(userID=user_id)
    response = elio_client.p_ost_meetings_get_user_by_id(request)
    return response


def trigger_elio_meeting_metadata_ready(metadata) -> None:
    try:
        elio_client.p_ost_comms_meeting_metadata_ready(metadata)
    except Exception as e:
        logger.error(f"Failed to notify Elio about metadata ready: {str(e)}")
        raise


def trigger_elio_meeting_suggestions_upsert(
    session_id: str, recurrence_id: str, suggestions: List[ApiMeetingSuggestionInput]
):
    """Trigger Elio to store meeting suggestions"""
    try:
        logger.info(
            "Upserting meeting suggestions in Elio",
            session_id=session_id,
            recurrence_id=recurrence_id,
            count=len(suggestions),
        )

        request = POSTMeetingsUpsertMeetingSuggestionsRequest(
            sessionID=session_id, recurrenceID=recurrence_id, suggestions=suggestions
        )

        response = elio_client.p_ost_meetings_upsert_meeting_suggestions(request)

        logger.info(
            "[trigger_elio_meeting_suggestions_upsert] success. response: {}", response
        )
        return response
    except Exception as e:
        logger.error(
            "[trigger_elio_meeting_suggestions_upsert] failed to upsert meeting suggestions: {}",
            str(e),
        )
        raise
