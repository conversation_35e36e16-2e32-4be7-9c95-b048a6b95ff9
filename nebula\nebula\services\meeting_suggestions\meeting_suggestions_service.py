"""Service for generating meeting suggestions using LLM."""

import json

from nebula.clients.llms import mm_openai_client
from nebula.services.meeting_suggestions.models import (
    MeetingSuggestionsExtraction,
    MeetingSuggestionsGenerationData,
    AllUsersMeetingSuggestionsExtraction,
    UserMeetingSuggestionItem,
)
from nebula.services.meeting_suggestions.prompts import SMART_SUGGESTIONS_TEMPLATE, MULTI_USER_SUGGESTIONS_TEMPLATE
from nebula.settings import settings
from nebula.temporal.types import ExtractAllUsersMeetingSuggestionsInput


async def generate_meeting_suggestions_with_llm(
    generation_data: MeetingSuggestionsGenerationData,
) -> MeetingSuggestionsExtraction:
    """
    Generate meeting suggestions using LLM based on meeting context.
    
    Args:
        generation_data: Meeting data for generating suggestions
        
    Returns:
        MeetingSuggestionsExtraction containing the generated suggestions
    """
    # Format the prompt with meeting data
    formatted_prompt = SMART_SUGGESTIONS_TEMPLATE.format(
        title=generation_data.title,
        date=generation_data.date,
        user_name=generation_data.user_name,
        transcript=generation_data.transcript
    )
    
    # Call OpenAI API
    response = await mm_openai_client.chat.completions.create(
        model=settings.openai_rag_model,
        response_format={"type": "json_object"},
        messages=[{"role": "user", "content": formatted_prompt}],
        temperature=0.7,  # Slightly creative for varied suggestions
    )
    
    content = response.choices[0].message.content
    if not content:
        raise Exception("Failed to generate meeting suggestions - empty response from LLM")
    
    try:
        # Parse the JSON response
        suggestions_data = json.loads(content)
        
        # Validate the response structure
        if "suggestions" not in suggestions_data:
            raise ValueError("Response missing 'suggestions' field")
            
        suggestions_list = suggestions_data["suggestions"]
        if not isinstance(suggestions_list, list) or len(suggestions_list) != 3:
            raise ValueError(f"Expected exactly 3 suggestions, got {len(suggestions_list) if isinstance(suggestions_list, list) else 'non-list'}")
        
        # Create and return the extraction model
        return MeetingSuggestionsExtraction(suggestions=suggestions_list)
        
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse LLM response as JSON: {str(e)}")
    except Exception as e:
        raise Exception(f"Failed to process meeting suggestions: {str(e)}")


async def generate_multi_user_meeting_suggestions_with_llm(
    input: ExtractAllUsersMeetingSuggestionsInput
) -> AllUsersMeetingSuggestionsExtraction:
    """
    Generate meeting suggestions for all users in a single LLM call.
    
    Args:
        input: Contains meeting data and participants
        
    Returns:
        AllUsersMeetingSuggestionsExtraction with suggestions for all users
    """
    try:
        # Format participants list for prompt
        participants_str = ", ".join([f"{p.name} (ID: {p.user_id})" for p in input.participants])
        
        # Format the prompt
        formatted_prompt = MULTI_USER_SUGGESTIONS_TEMPLATE.format(
            title=input.title,
            date=input.date,
            participants=participants_str,
            transcript=input.transcript
        )
        
        # Call OpenAI API
        response = await mm_openai_client.chat.completions.create(
            model=settings.openai_rag_model,
            response_format={"type": "json_object"},
            messages=[{"role": "user", "content": formatted_prompt}],
            temperature=0.7,  # Slightly creative for varied suggestions
        )
        
        content = response.choices[0].message.content
        if not content:
            raise Exception("Failed to generate multi-user meeting suggestions - empty response from LLM")
        
        # Parse response
        response_data = json.loads(content)
        
        # Validate response structure
        if "user_suggestions" not in response_data:
            raise ValueError("Response missing 'user_suggestions' field")
        
        user_suggestions_list = response_data["user_suggestions"]
        if not isinstance(user_suggestions_list, list):
            raise ValueError("user_suggestions must be a list")
        
        # Convert to UserMeetingSuggestionItem objects
        user_suggestions = []
        for suggestion in user_suggestions_list:
            if not all(key in suggestion for key in ["user_id", "category", "prompt"]):
                raise ValueError(f"Invalid suggestion format: {suggestion}")
            
            # Skip suggestions with empty prompts
            prompt = suggestion["prompt"].strip() if suggestion["prompt"] else ""
            if not prompt:
                continue
            
            user_suggestions.append(UserMeetingSuggestionItem(
                user_id=suggestion["user_id"],
                category=suggestion["category"], 
                prompt=prompt
            ))
        
        return AllUsersMeetingSuggestionsExtraction(user_suggestions=user_suggestions)
        
    except json.JSONDecodeError as e:
        raise Exception(f"Failed to parse multi-user LLM response as JSON: {str(e)}")
    except Exception as e:
        raise Exception(f"Error generating multi-user suggestions: {str(e)}") 