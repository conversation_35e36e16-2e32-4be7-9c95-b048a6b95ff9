package comms

import (
	"context"
	"strings"
	"time"

	"encore.app/meetings"
	meetings_api "encore.app/meetings/api"
	"encore.app/pkg/featureflags"
	"encore.dev/rlog"
	"github.com/samber/lo"
)

// getPersonalizedSuggestions fetches meeting suggestions for a user if the feature flag is enabled
func getPersonalizedSuggestions(ctx context.Context, userID, sessionID, recurrenceID, email string) []string {
	// Check if meeting suggestions feature flag is enabled for this user
	if !featureflags.IsEnabled(featureflags.MeetingSuggestions, userID) {
		rlog.Debug("meeting suggestions feature flag disabled for user", "userID", userID, "email", email)
		return []string{}
	}

	var suggestions []string
	
	// Fetch personalized suggestions for this user with panic recovery
	func() {
		defer func() {
			if r := recover(); r != nil {
				rlog.Error("panic while fetching personalized suggestions", "panic", r, "userID", userID, "email", email)
				suggestions = []string{}
			}
		}()
		
		// Create a timeout context for suggestions fetching (max 5 seconds per user)
		suggestionsCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		
		suggestionsResp, err := meetings.GetMeetingSuggestionsByUserInternal(suggestionsCtx, &meetings_api.GetMeetingSuggestionsInternalRequest{
			SessionID:    sessionID,
			RecurrenceID: recurrenceID,
			UserID:       userID,
		})
		
		if err != nil {
			rlog.Warn("failed to fetch personalized meeting suggestions", "error", err, "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", userID, "email", email)
			suggestions = []string{}
		} else if suggestionsResp != nil && suggestionsResp.Data.Suggestions != nil {
			// Extract content from suggestion objects and filter out empty suggestions
			suggestions = lo.FilterMap(suggestionsResp.Data.Suggestions, func(suggestion meetings_api.SuggestionResponseDTO, _ int) (string, bool) {
				content := strings.TrimSpace(suggestion.Content)
				return content, content != ""
			})
			rlog.Debug("successfully fetched personalized meeting suggestions", "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", userID, "email", email, "count", len(suggestions))
		} else {
			suggestions = []string{}
		}
	}()
	
	return suggestions
} 