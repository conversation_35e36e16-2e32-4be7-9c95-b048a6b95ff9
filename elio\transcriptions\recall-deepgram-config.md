# Recall Deepgram Config

For some reason, the recall deepgram config is managed via sending a file to them in slack. This is important for us to have a record of the config in our repo. The token parameters are either in our vault or in the transciptions-vault.cue file. If we change the tokens or the destination url, we need to update the config in the slack channel.

## For develop environment:

```json
{
	"bot_name": "Rumi.ai Develop Notetaker",
	"transcription_options": {
		"provider": "deepgram",
		"deepgram": {
			"punctuate": false,
			"encoding": "linear16",
			"sample_rate": 96000,
			"model": "nova-3",
			"language": "en-US",
			"smart_format": true,
			"interim_results": false,
			"channels": 1,
			"vad_events": false,
			"endpointing": 5000,
			"filler_words": true,
			"replace": [
				"romy:Rumi",
				"Romy:Rumi",
				"roombi:<PERSON>umi",
				"Roombi:Rumi",
				"roomie:<PERSON><PERSON>",
				"Roomie:<PERSON><PERSON>",
				"roomie's:<PERSON><PERSON>'s",
				"Roomie's:<PERSON><PERSON>'s",
				"roomie.ai:Rumi.ai",
				"Roomie.ai:Rumi.ai",
				"roomy:<PERSON><PERSON>",
				"Roomy:<PERSON><PERSON>",
				"Roomi:<PERSON><PERSON>",
				"roomi:<PERSON><PERSON>",
				"trumy:Rumi",
				"Trumy:Rumi",
				"twitchroom:Waitroom",
				"Twitchroom:Waitroom",
				"waitrumb:Waitroom",
				"Waitrumb:Waitroom"
			]
		}
	},
	"real_time_transcription": {
		"destination_url": "https://api.develop.rumi.ai/preview.HandleRecallWebhook?token=CP79rfhTjf3KZOMJqCxPYhhD06BU8LMG",
		"enhanced_diarization": true
	},
	"google_meet": {
		"login_required": true,
		"google_login_group_id": "4d016b77-cff2-42a9-92eb-b22dbb819d4d"
	}
}
```

## For staging environment:

```json
{
	"bot_name": "Rumi.ai Staging Notetaker",
	"transcription_options": {
		"provider": "deepgram",
		"deepgram": {
			"punctuate": false,
			"encoding": "linear16",
			"sample_rate": 96000,
			"model": "nova-3",
			"language": "en-US",
			"smart_format": true,
			"interim_results": false,
			"channels": 1,
			"vad_events": false,
			"endpointing": 5000,
			"filler_words": true,
			"replace": [
				"romy:Rumi",
				"Romy:Rumi",
				"roombi:Rumi",
				"Roombi:Rumi",
				"roomie:Rumi",
				"Roomie:Rumi",
				"roomie's:Rumi's",
				"Roomie's:Rumi's",
				"roomie.ai:Rumi.ai",
				"Roomie.ai:Rumi.ai",
				"roomy:Rumi",
				"Roomy:Rumi",
				"Roomi:Rumi",
				"roomi:Rumi",
				"trumy:Rumi",
				"Trumy:Rumi",
				"twitchroom:Waitroom",
				"Twitchroom:Waitroom",
				"waitrumb:Waitroom",
				"Waitrumb:Waitroom"
			]
		}
	},
	"real_time_transcription": {
		"destination_url": "https://api.staging.rumi.ai/preview.HandleRecallWebhook?token=mgyQy7bhglInaMA2TYXiLMtJ73eobgJh",
		"enhanced_diarization": true
	},
	"google_meet": {
		"login_required": true,
		"google_login_group_id": "4c7187aa-23db-4c5f-9502-d4aaee626ef4"
	}
}
```

## For main environment:

```json
{
	"bot_name": "Rumi.ai Notetaker",
	"transcription_options": {
		"provider": "deepgram",
		"deepgram": {
			"punctuate": false,
			"encoding": "linear16",
			"sample_rate": 96000,
			"model": "nova-3",
			"language": "en-US",
			"smart_format": true,
			"interim_results": false,
			"channels": 1,
			"vad_events": false,
			"endpointing": 5000,
			"filler_words": true,
			"replace": [
				"romy:Rumi",
				"Romy:Rumi",
				"roombi:Rumi",
				"Roombi:Rumi",
				"roomie:Rumi",
				"Roomie:Rumi",
				"roomie's:Rumi's",
				"Roomie's:Rumi's",
				"roomie.ai:Rumi.ai",
				"Roomie.ai:Rumi.ai",
				"roomy:Rumi",
				"Roomy:Rumi",
				"Roomi:Rumi",
				"roomi:Rumi",
				"trumy:Rumi",
				"Trumy:Rumi",
				"twitchroom:Waitroom",
				"Twitchroom:Waitroom",
				"waitrumb:Waitroom",
				"Waitrumb:Waitroom"
			]
		}
	},
	"real_time_transcription": {
		"destination_url": "https://wormhole.prod.waitroom.com/v1.0/transcriptions/bot/webhook?token=8kYHF9PZgoZpBanXr3wDNjMMRVjIAFvq",
		"enhanced_diarization": true
	},
	"google_meet": {
		"login_required": true,
		"google_login_group_id": "861a9def-3c2f-45dc-85ff-8444be3d9987"
	}
}
```
