package config

import "encore.dev/config"

type Kafka struct {
	TLS                  config.Bool
	SASL                 config.Bool
	TopicLiveTranscripts config.String
}

type TranscriptionsConfig struct {
	FrontendURL             config.String
	DeepgramModel           config.String
	WebsocketBaseURL        config.String
	EndpointMS              config.String
	Vocabulary              config.Values[string]
	Kafka                   Kafka
	RecallAiWebhookToken    config.String
	RecallWebhookURL        config.String
	GoogleOAuth2ClientID    config.String
	GoogleLoginGroupId      config.String
	AWSRegion               config.String
	AWSBucketName           config.String
	MarsPostgresOverride    config.String
	MicrosoftOAuth2ClientID config.String
}
