# ApiMeetingSuggestionInput


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**category** | **str** |  | 
**prompt** | **str** |  | 
**user_id** | **str** |  | 

## Example

```python
from elio_client.models.api_meeting_suggestion_input import ApiMeetingSuggestionInput

# TODO update the JSON string below
json = "{}"
# create an instance of ApiMeetingSuggestionInput from a JSON string
api_meeting_suggestion_input_instance = ApiMeetingSuggestionInput.from_json(json)
# print the JSON string representation of the object
print(ApiMeetingSuggestionInput.to_json())

# convert the object into a dict
api_meeting_suggestion_input_dict = api_meeting_suggestion_input_instance.to_dict()
# create an instance of ApiMeetingSuggestionInput from a dict
api_meeting_suggestion_input_from_dict = ApiMeetingSuggestionInput.from_dict(api_meeting_suggestion_input_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


