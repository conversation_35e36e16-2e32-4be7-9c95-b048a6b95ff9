from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta
    from nebula.temporal.types import (
        MetadataWorkflowParams,
        BackFillMetadataWorkflowParams,
    )
    from nebula.temporal.workflows.metadata_wf import MetadataWorkflow

    from nebula.settings import settings

    from nebula.temporal.activities.recurrence_act import RecurrenceActivities


@workflow.defn(name="BackfillMetadataGeneration")
class BackfillMetadataWorkflow:
    """Backfill workflow for metadata generation from meeting transcripts"""

    @workflow.run
    async def run(self, params: BackFillMetadataWorkflowParams) -> None:
        """
        Run the backfill workflow.
        """

        workflow.logger.info("Starting backfill metadata gen workflow")

        recurrences = await workflow.execute_activity_method(
            RecurrenceActivities.get_past_ai_enabled_recurrences,
            arg=params.interval,
            start_to_close_timeout=timedelta(seconds=30),
        )

        workflow.logger.info(f"Found {len(recurrences)} recurrences")

        for recurrence in recurrences:
            await workflow.execute_child_workflow(
                MetadataWorkflow.run,
                arg=MetadataWorkflowParams(
                    session_id=str(recurrence["id"]),
                    recurrence_id=str(recurrence["recurrenceID"]),
                ),
                task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
            )
            workflow.logger.info(
                f"Completed metadata gen for recurrence {recurrence['recurrenceID']}"
            )
