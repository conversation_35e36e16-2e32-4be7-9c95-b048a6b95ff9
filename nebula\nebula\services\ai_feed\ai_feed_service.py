import asyncio
import json
from typing import Any, List, Optional

from loguru import logger
from piccolo.columns import Column
from piccolo.columns.combination import And

from nebula.db.models.ai_feed import AIFeed
from nebula.services.mars.api_service import (
    mars_get_users_batch,
)
from nebula.services.ai_feed.models import (
    PostAIFeedActionItemsRequestDTO,
    PostAIFeedCatchupSummaryRequestDTO,
    PostAIFeedInsightsRequestDTO,
    patch_ai_feed_json_fields,
)


def create_ai_feed(  # noqa: WPS211
    session_id: str,
    session_recurrence_id: str,
    transcription_batch_id: str,
    lang: Optional[str],
    msg_type: AIFeed.AIFeedMsgType,
    msg: str,
    msg_dynamic_fields: Optional[dict[Any, Any]],
    metadata_dynamic_fields: Optional[dict[Any, Any]],
    start_transcription_id: str,
    start_transcription_time: int,
    end_transcription_id: str,
    end_transcription_time: int,
    duration: int,
) -> AIFeed:
    """Create new summary message for ai feed.

    :param session_id: session id
    :param session_recurrence_id: session recurrence id
    :param transcription_batch_id: transcription batch id
    :param msg_type: msg type
    :param msg: msg
    :param lang: language
    :param msg_dynamic_fields: msg dynamic fields
    :param metadata_dynamic_fields: metadata dynamic fields
    :param start_transcription_id: start transcription id
    :param start_transcription_time: start transcription time
    :param end_transcription_id: end transcription id
    :param end_transcription_time: end transcription time
    :param duration: duration

    :return: AIFeed
    """
    return AIFeed(
        session_id=session_id,
        session_recurrence_id=session_recurrence_id,
        transcription_batch_id=transcription_batch_id,
        lang=lang if lang is not None else "en-US",
        msg_type=msg_type,
        msg=msg,
        msg_dynamic_fields=msg_dynamic_fields,
        metadata_dynamic_fields=metadata_dynamic_fields,
        start_transcription_id=start_transcription_id,
        start_transcription_time=start_transcription_time,
        end_transcription_id=end_transcription_id,
        end_transcription_time=end_transcription_time,
        duration=duration,
    )


async def create_new_action_items_msg_for_ai_feed(
    action_items_resp: PostAIFeedActionItemsRequestDTO,
) -> list[AIFeed]:
    """Creates new action items message for ai feed.

    :param action_items_resp: action items response from ray job
    :return: list[AIFeed]
    """
    action_items: list[AIFeed] = []
    for item in action_items_resp.actionItems:
        dynamic_fields = {
            "assignee": item.assignee,
        }
        if item.context:
            dynamic_fields["context"] = item.context

        action_items.append(
            create_ai_feed(
                session_id=action_items_resp.sessionID,
                session_recurrence_id=action_items_resp.sessionRecurrenceID,
                transcription_batch_id=action_items_resp.batchID,
                msg_type=AIFeed.AIFeedMsgType.ActionItem,
                lang=action_items_resp.lang,
                msg=item.actionItem,
                msg_dynamic_fields=dynamic_fields,
                metadata_dynamic_fields={
                    "SMARTCriteriaMet": item.SMARTCriteriaMet,
                },
                start_transcription_id=str(
                    action_items_resp.batchMeta.firstTranscriptionID,
                ),
                start_transcription_time=action_items_resp.batchMeta.firstTimeUnix,
                end_transcription_id=str(
                    action_items_resp.batchMeta.lastTranscriptionID,
                ),
                end_transcription_time=action_items_resp.batchMeta.lastTimeUnix,
                duration=action_items_resp.batchMeta.batchDuration,
            ),
        )

    # create new action items and update the job status to completed
    async with AIFeed._meta.db.transaction():  # noqa: WPS437
        await AIFeed.insert(*action_items)

    return action_items


async def create_new_summary_msg_for_ai_feed(
    summary_resp: PostAIFeedCatchupSummaryRequestDTO,
) -> AIFeed:
    """Creates new summary message for ai feed.

    :param summary_resp: summary response from ray job
    :return: AIFeed
    """
    dynamic_fields = (
        {
            "context": summary_resp.context,
        }
        if summary_resp.context
        else None
    )

    summary = create_ai_feed(
        session_id=summary_resp.sessionID,
        session_recurrence_id=summary_resp.sessionRecurrenceID,
        transcription_batch_id=summary_resp.batchID,
        msg_type=AIFeed.AIFeedMsgType.Summary,
        lang=summary_resp.lang,
        msg=summary_resp.summary,
        msg_dynamic_fields=dynamic_fields,
        metadata_dynamic_fields=None,
        start_transcription_id=str(
            summary_resp.batchMeta.firstTranscriptionID,
        ),
        start_transcription_time=summary_resp.batchMeta.firstTimeUnix,
        end_transcription_id=str(
            summary_resp.batchMeta.lastTranscriptionID,
        ),
        end_transcription_time=summary_resp.batchMeta.lastTimeUnix,
        duration=summary_resp.batchMeta.batchDuration,
    )

    # create new summary and update the job status to completed
    async with AIFeed._meta.db.transaction():  # noqa: WPS437
        await summary.save()

    return summary


async def create_new_insights_msg_for_ai_feed(
    insights_resp: PostAIFeedInsightsRequestDTO,
) -> list[AIFeed]:
    """Creates new action items message for ai feed.

    :param insights_resp: insights response from ray job
    :return: list[AIFeed]
    """
    insights: list[AIFeed] = []
    for item in insights_resp.insights:
        dynamic_fields = (
            {
                "context": item.context,
            }
            if item.context
            else None
        )

        insights.append(
            create_ai_feed(
                session_id=insights_resp.sessionID,
                session_recurrence_id=insights_resp.sessionRecurrenceID,
                transcription_batch_id=insights_resp.batchID,
                msg_type=AIFeed.AIFeedMsgType.Insight,
                lang=insights_resp.lang,
                msg=item.insight,
                msg_dynamic_fields=dynamic_fields,
                metadata_dynamic_fields=None,
                start_transcription_id=str(
                    insights_resp.batchMeta.firstTranscriptionID,
                ),
                start_transcription_time=insights_resp.batchMeta.firstTimeUnix,
                end_transcription_id=str(
                    insights_resp.batchMeta.lastTranscriptionID,
                ),
                end_transcription_time=insights_resp.batchMeta.lastTimeUnix,
                duration=insights_resp.batchMeta.batchDuration,
            ),
        )

    # create new insights and update the job status to completed
    async with AIFeed._meta.db.transaction():  # noqa: WPS437
        await AIFeed.insert(*insights)

    return insights


async def fetch_ai_feed_msgs_paginated(
    session_id: str,
    session_recurrence_id: str,
    limit: int = 100,
    skip: int = 0,
    sort: str = "desc",
) -> tuple[int, list[AIFeed]]:
    """Fetch AI Feed msgs paginated.

    :param session_id: session id
    :param session_recurrence_id: session recurrence id
    :param limit: limit
    :param skip: skip
    :param sort: sort

    :return: tuple[int, list[AIFeed]]
    """
    where = And(
        AIFeed.session_id == session_id,
        AIFeed.session_recurrence_id == session_recurrence_id,
    )

    count_query = AIFeed.count().where(where)

    is_asc_sort = False if sort == "desc" else True  # noqa: WPS502
    data_query = (
        AIFeed.select()
        .where(where)
        .order_by(AIFeed.end_transcription_time, ascending=is_asc_sort)
        .limit(limit)
        .offset(skip)
    )

    results = await asyncio.gather(count_query, data_query)

    ai_feed_msgs = list(
        map(lambda msg: AIFeed(patch_ai_feed_json_fields(msg)), results[1])
    )

    return results[0], ai_feed_msgs


async def get_catchup_summaries_paginated(
    session_id: str,
    session_recurrence_id: str,
    limit: int = 100,
    skip: int = 0,
    sort: str = "desc",
) -> tuple[int, list[AIFeed]]:
    """
    Returns catchup summaries total count for a session, as well as a list of catchup summaries paginated.

    :param session_id: Session ID
    :param session_recurrence_id: Session recurrence ID
    :param limit: Limit
    :param skip: Skip
    :param sort: Order
    :return: tuple[int, list[AIFeed]]
    """
    where = And(
        And(
            AIFeed.session_id == session_id,
            AIFeed.session_recurrence_id == session_recurrence_id,
        ),
        AIFeed.msg_type == AIFeed.AIFeedMsgType.Summary,
    )

    is_asc_sort = False if sort == "desc" else True  # noqa: WPS502

    count_query = AIFeed.count().where(where)

    summaries_query = (
        AIFeed.objects()
        .where(where)
        .order_by(AIFeed.end_transcription_time, ascending=is_asc_sort)
        .limit(limit)
        .offset(skip)
    )

    results = await asyncio.gather(count_query, summaries_query)
    return results[0], results[1]


async def get_action_items_for_session(
    session_id: str, session_recurrence_id: str
) -> List[dict[str, Any]]:
    try:
        query = AIFeed.select().where(
            (AIFeed.session_id == session_id)
            & (AIFeed.session_recurrence_id == session_recurrence_id)
            & (AIFeed.msg_type == AIFeed.AIFeedMsgType.ActionItem)
        )
        action_items = await asyncio.wait_for(
            query.run(), timeout=10
        )  # 10 seconds timeout

        # Group action items by assignee
        grouped_action_items = {}
        for item in action_items:
            msg_dynamic_fields = (
                json.loads(item["msg_dynamic_fields"])
                if item["msg_dynamic_fields"]
                else {}
            )
            completed = msg_dynamic_fields.get("completed", {}).get("value", False)
            deleted = msg_dynamic_fields.get("deleted", {}).get("value", False)
            assignee = msg_dynamic_fields.get("assignee", "")

            if not completed and not deleted:
                if assignee not in grouped_action_items:
                    grouped_action_items[assignee] = []
                grouped_action_items[assignee].append(item["msg"])

        # Convert grouped action items to the desired format
        result = [
            {"assignee": assignee, "items": items}
            for assignee, items in grouped_action_items.items()
        ]

        logger.info(
            f"Fetched {len(result)} active action items successfully for session {session_id} and recurrence {session_recurrence_id}."
        )
        return result

    except asyncio.TimeoutError:
        logger.error("Query timed out.")
    except Exception as e:
        logger.error(f"An error occurred: {e}")


async def get_ai_feed_by_id(ai_feed_id: int) -> AIFeed | None:
    ai_feed = await AIFeed.objects().where(AIFeed.id == ai_feed_id).first()

    if ai_feed is not None:
        ai_feed = patch_ai_feed_json_fields(ai_feed)

    return ai_feed


async def update_ai_feed_msg_dynamic_fields(
    ai_feed_id: int,
    updated_fields: dict[Column | str, Any] | None,
) -> list[dict]:
    return (
        await AIFeed.update(
            {
                AIFeed.msg_dynamic_fields: updated_fields,
            },
        )
        .where(AIFeed.id == ai_feed_id)
        .returning(*AIFeed.all_columns())
    )


async def update_ai_feed_msg_links(
    ai_feed_id: int,
    updated_links: List[dict] | None,
) -> list[dict]:
    return (
        await AIFeed.update(
            {
                AIFeed.links: updated_links,
            },
        )
        .where(AIFeed.id == ai_feed_id)
        .returning(*AIFeed.all_columns())
    )


async def populate_owner_for_ai_feed_msg_links(
    ai_feed_msgs: list[AIFeed],
):
    """Populate user names for ai feed msgs from owner id. Mutates the input list.

    :param ai_feed_msgs: list[AIFeed]
    """
    owner_ids = []

    for ai_feed in ai_feed_msgs:
        if ai_feed.links is not None:
            for link in ai_feed.links:
                if link.get("owner_id"):
                    owner_ids.append(link["owner_id"])

    # skip all owner_ids that are not numeric
    numeric_owner_ids = [owner_id for owner_id in owner_ids if owner_id.isnumeric()]
    if len(numeric_owner_ids) == 0:
        return
    users = await mars_get_users_batch(numeric_owner_ids)
    if users.get("data") is None or users["data"].get("users") is None:
        logger.error(
            f"Failed to fetch users for owner ids: {numeric_owner_ids}",
        )
        return

    for ai_feed in ai_feed_msgs:
        if ai_feed.links is not None:
            for link in ai_feed.links:
                if link.get("owner_id") in owner_ids:
                    user = next(
                        (
                            u
                            for u in users["data"]["users"]
                            if u.get("id") == link["owner_id"]
                        ),
                        None,
                    )
                    user["fullName"] = f"{user['firstName']} {user['lastName']}".strip()
                    link["owner"] = user
