{"components": {"responses": {"APIError": {"content": {"application/json": {"schema": {"externalDocs": {"url": "https://pkg.go.dev/encore.dev/beta/errs#Error"}, "properties": {"code": {"description": "Error code", "example": "not_found", "externalDocs": {"url": "https://pkg.go.dev/encore.dev/beta/errs#ErrCode"}, "type": "string"}, "details": {"description": "Error details", "type": "object"}, "message": {"description": "Error message", "type": "string"}}, "title": "APIError", "type": "object"}}}, "description": "Error response"}}, "schemas": {"api.AIFeedEventType": {"type": "string"}, "api.AccessControlItem": {"properties": {"type": {"type": "string"}, "value": {"type": "string"}}, "required": ["type", "value"], "type": "object"}, "api.AccessRuleType": {"type": "string"}, "api.AccessType": {"type": "string"}, "api.ChannelRole": {"type": "string"}, "api.GenerateXRayInfoResponseData": {"properties": {"icon": {"type": "string"}, "shortSummary": {"type": "string"}, "title": {"type": "string"}}, "required": ["title", "icon", "shortSummary"], "type": "object"}, "api.GenerateXRayPromptResponseData": {"properties": {"prompt": {"type": "string"}, "type": {"type": "string"}}, "required": ["type", "prompt"], "type": "object"}, "api.GetUserPaymentMethodDetailsResponseData": {"properties": {"lastPayment": {"$ref": "#/components/schemas/shared.BillingPaymentResultDTO"}}, "required": ["lastPayment"], "type": "object"}, "api.GetXRayNotificationsResponseData": {"properties": {"hasMore": {"type": "boolean"}, "notifications": {"items": {"$ref": "#/components/schemas/api.XRayNotificationDTO"}, "type": "array"}, "totalCount": {"format": "int64", "type": "integer"}}, "required": ["notifications", "totalCount", "hasMore"], "type": "object"}, "api.HeardSpeaker": {"properties": {"audioSourceUser": {"$ref": "#/components/schemas/shared.UserDTO"}, "firstUtterance": {"format": "date-time", "title": "FirstUtterance is the time when the speaker started speaking\n", "type": "string"}, "lastUtterance": {"format": "date-time", "title": "LastUtterance is the time when the speaker stopped speaking\n", "type": "string"}, "snippets": {"items": {"$ref": "#/components/schemas/api.SpeakerSnippet"}, "title": "Snippets is a collection of utterances from this speaker with their time ranges\n", "type": "array"}, "speakerIdentity": {"$ref": "#/components/schemas/api.IdentifiedSpeaker"}, "speakerName": {"title": "Speaker<PERSON><PERSON> is the name of the speaker, will be \"Speaker 1\" when unidentified\n", "type": "string"}, "speakerUID": {"title": "SpeakerUID is the unique identifier of the speaker contains the userID\n", "type": "string"}}, "required": ["audioSourceUser", "<PERSON><PERSON><PERSON>", "speakerUID", "speakerIdentity", "firstUtterance", "lastUtterance", "snippets"], "type": "object"}, "api.IdentifiedSpeaker": {"properties": {"speakerEmail": {"title": "SpeakerEmail optional: email address of the identified speaker\n", "type": "string"}, "speakerFullName": {"title": "Speaker<PERSON><PERSON><PERSON><PERSON> optional: first name of the identified speaker\n", "type": "string"}, "speakerUID": {"title": "SpeakerUID is the unique identifier of the speaker\n", "type": "string"}, "speakerUserID": {"title": "SpeakerUserID Voids other identifying fields when present\n", "type": "string"}}, "required": ["speakerUID", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "speakerEmail", "speakerUserID"], "type": "object"}, "api.InReviewAccessRequestDTO": {"properties": {"accessType": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"$ref": "#/components/schemas/api.RestrictionStatus"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "type": {"$ref": "#/components/schemas/api.AccessRuleType"}, "updatedAt": {"format": "int64", "type": "integer"}, "user": {"properties": {"avatar": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "lastName": {"type": "string"}}, "required": ["id", "avatar", "firstName", "lastName"], "type": "object"}, "value": {"type": "string"}}, "required": ["id", "restrictionStatus", "requestMessage", "sessionID", "sessionRecurrenceID", "type", "value", "accessType", "updatedAt", "createdAt", "user"], "type": "object"}, "api.ListSessionsResponseData": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/shared.SessionDTO"}, "type": "array"}}, "required": ["sessions"], "type": "object"}, "api.ListUserTransactionsResponseData": {"properties": {"invoiceUrl": {"type": "string"}, "transaction": {"$ref": "#/components/schemas/shared.BillingTransactionDTO"}}, "required": ["transaction", "invoiceUrl"], "type": "object"}, "api.ListXRayTemplatesResponseData": {"properties": {"hasMore": {"type": "boolean"}, "templates": {"items": {"$ref": "#/components/schemas/api.XRayTemplateDTO"}, "type": "array"}, "totalCount": {"format": "int64", "type": "integer"}}, "required": ["templates", "totalCount", "hasMore"], "type": "object"}, "api.ListXRaysResponseData": {"properties": {"hasMore": {"type": "boolean"}, "totalCount": {"format": "int64", "type": "integer"}, "xrays": {"items": {"$ref": "#/components/schemas/api.XRayDTO"}, "type": "array"}}, "required": ["xrays", "totalCount", "hasMore"], "type": "object"}, "api.MarkXRayNotificationsSeenResponseData": {"properties": {"markedCount": {"format": "int64", "type": "integer"}}, "required": ["markedCount"], "type": "object"}, "api.MeetingMetadata": {"properties": {"key_decisions": {"items": {"type": "string"}, "type": "array"}, "keywords": {"items": {"type": "string"}, "type": "array"}, "meeting_id": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}, "short_summary": {"type": "string"}, "title": {"type": "string"}, "topics": {"items": {"type": "string"}, "type": "array"}, "type": {"type": "string"}}, "required": ["meeting_id", "title", "participants", "type", "topics", "short_summary", "key_decisions", "keywords"], "type": "object"}, "api.MeetingSuggestionDTO": {"properties": {"category": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "prompt": {"type": "string"}, "sessionId": {"type": "string"}, "sessionRecurrenceId": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}, "userId": {"type": "string"}}, "required": ["id", "sessionId", "sessionRecurrenceId", "userId", "category", "prompt", "createdAt", "updatedAt"], "type": "object"}, "api.MeetingSuggestionInput": {"properties": {"category": {"type": "string"}, "prompt": {"type": "string"}, "userId": {"type": "string"}}, "required": ["userId", "category", "prompt"], "type": "object"}, "api.RecallSearchHit": {"properties": {"confidence": {"type": "number"}, "end_time": {"type": "number"}, "start_time": {"type": "number"}, "text": {"type": "string"}}, "required": ["text", "start_time", "end_time", "confidence"], "type": "object"}, "api.RecallTranscriptionsWebhookData": {"properties": {"bot_id": {"type": "string"}, "log": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookLog"}, "search": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookSearch"}, "status": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookStatus"}, "transcript": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookTranscript"}}, "required": ["bot_id", "transcript", "search", "status", "log"], "type": "object"}, "api.RecallTranscriptionsWebhookLog": {"properties": {"created_at": {"type": "string"}, "level": {"type": "string"}, "message": {"type": "string"}, "output_id": {"type": "string"}}, "required": ["level", "message", "created_at", "output_id"], "type": "object"}, "api.RecallTranscriptionsWebhookSearch": {"properties": {"hits": {"items": {"$ref": "#/components/schemas/api.RecallSearchHit"}, "type": "array"}, "original_transcript_id": {"format": "int64", "type": "integer"}, "speaker": {"type": "string"}}, "required": ["speaker", "original_transcript_id", "hits"], "type": "object"}, "api.RecallTranscriptionsWebhookStatus": {"properties": {"code": {"type": "string"}, "created_at": {"type": "string"}, "message": {"type": "string"}, "recording_id": {"type": "string"}, "sub_code": {"type": "string"}}, "required": ["code", "sub_code", "message", "recording_id", "created_at"], "type": "object"}, "api.RecallTranscriptionsWebhookTranscript": {"properties": {"is_final": {"type": "boolean"}, "language": {"type": "string"}, "original_transcript_id": {"format": "int64", "type": "integer"}, "speaker": {"type": "string"}, "speaker_id": {"format": "int64", "type": "integer"}, "transcription_provider_speaker": {"type": "string"}, "words": {"items": {"$ref": "#/components/schemas/api.RecallWord"}, "type": "array"}}, "required": ["speaker", "speaker_id", "transcription_provider_speaker", "language", "original_transcript_id", "words", "is_final"], "type": "object"}, "api.RecallWord": {"properties": {"end_time": {"type": "number"}, "start_time": {"type": "number"}, "text": {"type": "string"}}, "required": ["text", "start_time", "end_time"], "type": "object"}, "api.RestrictionStatus": {"type": "string"}, "api.SessionAccessDTO": {"properties": {"id": {"type": "string"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"$ref": "#/components/schemas/api.RestrictionStatus"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "type": {"$ref": "#/components/schemas/api.AccessRuleType"}, "user": {"$ref": "#/components/schemas/api.UserSummaryDTO"}, "value": {"title": "Value is the domain name when Type field is domain otherwise omitted\n", "type": "string"}}, "required": ["id", "sessionID", "sessionRecurrenceID", "restrictionStatus", "type"], "type": "object"}, "api.SessionAccessRulesDTO": {"properties": {"type": {"type": "string"}, "value": {"type": "string"}}, "required": ["type", "value"], "type": "object"}, "api.SessionDefaultGuest": {"properties": {"email": {"type": "string"}, "fullName": {"type": "string"}, "joined": {"type": "boolean"}, "surrogateID": {"type": "string"}}, "required": ["surrogateID", "fullName", "email", "joined"], "type": "object"}, "api.SessionDefaultUser": {"properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "joined": {"type": "boolean"}, "lastName": {"type": "string"}}, "required": ["email", "joined"], "type": "object"}, "api.SessionDefaultUsersDTO": {"properties": {"guests": {"items": {"$ref": "#/components/schemas/api.SessionDefaultGuest"}, "type": "array"}, "users": {"items": {"$ref": "#/components/schemas/api.SessionDefaultUser"}, "type": "array"}}, "type": "object"}, "api.SessionGuest": {"properties": {"email": {"type": "string"}, "fullName": {"type": "string"}, "surrogateID": {"type": "string"}}, "required": ["surrogateID", "fullName", "email"], "type": "object"}, "api.SessionRequestCount": {"properties": {"count": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "recurrenceID": {"type": "string"}}, "required": ["id", "recurrenceID", "count"], "type": "object"}, "api.SessionUser": {"properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "userID": {"type": "string"}}, "required": ["email"], "type": "object"}, "api.SessionUserDTO": {"properties": {"guest": {"$ref": "#/components/schemas/api.SessionGuest"}, "isViewerAccessRevoked": {"type": "boolean"}, "joined": {"type": "boolean"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "sessionUserID": {"type": "string"}, "user": {"$ref": "#/components/schemas/api.SessionUser"}}, "required": ["sessionUserID", "joined", "isViewerAccessRevoked", "roleIDs"], "type": "object"}, "api.SpeakerSnippet": {"properties": {"createdAt": {"format": "date-time", "title": "CreatedAt is when this snippet was created\n", "type": "string"}, "endSec": {"title": "EndSec is the end time of the snippet in seconds\n", "type": "number"}, "startSec": {"title": "StartSec is the start time of the snippet in seconds\n", "type": "number"}, "text": {"title": "Text is the transcript of the snippet\n", "type": "string"}}, "required": ["text", "startSec", "endSec", "createdAt"], "type": "object"}, "api.SuggestionResponseDTO": {"properties": {"category": {"type": "string"}, "content": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "isPersonal": {"type": "boolean"}}, "required": ["id", "content", "category", "isPersonal", "createdAt"], "type": "object"}, "api.UpdateAccessRule": {"properties": {"accessType": {"$ref": "#/components/schemas/api.AccessType"}, "guestEmail": {"type": "string"}, "guestFullName": {"type": "string"}, "guestSurrogateID": {"format": "int64", "type": "integer"}, "id": {"title": "ID when present all fields except RestrictionStatus are ignored\n", "type": "string"}, "isExternalRequest": {"type": "boolean"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"$ref": "#/components/schemas/api.RestrictionStatus"}, "type": {"$ref": "#/components/schemas/api.AccessRuleType"}, "value": {"type": "string"}}, "type": "object"}, "api.UpdateUserRequestData": {"properties": {"about": {"type": "string"}, "avatar": {"title": "Empty string represents a nil value\n", "type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "social": {"$ref": "#/components/schemas/shared.Socials"}}, "type": "object"}, "api.UserMeetingTypeRequestDTO": {"properties": {"id": {"type": "string"}}, "required": ["id"], "type": "object"}, "api.UserSummaryDTO": {"properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "fullName": {"type": "string"}, "lastName": {"type": "string"}}, "required": ["fullName", "email"], "type": "object"}, "api.XRayDTO": {"properties": {"alertChannels": {"additionalProperties": {"type": "boolean"}, "type": "object"}, "createdAt": {"format": "int64", "type": "integer"}, "currentCommit": {"$ref": "#/components/schemas/api.XRayDocCommit"}, "currentCommitId": {"format": "int64", "type": "integer"}, "description": {"type": "string"}, "frequency": {"type": "string"}, "icon": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "isActive": {"type": "boolean"}, "lastDigestAt": {"format": "int64", "type": "integer"}, "ownerId": {"format": "int64", "type": "integer"}, "prompt": {"type": "string"}, "scope": {"type": "string"}, "shortSummary": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "unreadNotificationsCount": {"format": "int64", "type": "integer"}, "updatedAt": {"format": "int64", "type": "integer"}, "visibility": {"type": "string"}}, "required": ["id", "ownerId", "title", "description", "prompt", "icon", "shortSummary", "currentCommitId", "currentCommit", "alertChannels", "isActive", "visibility", "type", "scope", "unreadNotificationsCount", "frequency", "lastDigestAt", "createdAt", "updatedAt"], "type": "object"}, "api.XRayDocCommit": {"properties": {"authorId": {"format": "int64", "type": "integer"}, "content": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"format": "int64", "type": "integer"}, "updatedAt": {"format": "int64", "type": "integer"}, "xrayId": {"format": "int64", "type": "integer"}}, "required": ["id", "xrayId", "content", "authorId", "createdAt", "updatedAt"], "type": "object"}, "api.XRayNotificationDTO": {"properties": {"content": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"format": "int64", "type": "integer"}, "seen": {"type": "boolean"}, "source": {"type": "object"}, "updatedAt": {"format": "int64", "type": "integer"}, "userId": {"format": "int64", "type": "integer"}, "xrayDocCommitId": {"format": "int64", "type": "integer"}}, "required": ["id", "xrayDocCommitId", "userId", "seen", "content", "createdAt", "updatedAt", "source"], "type": "object"}, "api.XRayTemplateDTO": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "description": {"type": "string"}, "icon": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "owner": {"$ref": "#/components/schemas/shared.UserDTO"}, "ownerId": {"format": "int64", "type": "integer"}, "prompt": {"type": "string"}, "shortSummary": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["id", "ownerId", "title", "description", "prompt", "icon", "shortSummary", "type", "createdAt", "updatedAt", "owner"], "type": "object"}, "mars_api.UserSocial": {"properties": {"avatar": {"type": "string"}, "bio": {"type": "string"}, "firstName": {"type": "string"}, "fullName": {"type": "string"}, "lastName": {"type": "string"}, "platform": {"type": "string"}, "url": {"type": "string"}, "userName": {"type": "string"}}, "required": ["avatar", "bio", "firstName", "fullName", "lastName", "platform", "url", "userName"], "type": "object"}, "recallai.BotMeetingLink": {"properties": {"meeting_id": {"type": "string"}, "platform": {"type": "string"}}, "required": ["meeting_id", "platform"], "type": "object"}, "recallai.CalendarMeeting": {"properties": {"calendar_user": {"$ref": "#/components/schemas/recallai.CalendarUser"}, "end_time": {"type": "string"}, "id": {"type": "string"}, "start_time": {"type": "string"}}, "required": ["id", "start_time", "end_time", "calendar_user"], "type": "object"}, "recallai.CalendarUser": {"properties": {"connections": {"items": {"$ref": "#/components/schemas/recallai.RecallConnection"}, "type": "array"}, "external_id": {"type": "string"}, "id": {"type": "string"}, "preferences": {"$ref": "#/components/schemas/recallai.RecordingPreferences"}}, "required": ["id", "external_id", "connections", "preferences"], "type": "object"}, "recallai.MeetingMetadata": {"properties": {"title": {"type": "string"}, "zoom_meeting_uuid": {"title": "Only included if it's a Zoom meeting\n", "type": "string"}}, "required": ["title", "zoom_meeting_uuid"], "type": "object"}, "recallai.MeetingParticipant": {"properties": {"id": {"format": "int64", "type": "integer"}, "is_host": {"type": "boolean"}, "name": {"type": "string"}, "platform": {"type": "string"}}, "required": ["id", "name", "is_host", "platform"], "type": "object"}, "recallai.RecallConnection": {"properties": {"connected": {"type": "boolean"}, "email": {"type": "string"}, "platform": {"type": "string"}}, "required": ["connected", "platform", "email"], "type": "object"}, "recallai.RecordingPreferences": {"properties": {"bot_name": {"type": "string"}, "record_confirmed": {"type": "boolean"}, "record_external": {"type": "boolean"}, "record_internal": {"type": "boolean"}, "record_non_host": {"type": "boolean"}, "record_only_host": {"type": "boolean"}, "record_recurring": {"type": "boolean"}}, "required": ["record_non_host", "record_recurring", "record_external", "record_internal", "record_confirmed", "record_only_host", "bot_name"], "type": "object"}, "shared.BillingCard": {"type": "object"}, "shared.BillingCycleDTO": {"properties": {"frequency": {"type": "number"}, "interval": {"type": "string"}}, "type": "object"}, "shared.BillingPaymentMethodDetailsDTO": {"properties": {"card": {"$ref": "#/components/schemas/shared.BillingCard"}, "type": {"type": "string"}}, "required": ["type", "card"], "type": "object"}, "shared.BillingPaymentResultDTO": {"properties": {"errorCode": {"$ref": "#/components/schemas/shared.BillingPaymentResultDTOErrorCode"}, "status": {"$ref": "#/components/schemas/shared.BillingPaymentResultDTOStatus"}}, "required": ["status", "errorCode"], "type": "object"}, "shared.BillingPaymentResultDTOErrorCode": {"type": "string"}, "shared.BillingPaymentResultDTOStatus": {"type": "string"}, "shared.BillingPriceDTO": {"type": "object"}, "shared.BillingTransactionDTO": {"properties": {"customData": {"type": "object"}, "items": {"items": {"$ref": "#/components/schemas/shared.BillingTransactionItemDTO"}, "type": "array"}, "payments": {"items": {"$ref": "#/components/schemas/shared.BillingTransactionPaymentAttemptDTO"}, "type": "array"}}, "required": ["customData", "items", "payments"], "type": "object"}, "shared.BillingTransactionItemDTO": {"properties": {"price": {"$ref": "#/components/schemas/shared.BillingPriceDTO"}}, "required": ["price"], "type": "object"}, "shared.BillingTransactionPaymentAttemptDTO": {"properties": {"method_details": {"$ref": "#/components/schemas/shared.BillingPaymentMethodDetailsDTO"}}, "required": ["method_details"], "type": "object"}, "shared.DataVisibility": {"type": "string"}, "shared.GuestDTO": {"properties": {"email": {"type": "string"}, "fullName": {"type": "string"}, "surrogateID": {"format": "int64", "type": "integer"}}, "required": ["surrogateID"], "type": "object"}, "shared.HttpErrorCode": {"format": "int64", "type": "integer"}, "shared.LobbyDTO": {"properties": {"isActive": {"title": "IsActive is true when the lobby is active false when it is not reachable from\nthe URL\n", "type": "boolean"}, "lobbyID": {"title": "LobbyID is the unique identifier of the lobby\n", "type": "string"}, "slug": {"title": "Slug is the unique identifier of the lobby\n", "type": "string"}}, "required": ["lobbyID", "slug", "isActive"], "type": "object"}, "shared.LobbyParticipantDTO": {"properties": {"avatar": {"title": "Avatar the avatar of the user\n", "type": "string"}, "email": {"title": "Email assuming this is shared with the owner of the lobby only!\n", "type": "string"}, "firstName": {"title": "FirstName the first name of the user\n", "type": "string"}, "fullName": {"title": "FullName the full name of the user\n", "type": "string"}, "guestSurrogateID": {"format": "int64", "title": "GuestSurrogate<PERSON> is the id of the guest surrogate\n", "type": "integer"}, "id": {"title": "ID could present either userID or guestSurrogateID\n", "type": "string"}, "isOwner": {"title": "IsOwner is true when the user is the owner of the lobby\n", "type": "boolean"}, "lastName": {"title": "LastName the last name of the use\n", "type": "string"}, "participantID": {"title": "ParticipantID is the id of the participant\n", "type": "string"}, "userID": {"title": "UserID is the id of the user\n", "type": "string"}}, "required": ["participantID", "id", "fullName", "isOwner"], "type": "object"}, "shared.NotificationSettings": {"properties": {"isEmailNotificationEnabledGlobal": {"title": "whether email notifications are enabled globally\n", "type": "boolean"}, "isPushNotificationEnabledGlobal": {"title": "whether push notifications are enabled globally\n", "type": "boolean"}}, "type": "object"}, "shared.OffsetPaginationResponse": {"properties": {"limit": {"format": "int64", "type": "integer"}, "offset": {"format": "int64", "type": "integer"}, "totalCount": {"format": "int64", "type": "integer"}}, "required": ["totalCount", "limit", "offset"], "type": "object"}, "shared.OnboardingFlags": {"properties": {"0": {"type": "boolean"}, "1": {"type": "boolean"}, "2": {"type": "boolean"}, "3": {"type": "boolean"}, "4": {"type": "boolean"}, "5": {"type": "boolean"}, "6": {"type": "string"}}, "type": "object"}, "shared.ParticipantMetadata": {"properties": {"IsDiarized": {"type": "boolean"}, "guest": {"$ref": "#/components/schemas/shared.GuestDTO"}, "isLoaded": {"type": "boolean"}, "region": {"type": "string"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user", "guest", "isLoaded", "IsDiarized", "region"], "type": "object"}, "shared.PriceDTO": {"properties": {"billingCycle": {"$ref": "#/components/schemas/shared.BillingCycleDTO"}, "id": {"type": "string"}, "productID": {"type": "string"}, "quantity": {"$ref": "#/components/schemas/shared.PriceQuantityDTO"}, "status": {"$ref": "#/components/schemas/shared.PriceDTOStatus"}, "taxMode": {"$ref": "#/components/schemas/shared.PriceDTOTaxMode"}, "trialPeriod": {"$ref": "#/components/schemas/shared.PriceDTOTrialPeriod"}, "unitPrice": {"$ref": "#/components/schemas/shared.UnitPriceDTO"}, "unitPriceOverrides": {"items": {"$ref": "#/components/schemas/shared.UnitPriceOverrideDTO"}, "type": "array"}}, "type": "object"}, "shared.PriceDTOStatus": {"type": "string"}, "shared.PriceDTOTaxMode": {"type": "string"}, "shared.PriceDTOTrialPeriod": {"properties": {"frequency": {"type": "number"}, "interval": {"type": "string"}}, "type": "object"}, "shared.PriceQuantityDTO": {"properties": {"maximum": {"type": "number"}, "minimum": {"type": "number"}}, "type": "object"}, "shared.SessionAccessRuleDTO": {"properties": {"accessType": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "isExternalRequest": {"type": "boolean"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "type": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}, "value": {"type": "string"}}, "type": "object"}, "shared.SessionDTO": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}, "shared.SessionDTOAccessStatus": {"type": "string"}, "shared.SessionDTOType": {"type": "string"}, "shared.SessionIntegrationsDTO": {"properties": {"salesforceBindingId": {"type": "string"}}, "required": ["salesforceBindingId"], "type": "object"}, "shared.SessionState": {"type": "string"}, "shared.SessionSubscriptionPlanDTO": {"properties": {"planConfig": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTO"}, "planConfigOverrides": {"type": "object"}}, "type": "object"}, "shared.Socials": {"items": {"$ref": "#/components/schemas/shared.UserSocialDTO"}, "type": "array"}, "shared.StateUpdatedAt": {"properties": {"state": {"$ref": "#/components/schemas/shared.SessionState"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["state", "updatedAt"], "type": "object"}, "shared.SubscriptionItemDTO": {"properties": {"createdAt": {"type": "string"}, "customData": {"type": "object"}, "nextBilledAt": {"type": "string"}, "previouslyBilledAt": {"type": "string"}, "price": {"$ref": "#/components/schemas/shared.PriceDTO"}, "quantity": {"type": "number"}, "recurring": {"type": "boolean"}, "status": {"$ref": "#/components/schemas/shared.SubscriptionItemDTOStatus"}, "trialDates": {"properties": {"endsAt": {"type": "string"}, "startsAt": {"type": "string"}}, "type": "object"}, "updatedAt": {"type": "string"}}, "type": "object"}, "shared.SubscriptionItemDTOStatus": {"type": "string"}, "shared.SubscriptionPlanConfigDTO": {"properties": {"aiFeed": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "bots": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "createdAt": {"format": "int64", "type": "integer"}, "crm": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "customFeedItems": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "customIntegrations": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "id": {"type": "string"}, "integrations": {"properties": {"apps": {"items": {"type": "string"}, "type": "array"}, "enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingMemory": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingSummary": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingTemplates": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingWorkflows": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetings": {"properties": {"enabled": {"type": "boolean"}, "max": {"type": "number"}}, "required": ["enabled", "max"], "type": "object"}, "modelSegregation": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "offTheRecord": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "paddleProductID": {"type": "string"}, "paddleProductName": {"type": "string"}, "queueMode": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "recording": {"properties": {"enabled": {"type": "boolean"}, "local": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "stream": {"properties": {"enabled": {"type": "boolean"}, "quality": {"type": "number"}}, "required": ["enabled", "quality"], "type": "object"}, "support": {"properties": {"enabled": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTOSupportType"}}, "required": ["enabled", "type"], "type": "object"}, "timeLimit": {"properties": {"enabled": {"type": "boolean"}, "max": {"format": "int64", "type": "integer"}}, "required": ["enabled", "max"], "type": "object"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["aiFeed", "customFeedItems", "customIntegrations", "id", "integrations", "meeting<PERSON><PERSON><PERSON>", "meeting<PERSON>ummary", "meetingTemplates", "meetingWorkflows", "meetings", "modelSegregation", "offTheRecord", "paddleProductID", "paddleProductName", "queueMode", "recording", "stream", "support", "crm", "bots", "timeLimit", "createdAt", "updatedAt"], "type": "object"}, "shared.SubscriptionPlanConfigDTOSupportType": {"type": "string"}, "shared.TeamWithInlineRelationsDTO": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "domains": {"items": {"type": "string"}, "type": "array"}, "id": {"type": "string"}, "membersCount": {"format": "int64", "type": "integer"}, "name": {"type": "string"}, "role": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["id", "name", "domains", "createdAt", "updatedAt"], "type": "object"}, "shared.TranscriptionDTO": {"properties": {"batchID": {"type": "string"}, "id": {"type": "string"}, "languageLocale": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "speakerEmail": {"type": "string"}, "speakerFirstName": {"type": "string"}, "speakerFullName": {"type": "string"}, "speakerLastName": {"type": "string"}, "speakerUID": {"type": "string"}, "speakerUserID": {"type": "string"}, "text": {"type": "string"}, "timeUnix": {"format": "int64", "type": "integer"}}, "required": ["id", "batchID", "sessionID", "sessionRecurrenceID", "text", "speakerUID", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "object"}, "shared.UnitPriceDTO": {"properties": {"amount": {"type": "string"}, "currencyCode": {"type": "string"}}, "type": "object"}, "shared.UnitPriceOverrideDTO": {"properties": {"countryCodes": {"items": {"type": "string"}, "type": "array"}, "unitPrice": {"$ref": "#/components/schemas/shared.UnitPriceDTO"}}, "type": "object"}, "shared.UserCryptoToken": {"properties": {"blockchain": {"title": "the blockchain of the token\n", "type": "number"}, "createdAt": {"format": "int64", "title": "the timestamp when the token was created\n", "type": "integer"}, "details": {"title": "the metadata of the token\n", "type": "object"}, "id": {"title": "the ID of the token\n", "type": "string"}, "purposeType": {"title": "the purpose type of the token\n", "type": "string"}, "token": {"title": "the token\n", "type": "string"}, "updatedAt": {"format": "int64", "title": "the timestamp when the token was last updated\n", "type": "integer"}, "url": {"title": "the URL of the NFT\n", "type": "string"}, "userID": {"title": "owner of the token\n", "type": "string"}, "walletID": {"title": "the wallet ID of the token\n", "type": "string"}}, "required": ["blockchain", "createdAt", "details", "id", "purposeType", "token", "updatedAt", "url", "userID", "walletID"], "type": "object"}, "shared.UserDTO": {"properties": {"about": {"title": "the about section of the user\n", "type": "string"}, "appleID": {"title": "the apple ID of the user if they signed up with apple\n", "type": "string"}, "avatar": {"title": "the avatar of the user\n", "type": "string"}, "createdAt": {"format": "int64", "title": "the timestamp when the user was created\n", "type": "integer"}, "customerID": {"title": "the customer ID of the user\n", "type": "string"}, "email": {"title": "the email of the user\n", "type": "string"}, "fingerprint": {"title": "the fingerprint of the user\n", "type": "object"}, "firstName": {"title": "the first name of the user\n", "type": "string"}, "googleID": {"title": "the google ID of the user if they signed up with google\n", "type": "string"}, "id": {"title": "the ID of the user\n", "type": "string"}, "isTestUser": {"title": "whether the user is a test user\n", "type": "boolean"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "lobbies": {"items": {"$ref": "#/components/schemas/shared.LobbyDTO"}, "title": "Lobbies any lobby associated with the user\n", "type": "array"}, "marketingOptIn": {"title": "whether the user opted in to marketing\n", "type": "boolean"}, "nftAvatar": {"$ref": "#/components/schemas/shared.UserCryptoToken"}, "notificationSettings": {"$ref": "#/components/schemas/shared.NotificationSettings"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "roleIDs": {"items": {"type": "string"}, "title": "the role IDs of the user\n", "type": "array"}, "signUpTimestamp": {"format": "int64", "title": "the timestamp when the user signed up\n", "type": "integer"}, "social": {"$ref": "#/components/schemas/shared.Socials"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTO"}, "team": {"$ref": "#/components/schemas/shared.TeamWithInlineRelationsDTO"}, "timezone": {"title": "the timezone of the user\n", "type": "string"}, "updatedAt": {"format": "int64", "title": "the timestamp when the user was last updated\n", "type": "integer"}}, "type": "object"}, "shared.UserMeetingTypeDTO": {"properties": {"createdBy": {"format": "int64", "type": "integer"}, "description": {"type": "string"}, "id": {"type": "string"}, "promptTemplate": {"type": "string"}, "title": {"type": "string"}}, "required": ["description", "id", "promptTemplate", "title"], "type": "object"}, "shared.UserSocialDTO": {"properties": {"avatar": {"title": "the avatar of the user\n", "type": "string"}, "bio": {"title": "the bio of the user\n", "type": "string"}, "firstName": {"title": "the first name of the user\n", "type": "string"}, "fullName": {"title": "the full name of the user\n", "type": "string"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "platform": {"title": "the platform of the user\n", "type": "string"}, "url": {"title": "the URL of the user\n", "type": "string"}, "userName": {"title": "the username of the user\n", "type": "string"}}, "required": ["url"], "type": "object"}, "shared.UserSubscriptionPlanDTO": {"properties": {"addressID": {"type": "string"}, "billingCycle": {"$ref": "#/components/schemas/shared.BillingCycleDTO"}, "billingDetails": {"properties": {"additionalInformation": {"type": "string"}, "enableCheckout": {"type": "boolean"}, "purchaseOrderMinimum": {"type": "string"}}, "type": "object"}, "businessID": {"type": "string"}, "canceledAt": {"type": "string"}, "collectionMode": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTOCollectionMode"}, "createdAt": {"format": "int64", "type": "integer"}, "currencyCode": {"type": "string"}, "currentBillingPeriod": {"properties": {"endsAt": {"type": "string"}, "startsAt": {"type": "string"}}, "type": "object"}, "customData": {"type": "object"}, "customerID": {"type": "string"}, "firstBilledAt": {"type": "string"}, "id": {"type": "string"}, "items": {"items": {"$ref": "#/components/schemas/shared.SubscriptionItemDTO"}, "type": "array"}, "managementUrls": {"properties": {"cancel": {"type": "string"}, "updatePaymentMethod": {"type": "string"}}, "type": "object"}, "nextBilledAt": {"type": "string"}, "paddleSubscriptionID": {"type": "string"}, "pausedAt": {"type": "string"}, "planConfig": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTO"}, "planConfigOverrides": {"type": "object"}, "scheduledChange": {"properties": {"action": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTOScheduledChangeAction"}, "effectiveAt": {"type": "string"}, "resumeAt": {"type": "string"}}, "type": "object"}, "startedAt": {"type": "string"}, "status": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTOStatus"}, "trialEndsAt": {"format": "int64", "type": "integer"}, "updatedAt": {"format": "int64", "type": "integer"}, "userID": {"type": "string"}}, "type": "object"}, "shared.UserSubscriptionPlanDTOCollectionMode": {"type": "string"}, "shared.UserSubscriptionPlanDTOScheduledChangeAction": {"type": "string"}, "shared.UserSubscriptionPlanDTOStatus": {"type": "string"}}}, "info": {"description": "Generated by encore", "title": "API for elio-bzw2", "version": "1", "x-logo": {"altText": "Encore logo", "backgroundColor": "#EEEEE1", "url": "https://encore.dev/assets/branding/logo/logo-black.png"}}, "openapi": "3.0.0", "paths": {"/.well-known/microsoft-identity-association.json": {"delete": {"operationId": "DELETE:transcriptions.IdentityAssociated", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed to verify domains for Microsoft OAuth2 integration\n"}, "get": {"operationId": "GET:transcriptions.IdentityAssociated", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed to verify domains for Microsoft OAuth2 integration\n"}, "head": {"operationId": "HEAD:transcriptions.IdentityAssociated", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed to verify domains for Microsoft OAuth2 integration\n"}, "patch": {"operationId": "PATCH:transcriptions.IdentityAssociated", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed to verify domains for Microsoft OAuth2 integration\n"}, "post": {"operationId": "POST:transcriptions.IdentityAssociated", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed to verify domains for Microsoft OAuth2 integration\n"}, "put": {"operationId": "PUT:transcriptions.IdentityAssociated", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed to verify domains for Microsoft OAuth2 integration\n"}}, "/comms.MeetingMetadataReady": {"post": {"description": "<PERSON><PERSON> will then store the metadata title if it's a Recall or Listening Mode session.\n", "operationId": "POST:comms.MeetingMetadataReady", "requestBody": {"content": {"application/json": {"schema": {"properties": {"metadata": {"$ref": "#/components/schemas/api.MeetingMetadata"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "metadata"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "MeetingMetadataReady is a endpoint to notify <PERSON><PERSON> that a specific session's\nmetadata has been generated.\n"}}, "/comms.PostSessionSummaryReady": {"get": {"description": "TODO when tag:internal is merged, is that instead of tag:trixta\n", "operationId": "GET:comms.PostSessionSummaryReady", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"format": "int64", "type": "integer"}, "message": {"type": "string"}, "recipients": {"items": {"type": "string"}, "type": "array"}}, "required": ["message", "code", "recipients"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryReady: Receive a request from nebula letting us know that the\nPSS was generated\n"}}, "/comms.TriggerGuestWaitingInLobbyNotification": {"post": {"operationId": "POST:comms.TriggerGuestWaitingInLobbyNotification", "requestBody": {"content": {"application/json": {"schema": {"properties": {"guestEmail": {"type": "string"}, "guestFullName": {"type": "string"}, "lobbyLink": {"type": "string"}, "toEmail": {"type": "string"}}, "required": ["toEmail", "guestEmail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lobbyLink"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.CreateSessionWithUser": {"post": {"operationId": "POST:meetings.CreateSessionWithUser", "parameters": [{"allowEmptyValue": true, "description": "This should match the old request payload from Mars:\n", "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"type": "string"}, "avatar": {"type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "category": {"type": "string"}, "cover": {"type": "string"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "isPubliclyVisible": {"type": "boolean"}, "isViewerAccessRestricted": {"type": "boolean"}, "lobbyID": {"title": "Optional\n", "type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "sessionSettings": {"type": "object"}, "sessionState": {"type": "string"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "userID": {"title": "Optional, specify the userID to create the session for\n", "type": "string"}, "userMeetingType": {"$ref": "#/components/schemas/api.UserMeetingTypeRequestDTO"}, "viewerAccessRules": {"items": {"$ref": "#/components/schemas/api.SessionAccessRulesDTO"}, "type": "array"}}, "required": ["about", "accessStatus", "avatar", "calendarEventEditURL", "calendarID", "calendarType", "category", "cover", "dataVisibility", "endTimestamp", "meetingType", "ogMetadata", "sessionSettings", "startTimestamp", "sessionState", "sessionTags", "sessionTitle", "userMeetingType", "viewerAccessRules", "isPubliclyVisible", "isViewerAccessRestricted", "userID", "lobbyID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.GetAllAccessRequests": {"get": {"description": "TODO evaluate tag:trixta in this endpoint, if it's used we should make a specific one for external requests\n", "operationId": "GET:meetings.GetAllAccessRequests", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "restrictionStatus", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/api.InReviewAccessRequestDTO"}, "type": "array"}, "cursor": {"type": "string"}, "totalCount": {"format": "int64", "type": "integer"}}, "required": ["accessRules", "cursor", "totalCount"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead\n"}}, "/meetings.GetLatestRecurrenceCompact": {"get": {"operationId": "GET:meetings.GetLatestRecurrenceCompact", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "status": {"type": "string"}}, "required": ["sessionTitle", "sessionID", "sessionRecurrenceID", "status", "startTimestamp", "startedAt"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.GetLobbyGuests": {"post": {"operationId": "POST:meetings.GetLobbyGuests", "parameters": [{"allowEmptyValue": true, "description": "LobbyID is the unique ID of the lobby\n", "explode": true, "in": "query", "name": "lobbyID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"participants": {"items": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}, "type": "array"}}, "required": ["participants"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Get<PERSON><PERSON><PERSON>G<PERSON><PERSON> returns the list of guests in the lobby\n"}}, "/meetings.GetMeetingSuggestionsBySession": {"get": {"operationId": "GET:meetings.GetMeetingSuggestionsBySession", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "session_id", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrence_id", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"suggestions": {"items": {"$ref": "#/components/schemas/api.MeetingSuggestionDTO"}, "type": "array"}}, "required": ["suggestions"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session\n(for future use)\n"}}, "/meetings.GetMeetingSuggestionsByUserInternal": {"get": {"operationId": "GET:meetings.GetMeetingSuggestionsByUserInternal", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "session_id", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrence_id", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "user_id", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"suggestions": {"items": {"$ref": "#/components/schemas/api.SuggestionResponseDTO"}, "type": "array"}, "total": {"format": "int64", "type": "integer"}}, "required": ["suggestions", "total"], "type": "object"}, "message": {"type": "string"}}, "required": ["data", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetMeetingSuggestionsByUserInternal retrieves meeting suggestions for a specific\nuser (internal service calls)\n"}}, "/meetings.GetSessionAccessRules": {"post": {"operationId": "POST:meetings.GetSessionAccessRules", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.SessionAccessDTO"}, "type": "array"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionAccessRules returns all access rules for a session\n"}}, "/meetings.GetSessionByID": {"get": {"description": "TODO replace tag:trixta with tag:internal when the tag is merged\n", "operationId": "GET:meetings.GetSessionByID", "parameters": [{"allowEmptyValue": true, "description": "SessionID is the snowflake ID of the session to get\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "RecurrenceID is the snowflake ID of the recurrence to get, when empty the latest recurrence is returned\n", "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionByID Replaces `MarsClient.SessionControllerGetSessionByID`, that used\nto sent a request to: src/modules/sessions/controllers/session.controller.ts in\nmars\n"}}, "/meetings.GetSessionRecurrencesById": {"get": {"description": "states the recurrence should have been in to appear in the response. When no sessions match the sessionID or optional filter the resulting \\`Sessions\\` array in api.GetSessionRecurrencesByIDResponse will be empty.\n", "operationId": "GET:meetings.GetSessionRecurrencesById", "parameters": [{"allowEmptyValue": true, "description": "SessionID is the snowflake ID of the session\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "FilterStates is a comma separated list of states to filter by\n", "explode": true, "in": "query", "name": "filterStates", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/shared.SessionDTO"}, "type": "array"}}, "required": ["sessions"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionRecurrencesById returns a list of recurrences for the passed\nsessionID, can optionally search for specific\n"}}, "/meetings.GetSessionUsersBySessionID": {"get": {"operationId": "GET:meetings.GetSessionUsersBySessionID", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessionUsers": {"items": {"$ref": "#/components/schemas/api.SessionUserDTO"}, "type": "array"}}, "required": ["sessionUsers"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.GetUserByID": {"post": {"operationId": "POST:meetings.GetUserByID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"userID": {"type": "string"}}, "required": ["userID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"title": "the about section of the user\n", "type": "string"}, "appleID": {"title": "the apple ID of the user if they signed up with apple\n", "type": "string"}, "avatar": {"title": "the avatar of the user\n", "type": "string"}, "createdAt": {"format": "int64", "title": "the timestamp when the user was created\n", "type": "integer"}, "customerID": {"title": "the customer ID of the user\n", "type": "string"}, "email": {"title": "the email of the user\n", "type": "string"}, "fingerprint": {"title": "the fingerprint of the user\n", "type": "object"}, "firstName": {"title": "the first name of the user\n", "type": "string"}, "googleID": {"title": "the google ID of the user if they signed up with google\n", "type": "string"}, "id": {"title": "the ID of the user\n", "type": "string"}, "isTestUser": {"title": "whether the user is a test user\n", "type": "boolean"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "lobbies": {"items": {"$ref": "#/components/schemas/shared.LobbyDTO"}, "title": "Lobbies any lobby associated with the user\n", "type": "array"}, "marketingOptIn": {"title": "whether the user opted in to marketing\n", "type": "boolean"}, "nftAvatar": {"$ref": "#/components/schemas/shared.UserCryptoToken"}, "notificationSettings": {"$ref": "#/components/schemas/shared.NotificationSettings"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "roleIDs": {"items": {"type": "string"}, "title": "the role IDs of the user\n", "type": "array"}, "signUpTimestamp": {"format": "int64", "title": "the timestamp when the user signed up\n", "type": "integer"}, "social": {"$ref": "#/components/schemas/shared.Socials"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTO"}, "team": {"$ref": "#/components/schemas/shared.TeamWithInlineRelationsDTO"}, "timezone": {"title": "the timezone of the user\n", "type": "string"}, "updatedAt": {"format": "int64", "title": "the timestamp when the user was last updated\n", "type": "integer"}}, "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetUserByID gets user details by ID\n"}}, "/meetings.GetUserByIDWithRelations": {"post": {"operationId": "POST:meetings.GetUserByIDWithRelations", "requestBody": {"content": {"application/json": {"schema": {"properties": {"userID": {"type": "string"}}, "required": ["userID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.JoinLobbyAsGuest": {"post": {"operationId": "POST:meetings.JoinLobbyAsGuest", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the unique ID of the lobby\n", "type": "string"}, "participant": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}}, "required": ["lobbyID", "participant"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Join<PERSON><PERSON>byAsGuest adds a guest to the lobby and publishes the event to the lobby\nlisteners\n"}}, "/meetings.ListSessionsByLobbyID": {"post": {"operationId": "POST:meetings.ListSessionsByLobbyID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"active": {"type": "boolean"}, "lobbyID": {"type": "string"}}, "required": ["lobbyID", "active"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/shared.SessionDTO"}, "type": "array"}}, "required": ["sessions"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.RemoveGuestFromLobby": {"post": {"operationId": "POST:meetings.RemoveGuestFromLobby", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the unique ID of the lobby\n", "type": "string"}, "participantID": {"title": "ParticipantID is the unique ID of the participant to remove\n", "type": "string"}}, "required": ["lobbyID", "participantID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "RemoveGuestFrom<PERSON><PERSON>by removes a guest from the lobby and publishes the event to\nthe lobby listeners\n"}}, "/meetings.RevokeAccessSessionUser": {"post": {"operationId": "POST:meetings.RevokeAccessSessionUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"accessRuleId": {"type": "string"}}, "required": ["accessRuleId"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.UpsertMeetingSuggestions": {"post": {"operationId": "POST:meetings.UpsertMeetingSuggestions", "requestBody": {"content": {"application/json": {"schema": {"properties": {"recurrenceID": {"type": "string"}, "sessionID": {"type": "string"}, "suggestions": {"items": {"$ref": "#/components/schemas/api.MeetingSuggestionInput"}, "type": "array"}}, "required": ["sessionID", "recurrenceID", "suggestions"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"count": {"format": "int64", "type": "integer"}}, "required": ["count"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UpsertMeetingSuggestions stores or updates meeting suggestions for a session\n(called by Nebula)\n"}}, "/meetings.UpsertSessionUser": {"post": {"operationId": "POST:meetings.UpsertSessionUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"fingerprint": {"items": {"type": "string"}, "type": "array"}, "guestEmail": {"type": "string"}, "guestFullName": {"type": "string"}, "guestSurrogateID": {"format": "int64", "type": "integer"}, "isViewerAccessRevoked": {"type": "boolean"}, "joined": {"type": "boolean"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "userEmail": {"type": "string"}, "userID": {"type": "string"}}, "required": ["userEmail", "userID", "sessionID", "sessionRecurrenceID", "fingerprint", "roleIDs", "isViewerAccessRevoked", "joined", "guestSurrogate<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guestEmail"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.CleanupRecallUsers": {"get": {"description": "This prevents us from having tons of users with no connections on the recall side.\n", "operationId": "GET:transcriptions.CleanupRecallUsers", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "CleanupRecallUsers is a cron job that removes users from Recall that have no\nconnections.\n"}, "post": {"description": "This prevents us from having tons of users with no connections on the recall side.\n", "operationId": "POST:transcriptions.CleanupRecallUsers", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "CleanupRecallUsers is a cron job that removes users from Recall that have no\nconnections.\n"}}, "/transcriptions.CreateEndOfSessionTranscriptsBatch": {"get": {"description": "elio/livekit/session-consumer.go#L74\n", "operationId": "GET:transcriptions.CreateEndOfSessionTranscriptsBatch", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "meetingType", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Private endpoint to be called from the session ended event handler in\n"}}, "/transcriptions.GenerateAudioIngressKey": {"post": {"operationId": "POST:transcriptions.GenerateAudioIngressKey", "requestBody": {"content": {"application/json": {"schema": {"properties": {"participant": {"$ref": "#/components/schemas/shared.ParticipantMetadata"}, "session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["participant", "session"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"ingressWebsocketURL": {"type": "string"}, "key": {"title": "Key can be used to authenticate ingress calls\n", "type": "string"}}, "required": ["key", "ingressWebsocketURL"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GenerateAudioIngress<PERSON>ey represents a key used to authenticate audio ingress\n"}}, "/transcriptions.GetBatchIDs": {"get": {"operationId": "GET:transcriptions.GetBatchIDs", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"data": {"items": {"type": "string"}, "type": "array"}}, "required": ["data"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.GetSpeakersAsSessionGuestsBySessionID": {"get": {"operationId": "GET:transcriptions.GetSpeakersAsSessionGuestsBySessionID", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"guests": {"items": {"$ref": "#/components/schemas/shared.GuestDTO"}, "type": "array"}}, "required": ["guests"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.GetTranscriptionBatch": {"get": {"operationId": "GET:transcriptions.GetTranscriptionBatch", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "batchID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"Transcriptions": {"items": {"$ref": "#/components/schemas/shared.TranscriptionDTO"}, "type": "array"}}, "required": ["Transcriptions"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.ProcessBotRecording": {"post": {"operationId": "POST:transcriptions.ProcessBotRecording", "requestBody": {"content": {"application/json": {"schema": {"properties": {"bot_id": {"type": "string"}, "recurrence_id": {"type": "string"}, "session_id": {"type": "string"}, "video_url": {"type": "string"}}, "required": ["video_url", "session_id", "recurrence_id", "bot_id"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.RemoveUsersFromRecallAccount": {"post": {"description": "We need to take into account that the delete user endpoint is rate limited to 10 request per minute.\n", "operationId": "POST:transcriptions.RemoveUsersFromRecallAccount", "requestBody": {"content": {"application/json": {"schema": {"properties": {"exceptions": {"items": {"type": "string"}, "title": "Exceptions List of external user IDs to exclude from deletion\n", "type": "array"}, "recallCustomAPIKey": {"title": "RecallCustomAPIKey valid Recall API key\n", "type": "string"}}, "required": ["recallCustomAPIKey", "exceptions"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "RemoveUsersFromRecallAccount Remove all users from the token passed in the\nparams.\n"}}, "/transcriptions.Update": {"post": {"operationId": "POST:transcriptions.Update", "requestBody": {"content": {"application/json": {"schema": {"properties": {"offTheRecord": {"type": "boolean"}, "recurrenceID": {"type": "string"}, "sessionID": {"type": "string"}}, "required": ["offTheRecord", "sessionID", "recurrenceID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/users": {"get": {"operationId": "GET:meetings.ListUsers", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderBy", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderDirection", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "email", "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"offsetPagination": {"$ref": "#/components/schemas/shared.OffsetPaginationResponse"}, "users": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}}, "required": ["offsetPagination", "users"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:meetings.CreateUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "avatar": {"type": "string"}, "fingerprint": {"type": "string"}, "marketingOptIn": {"type": "boolean"}, "paddleCustomerID": {"type": "string"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "timezone": {"type": "string"}, "userEmail": {"type": "string"}, "userFirstName": {"type": "string"}, "userLastName": {"type": "string"}}, "required": ["userEmail", "userFirstName", "userLastName", "roleIDs"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"userID": {"type": "string"}}, "required": ["userID"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/ai-feed/event": {"post": {"operationId": "POST:meetings.AIFeedEvent", "requestBody": {"content": {"application/json": {"schema": {"properties": {"aiFeedID": {"format": "int64", "type": "integer"}, "eventType": {"$ref": "#/components/schemas/api.AIFeedEventType"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["eventType", "aiFeedID", "sessionID", "sessionRecurrenceID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/ai-feed/get": {"get": {"operationId": "GET:meetings.GetAIFeed", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/calendar/ms_oauth_callback": {"delete": {"operationId": "DELETE:transcriptions.MicrosoftRedirectOfRedirect", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed redirect back to recall\n"}, "get": {"operationId": "GET:transcriptions.MicrosoftRedirectOfRedirect", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed redirect back to recall\n"}, "head": {"operationId": "HEAD:transcriptions.MicrosoftRedirectOfRedirect", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed redirect back to recall\n"}, "patch": {"operationId": "PATCH:transcriptions.MicrosoftRedirectOfRedirect", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed redirect back to recall\n"}, "post": {"operationId": "POST:transcriptions.MicrosoftRedirectOfRedirect", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed redirect back to recall\n"}, "put": {"operationId": "PUT:transcriptions.MicrosoftRedirectOfRedirect", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Needed redirect back to recall\n"}}, "/v1.0/chat/token": {"get": {"operationId": "GET:meetings.GetChatToken", "parameters": [{"allowEmptyValue": true, "description": "The session ID of the user\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "The session recurrence ID of the user\n", "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "data": {"properties": {"channelRole": {"$ref": "#/components/schemas/api.ChannelRole"}, "chatRoomId": {"title": "The ID of the chat room\n", "type": "string"}, "token": {"title": "The chat token\n", "type": "string"}}, "required": ["token", "channelRole", "chatRoomId"], "title": "The data returned by the API\n", "type": "object"}, "message": {"title": "A message from the API\n", "type": "string"}, "success": {"title": "Whether the code is a success\n", "type": "boolean"}}, "required": ["message", "success", "code", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/meetings/{sessionID}/{recurrenceID}/suggestions/user": {"get": {"operationId": "GET:meetings.GetMeetingSuggestionsByUser", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"suggestions": {"items": {"$ref": "#/components/schemas/api.SuggestionResponseDTO"}, "type": "array"}, "total": {"format": "int64", "type": "integer"}}, "required": ["suggestions", "total"], "type": "object"}, "message": {"type": "string"}}, "required": ["data", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user\n"}}, "/v1.0/post-session-summaries/by-session-ids/{sessionID}": {"delete": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "DELETE:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "get": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "GET:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "head": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "HEAD:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "patch": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "PATCH:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "post": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "POST:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "put": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "PUT:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}}, "/v1.0/recurrences/past": {"get": {"operationId": "GET:meetings.GetPastSessionRecurrences", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "summaAI", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/session/transcriptions": {"get": {"operationId": "GET:transcriptions.Download", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions": {"post": {"operationId": "POST:meetings.CreateSession", "parameters": [{"allowEmptyValue": true, "description": "This should match the old request payload from Mars:\n", "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"type": "string"}, "avatar": {"type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "category": {"type": "string"}, "cover": {"type": "string"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "isPubliclyVisible": {"type": "boolean"}, "isViewerAccessRestricted": {"type": "boolean"}, "lobbyID": {"title": "Optional\n", "type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "sessionSettings": {"type": "object"}, "sessionState": {"type": "string"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "userID": {"title": "Optional, specify the userID to create the session for\n", "type": "string"}, "userMeetingType": {"$ref": "#/components/schemas/api.UserMeetingTypeRequestDTO"}, "viewerAccessRules": {"items": {"$ref": "#/components/schemas/api.SessionAccessRulesDTO"}, "type": "array"}}, "required": ["about", "accessStatus", "avatar", "calendarEventEditURL", "calendarID", "calendarType", "category", "cover", "dataVisibility", "endTimestamp", "meetingType", "ogMetadata", "sessionSettings", "startTimestamp", "sessionState", "sessionTags", "sessionTitle", "userMeetingType", "viewerAccessRules", "isPubliclyVisible", "isViewerAccessRestricted", "userID", "lobbyID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access": {"post": {"operationId": "POST:meetings.UpdateSessionAccessControlRules", "requestBody": {"content": {"application/json": {"schema": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/api.UpdateAccessRule"}, "type": "array"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "accessRules"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/add": {"put": {"operationId": "PUT:meetings.AddSessionAccessControlRules", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"domains": {"items": {"type": "string"}, "type": "array"}, "emails": {"items": {"type": "string"}, "type": "array"}, "message": {"type": "string"}, "overrideRules": {"type": "boolean"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "viewerAccessControl": {"items": {"$ref": "#/components/schemas/api.AccessControlItem"}, "type": "array"}}, "required": ["domains", "emails", "sessionID", "sessionRecurrenceID", "message", "overrideRules", "viewerAccessControl"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/shared.SessionAccessRuleDTO"}, "type": "array"}}, "required": ["accessRules"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AddSessionAccessControlRules is now deprecated, please use\nUpdateSessionAccessControlRules (later down in this file)\n"}}, "/v1.0/sessions/access/get-in-review-access-requests-count": {"post": {"operationId": "POST:meetings.CountInReviewRequests", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/api.SessionRequestCount"}, "type": "array"}}, "required": ["sessions"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.SessionRequestCount"}, "type": "array"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/grouped": {"get": {"operationId": "GET:meetings.GetAccessRequestsBraidable", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/in-review": {"get": {"operationId": "GET:meetings.GetInReviewBraidable", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead\n"}}, "/v1.0/sessions/access/remove": {"delete": {"operationId": "DELETE:meetings.RemoveSessionAccessControlRules", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/request": {"put": {"operationId": "PUT:meetings.ApproveOrDenyedSessionAccessRequest", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"callTrixtaAction": {"type": "boolean"}, "id": {"type": "string"}, "restrictionStatus": {"type": "string"}}, "required": ["callTrixtaAction", "id", "restrictionStatus"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}, "sessionId": {"type": "string"}}, "required": ["sessionId", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "ApproveOrDenyedSessionAccessRequest is deprecated and should not be used.\n"}}, "/v1.0/sessions/access/request-access": {"post": {"operationId": "POST:meetings.CreateAccessRequest", "requestBody": {"content": {"application/json": {"schema": {"properties": {"requestMessage": {"type": "string"}, "sessionID": {"type": "string"}}, "required": ["sessionID", "requestMessage"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/future": {"get": {"operationId": "GET:meetings.GetFutureSessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/guest": {"post": {"operationId": "POST:meetings.LoginGuestUserWithSession", "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"title": "optional email of the guest user\n", "type": "string"}, "fullName": {"title": "optional full name of the guest user\n", "type": "string"}, "sessionID": {"title": "session ID\n", "type": "string"}}, "required": ["email", "fullName", "sessionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "data": {"properties": {"authToken": {"title": "the JWT token for the user\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}}, "required": ["authToken", "refreshToken"], "title": "the response data\n", "type": "object"}, "message": {"type": "string"}}, "required": ["code", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/library/upcoming": {"get": {"description": "Deprecated: Use GetFutureSessions instead\n", "operationId": "GET:meetings.GetLibraryUpcomingSessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Get future sessions\n"}}, "/v1.0/sessions/meeting-memory": {"get": {"description": "Deprecated: Use GetPastSessionRecurrences instead, with SummaAI=true Remove once the FE is updated to use GetPastSessionRecurrences\n", "operationId": "GET:meetings.GetMeetingMemorySessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetMeetingMemorySessions returns all past sessions that have summaAI enabled\n"}}, "/v1.0/sessions/past": {"get": {"description": "Deprecated: Use GetPastSessionRecurrences instead Remove once the FE is updated to use GetPastSessionRecurrences\n", "operationId": "GET:meetings.GetPastSessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetPastSessions returns all past sessions\n"}}, "/v1.0/sessions/presence/{id}": {"get": {"operationId": "GET:meetings.Presence", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/users/{sessionID}/{recurrenceID}": {"get": {"operationId": "GET:meetings.GetSessionUsers", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.SessionDefaultUsersDTO"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/{sessionID}/recurrence/{recurrenceID}": {"patch": {"operationId": "PATCH:meetings.UpdateSession", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"type": "string"}, "dataVisibility": {"type": "string"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "isPubliclyVisible": {"type": "boolean"}, "primaryHostUserID": {"type": "string"}, "sessionCategory": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"title": "Title sessionTitle is the field name from the FE\n", "type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"properties": {"id": {"type": "string"}}, "required": ["id"], "type": "object"}}, "required": ["startTimestamp", "endTimestamp", "startedAt", "endedAt", "sessionTitle", "about", "sessionState", "sessionSettings", "sessionCategory", "sessionTags", "primaryHostUserID", "isPubliclyVisible", "accessStatus", "dataVisibility", "userMeetingType"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"Code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "Message": {"type": "string"}, "data": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}, "required": ["Code", "Message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur": {"patch": {"operationId": "PATCH:meetings.RecurSessionNoRequest", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur-at": {"patch": {"operationId": "PATCH:meetings.RecurSession", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-client-info", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"endTimestamp": {"format": "int64", "type": "integer"}, "startTimestamp": {"format": "int64", "type": "integer"}}, "required": ["startTimestamp", "endTimestamp"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/audio-ingress": {"get": {"operationId": "GET:transcriptions.AudioIngressWebsocket", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AudioIngressWebsocket handles the websocket connection for raw pcm audio from\nLivekit/other sources\n"}}, "/v1.0/transcriptions/bot/calendar-user": {"get": {"operationId": "GET:transcriptions.GetCalendarUser", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"connections": {"items": {"$ref": "#/components/schemas/recallai.RecallConnection"}, "type": "array"}, "external_id": {"type": "string"}, "id": {"type": "string"}, "preferences": {"$ref": "#/components/schemas/recallai.RecordingPreferences"}}, "required": ["id", "external_id", "connections", "preferences"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/bot/create-for-meeting": {"post": {"operationId": "POST:transcriptions.CreateBotForMeeting", "requestBody": {"content": {"application/json": {"schema": {"properties": {"meetingLink": {"type": "string"}}, "required": ["meetingLink"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"calendar_meetings": {"items": {"$ref": "#/components/schemas/recallai.CalendarMeeting"}, "type": "array"}, "id": {"type": "string"}, "meeting_metadata": {"$ref": "#/components/schemas/recallai.MeetingMetadata"}, "meeting_participants": {"items": {"$ref": "#/components/schemas/recallai.MeetingParticipant"}, "type": "array"}, "meeting_url": {"$ref": "#/components/schemas/recallai.BotMeetingLink"}, "video_url": {"type": "string"}}, "required": ["id", "meeting_url", "meeting_metadata", "meeting_participants", "calendar_meetings", "video_url"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/bot/disconnect": {"post": {"operationId": "POST:transcriptions.DisconnectCalendar", "requestBody": {"content": {"application/json": {"schema": {"properties": {"platform": {"title": "Should be the string 'google' or 'microsoft'\n", "type": "string"}}, "required": ["platform"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Disconnect a user's calendar platform from our Rumi bots recall.ai account\n"}}, "/v1.0/transcriptions/bot/google-auth": {"get": {"operationId": "GET:transcriptions.GenerateBotGoogleAuthURL", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"authURL": {"type": "string"}}, "required": ["authURL"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GenerateBotAuthURL Generate a URL for the user to authenticate with Google\nCalendar.\n"}}, "/v1.0/transcriptions/bot/microsoft-auth": {"get": {"operationId": "GET:transcriptions.GenerateBotMicrosoftAuthURL", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"authURL": {"type": "string"}}, "required": ["authURL"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GenerateBotAuthURL Generate a URL for the user to authenticate with Microsoft\nCalendar.\n"}}, "/v1.0/transcriptions/bot/webhook": {"post": {"operationId": "POST:transcriptions.RecallTranscriptionsWebhook", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-id", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-timestamp", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-signature", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "token", "required": true, "schema": {"type": "string"}, "style": "form"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookData"}, "event": {"type": "string"}}, "required": ["event", "data"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/speakers": {"get": {"operationId": "GET:transcriptions.FetchHeardSpeakers", "parameters": [{"allowEmptyValue": true, "description": "SessionID is the session for which we want to fetch heard speakers\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "RecurrenceID is the recurrence for which we want to fetch heard speakers\n", "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "IncludeSnippets determines whether to include speaker snippets in the response, defaults to false\n", "explode": true, "in": "query", "name": "includeSnippets", "required": true, "schema": {"type": "boolean"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"speakers": {"items": {"$ref": "#/components/schemas/api.HeardSpeaker"}, "title": "Speakers is the list of speakers heard in the session\n", "type": "array"}}, "required": ["speakers"], "title": "Data is the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "FetchHeardSpeakers fetches array of speakers heard in a session\n"}, "post": {"operationId": "POST:transcriptions.IdentifyHeardSpeakers", "requestBody": {"content": {"application/json": {"schema": {"properties": {"identifiedSpeakers": {"items": {"$ref": "#/components/schemas/api.IdentifiedSpeaker"}, "title": "IdentifiedSpeakers is the list of speakers to identify\n", "type": "array"}, "recurrenceID": {"title": "RecurrenceID is the recurrence for which we want to identify speakers\n", "type": "string"}, "sessionID": {"title": "SessionID is the session for which we want to identify speakers\n", "type": "string"}}, "required": ["sessionID", "recurrenceID", "identifiedSpeakers"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "IdentifyHeardSpeakers identifies an array of speakers heard in a session\n"}}, "/v1.0/users/id/{userID}": {"put": {"operationId": "PUT:meetings.UpdateUserByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"userData": {"$ref": "#/components/schemas/api.UpdateUserRequestData"}}, "required": ["userData"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UpdateUserByID updates user details by their ID\n"}}, "/v1.0/users/id/{userID}/payment-method-details": {"get": {"operationId": "GET:meetings.GetUserPaymentMethodDetails", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.GetUserPaymentMethodDetailsResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/id/{userID}/plan": {"get": {"operationId": "GET:meetings.GetUserPlanByUserID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/id/{userID}/transactions": {"get": {"operationId": "GET:meetings.ListUserTransactions", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "includeInvoices", "schema": {"type": "boolean"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.ListUserTransactionsResponseData"}, "type": "array"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/id/{userID}/update-payment-method-transaction": {"get": {"operationId": "GET:meetings.GetUpdateUserPaymentMethodTransaction", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/shared.BillingTransactionDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xray-templates": {"get": {"operationId": "GET:xray.ListXRayTemplates", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "type_filter", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sort_by", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListXRayTemplatesResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xray-templates/{templateID}": {"get": {"operationId": "GET:xray.GetXRayTemplate", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "templateID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.XRayTemplateDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays": {"get": {"operationId": "GET:xray.ListXRays", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "type_filter", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sort_by", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListXRaysResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:xray.Create", "requestBody": {"content": {"application/json": {"schema": {"properties": {"alertChannels": {"additionalProperties": {"type": "boolean"}, "title": "TODO: Revisit this, probably shouldn't be map[string]bool\n", "type": "object"}, "description": {"type": "string"}, "frequency": {"type": "string"}, "icon": {"type": "string"}, "prompt": {"type": "string"}, "shortSummary": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string"}}, "required": ["description", "type", "prompt", "title", "icon", "shortSummary", "frequency", "alertChannels"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.XRayDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/generate/info": {"post": {"operationId": "POST:xray.GenerateXRayInfo", "requestBody": {"content": {"application/json": {"schema": {"properties": {"prompt": {"type": "string"}, "type": {"type": "string"}}, "required": ["type", "prompt"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.GenerateXRayInfoResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/generate/prompt": {"post": {"operationId": "POST:xray.GenerateXRayPrompt", "requestBody": {"content": {"application/json": {"schema": {"properties": {"description": {"type": "string"}}, "required": ["description"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.GenerateXRayPromptResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/id/{xrayID}": {"get": {"operationId": "GET:xray.GetXRay", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "xrayID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.XRayDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "patch": {"operationId": "PATCH:xray.UpdateXRay", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "xrayID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"alertChannels": {"additionalProperties": {"type": "boolean"}, "type": "object"}, "frequency": {"type": "string"}, "icon": {"type": "string"}, "isActive": {"type": "boolean"}, "prompt": {"type": "string"}, "title": {"type": "string"}}, "required": ["title", "icon", "prompt", "alertChannels", "isActive", "frequency"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.XRayDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/id/{xrayID}/notifications": {"get": {"operationId": "GET:xray.GetXRayNotifications", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "xrayID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.GetXRayNotificationsResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/id/{xrayID}/notifications/mark-seen": {"patch": {"operationId": "PATCH:xray.MarkXRayNotificationsSeen", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "xrayID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.MarkXRayNotificationsSeenResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/id/{xrayID}/share": {"post": {"operationId": "POST:xray.ShareXRayAsTemplate", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "xrayID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.XRayDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/xrays/{xrayID}": {"delete": {"operationId": "DELETE:xray.DeleteXRay", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "xrayID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v3.0/sessions/{sessionID}/recurrences/{recurrenceID}": {"get": {"description": "It listens for updates on the session and in-review topics to refresh the session and access request data. When recurrenceID is \"latest\", it fetches the latest recurrence ID of the session.\n", "operationId": "GET:meetings.GetSessionRecurrenceByIdBraidableWithAccess", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session\nand access DTO if any access rule is associated with the authenticated user.\n"}}}, "servers": [{"description": "Encore local dev environment", "url": "http://localhost:4000"}]}