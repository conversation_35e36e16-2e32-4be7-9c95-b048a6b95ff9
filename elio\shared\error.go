package shared

import (
	"errors"
	"fmt"
	"net/http"

	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"encore.dev/storage/sqldb"
	"github.com/tidwall/gjson"
)

// TODO rename this to ErrorCodeID
type HttpErrorCode int

const (
	Unauthorized                         HttpErrorCode = 2000
	ValidationErrorCode                  HttpErrorCode = 2001
	HttpSuccessCode                      HttpErrorCode = 200
	UserNotFound                         HttpErrorCode = 2009
	UnauthorizedToAccessSessionErrorCode HttpErrorCode = 2076
	SessionNotFoundErrorCode             HttpErrorCode = 2034
	OffTheRecordError                    HttpErrorCode = 2501
	OffTheRecordAlreadyDisabled          HttpErrorCode = 2503
	OffTheRecordAlreadyEnabled           HttpErrorCode = 2504
	SessionUpdatedSuccessfully           HttpErrorCode = 2064
	SessionAccessRequestedSuccessfully   HttpErrorCode = 2082
	SessionAccessRequestedAlready        HttpErrorCode = 2088
	UserAddedToChatRoom                  HttpErrorCode = 2132
	UnauthorizedToAccessUnlockedSession  HttpErrorCode = 2133
	UnauthorizedToAccessLockedSession    HttpErrorCode = 2134
	InternalServerError                  HttpErrorCode = 2004
	RecurActiveMeetingErrorCode          HttpErrorCode = 2135
	GuestUserCreatedWithGrantedAccess    HttpErrorCode = 2136
	GuestUserCreatedWithInReviewRequest  HttpErrorCode = 2137
	GuestUSerCreatedWithoutAccessRequest HttpErrorCode = 2138
	UnauthorizedToAccessInactiveSession  HttpErrorCode = 2139
	InvalidSessionStateUpdate            HttpErrorCode = 2073
	InvalidSessionVisibilityUpdate       HttpErrorCode = 2074
	FailedToRecurSession                 HttpErrorCode = 2035
	DuplicateLobbySlugErrorCode          HttpErrorCode = 2140
	LobbyLimitErrorCode                  HttpErrorCode = 2141
	PostSessionSummaryPending            HttpErrorCode = 2142
	PostSessionSummaryNotFound           HttpErrorCode = 2143
	TeamNotFound                         HttpErrorCode = 2133
	TeamOwnerNotFound                    HttpErrorCode = 2134
	TeamMemberNotFound                   HttpErrorCode = 2135
	TeamAlreadyHasOwner                  HttpErrorCode = 2144
	TeamAlreadyHasMemberWithSameUserID   HttpErrorCode = 2143
	PaddleCustomerDoesNotExist           HttpErrorCode = 2120
	UserPaymentDetailsNotFound           HttpErrorCode = 2121
	UserSubscriptionPlanNotFound         HttpErrorCode = 2118
	UserAlreadyExists                    HttpErrorCode = 2005
)

var ErrorMessages = map[HttpErrorCode]string{
	Unauthorized:                         "Unauthorized",
	ValidationErrorCode:                  "Validation error",
	HttpSuccessCode:                      "Success",
	UserNotFound:                         "User not found",
	UnauthorizedToAccessSessionErrorCode: "Unauthorized to access session",
	SessionUpdatedSuccessfully:           "Successfully updated session",
	SessionNotFoundErrorCode:             "Session not found",
	OffTheRecordError:                    "Off the record error",
	OffTheRecordAlreadyDisabled:          "Off the record already disabled",
	OffTheRecordAlreadyEnabled:           "Off the record already enabled",
	UserAddedToChatRoom:                  "User added to chat room",
	UnauthorizedToAccessUnlockedSession:  "Unauthorized access unlocked session",
	UnauthorizedToAccessLockedSession:    "Unauthorized access Locked session",
	InternalServerError:                  "Internal server error",
	RecurActiveMeetingErrorCode:          "Cannot recur active meeting",
	GuestUserCreatedWithGrantedAccess:    "Guest user created with granted access",
	GuestUserCreatedWithInReviewRequest:  "Guest user created with in review request",
	GuestUSerCreatedWithoutAccessRequest: "Guest user created without access request",
	UnauthorizedToAccessInactiveSession:  "Unauthorized to access inactive session",
	InvalidSessionStateUpdate:            "Invalid session state update",
	InvalidSessionVisibilityUpdate:       "Session's visibility can only be changed for private sessions",
	FailedToRecurSession:                 "Failed to recur session to next occurrence",
	DuplicateLobbySlugErrorCode:          "Duplicate lobby slug",
	LobbyLimitErrorCode:                  "Lobby limit reached for this user",
	PostSessionSummaryPending:            "Post session summary is pending",
	PostSessionSummaryNotFound:           "Post session summary not found",
	TeamAlreadyHasOwner:                  "Team already has an owner",
	PaddleCustomerDoesNotExist:           "Paddle customer does not exist",
	UserPaymentDetailsNotFound:           "User payment details not found",
	UserSubscriptionPlanNotFound:         "User subscription plan not found",
	UserAlreadyExists:                    "User already exists",
}

// ErrorRPCCode maps our custom HTTP error codes to Encore's RPC error codes
var ErrorRPCCode = map[HttpErrorCode]errs.ErrCode{
	Unauthorized:                         errs.PermissionDenied,
	ValidationErrorCode:                  errs.InvalidArgument,
	UnauthorizedToAccessSessionErrorCode: errs.PermissionDenied,
	SessionNotFoundErrorCode:             errs.NotFound,
	OffTheRecordAlreadyDisabled:          errs.FailedPrecondition,
	OffTheRecordAlreadyEnabled:           errs.FailedPrecondition,
	UserNotFound:                         errs.Internal,
	UnauthorizedToAccessUnlockedSession:  errs.PermissionDenied,
	UnauthorizedToAccessLockedSession:    errs.PermissionDenied,
	InternalServerError:                  errs.Internal,
	RecurActiveMeetingErrorCode:          errs.FailedPrecondition,
	UnauthorizedToAccessInactiveSession:  errs.PermissionDenied,
	InvalidSessionStateUpdate:            errs.FailedPrecondition,
	InvalidSessionVisibilityUpdate:       errs.FailedPrecondition,
	FailedToRecurSession:                 errs.FailedPrecondition,
	DuplicateLobbySlugErrorCode:          errs.FailedPrecondition,
	LobbyLimitErrorCode:                  errs.FailedPrecondition,
}

type ValidationTypes string

const (
	ValidationRequired     ValidationTypes = "validation.required"
	ValidationAlphaNumeric ValidationTypes = "validation.alphanumeric"
	ValidationMinlength    ValidationTypes = "validation.minlength"
)

type ValidationErrorDetail struct {
	Message string            `json:"message"`
	Path    []string          `json:"path"`
	Context map[string]string `json:"context"`
}

func NewFieldValidationError(fieldName string, validation ValidationTypes, context map[string]string) ValidationErrorDetail {
	if context == nil {
		context = make(map[string]string)
	}
	context["label"] = fieldName
	return ValidationErrorDetail{
		Message: string(validation),
		Path:    []string{fieldName},
		Context: context,
	}
}

type CodeDetails struct {
	Code    int                     `json:"code,omitempty"`
	ErrCode HttpErrorCode           `json:"errCode"`
	Message string                  `json:"message"`
	Errors  []ValidationErrorDetail `json:"errors"` // Errors Any validation errors that should be presented on the frontend
}

func (e CodeDetails) ErrDetails() {}

type ErrBuilder struct {
	*errs.Builder
}

type SQLStateError string

const (
	// SQLUniqueViolation is the SQLState error code for a duplicate key existing
	SQLUniqueViolation SQLStateError = "23505"
	// SQLFKViolation is the SQLState error code when a foreign key is missing or invalid
	SQLFKViolation SQLStateError = "23503"
	SQLNoRows      SQLStateError = "sql: no rows in result set"
)

func IsSQLErr(err error, code SQLStateError) bool {
	type checker interface {
		SQLState() string
	}
	if e, ok := err.(checker); ok {
		return e.SQLState() == string(code)
	}
	if errors.Is(err, sqldb.ErrNoRows) {
		if code == SQLNoRows {
			return true
		}
	}
	return false
}

func B() *ErrBuilder {
	return &ErrBuilder{errs.B()}
}

func (b *ErrBuilder) ErrorID(c HttpErrorCode) *ErrBuilder {
	if rpcCode, found := ErrorRPCCode[c]; found {
		b.Code(rpcCode)
	} else {
		b.Code(errs.OK)
	}
	b.Msg(ErrorMessages[c])
	b.Details(newCodeDetails(c))
	return b
}

func (b *ErrBuilder) ErrorCodeMsg(c HttpErrorCode, message string) *ErrBuilder {
	if rpcCode, found := ErrorRPCCode[c]; found {
		b.Code(rpcCode)
	} else {
		b.Code(errs.OK)
	}
	b.Msg(ErrorMessages[c])
	details := newCodeDetails(c)
	details.Message = message
	b.Details(details)
	return b
}

func (b *ErrBuilder) ValidationErrs(validationErrorDetails []ValidationErrorDetail) *ErrBuilder {
	c := ValidationErrorCode
	if rpcCode, found := ErrorRPCCode[c]; found {
		b.Code(rpcCode)
	} else {
		b.Code(errs.OK)
	}
	b.Msg(ErrorMessages[c])
	details := newCodeDetails(c)
	details.Errors = validationErrorDetails
	b.Details(details)
	return b
}

func newCodeDetails(c HttpErrorCode) CodeDetails {
	return CodeDetails{
		ErrCode: c,
		Message: ErrorMessages[c],
	}
}

func (b *ErrBuilder) Meta(metaPairs ...interface{}) *ErrBuilder {
	b.Builder.Meta(metaPairs...)
	return b
}

func (b *ErrBuilder) Cause(err error) *ErrBuilder {
	b.Builder.Cause(err)
	return b
}

func (b *ErrBuilder) WithCode(c HttpErrorCode) error {
	return b.ErrorID(c).Err()
}

// WithValidation when validationErrorDetails is empty nil is returned
func (b *ErrBuilder) WithValidation(validationErrorDetails []ValidationErrorDetail) error {
	if len(validationErrorDetails) == 0 {
		return nil
	}
	return b.ValidationErrs(validationErrorDetails).Err()
}

func LogErrorHttpResponse(err error, message string, httpErrorCode HttpErrorCode, statusCode int) error {
	rlog.Error(message, "err", err)
	if statusCode == http.StatusInternalServerError {
		return HttpResponseError("Internal server error", httpErrorCode, statusCode)
	}
	return HttpResponseError(message, httpErrorCode, statusCode)
}

func HttpResponseError(message string, httpErrorCode HttpErrorCode, statusCode int) error {
	// resp := HttpErrorResp{}
	// resp.Success = false
	resp := errs.B()
	resp.Details(HttpErrorDetail{
		Message:    message,
		ErrCode:    httpErrorCode,
		StatusCode: statusCode,
	})
	resp.Msg(message)
	resp.Code(errs.Unknown)
	if statusCode == 404 {
		resp.Code(errs.NotFound)
	}
	if statusCode >= 400 && statusCode < 500 {
		resp.Code(errs.InvalidArgument)
	}
	if statusCode == 401 {
		resp.Code(errs.Unauthenticated)
	}
	if statusCode >= 500 {
		resp.Code(errs.Internal)
	}
	return resp.Err()
}

func ErrFromMarsResponse(response []byte) *HttpErrorResp {
	name := gjson.Get("error.name", string(response)).String()
	message := gjson.Get("error.message", string(response)).String()
	statusCode := gjson.Get("error.statusCode", string(response)).Int()
	errCode := gjson.Get("error.errCode", string(response)).Int()
	success := gjson.Get("success", string(response)).Bool()
	return &HttpErrorResp{
		Code:    int(statusCode),
		ErrCode: HttpErrorCode(errCode),
		Message: message,
		Success: success,
		ErrorDetail: HttpErrorDetail{
			Name:       name,
			Message:    message,
			StatusCode: int(statusCode),
			ErrCode:    HttpErrorCode(errCode),
		},
	}
}

// HttpErrorResp Defined as per Notion spec: https://www.notion.so/waitroom/Rest-API-Spec-including-errors-327d22766ee4474e8bd847006a9dd994
type HttpErrorResp struct {
	// Code is the error code to return.
	Code    int           `json:"code,omitempty"`
	ErrCode HttpErrorCode `json:"errCode,omitempty"`
	// Message is a descriptive message of the error.
	Message string `json:"message"`
	// Details are user-defined additional details.
	Details errs.ErrDetails `json:"details"`
	// Meta are arbitrary key-value pairs for use within
	// the Encore application. They are not exposed to external clients.
	Meta        errs.Metadata   `json:"-"`
	Success     bool            `json:"success"`
	ErrorDetail HttpErrorDetail `json:"error"`
	// Errors is a list of validation errors.
	Errors []ValidationError `json:"errors,omitempty"`
	Data   interface{}       `json:"data"`
}

func (r HttpErrorResp) Error() string {
	return r.Message
}

type HttpErrorDetail struct {
	Name       string        `json:"name"`
	Message    string        `json:"message"`
	StatusCode int           `json:"statusCode,omitempty"`
	ErrCode    HttpErrorCode `json:"errCode,omitempty"`
}

func (h HttpErrorDetail) ErrDetails() {
	//
}

type ValidationError struct {
	Message string            `json:"message"`
	Path    []string          `json:"path"`
	Type    string            `json:"type"`
	Context ValidationContext `json:"context"`
}

type ValidationContext struct {
	Label string `json:"label"`
	Value string `json:"value"`
	Key   string `json:"key"`
}

// Logs an err using rlog with additional key val args and returns a formatted error.
// When returning Encore error responses ErrBuilder should be used instead.
func LogAndWrapError(msg string, err error, keysAndValues ...any) error {
	rlog.Error(msg, "err", err)
	return fmt.Errorf("%s: %w", msg, err)
}
