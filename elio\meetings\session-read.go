package meetings

import (
	"context"
	"database/sql"
	"encoding/json"
	"encore.app/pkg/middleware"
	"encore.dev/beta/auth"
	"errors"
	"net/http"
	"strconv"
	"strings"
	"time"

	"encore.app/hubble"
	hubble_api "encore.app/hubble/api"
	"encore.app/pkg/braid"
	"encore.app/pkg/broadcast"
	db "encore.app/pkg/database"
	"encore.dev/rlog"
	"github.com/jmoiron/sqlx"
	"github.com/samber/lo"

	"encore.app/meetings/api"
	"encore.app/meetings/database"
	"encore.app/shared"
)

// GetSessionByID Replaces `MarsClient.SessionControllerGetSessionByID`, that used to sent a request to: src/modules/sessions/controllers/session.controller.ts in mars
// TODO replace tag:trixta with tag:internal when the tag is merged
//
//encore:api auth method=GET tag:trixta
func (meetings *Meetings) GetSessionByID(ctx context.Context, req *api.GetSessionByIDRequest) (*api.GetSessionByIDResponse, error) {
	var sessionRow *database.SessionRow
	var err error
	if req.RecurrenceID == "" || req.RecurrenceID == "latest" {
		sessionRow, err = database.GetSessionByID(ctx, req.SessionID)
	} else {
		sessionRow, err = database.GetSessionByRecurrenceID(ctx, req.SessionID, req.RecurrenceID)
	}

	if errors.Is(err, sql.ErrNoRows) {
		return nil, shared.B().WithCode(shared.SessionNotFoundErrorCode)
	}

	if err != nil {
		rlog.Error("Failed to get session by ID", "error", err)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	return &api.GetSessionByIDResponse{Session: sessionRow.ToSessionDTOWithRelations(ctx)}, nil
}

// encore:api private method=GET
func (c *Meetings) GetLatestRecurrenceCompact(ctx context.Context, req *api.GetLatestRecurrenceCompactRequest) (*api.GetLatestRecurrenceCompactResponse, error) {
	sessionRow, err := database.GetSessionByID(ctx, req.SessionID)
	if errors.Is(err, sql.ErrNoRows) {
		return nil, shared.B().WithCode(shared.SessionNotFoundErrorCode)
	}
	if err != nil {
		return nil, err
	}

	latestRecurrenceID := strconv.FormatInt(sessionRow.RecurrenceID, 10)

	latestRecurrenceRow, err := database.GetSessionByRecurrenceID(
		ctx,
		req.SessionID,
		latestRecurrenceID,
	)

	if err != nil {
		return nil, err
	}

	if latestRecurrenceRow == nil {
		return nil, nil
	}

	if latestRecurrenceRow.State == shared.SessionStateEnded {
		return nil, nil
	}

	var startTimestamp int
	if latestRecurrenceRow.StartTimestamp.Valid {
		startTimestamp = int(latestRecurrenceRow.StartTimestamp.Int64)
	}
	var startedAt int
	if latestRecurrenceRow.StartedAt.Valid {
		startedAt = int(latestRecurrenceRow.StartedAt.Int64)
	}
	return &api.GetLatestRecurrenceCompactResponse{
		SessionTitle:        latestRecurrenceRow.Title,
		SessionID:           req.SessionID,
		SessionRecurrenceID: latestRecurrenceID,
		Status:              string(latestRecurrenceRow.State),
		StartTimestamp:      startTimestamp,
		StartedAt:           startedAt,
	}, nil
}

// ResolveSessionAccessWithAuth is a helper function that resolves the session access request for the authenticated user.
// Non encore functions like ResolveAccessRequest do not get the auth context from test suites, we provide the decoded token as a param.
func resolveSessionAccessWithAuth(ctx context.Context, sessionDTO shared.SessionDTO, fingerprint string) (*api.SessionAccessDTO, error) {
	// Return gracefully if the requesting user is not authenticated or is an internal API call
	if id, authed := auth.UserID(); !authed || id == middleware.AuthTrixtaBasic {
		return nil, nil
	}
	// Cast to the decoded JWT token info which we'll need to identify the user
	decodedToken := auth.Data().(*shared.DecodedToken)
	return ResolveAccessRequest(ctx, *decodedToken, sessionDTO, fingerprint)
}

// GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session and access DTO if any access rule is associated with the authenticated user.
// It listens for updates on the session and in-review topics to refresh the session and access request data. When recurrenceID is "latest", it fetches the
// latest recurrence ID of the session.
//
//encore:api auth raw path=/v3.0/sessions/:sessionID/recurrences/:recurrenceID method=GET
func (c *Meetings) GetSessionRecurrenceByIdBraidableWithAccess(w http.ResponseWriter, req *http.Request) {
	// Prepare the request payload and validate it
	request := api.MakeGetSessionByIDRequest()
	if err := request.Validate(); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	// braid.Upgrader init that assigns the writer and request for proceeding braid upgrade logic
	upgrader := braid.Upgrader{}
	upgrader.Braidify(w, req)
	// broadcast.NewListener init global broadcast listeners for session updates and in-review updates from other goroutines and networked pods running
	// alongside this local one
	bcastSession := broadcast.NewListener(shared.BCast_SessionUpdatedTopic, request.SessionID)
	bcastInReview := broadcast.NewListener(shared.BCast_InReviewTopic, request.SessionID)
	// Close ensures we release the broadcast resources when this goroutine exits
	defer bcastSession.Close()
	defer bcastInReview.Close()
	// Init both refreshSession and refreshAccessRequest flags as true to instruct the main loop the data is stale and needs to be refreshed from the database.
	// Note: we toggle these flags to true in the main thread usually when events are received and toggle them to false when the data is fetched from the database.
	refreshSession := true
	refreshAccessRequest := true
	// Init the dataVis value to empty, this used to cater for an edge case where sessions can toggle team mode at any time.
	// This change updates the shared.SessionDTO DataVisibility field which could grant or revoke access for the user.
	dataVis := shared.DataVisibility("")
	// Init a nil pointer to track the shared.SessionDTO after it is fetched from the database. We need this to regenerate a payload that includes the session data
	// without re-fetching it from the database on every loop iteration.
	var sessionDTO *shared.SessionDTO = nil
	// Init a nil pointer to the possible next recurrence so the meeting console knows if it's currently seeing an older recurrence or is still latest one.
	// This info in-combination with SessionDTO state field presents a "Go to live session" button in the meeting console.
	var latestRecurrenceCompact *api.GetLatestRecurrenceCompactResponse = nil
	// Init a nil pointer to the access request result from meetings.GetAccessRequest this might be from the database or generated on the fly.
	// The client can use this value to display the access request form or present the restriction status of the access request.
	var accessRequest *api.SessionAccessDTO = nil
	// Init isAuthorized flag which determines if the user has access to the session based on hubble.IsAuthorized check. This is detatched from the access request
	// as team users do not require an access request to view the session, but they still need to be authorization to view it.
	// When isAuthorized is true the client can expect the full SessionDTO to be present in the response, otherwise it will be nil.
	isAuthorized := false
	// The response above will be serialized to JSON and the resulting bytes should be stored in payload variable,
	// we store these bytes so that braid can calculate the next set of patches that need to be sent the client if the payload data changes.
	payload := make([]byte, 0)
	// Every minute we should check if refetch permissions just in case there were no updates received from the broadcast channels
	staleCheckTimer := time.NewTimer(1 * time.Minute)
	// If the timer is still running we stop it to avoid memory leaks
	defer staleCheckTimer.Stop()
	// Begin the main loop which will terminate on the following conditions:
	// - the connection is closed
	// - an internal server error is encountered
	// - request is not a braid subscription
	//   - in which case we return a normal HTTP response
	for {
		// Hook into realtime channels when the request is a braid subscription otherwise don't worry about receiving updates
		if upgrader.IsSubscription {
			select {
			// When the incoming http connection is closed, we exit the main loop
			case <-req.Context().Done():
				return
			// Keep-alive headers are sent on a interval negotiated with client to detect stalled connections and terminate them early
			case <-upgrader.KeepAliveChannel():
				upgrader.SendKeepAlive()
				continue
			// When session events are received we flip the refreshSession flag to true
			case <-bcastSession.Receive():
				rlog.Debug("Received session update", "sessionID", request.SessionID)
				refreshSession = true
			// When in-review (access request) events are received we flip the refreshAccessRequest flag to true
			case <-bcastInReview.Receive():
				// If we are already authorized there is no need to refresh, however in the future we might want to revoke access
				if !isAuthorized {
					rlog.Debug("Received in review update", "sessionID", request.SessionID)
					refreshAccessRequest = true
				}
			// As a fix for a potential break in channel communication, we include a timer to refetch access for the unauthorized user
			// with the hope that they will always have the latest state within a 1-minute window.
			case <-staleCheckTimer.C:
				// When the user is still not authorized the stale time should trigger a refresh
				if !isAuthorized {
					rlog.Debug("Stale check timer ticked", "sessionID", request.SessionID)
					refreshAccessRequest = true
				}
				// Regularly check if the row version doesn't match the latest version in the database, if it doesn't we set refreshSession to true
				// because the session bcastSession.Receive() may not have been received an event when the session was updated, this is a fallback.
				if !refreshSession && sessionDTO != nil {
					rowVersion := 0
					if err := database.FetchRowVersion.GetValues(req.Context(), db.Args{"sessionID": request.SessionID}, &rowVersion); err == nil {
						refreshSession = rowVersion != sessionDTO.Rowversion
					}
				}
				staleCheckTimer.Reset(1 * time.Minute)
			}
		}
		// Begin session refetching branch:
		// When complete: assigns pointers and resets the refreshSession false to avoid re-fetching the same data
		if refreshSession {
			// Fetch the SessionDTO with the pre-validated request payload from the beginning of this function
			resp, err := GetSessionByID(req.Context(), &request)
			// Bail on any internal error encountered while fetching the session
			if err != nil {
				b, _ := json.Marshal(err)
				w.WriteHeader(http.StatusInternalServerError)
				w.Write(b)
				return
			}
			// In cases where the dataVis has already been assigned from a previous iteration,
			// the edge case where team mode is toggled need to be handled by setting refreshAccessRequest to true
			if dataVis != "" && resp.Session.DataVisibility != dataVis {
				// There is another broadcast event listner for data visibility changes that will propagate changes to Casbin.
				// Hacky: since there's no way to know when Casbin is ready for our next call to hubble.IsAuthorized,
				// we time.Sleep for a short period to time giving Casbin a head-start in populates the latest policies.
				time.Sleep(100 * time.Millisecond)
				refreshAccessRequest = true
			}
			// Re/Assign the latest data visibility which is also to say, assign the team mode settings for this session
			dataVis = resp.Session.DataVisibility
			// Store the address of the full SessionDTO to be serialized later if the user is authorized to view it
			sessionDTO = &resp.Session
			// If RecurrenceID is not empty then the frontend is requesting a specific recurrence and we need to check if a newer recurrence is available
			// so the meeting console can inform the user about it.
			if request.RecurrenceID != "" {
				// Fetch and assign the latest recurrence pointer if any exists, this will be serialized in the response later
				latestRecurrenceCompact, err = GetLatestRecurrenceCompact(
					req.Context(),
					&api.GetLatestRecurrenceCompactRequest{
						SessionID: request.SessionID,
					},
				)
				// Bail on any internal error encountered while fetching the latest recurrence
				if err != nil {
					rlog.Error("Failed to get latest recurrence", "error", err)
					w.WriteHeader(http.StatusInternalServerError)
					return
				}
			}
			// Reset the refreshSession flag to false, which that allowed this refetch branch to execute
			refreshSession = false
			// End session refetching branch
		}

		// Begin access request refetching branch:
		// When complete: assigns accessRequest pointer, sets isAuthorized and resets the refreshAccessRequest false to avoid re-fetching
		if refreshAccessRequest {
			// Fetch the access request, passing along the JWT token decoded data, sessionDTO, and optional fingerprint header to be presented in the DB
			resp, err := resolveSessionAccessWithAuth(req.Context(), *sessionDTO, req.Header.Get("X-Fingerprint"))
			// Bail on any internal error encountered while fetching the access request
			if err != nil {
				rlog.Error("Failed to get access request", "error", err)
				w.WriteHeader(http.StatusInternalServerError)
				return
			}
			// Assign access request pointer to be serialized later
			accessRequest = resp
			// Check if with hubble the user is authorized to view the session, this is a separate check from the access request
			authErr := hubble.IsAuthorized(req.Context(), &hubble_api.IsAuthorizedRequest{
				Sub: shared.GetUserSubjectWithRole(),
				Obj: shared.GetSessionObjectByID(request.SessionID),
				Act: shared.AuthPolicyActionGet,
			})
			// When authErr is not nil then the user does not have access to the session, we set isAuthorized to false
			isAuthorized = authErr == nil
			// Reset the refreshAccessRequest flag to false, which that allowed this refetch branch to execute
			refreshAccessRequest = false
			// End access request refetching branch
		}
		// When the braid connection is still in the "subscribe" state we should send appropriate headers to the client acknowledging the subscription
		if upgrader.Subscribe {
			upgrader.StartSubscription()
		}
		// Build a response payload to send to the frontend
		response := api.GetSessionByIDWithAccessResponse{
			Code: shared.HttpSuccessCode,
			Data: &api.SessionWithAccess{Success: true, IsAuthorized: isAuthorized, AccessRequest: accessRequest},
		}
		// Only when the user is authorized do we include the sessionDTO and latestRecurrenceCompact in the response
		if isAuthorized {
			response.Data.Session = sessionDTO
			response.Data.LatestRecurrence = latestRecurrenceCompact
		}
		// Serialize the full payload
		newPayload, _ := json.Marshal(response)
		// Diff the latest payload with the previous bytes, if this is the first patch the full payload will be sent to the client
		if err := upgrader.SendJSONOrPatch(payload, newPayload); err != nil {
			rlog.Error("Failed to send JSON", "err", err)
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		// If the request is not a braid connection we end the loop here
		if !upgrader.Subscribe {
			return
		}
		// Assign the payload bytes with just serialized for future diffs in upgrader.SendJSONOrPatch
		payload = newPayload
		// Loop to wait for the start and next channel signal
	}
}

// GetSessionRecurrencesById returns a list of recurrences for the passed sessionID, can optionally search for specific
// states the recurrence should have been in to appear in the response. When no sessions match the sessionID or optional
// filter the resulting `Sessions` array in api.GetSessionRecurrencesByIDResponse will be empty.
//
//encore:api auth method=GET tag:trixta
func (meetings *Meetings) GetSessionRecurrencesById(ctx context.Context, req *api.GetSessionRecurrencesByIDRequest) (*api.GetSessionRecurrencesByIDResponse, error) {
	var err error = nil
	response := &api.GetSessionRecurrencesByIDResponse{Sessions: make([]shared.SessionDTO, 0)}
	filter := strings.Split(req.FilterStates, ",")
	database.GetRecurrencesByID.Queryx(ctx, db.Args{"sessionID": req.SessionID}, func(rows *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}
		sessionRecurrence := database.SessionRow{}
		if err = rows.StructScan(&sessionRecurrence); err != nil {
			return false
		}
		if len(req.FilterStates) > 0 {
			_, exists := lo.Find(sessionRecurrence.StateUpdatedAt, func(item shared.StateUpdatedAt) bool {
				return lo.Contains(filter, string(item.State))
			})
			if !exists {
				return true
			}
		}
		dto := sessionRecurrence.ToSessionDTO()
		response.Sessions = append(response.Sessions, dto)
		return true
	})
	if err != nil {
		rlog.Error("Failed to get session recurrences by ID", "error", err)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}
	return response, nil
}

//encore:api private
func (meetings *Meetings) ListSessionsByLobbyID(ctx context.Context, req *api.ListSessionsByLobbyIDRequest) (*api.ListSessionsByLobbyIDResponse, error) {
	var sessionRows []database.SessionRow
	var err error

	if req.Active != nil && *req.Active == true {
		// List active sessions only
		sessionRows, err = database.ListSessionsByLobbyID(ctx, req.LobbyID)
	} else {
		// List all sessions
		sessionRows, err = database.ListActiveSessionsByLobbyID(ctx, req.LobbyID)
	}

	if errors.Is(err, sql.ErrNoRows) {
		return nil, shared.B().WithCode(shared.SessionNotFoundErrorCode)
	}

	if err != nil {
		rlog.Error("Failed to get session by LobbyID", "error", err)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	sessionDTOs := lo.Map(sessionRows, func(item database.SessionRow, index int) shared.SessionDTO {
		return item.ToSessionDTO()
	})

	return &api.ListSessionsByLobbyIDResponse{Sessions: sessionDTOs}, nil
}
