from functools import partial
import asyncio
import aiopg
import logging
from aiopg.connection import Connection
from typing import Optional, Dict

from nebula.constants import DB_POOL_CONNECTION_TIMEOUT
from nebula.db.mars.prep_stmts import MARS_PREPARED_STMTS
from nebula.db.prepare import Statements, prepare_statements_for_aiopg_conns
from nebula.settings import settings

logger = logging.getLogger(__name__)


async def handle_aiopg_connect(statements: Statements, conn: Connection):
    logger.info("aiopg connection established. preparing statements")
    await prepare_statements_for_aiopg_conns(conns=[conn], statements=statements)


async def init_db_pool(
    dsn: str,
    timeout: int,
    minsize: int = 6,
    prepared_statements: Statements | None = None,
    schema: str | None = None,
) -> aiopg.Pool:
    # Funny enough, and in contrast to asyncpg, aiopg "options" param does actually allow us to set the search_path!
    # BUT! As Neon DBs uses public schemas by default, and we have to make Nebula's schema "public", to also support asyncpg pools with <PERSON><PERSON><PERSON> (which doesn't support search_path),
    # here we only set the "non-public" schema in local to leverage the database created from `docker-compose.yml (rumi local db)
    if schema and schema != "public":
        dsn += f" options='-c search_path={schema}'"

    on_connect = (
        partial(handle_aiopg_connect, prepared_statements)
        if prepared_statements
        else None
    )

    return await aiopg.create_pool(
        dsn,
        timeout=timeout,
        minsize=minsize,
        on_connect=on_connect,
    )


async def init_mars_db_pool(
    host: str = settings.mars_db_host,
    name: str = settings.mars_db_database,
    user: str = settings.mars_db_user,
    password: str = settings.mars_db_password,
    schema: str = settings.mars_db_schema,
    timeout=DB_POOL_CONNECTION_TIMEOUT,
    prepared_statements: Statements = MARS_PREPARED_STMTS,
) -> aiopg.Pool:
    minsize = 6 if settings.environment != "local" else 1
    logger.debug(
        f"[init_mars_db_pool] initializing db pool with {len(prepared_statements)} prepared statements and minsize {minsize}"
    )
    ssl_mode = "require" if settings.db_require_ssl else "disable"
    mars_dsn = (
        f"dbname={name} user={user} password={password} host={host} sslmode={ssl_mode}"
    )
    return await init_db_pool(
        dsn=mars_dsn,
        timeout=timeout,
        minsize=minsize,
        prepared_statements=prepared_statements,
        schema=schema,
    )


async def init_luxor_db_pool(
    host: str = settings.luxor_db_host,
    name: str = settings.luxor_db_database,
    user: str = settings.luxor_db_user,
    password: str = settings.luxor_db_password,
    schema: str = settings.luxor_db_schema,
    timeout=DB_POOL_CONNECTION_TIMEOUT,
    prepared_statements: Statements | None = None,
) -> aiopg.Pool:
    ssl_mode = "require" if settings.db_require_ssl else "disable"
    luxor_dsn = (
        f"dbname={name} user={user} password={password} host={host} sslmode={ssl_mode}"
    )

    return await init_db_pool(
        dsn=luxor_dsn,
        timeout=timeout,
        prepared_statements=prepared_statements,
        schema=schema,
    )


async def init_nebula_db_pool(
    host: str = settings.postgres_host,
    name: str = settings.postgres_db,
    user: str = settings.postgres_user,
    password: str = settings.postgres_pass,
    schema: str = settings.postgres_schema,
    timeout=DB_POOL_CONNECTION_TIMEOUT,
    prepared_statements: Statements | None = None,
) -> aiopg.Pool:
    ssl_mode = "require" if settings.db_require_ssl else "disable"
    nebula_dsn = (
        f"dbname={name} user={user} password={password} host={host} sslmode={ssl_mode}"
    )

    return await init_db_pool(
        dsn=nebula_dsn,
        timeout=timeout,
        prepared_statements=prepared_statements,
        schema=schema,
    )


# Pool instances
_pools: Dict[str, Optional[aiopg.Pool]] = {"nebula": None, "luxor": None, "mars": None}
_initialization_lock = asyncio.Lock()


async def get_nebula_pool() -> aiopg.Pool:
    """Get the Nebula DB pool, initializing it if needed."""
    if _pools["nebula"] is None:
        async with _initialization_lock:
            if _pools["nebula"] is None:
                _pools["nebula"] = await init_nebula_db_pool()
    return _pools["nebula"]


async def get_luxor_pool() -> aiopg.Pool:
    """Get the Luxor DB pool, initializing it if needed."""
    if _pools["luxor"] is None:
        async with _initialization_lock:
            if _pools["luxor"] is None:
                _pools["luxor"] = await init_luxor_db_pool()
    return _pools["luxor"]


async def get_mars_pool() -> aiopg.Pool:
    """Get the Mars DB pool, initializing it if needed."""
    if _pools["mars"] is None:
        async with _initialization_lock:
            if _pools["mars"] is None:
                _pools["mars"] = await init_mars_db_pool(
                    prepared_statements=MARS_PREPARED_STMTS
                )
    return _pools["mars"]


async def initialize_pools():
    """Initialize all database pools explicitly."""
    global _pools

    async with _initialization_lock:
        _pools["nebula"] = await init_nebula_db_pool()
        _pools["luxor"] = await init_luxor_db_pool()
        _pools["mars"] = await init_mars_db_pool()

    return _pools


# For backwards compatibility, provide these variables
# They'll be assigned in FastAPI startup or can be explicitly assigned
# by awaiting the respective get_*_pool() functions
nebula_db_pool: Optional[aiopg.Pool] = None
luxor_db_pool: Optional[aiopg.Pool] = None
mars_db_pool: Optional[aiopg.Pool] = None
