# ApiGenerateXRayInfoResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**icon** | **str** |  | 
**short_summary** | **str** |  | 
**title** | **str** |  | 

## Example

```python
from elio_client.models.api_generate_x_ray_info_response_data import ApiGenerateXRayInfoResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiGenerateXRayInfoResponseData from a JSON string
api_generate_x_ray_info_response_data_instance = ApiGenerateXRayInfoResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiGenerateXRayInfoResponseData.to_json())

# convert the object into a dict
api_generate_x_ray_info_response_data_dict = api_generate_x_ray_info_response_data_instance.to_dict()
# create an instance of ApiGenerateXRayInfoResponseData from a dict
api_generate_x_ray_info_response_data_from_dict = ApiGenerateXRayInfoResponseData.from_dict(api_generate_x_ray_info_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


