name: Nebula Run Tests

on:
  pull_request:
    branches: [staging]
    paths:
      - "nebula/**"

jobs:
  run_tests:
    name: Run Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft != true
    defaults:
      run:
        working-directory: ./nebula

    services:
      postgres:
        image: postgres:17
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: nebula_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      # Configure Connection to OnePassword Connect Operator
      - name: ⚙️ Configure 1Password Connect
        uses: 1password/load-secrets-action/configure@v1.2.0
        with:
          connect-host: https://op.ops.waitroom.com
          connect-token: ${{ secrets.OP_CONNECT_TOKEN }}

      # Load the environment variables required for tests from 1Password
      - name: 🤫 Load 1Password secrets
        id: secrets
        if: ${{ success() }}
        uses: 1password/load-secrets-action@v2.0.0
        env:
          # OpenAI API Keys
          OPENAI_FEED_API_KEY: op://develop-nebula/openai-feed-api-key/credential
          OPENAI_PMS_API_KEY: op://develop-nebula/openai-pms-api-key/credential
          OPENAI_MM_API_KEY: op://develop-nebula/openai-mm-api-key/credential
          OPENAI_METADATA_API_KEY: op://develop-nebula/openai-metadata-api-key/credential
          # Cohere API Key
          COHERE_API_KEY: op://develop-nebula/cohere-api-key/credential
          # Typesense
          TYPESENSE_ADMIN_API_KEY: op://develop-nebula/typesense-admin-api-key/credential
          # Basic Auth Credentials
          BASIC_AUTH_USERNAME: op://develop-nebula/basic-auth-username/credential
          BASIC_AUTH_PASSWORD: op://develop-nebula/basic-auth-password/credential
          # Service Auth Credentials  
          LUXOR_BASIC_AUTH_USERNAME: op://develop-nebula/luxor-basic-auth-username/credential
          LUXOR_BASIC_AUTH_PASSWORD: op://develop-nebula/luxor-basic-auth-password/credential
          MARS_BASIC_AUTH_USERNAME: op://develop-nebula/mars-basic-auth-username/credential
          MARS_BASIC_AUTH_PASSWORD: op://develop-nebula/mars-basic-auth-password/credential
          DRACONIDS_AUTH_USERNAME: op://develop-nebula/draconids-auth-username/credential
          DRACONIDS_AUTH_PASSWORD: op://develop-nebula/draconids-auth-password/credential

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: latest
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Load cached venv
        id: cached-poetry-dependencies
        uses: actions/cache@v4
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
        run: poetry install --no-interaction --no-root

      - name: Install project
        run: poetry install --no-interaction

      - name: Set up test environment
        run: |
          echo "NEBULA_DB_BASE=nebula_test" >> $GITHUB_ENV
          echo "PICCOLO_CONF=piccolo_conf_test" >> $GITHUB_ENV
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/nebula_test" >> $GITHUB_ENV

      - name: Install PostgreSQL client
        run: |
          sudo apt-get update
          sudo apt-get install -y postgresql-client

      - name: Create test schema
        run: |
          PGPASSWORD=postgres psql -h localhost -U postgres -d nebula_test -c "CREATE SCHEMA IF NOT EXISTS test_schema_${{ github.run_id }};"

      - name: Run database migrations
        run: poetry run piccolo migrations forwards nebula
        env:
          ENVIRONMENT: test
          POSTGRES_HOST: localhost
          POSTGRES_PORT: "5432"
          POSTGRES_USER: postgres
          POSTGRES_PASS: postgres
          POSTGRES_DB: nebula_test
          POSTGRES_SCHEMA: test_schema_${{ github.run_id }}

      - name: Run tests
        run: poetry run pytest -v --cov=nebula --cov-report=term
        env:
          # Test database configuration (using exact names from Settings class)
          POSTGRES_HOST: localhost
          POSTGRES_PORT: "5432"
          POSTGRES_USER: postgres
          POSTGRES_PASS: postgres
          POSTGRES_DB: nebula_test
          POSTGRES_SCHEMA: public
          POSTGRES_ECHO: "false"
          DB_REQUIRE_SSL: "false"
          # Test environment settings
          ENVIRONMENT: test
          LOG_LEVEL: INFO
          ENABLE_SWAGGER_DOCS: false
          # API Keys from 1Password
          OPENAI_FEED_API_KEY: ${{ env.OPENAI_FEED_API_KEY }}
          OPENAI_PMS_API_KEY: ${{ env.OPENAI_PMS_API_KEY }}
          OPENAI_MM_API_KEY: ${{ env.OPENAI_MM_API_KEY }}
          OPENAI_METADATA_API_KEY: ${{ env.OPENAI_METADATA_API_KEY }}
          COHERE_API_KEY: ${{ env.COHERE_API_KEY }}
          TYPESENSE_ADMIN_API_KEY: ${{ env.TYPESENSE_ADMIN_API_KEY }}
          # Basic auth
          BASIC_AUTH_USERNAME: ${{ env.BASIC_AUTH_USERNAME }}
          BASIC_AUTH_PASSWORD: ${{ env.BASIC_AUTH_PASSWORD }}
          # Service URLs (test/mock endpoints)
          LUXOR_BASE_URL: http://localhost:3001
          LUXOR_BASIC_AUTH_USERNAME: ${{ env.LUXOR_BASIC_AUTH_USERNAME }}
          LUXOR_BASIC_AUTH_PASSWORD: ${{ env.LUXOR_BASIC_AUTH_PASSWORD }}
          MARS_BASE_URL: http://localhost:3002
          MARS_BASIC_AUTH_USERNAME: ${{ env.MARS_BASIC_AUTH_USERNAME }}
          MARS_BASIC_AUTH_PASSWORD: ${{ env.MARS_BASIC_AUTH_PASSWORD }}
          ELIO_BASE_URL: http://localhost:4000
          DRACONIDS_BASE_URL: http://localhost:3003
          DRACONIDS_AUTH_USERNAME: ${{ env.DRACONIDS_AUTH_USERNAME }}
          DRACONIDS_AUTH_PASSWORD: ${{ env.DRACONIDS_AUTH_PASSWORD }}
          # Test database configs for other services
          LUXOR_DB_USER: test_user
          LUXOR_DB_PASSWORD: test_pass
          LUXOR_DB_DATABASE: test_db
          LUXOR_DB_HOST: localhost
          LUXOR_DB_SCHEMA: public
          LUXOR_DB_PORT: 5432
          MARS_DB_USER: test_user
          MARS_DB_PASSWORD: test_pass
          MARS_DB_HOST: localhost
          MARS_DB_DATABASE: test_db
          MARS_DB_SCHEMA: public
          MARS_DB_PORT: 5432
          # Kafka (mock for tests)
          KAFKA_BOOTSTRAP_SERVERS: localhost:9092
          KAFKA_SSL: false
          KAFKA_TOPIC_BATCH_TRANSCRIPTIONS: sessions-live-transcriptions-batch-test
          # Temporal (mock for tests)
          TEMPORAL_HOST: localhost
          TEMPORAL_PORT: 7233
          TEMPORAL_NAMESPACE: test
          TEMPORAL_AI_END_MEETING_QUEUE: ai-end-meeting-test
          # Typesense (mock for tests)
          TYPESENSE_PROTOCOL: http
          TYPESENSE_HOST: localhost
          TYPESENSE_PORT: 8108
          TYPESENSE_CONNECTION_TIMEOUT_SECS: 2
          TYPESENSE_RAG_TOP_K: 950
          # Model configurations
          EMBEDDINGS_MODEL_NAME: all-mpnet-base-v2
          COHERE_EMBED_MODEL: embed-multilingual-v3.0
          COHERE_RERANK_MODEL: rerank-v3.5
          COHERE_CHAT_MODEL: command-r-plus
          COHERE_RERANK_TOP_N: 100
          OPENAI_AI_FEED_MODEL: gpt-4o-mini
          OPENAI_PMS_MODEL: gpt-4o-mini
          OPENAI_RAG_MODEL: gpt-4o-mini
          OPENAI_RAG_STEPS_MODEL: gpt-4o-mini
          OPENAI_METADATA_MODEL: gpt-4o-mini 