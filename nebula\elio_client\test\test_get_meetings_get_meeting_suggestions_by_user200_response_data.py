# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_meetings_get_meeting_suggestions_by_user200_response_data import GETMeetingsGetMeetingSuggestionsByUser200ResponseData

class TestGETMeetingsGetMeetingSuggestionsByUser200ResponseData(unittest.TestCase):
    """GETMeetingsGetMeetingSuggestionsByUser200ResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETMeetingsGetMeetingSuggestionsByUser200ResponseData:
        """Test GETMeetingsGetMeetingSuggestionsByUser200ResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETMeetingsGetMeetingSuggestionsByUser200ResponseData`
        """
        model = GETMeetingsGetMeetingSuggestionsByUser200ResponseData()
        if include_optional:
            return GETMeetingsGetMeetingSuggestionsByUser200ResponseData(
                suggestions = [
                    ''
                    ],
                total = 56
            )
        else:
            return GETMeetingsGetMeetingSuggestionsByUser200ResponseData(
                suggestions = [
                    ''
                    ],
                total = 56,
        )
        """

    def testGETMeetingsGetMeetingSuggestionsByUser200ResponseData(self):
        """Test GETMeetingsGetMeetingSuggestionsByUser200ResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
