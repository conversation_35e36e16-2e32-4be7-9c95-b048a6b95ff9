import asyncio
import json
import random
from nebula.web.api.search.sync import mm_non_streaming
from pydantic import ValidationError
from typing import Annotated, List

from fastapi import APIRouter, Depends, Header, Query, Request
from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse
from loguru import logger
from starlette import status as http_status

from nebula.clients.llms import ThreadRunNotFound
from nebula.db.models.message import Message
from nebula.db.models.suggestion import Suggestion
from nebula.db.models.thread import Thread
from nebula.db.models.user_suggestion import UserSuggestion
from nebula.services.search.common import (
    AskAIBody,
    AskAIResponseDTO,
    CreateTeamBody,
    CreateTeamMemberBody,
    DeleteThreadResponse,
    GetThreadMessagesResponse,
    GetThreadResponse,
    GetUserThreadsResponse,
    MessageOut,
    PatchTypesenseRecurrenceTeamAccess,
    PatchUserFeedbackBody,
    PatchUserFeedbackResponse,
    StopRunBody,
    StopRunResponseDTO,
    TypesenseDocumentMutationResponse,
    message_to_mm_message,
    msg_content_from_msg,
)
from nebula.services.search.common import (
    Thread as ThreadModel,
)
from nebula.services.search.rag import (
    cancel_thread_run,
    get_last_thread_run_id,
    rag,
    sse_msg_body_from_dict,
)
from nebula.services.search.typesense_queries import (
    add_team,
    add_team_access_to_recurrence,
    add_user_to_team,
    remove_team_access_from_recurrence,
    remove_user_from_team,
)
from starlette.types import Scope, Receive, Send
from nebula.services.suggestions.suggestions_service import (
    always_used_suggestions,
    generic_suggestions,
)
from nebula.web.api.auth import authorize

router = APIRouter()


@router.post(
    "/ask-ai-sync",
    dependencies=[Depends(authorize)],
)
async def ask_ai_non_streaming(
    req: Request,
    body: AskAIBody,
    x_user_id: str | None = Header(default=None),
    timeout: Annotated[float, Query(ge=1.0, le=300.0)] = 60.0,  # Allow 1 to 300 seconds
) -> JSONResponse:
    if not x_user_id:
        return JSONResponse(
            status_code=http_status.HTTP_401_UNAUTHORIZED,
            content=jsonable_encoder(
                AskAIResponseDTO(data=None, message="User id is missing")
            ),
        )

    try:
        result, status_code = await mm_non_streaming(body, x_user_id, timeout)
        return JSONResponse(
            status_code=status_code,
            content=jsonable_encoder(result),
        )
    except Exception as e:
        logger.error(f"Error processing non-streaming MM pipeline: {e}")
        return JSONResponse(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=jsonable_encoder(
                AskAIResponseDTO(data=None, message=f"Internal server error: {str(e)}")
            ),
        )


async def mm_sse_asgi(scope: Scope, receive: Receive, send: Send):
    assert scope["type"] == "http"

    if scope["method"] != "POST":
        await send(
            {
                "type": "http.response.start",
                "status": 405,
                "headers": [
                    (b"content-type", b"text/plain"),
                    (b"allow", b"POST"),
                ],
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b"Method Not Allowed. Use POST.",
                "more_body": False,
            }
        )
        return

    # Receive and reconstruct the body
    body = b""
    more_body = True
    while more_body:
        message = await receive()
        if message["type"] == "http.request":
            body += message.get("body", b"")
            more_body = message.get("more_body", False)

    payload: AskAIBody | None = None

    # Parse & validate with Pydantic
    try:
        raw_data = json.loads(body)
        payload = AskAIBody.model_validate(raw_data)
    except (json.JSONDecodeError, ValidationError) as e:
        await send(
            {
                "type": "http.response.start",
                "status": 400,
                "headers": [(b"content-type", b"application/json")],
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": json.dumps({"error": str(e)}).encode("utf-8"),
            }
        )
        return

    assert payload is not None

    headers = dict(scope["headers"])

    # headers dict has bytes keys (e.g., b'x-user-id'), so we need to decode them
    decoded_headers = {k.decode(): v.decode() for k, v in headers.items()}
    user_id = decoded_headers.get("x-user-id")

    if not user_id:
        await send(
            {
                "type": "http.response.body",
                "body": sse_msg_body_from_dict(
                    {
                        "type": "stream_failed",
                        "requestId": payload.requestId,
                        "code": 401,
                        "isFinished": True,
                        "data": {"content": "User id is missing"},
                    }
                ),
                "more_body": False,
            }
        )
        return

    await send(
        {
            "type": "http.response.start",
            "status": 200,
            "headers": [
                (b"content-type", b"text/event-stream"),
                (b"cache-control", b"no-cache"),
                (b"connection", b"keep-alive"),
            ],
        }
    )

    await rag(payload, send, user_id)


@router.post(
    "/stop",
    dependencies=[Depends(authorize)],
)
async def stop_run(
    req: Request,
    body: StopRunBody,
    x_user_id: str | None = Header(default=None),
) -> JSONResponse:
    thread = await Thread.objects().where(Thread.id == body.threadId).first()
    if not thread:
        return JSONResponse(
            status_code=http_status.HTTP_404_NOT_FOUND,
            content=jsonable_encoder(
                StopRunResponseDTO(
                    data=StopRunResponseDTO.Data(
                        threadId=body.threadId,
                        providerThreadId=None,
                        runId=None,
                    ),
                    message="Thread not found",
                    success=False,
                )
            ),
        )

    if thread.user_id != x_user_id:
        return JSONResponse(
            status_code=http_status.HTTP_403_FORBIDDEN,
            content=jsonable_encoder(
                StopRunResponseDTO(
                    data=StopRunResponseDTO.Data(
                        threadId=body.threadId,
                        providerThreadId=None,
                        runId=None,
                    ),
                    message="Forbidden",
                    success=False,
                )
            ),
        )

    try:
        last_run_id = await get_last_thread_run_id(thread.provider_thread_id)
        success = await cancel_thread_run(
            thread_id=thread.provider_thread_id, run_id=last_run_id
        )
        msg = (
            "Last run successfully cancelled" if success else "Failed to cancel the run"
        )

        return JSONResponse(
            status_code=http_status.HTTP_200_OK
            if success
            else http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=jsonable_encoder(
                StopRunResponseDTO(
                    data=StopRunResponseDTO.Data(
                        threadId=body.threadId,
                        providerThreadId=thread.provider_thread_id,
                        runId=last_run_id,
                    ),
                    message=msg,
                    success=success,
                )
            ),
        )
    except ThreadRunNotFound:
        logger.warning(f"thread run not found. thread id: {thread.provider_thread_id}")

        return JSONResponse(
            status_code=http_status.HTTP_404_NOT_FOUND,
            content=jsonable_encoder(
                StopRunResponseDTO(
                    data=StopRunResponseDTO.Data(
                        threadId=body.threadId,
                        providerThreadId=thread.provider_thread_id,
                        runId=None,
                    ),
                    message="Run not found",
                    success=False,
                )
            ),
        )
    except Exception as e:
        logger.error(f"failed to cancel the run. run: {e}")
        return JSONResponse(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=jsonable_encoder(
                StopRunResponseDTO(
                    data=StopRunResponseDTO.Data(
                        threadId=body.threadId,
                        providerThreadId=thread.provider_thread_id,
                        runId=None,
                    ),
                    message="Internal server error",
                    success=False,
                )
            ),
        )


@router.patch(
    "/feedback",
    dependencies=[Depends(authorize)],
)
async def user_feedback(
    req: Request,
    body: PatchUserFeedbackBody,
) -> JSONResponse:
    if body.feedback != 0 and body.feedback != 1 and body.feedback != 2:
        return JSONResponse(
            status_code=http_status.HTTP_400_BAD_REQUEST,
            content="Wrong feedback value",
        )

    message = await Message.objects().where(Message.id == body.messageId).first()

    if not message:
        return JSONResponse(
            status_code=http_status.HTTP_404_NOT_FOUND,
            content=jsonable_encoder(
                AskAIResponseDTO(data=None, message="Message not found")
            ),
        )

    message.user_feedback = body.feedback
    await message.save()

    return JSONResponse(
        content=jsonable_encoder(
            PatchUserFeedbackResponse(
                message="Success",
                data=PatchUserFeedbackResponse.Data(
                    message=message_to_mm_message(message)
                ),
            )
        )
    )


@router.get(
    "/threads",
    dependencies=[Depends(authorize)],
)
async def get_threads_by_user_id(
    req: Request,
    limit: Annotated[int, Query(ge=1, le=200)] = 16,
    skip: Annotated[int, Query(ge=0)] = 0,
    x_user_id: str = Header(),
) -> JSONResponse:
    count_q = Thread.raw(
        """
    WITH first_msg AS (
        SELECT DISTINCT ON (thread_id) *
        FROM message msg
        ORDER BY thread_id, created_at ASC
    )
    SELECT COUNT(*)
    FROM thread t
    JOIN first_msg ON t.id = first_msg.thread_id
    WHERE t.user_id = {}
    """,
        x_user_id,
    )

    threads_with_last_msg_q = Thread.raw(
        """
    WITH first_msg AS (
        SELECT DISTINCT ON (thread_id) *
        FROM message msg
        WHERE msg.role = 'user'
        ORDER BY thread_id, created_at ASC
    ),
    last_msg AS (
        SELECT DISTINCT ON (thread_id) *
        FROM message msg
        ORDER BY thread_id, created_at DESC
    )
    SELECT t.*,
            fm.items AS first_message_items,
            lm.created_at AS last_message_at
    FROM thread t
    JOIN first_msg fm ON t.id = fm.thread_id
    JOIN last_msg lm ON t.id = lm.thread_id
    WHERE t.user_id = {}
    ORDER BY t.updated_at DESC
    LIMIT {}
    OFFSET {}
    """,
        x_user_id,
        limit,
        skip,
    )

    total_count_row, threads_with_m_rows = await asyncio.gather(
        count_q, threads_with_last_msg_q
    )
    total_count = total_count_row[0].get("count", 0) if total_count_row else 0

    threads: List[ThreadModel] = []
    has_more = ((skip or 1) * limit) < total_count

    for row in threads_with_m_rows:
        title = json.loads(row.get("first_message_items"))[0].get("content")

        threads.append(
            ThreadModel(
                id=row.get("id"),
                title=title,
                createdAt=int(row.get("created_at").timestamp()),
                updatedAt=int(row.get("updated_at").timestamp()),
                lastMessageAt=int(row.get("last_message_at").timestamp()),
            )
        )

    return JSONResponse(
        content=jsonable_encoder(
            GetUserThreadsResponse(
                message="Success",
                data=GetUserThreadsResponse.Data(
                    total=total_count or 0, threads=threads, hasMore=has_more
                ),
                success=True,
            )
        )
    )


@router.get(
    "/threads/{thread_id}",
    dependencies=[Depends(authorize)],
)
async def get_thread_by_id(
    req: Request,
    thread_id: str,
    x_user_id: str = Header(),
) -> JSONResponse:
    rows = await Thread.raw(
        """WITH first_msg AS (
            SELECT DISTINCT ON (thread_id) *
            FROM message msg
            WHERE msg.role = 'user'
            ORDER BY thread_id, created_at ASC
        ),
        last_msg AS (
            SELECT DISTINCT ON (thread_id) *
            FROM message msg
            WHERE msg.role = 'user'
            ORDER BY thread_id, created_at DESC
        )
        SELECT t.*,
               fm.items AS first_message_items,
               lm.created_at AS last_message_at
        FROM thread t
        JOIN first_msg fm ON t.id = fm.thread_id
        JOIN last_msg lm ON t.id = lm.thread_id
        WHERE t.id = {} AND t.user_id = {}
        ORDER BY t.created_at DESC;
    """,
        thread_id,
        x_user_id,
    )

    if not rows:
        return JSONResponse(
            status_code=http_status.HTTP_404_NOT_FOUND,
            content=jsonable_encoder(
                GetThreadResponse(data=None, message="Thread not found", success=False)
            ),
        )
    row = rows[0]
    title = json.loads(row.get("first_message_items"))[0].get("content")

    return JSONResponse(
        content=jsonable_encoder(
            GetThreadResponse(
                message="Success",
                success=True,
                data=GetThreadResponse.Data(
                    thread=ThreadModel(
                        id=row.get("id"),
                        title=title,
                        createdAt=int(row.get("created_at").timestamp()),
                        updatedAt=int(row.get("updated_at").timestamp()),
                        lastMessageAt=int(row.get("last_message_at").timestamp()),
                    )
                ),
            )
        )
    )


@router.get(
    "/threads/{thread_id}/messages",
    dependencies=[Depends(authorize)],
)
async def get_thread_messages(
    req: Request,
    thread_id: str,
    limit: Annotated[int, Query(ge=1, le=200)] = 5,
    skip: Annotated[int, Query(ge=0)] = 0,
    x_user_id: str = Header(),
) -> JSONResponse:
    count_q = Message.count().where(Message.thread_id == thread_id)
    messages_q = (
        Message.objects()
        .where(Message.thread_id == thread_id)
        .order_by(Message.created_at, ascending=True)
    )

    total_count, messages = await asyncio.gather(count_q, messages_q)
    has_more = ((skip or 1) * limit) < total_count
    out_msgs = [
        MessageOut(
            id=msg.id,
            threadId=msg.thread_id,
            role=msg.role,  # type: ignore
            content=msg_content_from_msg(msg),
            feedback=msg.user_feedback,
            createdAt=int(msg.created_at.timestamp()),
            updatedAt=int(msg.updated_at.timestamp()),
            sources=json.loads(msg.sources) if msg.sources else None,
        )
        for msg in messages
    ]

    return JSONResponse(
        content=jsonable_encoder(
            GetThreadMessagesResponse(
                message="Success",
                success=True,
                data=GetThreadMessagesResponse.Data(
                    total=total_count, messages=out_msgs, hasMore=has_more
                ),
            )
        )
    )


@router.post(
    "/teams",
    dependencies=[Depends(authorize)],
)
async def create_team(
    req: Request,
    body: CreateTeamBody,
) -> JSONResponse:
    try:
        add_team(id=body.teamId, name=body.name, user_ids=body.memberIds)

        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Success",
                    success=True,
                )
            )
        )
    except Exception as e:
        logger.error(f"[create_team] failed to add team document. err: {e}")
        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Internal server error. Failed to add team document",
                    success=False,
                )
            ),
            status_code=500,
        )


@router.post(
    "/teams/{team_id}/members",
    dependencies=[Depends(authorize)],
)
async def add_team_member(
    req: Request,
    team_id: str,
    body: CreateTeamMemberBody,
) -> JSONResponse:
    try:
        add_user_to_team(team_id=team_id, user_id=body.userId)
        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Success",
                    success=True,
                )
            )
        )
    except Exception as e:
        logger.error(f"[add_team_member] failed to add team document. err: {e}")
        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Internal server error. Failed to add team member to team document",
                    success=False,
                )
            ),
            status_code=500,
        )


@router.delete(
    "/teams/{team_id}/members/{user_id}",
    dependencies=[Depends(authorize)],
)
async def remove_team_member(
    req: Request,
    team_id: str,
    user_id: str,
) -> JSONResponse:
    try:
        remove_user_from_team(team_id, user_id)
        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Success",
                    success=True,
                )
            )
        )
    except Exception as e:
        logger.error(
            f"[remove_team_member] failed to remove member from team document. err: {e}"
        )
        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Internal server error. Failed to remove team member from team document",
                    success=False,
                )
            ),
            status_code=500,
        )


@router.patch(
    "/recurrences/{recurrence_id}/access",
    dependencies=[Depends(authorize)],
)
async def patch_recurrence_team_access(
    req: Request,
    recurrence_id: str,
    body: PatchTypesenseRecurrenceTeamAccess,
) -> JSONResponse:
    try:
        if body.value:
            add_team_access_to_recurrence(
                recurrence_id=recurrence_id, team_id=body.teamId
            )
        else:
            remove_team_access_from_recurrence(recurrence_id=recurrence_id)

        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message="Success",
                    success=True,
                )
            )
        )
    except Exception as e:
        err_data = f"recurrence_id: {recurrence_id}, team_id: {body.teamId}"
        logger.error(
            f"[patch_recurrence_team_access] failed patch recurrence team access. {err_data}, err: {e}"
        )
        return JSONResponse(
            content=jsonable_encoder(
                TypesenseDocumentMutationResponse(
                    message=f"Internal server error. Failed to patch recurrence team access. {err_data}",
                    success=False,
                )
            ),
            status_code=500,
        )


@router.delete(
    "/threads/{thread_id}",
    dependencies=[Depends(authorize)],
)
async def delete_thread_by_id(
    req: Request,
    thread_id: str,
    x_user_id: str = Header(),
) -> JSONResponse:
    await (
        Thread.delete().where(Thread.id == thread_id, Thread.user_id == x_user_id).run()
    )

    return JSONResponse(
        content=jsonable_encoder(
            DeleteThreadResponse(
                message="Success",
                success=False,
                data=DeleteThreadResponse.Data(threadId=thread_id),
            )
        )
    )


@router.get(
    "/suggestions",
    dependencies=[Depends(authorize)],
)
async def get_suggestions(
    req: Request,
    limit: Annotated[int, Query(ge=1, le=200)] = 16,
    skip: Annotated[int, Query(ge=0)] = 0,
    x_user_id: str = Header(),
) -> JSONResponse:
    user_id = int(x_user_id)
    count_query = UserSuggestion.count().where(UserSuggestion.user_id == user_id)

    list_query = Suggestion.raw(
        """SELECT * FROM suggestion sug
    LEFT JOIN user_suggestion usg
    ON usg.suggestion_id = sug.id
    WHERE usg.user_id = {}
    ORDER BY sug.created_at DESC
    LIMIT {}
    OFFSET {}
    """,
        user_id,
        limit,
        skip,
    )

    total_count, suggestions_rows = await asyncio.gather(count_query, list_query)
    suggestions = always_used_suggestions

    for idx, sug in enumerate(suggestions_rows):
        raw_sources = json.loads(sug["sources"]) if sug.get("sources") else []
        suggestions.append(
            {
                "id": sug["id"],
                "text": sug["text"],
                "short": sug["short"],
                "explanation": sug["explanation"],
                "createdAt": int(sug["created_at"].timestamp()),
                "updatedAt": int(sug["updated_at"].timestamp()),
                "sources": [
                    {
                        "recurrenceId": raw.get("recurrence_id"),
                        "sessionId": raw.get("recurrence_id"),
                    }
                    for raw in raw_sources
                ],
            }
        )

    while len(suggestions) < min(
        limit, len(suggestions_rows) + len(generic_suggestions)
    ):
        for idx, sug in enumerate(generic_suggestions):
            if sug.get("always_show"):  # already added
                continue
            suggestions.append(sug)
            if idx == len(generic_suggestions) - 1:
                break

    return JSONResponse(
        status_code=http_status.HTTP_200_OK,
        content=jsonable_encoder(
            {
                "message": "Suggestions fetched successfully",
                "data": {
                    "total": total_count,
                    "suggestions": random.sample(suggestions, len(suggestions)),
                },
            }
        ),
    )
