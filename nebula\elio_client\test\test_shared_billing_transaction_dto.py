# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.shared_billing_transaction_dto import SharedBillingTransactionDTO

class TestSharedBillingTransactionDTO(unittest.TestCase):
    """SharedBillingTransactionDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SharedBillingTransactionDTO:
        """Test SharedBillingTransactionDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SharedBillingTransactionDTO`
        """
        model = SharedBillingTransactionDTO()
        if include_optional:
            return SharedBillingTransactionDTO(
                custom_data = elio_client.models.custom_data.customData(),
                items = [
                    elio_client.models.shared/billing_transaction_item_dto.shared.BillingTransactionItemDTO(
                        price = elio_client.models.shared/billing_price_dto.shared.BillingPriceDTO(), )
                    ],
                payments = [
                    elio_client.models.shared/billing_transaction_payment_attempt_dto.shared.BillingTransactionPaymentAttemptDTO(
                        method_details = elio_client.models.shared/billing_payment_method_details_dto.shared.BillingPaymentMethodDetailsDTO(
                            card = elio_client.models.shared/billing_card.shared.BillingCard(), 
                            type = '', ), )
                    ]
            )
        else:
            return SharedBillingTransactionDTO(
                custom_data = elio_client.models.custom_data.customData(),
                items = [
                    elio_client.models.shared/billing_transaction_item_dto.shared.BillingTransactionItemDTO(
                        price = elio_client.models.shared/billing_price_dto.shared.BillingPriceDTO(), )
                    ],
                payments = [
                    elio_client.models.shared/billing_transaction_payment_attempt_dto.shared.BillingTransactionPaymentAttemptDTO(
                        method_details = elio_client.models.shared/billing_payment_method_details_dto.shared.BillingPaymentMethodDetailsDTO(
                            card = elio_client.models.shared/billing_card.shared.BillingCard(), 
                            type = '', ), )
                    ],
        )
        """

    def testSharedBillingTransactionDTO(self):
        """Test SharedBillingTransactionDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
