package meetings

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"encore.app/hubble"
	hubble_api "encore.app/hubble/api"
	"encore.app/meetings/api"
	"encore.app/meetings/database"
	"encore.app/pkg/braid"
	"encore.app/pkg/broadcast"
	db "encore.app/pkg/database"
	"encore.app/pkg/notify"
	"encore.app/shared"
	"encore.dev/beta/auth"
	"encore.dev/rlog"
	"github.com/jmoiron/sqlx"
	"github.com/samber/lo"
)

var sessionNotFoundErr = errors.New("session not found")

// GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead
//
//encore:api auth raw path=/v1.0/sessions/access/in-review method=GET
func (meetings *Meetings) GetInReviewBraidable(w http.ResponseWriter, req *http.Request) {
	sessionID := req.URL.Query().Get("sessionID")
	if err := hubble.IsAuthorized(req.Context(), &hubble_api.IsAuthorizedRequest{
		Sub: shared.GetUserSubjectWithRole(),
		Obj: shared.GetSessionAccessRulesObjectByID(sessionID, "*"),
		Act: shared.AuthPolicyActionList,
	}); err != nil {
		w.WriteHeader(http.StatusForbidden)
		return
	}
	listener := broadcast.NewListener(shared.BCast_InReviewTopic, sessionID)
	defer listener.Close()

	upgrader := braid.Upgrader{}
	upgrader.Braidify(w, req)

	payload := make([]byte, 0) // we will use this payload to verify if we need to send a patch
	for {
		if upgrader.IsSubscription { // We rely on the fact that the subscription hasn't started on the first iteration
			timer := time.NewTimer(60 * time.Second) // Trigger the check every 60 seconds in case we don't get any updates
			select {
			case <-req.Context().Done():
				timer.Stop()
				return
			case <-upgrader.KeepAliveChannel():
				upgrader.SendKeepAlive()
				continue
			case <-timer.C:
			case <-listener.Receive():
			}
		}
		if upgrader.Subscribe {
			upgrader.StartSubscription() // Start the subscription now if we need to still do that
		}

		response, err := GetAllAccessRequests(req.Context(), &api.GetAccessRequests{
			SessionID: sessionID,
		})
		if err != nil {
			if errors.Is(err, sessionNotFoundErr) {
				w.WriteHeader(http.StatusNotFound)
				return
			}
			rlog.Error("Failed to get in review access requests", "err", err)
			w.WriteHeader(http.StatusInternalServerError)
			return
		}
		filteredRows := lo.Filter(response.Data.AccessRules, func(rule api.InReviewAccessRequestDTO, index int) bool {
			return rule.RestrictionStatus == "inReview"
		})
		response.Data.AccessRules = filteredRows

		newPayload, _ := json.Marshal(response)
		err = upgrader.SendJSONOrPatch(payload, newPayload)

		if err != nil {
			rlog.Error("Failed to send JSON", "err", err)
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		if !upgrader.Subscribe {
			return // We can end the request here
		}

		payload = newPayload // this payload becomes the next state
	}
}

func getUserActiveLobbies(ctx context.Context) []shared.LobbyDTO {
	uid, authed := auth.UserID()
	if !authed {
		return make([]shared.LobbyDTO, 0)
	}
	user, err := GetUserByID(ctx, &api.GetUserByIDRequest{UserID: string(uid)})
	if err != nil || user == nil || user.Lobbies == nil {
		return make([]shared.LobbyDTO, 0)
	}
	return lo.Filter(*user.Lobbies, func(item shared.LobbyDTO, index int) bool {
		return item.IsActive
	})
}

//encore:api auth raw path=/v1.0/sessions/access/grouped method=GET
func (meetings *Meetings) GetAccessRequestsBraidable(w http.ResponseWriter, req *http.Request) {
	sessionID := req.URL.Query().Get("sessionID")
	if err := hubble.IsAuthorized(req.Context(), &hubble_api.IsAuthorizedRequest{
		Sub: shared.GetUserSubjectWithRole(),
		Obj: shared.GetSessionAccessRulesObjectByID(sessionID, "*"),
		Act: shared.AuthPolicyActionList,
	}); err != nil {
		w.WriteHeader(http.StatusForbidden)
		return
	}
	activeLobbies := getUserActiveLobbies(req.Context())

	listenerInReview := broadcast.NewListener(shared.BCast_InReviewTopic, sessionID)
	defer listenerInReview.Close()
	// TODO we need the ability to listen to multiple lobbies somehow for now we default 0 and use the first active lobby if exists
	lobbyID := "0"
	if len(activeLobbies) > 0 {
		lobbyID = activeLobbies[0].LobbyID
	}
	listenerLobbies := broadcast.NewListener(shared.BCast_LobbiesUpdated, lobbyID)
	defer listenerLobbies.Close()

	upgrader := braid.Upgrader{}
	upgrader.Braidify(w, req)

	payload := make([]byte, 0)
	response := api.GetAccessRequestResponseDTO{}
	staleAccess := true
	staleLobbies := true
	for {
		if upgrader.IsSubscription { // We rely on the fact that the subscription hasn't started on the first iteration
			timer := time.NewTimer(3 * time.Minute) // Trigger the check every 3 minutes in case we don't get any updates
			select {
			case <-req.Context().Done():
				timer.Stop()
				return
			case <-upgrader.KeepAliveChannel():
				upgrader.SendKeepAlive()
				continue
			case <-listenerLobbies.Receive():
				staleLobbies = true
			case <-timer.C:
			case <-listenerInReview.Receive():
				staleAccess = true
			}
		}
		if upgrader.Subscribe {
			upgrader.StartSubscription() // Start the subscription now if we need to still do that
		}

		if staleAccess {
			accessResponse, err := GetSessionAccessRules(req.Context(), &api.GetSessionAccessRulesRequest{
				SessionID: sessionID,
			})
			if err != nil {
				if errors.Is(err, sessionNotFoundErr) {
					w.WriteHeader(http.StatusNotFound)
					return
				}
				rlog.Error("Failed to get in review access requests", "err", err)
				w.WriteHeader(http.StatusInternalServerError)
				return
			}
			response.Data.Granted = make([]api.SessionAccessDTO, 0)
			response.Data.InReview = make([]api.SessionAccessDTO, 0)
			response.Data.Denied = make([]api.SessionAccessDTO, 0)
			lo.ForEach(accessResponse.Data, func(item api.SessionAccessDTO, index int) {
				switch item.RestrictionStatus {
				case api.RestrictionStatusGranted:
					// for domain rules we need to push it to the top of the slice
					if item.Type == api.AccessRuleDomain {
						response.Data.Granted = append([]api.SessionAccessDTO{item}, response.Data.Granted...)
					} else {
						response.Data.Granted = append(response.Data.Granted, item)
					}
				case api.RestrictionStatusInReview:
					response.Data.InReview = append(response.Data.InReview, item)
				case api.RestrictionStatusDenied:
					response.Data.Denied = append(response.Data.Denied, item)
				}
			})
		}

		if staleLobbies {
			response.Data.Lobbies = lo.FlatMap(activeLobbies, func(lobby shared.LobbyDTO, index int) []api.LobbyParticipantSummaryDTO {
				guestsResponse, err := GetLobbyGuests(req.Context(), &api.LobbyParticipantListRequest{LobbyID: lobbyID})
				if err != nil {
					return make([]api.LobbyParticipantSummaryDTO, 0)
				}

				currentUids := make([]string, 0)
				nonOwners := lo.Filter(guestsResponse.Participants, func(item shared.LobbyParticipantDTO, index int) bool {
					if !lo.Contains(currentUids, item.ID) {
						currentUids = append(currentUids, item.ID)
						return !item.IsOwner
					}
					return false
				})
				return lo.Map(nonOwners, func(item shared.LobbyParticipantDTO, index int) api.LobbyParticipantSummaryDTO {
					summaryDTO := api.LobbyParticipantSummaryDTO{
						LobbySlug:     lobby.Slug,
						LobbyID:       lobby.LobbyID,
						ParticipantID: item.LobbyParticipantID,
					}
					summaryDTO.User.Avatar = item.Avatar
					summaryDTO.User.FirstName = item.FirstName
					summaryDTO.User.LastName = item.LastName
					summaryDTO.User.FullName = item.FullName

					return summaryDTO
				})
			})
		}

		newPayload, _ := json.Marshal(response)
		if err := upgrader.SendJSONOrPatch(payload, newPayload); err != nil {
			rlog.Error("Failed to send JSON", "err", err)
			w.WriteHeader(http.StatusInternalServerError)
			return
		}

		if !upgrader.Subscribe {
			return // We can end the request here
		}

		payload = newPayload // this payload becomes the next state
		staleAccess = false
		staleLobbies = false
	}
}

// createFrictionlessAccessRules create access rule with the appropriate restriction status:
//   - for unlocked sessions: granted so the user can access the session without host approval
//   - for locked sessions: inReview so the host can choose to approve or deny the request
//
// Only creates new access rule if the session is in scheduled or active state.
func createFrictionlessAccessRules(ctx context.Context, value string, session shared.SessionDTO, isSignup bool, guest *shared.GuestDTO) *database.AccessRuleRow {
	if value == "" {
		return nil
	}
	if session.SessionState != shared.SessionStateScheduled && session.SessionState != shared.SessionStateActive {
		return nil
	}
	accessRule := &database.AccessRuleRow{
		SessionID:           shared.ParseInt(session.SessionID),
		SessionRecurrenceID: shared.ParseInt(session.SessionRecurrenceID),
		AccessType:          string(api.AccessTypeViewer),
		Type:                string(api.AccessRuleEmail),
		Value:               value,
		RestrictionStatus:   string(api.RestrictionStatusGranted),
		IsExternalRequest:   false,
		CreatedAt:           time.Now().Unix(),
		UpdatedAt:           time.Now().Unix(),
	}
	if session.AccessStatus == shared.SessionDTOAccessStatusLocked {
		accessRule.RestrictionStatus = string(api.RestrictionStatusInReview)
		if !isSignup {
			return nil
		}
	}
	if guest != nil {
		accessRule.Type = string(api.AccessRuleGuest)
		accessRule.Value = shared.FormatInt(guest.SurrogateID)
		accessRule.GuestSurrogateID = sql.NullInt64{Valid: true, Int64: guest.SurrogateID}
		accessRule.GuestFullName = sql.NullString{Valid: true, String: guest.FullName}
		accessRule.GuestEmail = sql.NullString{Valid: true, String: guest.Email}
	}
	cols, err := database.InsertAccessRequest.Execx(ctx, accessRule)
	if err != nil {
		rlog.Error("Failed to create frictionless access rule", "err", err)
		return nil
	}
	accessRule.Id = shared.ParseInt(cols[0])
	broadcast.PubSub.Pub(shared.BCast_InReviewTopic, []byte{}, session.SessionID)
	return accessRule
}

// ResolveAccessRequest retrieves the access request for a session, preventively creating access rules when needed.
// Returns non nil api.SessionAccessDTO when session access dto is created or found, nil when no access request is found.
func ResolveAccessRequest(ctx context.Context, decodedToken shared.DecodedToken, session shared.SessionDTO, fingerprint string) (*api.SessionAccessDTO, error) {
	// Init query args to  fetch all known access requests for the provided session/recurrence
	args := db.Args{"sessionID": session.SessionID, "recurrenceID": session.SessionRecurrenceID}
	// Init err in case there is a failure inside the query callback
	var err error = nil
	// Init the found flag, which is assigned when a match is found in the access rules for the user
	found := false
	// Assign string version of the guest surrogate ID, which is used to match the access rule for the guest
	guestID := shared.FormatInt(decodedToken.GuestSurrogateID)
	// Init the AccessRuleRow record which we might find in the database
	usersRequest := api.SessionAccessDTO{}
	// Execute query to get all access requests for the session
	database.GetAccessRequests.Queryx(ctx, args, func(rows *sqlx.Rows, err2 error) bool {
		// Check if there was an error executing the query
		if err = err2; err != nil {
			// Return false to end the query iteration
			return false
		}
		// Init a destination for the access rule row
		rule := database.AccessRuleRow{}
		// Return false if there is an error scanning the row
		if err = rows.StructScan(&rule); err != nil {
			rlog.Error("Failed to scan access rule", "err", err)
			return false
		}
		// When the UserID is nonempty it means the user is a registered user
		if decodedToken.UserID != "" {
			// Because we trust the decoded token's email we can use it to match the access request with the same email
			if rule.Type == string(api.AccessRuleEmail) && decodedToken.Email == rule.Value {
				// Convert the database access rule row to the API DTO
				usersRequest = rule.ToSessionAccessDTO()
				// Assign the found flag to true
				found = true
				// We found the access rule, we can stop iterating
				return false
			}
			// As a lower priority fallback we can also match the access rule by the domain
			if rule.Type == string(api.AccessRuleDomain) && decodedToken.Domain == rule.Value {
				usersRequest = rule.ToSessionAccessDTO()
				found = true
				// Even if we found the access rule by the domain, we still want to continue iterating in case we find a more specific rule by email
				return true
			}

		}
		// When the surrogate guest ID is non-zero it means the user is a guest not registered as a user
		// we try to find the access rule by matching the rule value with the guest surrogate ID
		if decodedToken.GuestSurrogateID != 0 && rule.Type == string(api.AccessRuleGuest) && rule.Value == guestID {
			usersRequest = rule.ToSessionAccessDTO()
			found = true
			// Stop the query iteration since there will only be one valid type or rule for guests
			return false
		}
		// Keep iterating until the above conditions are met
		return true
	})
	// Return early if the meetings.GetSessionAccessRules call fails
	if err != nil {
		return nil, err
	}
	// If we still cant find the rule we call createFrictionlessAccessRules which will create a new rule if logic defined in that function allows it
	if !found && decodedToken.UserID != "" {
		if dbRow := createFrictionlessAccessRules(ctx, decodedToken.Email, session, false, nil); dbRow != nil {
			// Assign the new access rule if created so it can be returned later
			usersRequest = dbRow.ToSessionAccessDTO()
			found = true
		}
	}
	// If no rule is found by the guest surrogate ID, we call createFrictionlessAccessRules which will create a new rule for the guest
	if !found && decodedToken.GuestSurrogateID != 0 {
		if dbRow := createFrictionlessAccessRules(ctx, guestID, session, false, decodedToken.GuestDTO()); dbRow != nil {
			usersRequest = dbRow.ToSessionAccessDTO()
			found = true
		}
	}
	// If we found a access rule for the user, we can proceed to upsert the session user
	if found {
		// Security: Skip upsert if the user has not been granted access yet to avoid queries assuming access was granted.
		if usersRequest.RestrictionStatus != api.RestrictionStatusGranted {
			return &usersRequest, nil
		}
		// Build an upsert request wit the user ID and session-recurrence IDs
		upsertReq := &api.UpsertSessionUserRequest{UserID: &decodedToken.UserID, SessionID: session.SessionID, SessionRecurrenceID: &session.SessionRecurrenceID}
		if usersRequest.Type == "guest" {
			upsertReq.UserID = nil
			upsertReq.GuestSurrogateID = &decodedToken.GuestSurrogateID
		}
		// If unique fingerprint header string is provided, we will set it in the session user row
		if fingerprint != "" {
			upsertReq.Fingerprint = lo.ToPtr([]string{fingerprint})
		}
		if upsertErr := UpsertSessionUser(ctx, upsertReq); upsertErr != nil {
			rlog.Error("Failed to upsert session user", "err", upsertErr)
		}
		return &usersRequest, nil
	}
	// If the user is the session creator we want to always return a granted access rule even if we don't insert it
	// TODO this is kind of a weird pattern, can we confirm that the frontend expects an access rule returned for hosts or does it simply ignore
	if decodedToken.UserID != "" && decodedToken.UserID == session.SessionCreatorUserID {
		return &api.SessionAccessDTO{
			SessionID:         session.SessionID,
			RestrictionStatus: api.RestrictionStatusGranted,
		}, nil
	}
	return nil, nil
}

// GetSessionAccessRules returns all access rules for a session
//
//encore:api private tag:trixta
func (meetings *Meetings) GetSessionAccessRules(ctx context.Context, req *api.GetSessionAccessRulesRequest) (*api.GetSessionAccessRulesResponse, error) {
	response := &api.GetSessionAccessRulesResponse{}
	response.Data = make([]api.SessionAccessDTO, 0)
	userEmailsMap := make(map[string]int)
	var err error
	if req.SessionRecurrenceID == "" {
		sessionRecurrenceID, _ := database.GetRecurrenceID(ctx, req.SessionID)
		if sessionRecurrenceID == "" {
			return nil, sessionNotFoundErr
		}
		req.SessionRecurrenceID = sessionRecurrenceID
	}
	args := db.Args{"sessionID": req.SessionID, "recurrenceID": req.SessionRecurrenceID}
	database.GetAccessRequests.Queryx(ctx, args, func(rows *sqlx.Rows, err2 error) bool {
		err = err2
		if err != nil {
			rlog.Error("Failed to query for in review access requests", "err", err)
			return false
		}
		var rule database.AccessRuleRow
		if err = rows.StructScan(&rule); err != nil {
			rlog.Error("Failed to scan access rule", "err", err)
			return false
		}
		row := rule.ToSessionAccessDTO()
		if row.Type == api.AccessRuleEmail {
			userEmailsMap[row.User.Email] = len(response.Data)
		}
		response.Data = append(response.Data, row)
		return true
	})

	database.GetUserSummariesByEmail(ctx, MarsDB, func(userSummary database.UserSummaryRow, err2 error) bool {
		err = err2
		if err != nil {
			rlog.Error("Failed to query for in review access requests", "err", err)
			return false
		}
		index := userEmailsMap[userSummary.Email]
		response.Data[index].User.Avatar = userSummary.Avatar
		response.Data[index].User.FirstName = &userSummary.FirstName
		response.Data[index].User.LastName = &userSummary.LastName
		response.Data[index].User.FullName = userSummary.FullName()

		return true
	}, lo.Keys(userEmailsMap)...)

	return response, nil
}

// GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead
// TODO evaluate tag:trixta in this endpoint, if it's used we should make a specific one for external requests
//
//encore:api auth method=GET tag:trixta
func (meetings *Meetings) GetAllAccessRequests(ctx context.Context, req *api.GetAccessRequests) (*api.AccessRequestsResponse, error) {
	response := api.AccessRequestsResponse{}
	response.Data.AccessRules = make([]api.InReviewAccessRequestDTO, 0)
	userEmailsMap := make(map[string]int)
	var err error
	if req.SessionRecurrenceID == "" {
		sessionRecurrenceID, _ := database.GetRecurrenceID(ctx, req.SessionID)
		if sessionRecurrenceID == "" {
			return nil, sessionNotFoundErr
		}
		req.SessionRecurrenceID = sessionRecurrenceID
	}
	// for rule, err := range meetings.accessRules.GetAccessRequests(req.SessionID, req.SessionRecurrenceID) {
	args := db.Args{"sessionID": req.SessionID, "recurrenceID": req.SessionRecurrenceID}
	database.GetAccessRequests.Queryx(ctx, args, func(rows *sqlx.Rows, err2 error) bool {
		err = err2
		if err != nil {
			rlog.Error("Failed to query for in review access requests", "err", err)
			return false
			// return nil, err
		}
		var rule database.AccessRuleRow
		err = rows.StructScan(&rule)
		if err != nil {
			rlog.Error("Failed to scan access rule", "err", err)
			return false
		}
		if req.RestrictionStatus != "" && rule.RestrictionStatus != req.RestrictionStatus {
			return true
		}
		row := rule.ToAccessRequestDTO()
		if rule.Type == "email" {
			userEmailsMap[row.Value] = len(response.Data.AccessRules)
		}
		response.Data.AccessRules = append(response.Data.AccessRules, row)
		return true
	})

	// for userSummary, err := range meetings.users.GetUserSummariesByID(lo.Keys(userEmailsMap)...) {
	database.GetUserSummariesByEmail(ctx, MarsDB, func(userSummary database.UserSummaryRow, err2 error) bool {
		err = err2
		if err != nil {
			rlog.Error("Failed to query for in review access requests", "err", err)
			return false
		}
		index := userEmailsMap[userSummary.Email]
		response.Data.AccessRules[index].User.Id = strconv.FormatInt(userSummary.Id, 10)
		response.Data.AccessRules[index].User.Avatar = userSummary.Avatar
		response.Data.AccessRules[index].User.FirstName = userSummary.FirstName
		response.Data.AccessRules[index].User.LastName = userSummary.LastName
		return true
	}, lo.Keys(userEmailsMap)...)

	response.Data.Cursor = nil
	response.Data.TotalCount = len(response.Data.AccessRules)
	return &response, nil
}

//encore:api auth path=/v1.0/sessions/access/request-access method=POST
func (meetings *Meetings) CreateAccessRequest(ctx context.Context, req *api.CreateAccessRequest) error {
	token := auth.Data().(*shared.DecodedToken)
	session, err := database.GetSessionByID(ctx, req.SessionID)
	if err != nil || session == nil {
		return &shared.HttpErrorResp{
			Code:    http.StatusNotFound,
			ErrCode: shared.SessionNotFoundErrorCode,
			Message: "Session not found",
		}
	}
	hostsEmails, err := database.GetUsersMapByID(ctx, MarsDB, lo.Map(session.HostUserIDs, func(item int64, index int) string {
		return shared.FormatInt(item)
	}))
	if err != nil {
		return err
	}

	userEmail := ""
	userFirstName := ""
	userLastName := ""
	var reqRow *database.AccessRuleRow = nil
	if token.UserID != "" {
		database.GetUserSummariesByID(ctx, MarsDB, func(userSummary database.UserSummaryRow, err error) bool {
			if err == nil {
				userEmail = userSummary.Email
				userFirstName = userSummary.FirstName
				userLastName = userSummary.LastName
			}
			return false
		}, token.UserID)
		if userEmail == "" {
			return errors.New("user not found")
		}
		reqRow = &database.AccessRuleRow{
			Type:                "email",
			RestrictionStatus:   "inReview",
			SessionID:           shared.ParseInt(req.SessionID),
			SessionRecurrenceID: session.RecurrenceID,
			Value:               userEmail,
			AccessType:          "viewer",
			CreatedAt:           time.Now().Unix(),
			UpdatedAt:           time.Now().Unix(),
		}
	}
	if token.GuestSurrogateID != 0 {
		userEmail = token.GuestEmail
		userFirstName = strings.Split(token.GuestFullName, " ")[0]
		userLastName = strings.Join(strings.Split(token.GuestFullName, " ")[1:], " ")
		reqRow = &database.AccessRuleRow{
			Type:                "guest",
			RestrictionStatus:   "inReview",
			SessionID:           shared.ParseInt(req.SessionID),
			SessionRecurrenceID: session.RecurrenceID,
			Value:               shared.FormatInt(token.GuestSurrogateID),
			AccessType:          "viewer",
			GuestSurrogateID:    sql.NullInt64{Valid: true, Int64: token.GuestSurrogateID},
			GuestFullName:       sql.NullString{Valid: true, String: token.GuestFullName},
			GuestEmail:          sql.NullString{Valid: true, String: token.GuestEmail},
			CreatedAt:           time.Now().Unix(),
			UpdatedAt:           time.Now().Unix(),
		}
	}
	if req.RequestMessage != nil {
		reqRow.RequestMessage = sql.NullString{
			String: *req.RequestMessage,
			Valid:  true,
		}
	}
	cols, err := database.InsertAccessRequest.Execx(ctx, reqRow)
	if err == nil {
		parsedPublicUrl, _ := url.Parse(session.URL)
		reqRow.Id = shared.ParseInt(cols[0])
		broadcast.PubSub.Pub(shared.BCast_InReviewTopic, []byte{}, req.SessionID)
		meetings.Notify.Session(func(events notify.SessionEvents) {
			emailCtx := notify.SessionAccessRequestedContext{SessionID: req.SessionID,
				SessionRecurrenceID:      shared.FormatInt(session.RecurrenceID),
				AttendeeFirstName:        userFirstName,
				AttendeeLastName:         userLastName,
				AttendeeEmail:            userEmail,
				SessionTitle:             session.Title,
				AccessRequestMessage:     reqRow.RequestMessage.String,
				SessionShareDashboardUrl: fmt.Sprintf("https://%s/dashboard/sessions/%d/share", parsedPublicUrl.Hostname(), session.ID),
			}
			if session.State == shared.SessionStateActive {
				return // Skip sending emails if the session is active
			}
			recipients := lo.FlatMap(lo.Values(hostsEmails), func(item database.UserRow, index int) []string {
				if item.Email.Valid {
					return []string{item.Email.String}
				}
				return []string{}
			})
			events.SessionAccessRequested(recipients, emailCtx)
		})
		return nil
	}
	if strings.Contains(err.Error(), "SESSION_ACCESS_RULE_SESSION_ID_RECURRENCE_ID_TYPE_ACCESS_TYPE_V") {
		return &shared.HttpErrorResp{
			Code:    http.StatusConflict,
			ErrCode: shared.SessionAccessRequestedAlready,
			Message: "Session access requested already",
		}
	}
	return err
}

//encore:api auth path=/v1.0/sessions/access/get-in-review-access-requests-count method=POST
func (meetings Meetings) CountInReviewRequests(ctx context.Context, req *api.CountInReviewRequests) (*api.CountInReviewRequestsResponse, error) {
	response := &api.CountInReviewRequestsResponse{}
	response.Data = lo.Map(req.Sessions, func(session api.SessionRequestCount, index int) api.SessionRequestCount {
		session.Count, _ = database.CountInReviewRequests(ctx, session.Id, session.RecurrenceID)
		return session
	})
	return response, nil
}
