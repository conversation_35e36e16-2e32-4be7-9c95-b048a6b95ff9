# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_meetings_upsert_meeting_suggestions200_response import POSTMeetingsUpsertMeetingSuggestions200Response

class TestPOSTMeetingsUpsertMeetingSuggestions200Response(unittest.TestCase):
    """POSTMeetingsUpsertMeetingSuggestions200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTMeetingsUpsertMeetingSuggestions200Response:
        """Test POSTMeetingsUpsertMeetingSuggestions200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTMeetingsUpsertMeetingSuggestions200Response`
        """
        model = POSTMeetingsUpsertMeetingSuggestions200Response()
        if include_optional:
            return POSTMeetingsUpsertMeetingSuggestions200Response(
                count = 56,
                message = '',
                success = True
            )
        else:
            return POSTMeetingsUpsertMeetingSuggestions200Response(
                count = 56,
                message = '',
                success = True,
        )
        """

    def testPOSTMeetingsUpsertMeetingSuggestions200Response(self):
        """Test POSTMeetingsUpsertMeetingSuggestions200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
