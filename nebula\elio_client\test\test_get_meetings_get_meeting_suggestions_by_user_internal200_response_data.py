# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_meetings_get_meeting_suggestions_by_user_internal200_response_data import GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData

class TestGETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData(unittest.TestCase):
    """GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData:
        """Test GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData`
        """
        model = GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData()
        if include_optional:
            return GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData(
                suggestions = [
                    elio_client.models.api/suggestion_response_dto.api.SuggestionResponseDTO(
                        category = '', 
                        content = '', 
                        created_at = 56, 
                        id = '', 
                        is_personal = True, )
                    ],
                total = 56
            )
        else:
            return GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData(
                suggestions = [
                    elio_client.models.api/suggestion_response_dto.api.SuggestionResponseDTO(
                        category = '', 
                        content = '', 
                        created_at = 56, 
                        id = '', 
                        is_personal = True, )
                    ],
                total = 56,
        )
        """

    def testGETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData(self):
        """Test GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
