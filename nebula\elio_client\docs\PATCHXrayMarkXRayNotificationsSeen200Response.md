# PATCHXrayMarkXRayNotificationsSeen200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**ApiMarkXRayNotificationsSeenResponseData**](ApiMarkXRayNotificationsSeenResponseData.md) |  | 
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.patch_xray_mark_x_ray_notifications_seen200_response import PATCHXrayMarkXRayNotificationsSeen200Response

# TODO update the JSON string below
json = "{}"
# create an instance of PATCHXrayMarkXRayNotificationsSeen200Response from a JSON string
patch_xray_mark_x_ray_notifications_seen200_response_instance = PATCHXrayMarkXRayNotificationsSeen200Response.from_json(json)
# print the JSON string representation of the object
print(PATCHXrayMarkXRayNotificationsSeen200Response.to_json())

# convert the object into a dict
patch_xray_mark_x_ray_notifications_seen200_response_dict = patch_xray_mark_x_ray_notifications_seen200_response_instance.to_dict()
# create an instance of PATCHXrayMarkXRayNotificationsSeen200Response from a dict
patch_xray_mark_x_ray_notifications_seen200_response_from_dict = PATCHXrayMarkXRayNotificationsSeen200Response.from_dict(patch_xray_mark_x_ray_notifications_seen200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


