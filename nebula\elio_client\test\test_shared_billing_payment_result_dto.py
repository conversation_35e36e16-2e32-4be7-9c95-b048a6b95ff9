# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.shared_billing_payment_result_dto import SharedBillingPaymentResultDTO

class TestSharedBillingPaymentResultDTO(unittest.TestCase):
    """SharedBillingPaymentResultDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SharedBillingPaymentResultDTO:
        """Test SharedBillingPaymentResultDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SharedBillingPaymentResultDTO`
        """
        model = SharedBillingPaymentResultDTO()
        if include_optional:
            return SharedBillingPaymentResultDTO(
                error_code = '',
                status = ''
            )
        else:
            return SharedBillingPaymentResultDTO(
                error_code = '',
                status = '',
        )
        """

    def testSharedBillingPaymentResultDTO(self):
        """Test SharedBillingPaymentResultDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
