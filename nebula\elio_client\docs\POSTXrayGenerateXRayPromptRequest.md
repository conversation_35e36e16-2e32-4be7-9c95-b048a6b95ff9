# POSTXrayGenerateXRayPromptRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**description** | **str** |  | 

## Example

```python
from elio_client.models.post_xray_generate_x_ray_prompt_request import POSTXrayGenerateXRayPromptRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTXrayGenerateXRayPromptRequest from a JSON string
post_xray_generate_x_ray_prompt_request_instance = POSTXrayGenerateXRayPromptRequest.from_json(json)
# print the JSON string representation of the object
print(POSTXrayGenerateXRayPromptRequest.to_json())

# convert the object into a dict
post_xray_generate_x_ray_prompt_request_dict = post_xray_generate_x_ray_prompt_request_instance.to_dict()
# create an instance of POSTXrayGenerateXRayPromptRequest from a dict
post_xray_generate_x_ray_prompt_request_from_dict = POSTXrayGenerateXRayPromptRequest.from_dict(post_xray_generate_x_ray_prompt_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


