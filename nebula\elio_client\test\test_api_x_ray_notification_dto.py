# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_x_ray_notification_dto import ApiXRayNotificationDTO

class TestApiXRayNotificationDTO(unittest.TestCase):
    """ApiXRayNotificationDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiXRayNotificationDTO:
        """Test ApiXRayNotificationDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiXRayNotificationDTO`
        """
        model = ApiXRayNotificationDTO()
        if include_optional:
            return ApiXRayNotificationDTO(
                content = '',
                created_at = 56,
                id = 56,
                seen = True,
                source = elio_client.models.source.source(),
                updated_at = 56,
                user_id = 56,
                xray_doc_commit_id = 56
            )
        else:
            return ApiXRayNotificationDTO(
                content = '',
                created_at = 56,
                id = 56,
                seen = True,
                source = elio_client.models.source.source(),
                updated_at = 56,
                user_id = 56,
                xray_doc_commit_id = 56,
        )
        """

    def testApiXRayNotificationDTO(self):
        """Test ApiXRayNotificationDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
