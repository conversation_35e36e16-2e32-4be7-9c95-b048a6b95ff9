# SharedSubscriptionPlanConfigDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**ai_feed** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**bots** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**created_at** | **int** |  | 
**crm** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**custom_feed_items** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**custom_integrations** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**id** | **str** |  | 
**integrations** | [**SharedSubscriptionPlanConfigDTOIntegrations**](SharedSubscriptionPlanConfigDTOIntegrations.md) |  | 
**meeting_memory** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**meeting_summary** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**meeting_templates** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**meeting_workflows** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**meetings** | [**SharedSubscriptionPlanConfigDTOMeetings**](SharedSubscriptionPlanConfigDTOMeetings.md) |  | 
**model_segregation** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**off_the_record** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**paddle_product_id** | **str** |  | 
**paddle_product_name** | **str** |  | 
**queue_mode** | [**SharedSubscriptionPlanConfigDTOAiFeed**](SharedSubscriptionPlanConfigDTOAiFeed.md) |  | 
**recording** | [**SharedSubscriptionPlanConfigDTORecording**](SharedSubscriptionPlanConfigDTORecording.md) |  | 
**stream** | [**SharedSubscriptionPlanConfigDTOStream**](SharedSubscriptionPlanConfigDTOStream.md) |  | 
**support** | [**SharedSubscriptionPlanConfigDTOSupport**](SharedSubscriptionPlanConfigDTOSupport.md) |  | 
**time_limit** | [**SharedSubscriptionPlanConfigDTOTimeLimit**](SharedSubscriptionPlanConfigDTOTimeLimit.md) |  | 
**updated_at** | **int** |  | 

## Example

```python
from elio_client.models.shared_subscription_plan_config_dto import SharedSubscriptionPlanConfigDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedSubscriptionPlanConfigDTO from a JSON string
shared_subscription_plan_config_dto_instance = SharedSubscriptionPlanConfigDTO.from_json(json)
# print the JSON string representation of the object
print(SharedSubscriptionPlanConfigDTO.to_json())

# convert the object into a dict
shared_subscription_plan_config_dto_dict = shared_subscription_plan_config_dto_instance.to_dict()
# create an instance of SharedSubscriptionPlanConfigDTO from a dict
shared_subscription_plan_config_dto_from_dict = SharedSubscriptionPlanConfigDTO.from_dict(shared_subscription_plan_config_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


