import asyncio
from nebula.temporal.types import XRayDigestWorkflowParams
from nebula.temporal.workflows.xray_digest_wf import XRayDigestWorkflow
from temporalio import workflow
from temporalio.client import (
    Client,
    Schedule,
    ScheduleSpec,
    ScheduleActionStartWorkflow,
    ScheduleState,
)

interrupt_event = asyncio.Event()


with workflow.unsafe.imports_passed_through():
    import logging
    from nebula.temporal.client import create_temporal_client


async def schedule_digest_wf(client: Client):
    """Start X-Ray digest scan workflow for testing / once off run."""
    # Example parameters - adjust these for your testing
    xray_id = 1
    # recurrence_id = 1308724707679274372  # Replace with actual recurrence ID
    # user_id = 1053583008122013541  # Replace with actual user ID

    await client.create_schedule(
        f"xray-digest-schedule-{xray_id}",
        Schedule(
            action=ScheduleActionStartWorkflow(
                XRayDigestWorkflow.run,
                XRayDigestWorkflowParams(xray_id=xray_id),
                id=f"xray-digest-workflow-{xray_id}",
                task_queue="digest-task-queue",
            ),
            spec=ScheduleSpec(cron_expressions=["*/5 * * * *"]),
            state=ScheduleState(note="A test workflow that runs every 5 minutes"),
        ),
    )


async def main():
    try:
        logging.basicConfig(level=logging.INFO)
        logging.info("Creating client")
        client = await create_temporal_client()

        logging.info("Starting schedule")
        await schedule_digest_wf(client)
        logging.info("Schedule started")
    except Exception as e:
        print(f"Error: {e}")
        raise e


if __name__ == "__main__":
    asyncio.run(main())
