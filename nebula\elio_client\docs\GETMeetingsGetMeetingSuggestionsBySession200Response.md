# GETMeetingsGetMeetingSuggestionsBySession200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**suggestions** | [**List[ApiMeetingSuggestionDTO]**](ApiMeetingSuggestionDTO.md) |  | 

## Example

```python
from elio_client.models.get_meetings_get_meeting_suggestions_by_session200_response import GETMeetingsGetMeetingSuggestionsBySession200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETMeetingsGetMeetingSuggestionsBySession200Response from a JSON string
get_meetings_get_meeting_suggestions_by_session200_response_instance = GETMeetingsGetMeetingSuggestionsBySession200Response.from_json(json)
# print the JSON string representation of the object
print(GETMeetingsGetMeetingSuggestionsBySession200Response.to_json())

# convert the object into a dict
get_meetings_get_meeting_suggestions_by_session200_response_dict = get_meetings_get_meeting_suggestions_by_session200_response_instance.to_dict()
# create an instance of GETMeetingsGetMeetingSuggestionsBySession200Response from a dict
get_meetings_get_meeting_suggestions_by_session200_response_from_dict = GETMeetingsGetMeetingSuggestionsBySession200Response.from_dict(get_meetings_get_meeting_suggestions_by_session200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


