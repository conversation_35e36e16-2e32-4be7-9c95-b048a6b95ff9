#!/usr/bin/env python3

"""
Test script to demonstrate transcript processing with user IDs.
This shows how the system formats transcripts for multi-user meeting suggestions.
"""

from elio_client.models.shared_transcription_dto import SharedTranscriptionDTO
from nebula.shared.transcriptions import get_all_transcriptions_with_user_ids


def create_sample_transcription_dtos():
    """Create sample transcription DTOs based on the provided transcript example."""
    
    # Sample transcriptions with user IDs
    transcriptions = [
        SharedTranscriptionDTO(
            batchID="batch_1",
            id="trans_1",
            sessionID="123",
            sessionRecurrenceID="456",
            speakerUID="user_radek_123",
            speakerUserID="user_radek_123",  # This is the key field for user ID
            speakerFullName="Radek Rumi",
            speakerFirstName="Radek", 
            speakerLastName="Rumi",
            speakerEmail="<EMAIL>",
            text="The most tense waters on Earth better or worse, Malaysia is probably an economy we are going to start hearing a lot more about in coming years.",
            timeUnix=1733741037000,  # 2025-06-09 14:03:57
            languageLocale="en-US"
        ),
        SharedTranscriptionDTO(
            batchID="batch_1",
            id="trans_2",
            sessionID="123",
            sessionRecurrenceID="456",
            speakerUID="user_radek_123", 
            speakerUserID="user_radek_123",
            speakerFullName="Radek Rumi",
            speakerFirstName="Radek",
            speakerLastName="Rumi", 
            speakerEmail="<EMAIL>",
            text="So as always, there are a few important things to understand. What has been behind Malaysia's economic success?",
            timeUnix=1733741067000,  # 30 seconds later
            languageLocale="en-US"
        ),
        SharedTranscriptionDTO(
            batchID="batch_1",
            id="trans_3",
            sessionID="123",
            sessionRecurrenceID="456",
            speakerUID="user_john_456",
            speakerUserID="user_john_456",  # Different user
            speakerFullName="John Doe",
            speakerFirstName="John",
            speakerLastName="Doe",
            speakerEmail="<EMAIL>", 
            text="That's a great question about Malaysia's development model. I think the foreign investment strategy was particularly clever.",
            timeUnix=1733741097000,  # Another 30 seconds later
            languageLocale="en-US"
        ),
        SharedTranscriptionDTO(
            batchID="batch_1",
            id="trans_4",
            sessionID="123",
            sessionRecurrenceID="456",
            speakerUID="user_radek_123",  # Back to first speaker
            speakerUserID="user_radek_123",
            speakerFullName="Radek Rumi",
            speakerFirstName="Radek",
            speakerLastName="Rumi",
            speakerEmail="<EMAIL>",
            text="What challenges is it goes to face as it becomes a fully advanced economy? And finally, what will this growth mean for the region as a whole?",
            timeUnix=1733741127000,  # Another 30 seconds later
            languageLocale="en-US"
        )
    ]
    
    return transcriptions


def test_transcript_processing_with_user_ids():
    """Test the transcript processing with user IDs."""
    
    print("=== Testing Transcript Processing with User IDs ===\n")
    
    # Create sample transcription data
    transcriptions = create_sample_transcription_dtos()
    
    print("Input transcriptions:")
    for i, t in enumerate(transcriptions, 1):
        print(f"{i}. Speaker: {t.speaker_full_name} (ID: {t.speaker_user_id})")
        print(f"   Text: {t.text[:50]}...")
        print()
    
    # Process with user IDs
    formatted_transcripts, locale, unique_users = get_all_transcriptions_with_user_ids(transcriptions)
    
    print("=== PROCESSED OUTPUT ===\n")
    print(f"Detected locale: {locale}")
    print(f"Unique users found: {len(unique_users)}")
    for user_id, name in unique_users:
        print(f"  - {name} (ID: {user_id})")
    
    print(f"\nFormatted transcript with user IDs:")
    print("-" * 80)
    
    full_transcript = "\n".join(formatted_transcripts)
    print(full_transcript)
    
    print("-" * 80)
    print(f"\nTotal transcript length: {len(full_transcript)} characters")
    
    # Show how this would be used in meeting suggestions
    print("\n=== HOW THIS GETS USED IN MEETING SUGGESTIONS ===")
    print("The LLM prompt would receive:")
    print("1. Title: 'Test Meeting'")
    print("2. Date: '2025-06-09'") 
    print("3. Participants: user_radek_123 (Radek Rumi), user_john_456 (John Doe)")
    print("4. Transcript with user IDs (as shown above)")
    print("\nThe LLM generates suggestions for each user based on their participation!")
    
    # Pytest assertions instead of return
    assert len(unique_users) == 2
    assert len(formatted_transcripts) == 3
    assert locale == "en-US"
    assert "userID: user_radek_123" in full_transcript
    assert "userID: user_john_456" in full_transcript


def test_transcript_processing_no_user_ids():
    """Test what happens when transcripts don't have user IDs."""
    
    print("\n\n=== Testing Transcript WITHOUT User IDs ===\n")
    
    transcriptions = [
        SharedTranscriptionDTO(
            batchID="batch_2",
            id="trans_5",
            sessionID="789",
            sessionRecurrenceID="101112",
            speakerUID="unknown_speaker_1",
            speakerUserID=None,  # No user ID
            speakerFullName="Anonymous Speaker",
            text="This speaker has no user ID assigned.",
            timeUnix=1733741157000,
            languageLocale="en-US"
        )
    ]
    
    formatted_transcripts, locale, unique_users = get_all_transcriptions_with_user_ids(transcriptions)
    
    print(f"Unique users found: {len(unique_users)} (should be 0)")
    print(f"Formatted transcript:")
    print("-" * 40)
    print("\n".join(formatted_transcripts))
    print("-" * 40)
    print("Note: No '(userID: ...)' is added when speaker_user_id is None")
    
    # Pytest assertions
    assert len(unique_users) == 0
    assert len(formatted_transcripts) == 1
    assert "userID:" not in "\n".join(formatted_transcripts)


if __name__ == "__main__":
    # Test normal case with user IDs
    test_transcript_processing_with_user_ids()
    
    # Test edge case without user IDs
    test_transcript_processing_no_user_ids()
    
    print("\n✅ All transcript processing tests completed successfully!") 