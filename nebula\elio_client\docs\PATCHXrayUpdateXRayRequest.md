# PATCHXrayUpdateXRayRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**alert_channels** | **Dict[str, bool]** |  | 
**frequency** | **str** |  | 
**icon** | **str** |  | 
**is_active** | **bool** |  | 
**prompt** | **str** |  | 
**title** | **str** |  | 

## Example

```python
from elio_client.models.patch_xray_update_x_ray_request import PATCHXrayUpdateXRayRequest

# TODO update the JSON string below
json = "{}"
# create an instance of PATCHXrayUpdateXRayRequest from a JSON string
patch_xray_update_x_ray_request_instance = PATCHXrayUpdateXRayRequest.from_json(json)
# print the JSON string representation of the object
print(PATCHXrayUpdateXRayRequest.to_json())

# convert the object into a dict
patch_xray_update_x_ray_request_dict = patch_xray_update_x_ray_request_instance.to_dict()
# create an instance of PATCHXrayUpdateXRayRequest from a dict
patch_xray_update_x_ray_request_from_dict = PATCHXrayUpdateXRayRequest.from_dict(patch_xray_update_x_ray_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


