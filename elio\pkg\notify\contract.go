package notify

import (
	"fmt"
	"time"

	"encore.app/shared"
	"github.com/samber/lo"
)

type EmailType string

// Client is the interface for the notify client
// TODO figure out a go native way to lazy load the returned value
type Client interface {
	// Session callback executed if a notifier of the given type exists
	Session(func(SessionEvents))
	// User callback executed if a notifier of the given type exists
	User(func(events UserEvents))
	// Lobby callback executed if a notifier of the given type exists
	Lobby(func(events LobbyEvents))
}

type ClientImpl interface {
	//
}

type SessionEvents interface {
	PostSessionSummaryEmailReady(recipients []string, context PostSessionSummaryEmailReadyContext)
	SessionAccessRequested(recipients []string, context SessionAccessRequestedContext)
	SessionAccessApproved(recipients []string, context SessionAccessApprovedContext)
}

type UserEvents interface {
	UserRegistered(user shared.UserDTO)
	UserUpgradedToHost(user shared.UserDTO)
	UserSubscriptionPlan(user shared.UserDTO, previousPlan *shared.UserSubscriptionPlanDTO)
}

type LobbyEvents interface {
	GuestInLobby(recipients []string, context GuestInLobbyContext) error
}

type PostSessionSummaryEmailReadyContext struct {
	RecordingURL string   `json:"recording_url"`
	Title        string   `json:"title"`
	MeetingDate  string   `json:"meeting_date"`
	MeetingTime  string   `json:"meeting_time"`
	Duration     string   `json:"duration"`
	HTML         string   `json:"html"`
	Suggestions  []string `json:"suggestions,omitempty"`
}

func (c *PostSessionSummaryEmailReadyContext) CalcDuration(startedAt, endedAt time.Time) {
	duration := endedAt.Sub(startedAt)
	h := int(duration.Hours())
	m := int(duration.Minutes()) % 60
	s := int(duration.Seconds()) % 60
	if h > 0 {
		c.Duration = fmt.Sprintf("%d hour%s %d minute%s %d second%s", h, lo.Ternary(h == 1, "", "s"), m, lo.Ternary(m == 1, "", "s"), s, lo.Ternary(s == 1, "",
			"s"))
	} else if m > 0 {
		c.Duration = fmt.Sprintf("%d minute%s %d second%s", m, lo.Ternary(m == 1, "", "s"), s, lo.Ternary(s == 1, "", "s"))
	} else {
		c.Duration = fmt.Sprintf("%d second%s", s, lo.Ternary(s == 1, "", "s"))
	}
}

type SessionAccessRequestedContext struct {
	SessionID                string `json:"sessionID"`
	SessionRecurrenceID      string `json:"sessionRecurrenceID"`
	AttendeeFirstName        string `json:"attendeeFirstName"`
	AttendeeLastName         string `json:"attendeeLastName"`
	AttendeeEmail            string `json:"attendeeEmail"`
	SessionTitle             string `json:"sessionTitle"`
	AccessRequestMessage     string `json:"accessRequestMessage"`
	SessionShareDashboardUrl string `json:"sessionShareDashboardUrl"`
}

type Session struct {
	Title string `json:"title"`
}

type Host struct {
	Email     string `json:"email"`
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
}

type SessionAccessApprovedContext struct {
	Hosts         []Host  `json:"hosts"`
	Session       Session `json:"session"`
	AttendeeEmail string  `json:"email_address"`
}

type GuestInLobbyContextAttendee struct {
	FullName string `json:"fullName"`
	Email    string `json:"email"`
}

type GuestInLobbyContext struct {
	Attendee  GuestInLobbyContextAttendee `json:"attendee"`
	LobbyLink string                      `json:"lobbyLink"`
}
