import secrets

from fastapi import Depends, HTTPException, status, Header
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPBasicCredentials

from nebula.settings import settings

security = HTTPBasic()


def authorize(credentials: HTTPBasicCredentials = Depends(security)) -> None:
    """Authorize API Requests with Basic Auth.

    :param credentials: HTTPBasicCredentials
    :raises HTTPException: if credentials are invalid
    """
    is_user_ok = secrets.compare_digest(
        credentials.username,
        settings.basic_auth_username,
    )
    is_pass_ok = secrets.compare_digest(
        credentials.password,
        settings.basic_auth_password,
    )

    if not (is_user_ok and is_pass_ok):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password.",
            headers={"WWW-Authenticate": "Basic"},
        )


def user_id_from_header_or_err(x_user_id: str = Header(..., alias="x-user-id")) -> str:
    """Get user_id from header or raise an error if it's missing"""
    if not x_user_id:
        raise HTTPException(status_code=422, detail="Missing required user_id header")
    return x_user_id
