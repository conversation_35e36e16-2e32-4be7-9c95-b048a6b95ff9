package meetings_test

import (
	"context"
	"testing"

	"encore.app/meetings"
	"encore.app/meetings/api"
	"encore.app/meetings/database"
	db "encore.app/pkg/database"
	"encore.app/shared"
	"github.com/stretchr/testify/assert"
)

func TestSessions(t *testing.T) {
	setUp()

	t.Run("GetSessionByID", func(t *testing.T) {
		// Select all session from the db
		var sessions []database.SessionRow
		err := meetings.MarsDB.SelectContext(context.Background(), &sessions, `SELECT * FROM sessions`)
		assert.NoError(t, err)
		assert.Equal(t, 18, len(sessions)) // Number of sessions in seed_sessions.sql and seed_basic_session.sql

		req := api.GetSessionByIDRequest{
			SessionID:    "1",
			RecurrenceID: "1",
		}
		resp, err := meetings.GetSessionByID(context.Background(), &req)
		assert.NotNil(t, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)

		if assert.IsType(t, &api.GetSessionByIDResponse{}, resp) {
			assert.IsType(t, &shared.TeamWithInlineRelationsDTO{}, resp.Session.Hosts[0].Team)
		}
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Session.SubscriptionPlan)
		assert.Equal(t, "premium", resp.Session.SubscriptionPlan.PlanConfig.PaddleProductName)
	})

	t.Run("GetSessionDefaultTeamPlan", func(t *testing.T) {
		req := api.GetSessionByIDRequest{
			SessionID:    "2",
			RecurrenceID: "2",
		}
		resp, err := meetings.GetSessionByID(context.Background(), &req)

		assert.IsType(t, &api.GetSessionByIDResponse{}, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		if assert.NotNil(t, resp.Session.SubscriptionPlan) {
			assert.Equal(t, "premium", resp.Session.SubscriptionPlan.PlanConfig.PaddleProductName)
		}
	})

	t.Run("GetSessionByIDPremCustom", func(t *testing.T) {
		req := api.GetSessionByIDRequest{
			SessionID:    "3",
			RecurrenceID: "3",
		}
		resp, err := meetings.GetSessionByID(context.Background(), &req)

		assert.IsType(t, &api.GetSessionByIDResponse{}, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Session.SubscriptionPlan)
		assert.Equal(t, "premium", resp.Session.SubscriptionPlan.PlanConfig.PaddleProductName)
		assert.NotNil(t, resp.Session.SubscriptionPlan.PlanConfigOverrides)
		assert.Equal(t, "{\"foobar\": \"test\"}", string(*resp.Session.SubscriptionPlan.PlanConfigOverrides))
	})

	t.Run("GetSessionRecurrencesNoFilter", func(t *testing.T) {
		req := api.GetSessionRecurrencesByIDRequest{
			SessionID: "1",
		}
		resp, err := meetings.GetSessionRecurrencesById(context.Background(), &req)

		assert.IsType(t, &api.GetSessionRecurrencesByIDResponse{}, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		assert.NotNil(t, resp)
		if assert.Len(t, resp.Sessions, 1) {
			shared.PP(resp.Sessions[0])
		}
	})

	t.Run("GetSessionRecurrencesFilter", func(t *testing.T) {
		req := api.GetSessionRecurrencesByIDRequest{
			SessionID:    "3",
			FilterStates: "active",
		}
		resp, err := meetings.GetSessionRecurrencesById(context.Background(), &req)

		assert.IsType(t, &api.GetSessionRecurrencesByIDResponse{}, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Sessions, 1)
	})

	t.Run("GetSessionRecurrencesFilterNoResults", func(t *testing.T) {
		req := api.GetSessionRecurrencesByIDRequest{
			SessionID:    "3",
			FilterStates: "archived",
		}
		resp, err := meetings.GetSessionRecurrencesById(context.Background(), &req)

		assert.IsType(t, &api.GetSessionRecurrencesByIDResponse{}, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		assert.NotNil(t, resp)
		assert.Len(t, resp.Sessions, 0)
	})

	t.Run("GetLatestRecurrenceCompact", func(t *testing.T) {
		// Act
		req := api.GetLatestRecurrenceCompactRequest{
			SessionID: "1",
		}
		resp, err := meetings.GetLatestRecurrenceCompact(context.Background(), &req)

		// Assert
		assert.NotNil(t, resp)
		assert.NoError(t, err)
		assert.Equal(
			t,
			api.GetLatestRecurrenceCompactResponse{
				SessionTitle:        "Sample Session",
				SessionID:           "1",
				SessionRecurrenceID: "1",
				Status:              "scheduled",
				StartTimestamp:      1653528800,
			},
			*resp,
		)
	})
	t.Run("GetSessionNoPlan", func(t *testing.T) {
		// Update session 3 to have planless user
		meetings.MarsDB.Exec("UPDATE sessions SET \"primaryHostUserID\" = 4, \"hostUserIDs\" = '{4}' WHERE id = '3'")
		meetings.MarsDB.Exec("UPDATE \"sessions_recurrences\" SET \"primaryHostUserID\" = 4, \"hostUserIDs\" = '{4}' WHERE id = '3'")
		req := api.GetSessionByIDRequest{
			SessionID:    "3",
			RecurrenceID: "3",
		}
		resp, err := meetings.GetSessionByID(context.Background(), &req)

		assert.IsType(t, &api.GetSessionByIDResponse{}, resp)
		assert.NoError(t, err)
		db.AssertXNoOpenConns(t, meetings.MarsDB)
		assert.NotNil(t, resp)
		assert.Nil(t, resp.Session.SubscriptionPlan)
	})
}
