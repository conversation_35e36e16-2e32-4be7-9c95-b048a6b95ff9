deployment:
  image:
    pullPolicy: Always
    repository: 177319056053.dkr.ecr.us-east-1.amazonaws.com/nebula
    tag: main-1
  initialDelaySeconds: 120
  minReadySeconds: 60
  nodeSelector:
    environment: production
env:
  ### APM Related and System Required Variables
  - name: "DD_LOGS_INJECTION"
    value: "true"
  - name: "DD_ENV"
    value: "main"
  - name: "DD_PROFILING_ENABLED"
    value: "true"
  - name: "DD_TRACE_DEBUG"
    value: "false"
  - name: "DD_SERVICE"
    value: "nebula"

hpa:
  cpuUtilization:
    targetPercentage: 80
  maxReplicas: 5
  minReplicas: 2
replicaCount: 2
resources:
  requests:
    memory: "750Mi"
    cpu: "3000m"
  limits:
    memory: "2256Mi"
    cpu: "4000m"
service:
  externalPort: 80
  internalPort: 8000
  name: nebula-main
  type: ""

ingress:
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - name: waitroom.com
      rules:
        - subdomain: nebula.prod.
          path: /
