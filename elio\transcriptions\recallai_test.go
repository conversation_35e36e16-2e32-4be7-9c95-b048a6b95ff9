package transcriptions_test

import (
	"context"
	"encoding/json"
	"testing"

	"encore.app/transcriptions/database"

	"encore.app/meetings"
	mapi "encore.app/meetings/api"
	mdatabase "encore.app/meetings/database"
	"encore.app/shared"
	"encore.dev/et"

	"encore.app/pkg/recallai"
	"encore.app/transcriptions"
	"encore.app/transcriptions/api"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/assert"
)

// Mock the recallai SDK
type RecallSDKMock struct {
	Token                      string
	GetCalendarAuthTokenCalled int
	RetrieveBotCalled          int
}

func (m *RecallSDKMock) GetCalendarAuthToken(params recallai.CalendarAuthTokenParams) (*recallai.CalendarAuthTokenResponse, error) {
	m.GetCalendarAuthTokenCalled++
	return &recallai.CalendarAuthTokenResponse{
		Token: m.Token,
	}, nil
}

func (m *RecallSDKMock) GoogleCallbackURL() string {
	return ""
}

func (m *RecallSDKMock) MicrosoftCallbackURL() string {
	return ""
}

func (m *RecallSDKMock) DisconnectCalendarPlatform(params recallai.DisconnectCalendarPlatformParams, userToken string) error {
	return nil
}

func (m *RecallSDKMock) UpdateRecordingPreferences(userToken string, preferences *recallai.RecordingPreferences) error {
	return nil
}

func (m *RecallSDKMock) RetrieveBot(ctx context.Context, BotID string, userToken string) (*recallai.BotResponse, error) {
	m.RetrieveBotCalled++
	return &recallai.BotResponse{
		MeetingUrl: recallai.BotMeetingLink{
			MeetingId: "xyz",
			Platform:  "google",
		},
		CalendarMeetings: []recallai.CalendarMeeting{
			{
				CalendarUser: recallai.CalendarUser{
					ExternalId: "1",
				},
				StartTime: "2025-01-23T12:00:00Z",
			},
			{
				CalendarUser: recallai.CalendarUser{
					ExternalId: "2",
				},
				StartTime: "2025-01-23T12:00:00Z",
			},
		},
		MeetingParticipants: []recallai.MeetingParticipant{
			{
				Id:     1,
				Name:   "Michael Lastname",
				IsHost: true,
			},
			{
				Id:     2,
				Name:   "Sean Lastname",
				IsHost: false,
			},
		},
	}, nil
}

func (m *RecallSDKMock) GetCalendarUser(userToken string) (*recallai.CalendarUser, error) {
	return nil, nil
}

func (m *RecallSDKMock) GetDefaultPreferences() *recallai.RecordingPreferences {
	return nil
}

func (m *RecallSDKMock) GetMeetingURL(botResponse *recallai.BotResponse) string {
	return "https://meet.google.com/xyz"
}

func (m *RecallSDKMock) ListUsers() ([]recallai.CalendarUser, error) {
	return []recallai.CalendarUser{}, nil
}

func (m *RecallSDKMock) DeleteUser(userToken string) error {
	return nil
}

func (m *RecallSDKMock) CreateBot(ctx context.Context, userToken string, params recallai.CreateBotParams) (*recallai.BotResponse, error) {
	return nil, nil
}

func setUp() {
	et.EnableServiceInstanceIsolation()

	testMarsDB, err := et.NewTestDatabase(context.Background(), "mars")
	if err != nil {
		panic(err)
	}
	testMarsDBX := sqlx.NewDb(testMarsDB.Stdlib(), "pgx")
	// We have to validate statement as some tests do not call API functions
	if err := mdatabase.Statements.ValidateAll(context.Background(), testMarsDBX, "mars"); err != nil {
		panic(err)
	}
	if err := database.Statements.ValidateAll(context.Background(), testMarsDBX, "mars"); err != nil {
		panic(err)
	}

	// Ensure MarsDB is set before any other operations
	transcriptions.MarsDB = testMarsDBX
	meetings.MarsDB = testMarsDBX

	// Order matters here, seed_users.sql must be seeded first
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("seed_users.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("user_subscription_plans.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("seed_categories.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("seed_basic_session.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("teams.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("seed_sessions.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("seed_live_transcripts.sql"))
	shared.SeedDBTestDataFromFixtureX(testMarsDBX, shared.GetFixtureByName("seed_speakers.sql"))

	transcriptions.RecallSDK = &RecallSDKMock{
		Token: "test-token",
	}

	dbStorage := transcriptions.LuxorStorage{}
	transcriptions.DBStorage = transcriptions.Storage{
		Kafka: nil,
		Luxor: &dbStorage,
	}
}

func TestRecallTranscriptions(t *testing.T) {
	setUp()

	ourToken := transcriptions.Config.RecallAiWebhookToken()
	mock := transcriptions.RecallSDK.(*RecallSDKMock)

	t.Run("RecallTranscriptionsWebhook", func(t *testing.T) {
		// Test webhook with sample bot.status_change event with in_call_recording status
		webhookReq := api.RecallTranscriptionsWebhookRequest{
			Token: ourToken,
			Event: "bot.status_change",
			Data: api.RecallTranscriptionsWebhookData{
				BotID: "test-bot-1",
				Status: &api.RecallTranscriptionsWebhookStatus{
					Code:        "in_call_recording",
					SubCode:     "active",
					Message:     "Bot is recording the call",
					RecordingID: "rec-456",
					CreatedAt:   "2024-01-01T12:00:00Z",
				},
			},
		}

		err := transcriptions.RecallTranscriptionsWebhook(context.Background(), webhookReq)
		assert.NoError(t, err)

		assert.Equal(t, 1, mock.GetCalendarAuthTokenCalled)
		assert.Equal(t, 1, mock.RetrieveBotCalled)

		reqJson := "{\"Token\":\"\",\"SvixID\":\"\",\"SvixTimestamp\":0,\"SvixSignature\":\"\",\"event\":\"bot.transcription\",\"data\":{\"bot_id\":\"test-bot-1\",\"transcript\":{\"speaker\":\"Sean Martin\",\"speaker_id\":100,\"original_transcript_id\":105,\"words\":[{\"text\":\"It\",\"start_time\":287.6953,\"end_time\":287.77344},{\"text\":\"is\",\"start_time\":287.77344,\"end_time\":288.08594},{\"text\":\"roaring\",\"start_time\":288.08594,\"end_time\":288.58594},{\"text\":\"really\",\"start_time\":288.9453,\"end_time\":289.2578},{\"text\":\"easy.\",\"start_time\":289.2578,\"end_time\":289.64844}],\"is_final\":true}}}"
		var webhookReq2 api.RecallTranscriptionsWebhookRequest
		err = json.Unmarshal([]byte(reqJson), &webhookReq2)
		assert.NoError(t, err)

		webhookReq2.Token = ourToken
		err = transcriptions.RecallTranscriptionsWebhook(context.Background(), webhookReq2)
		assert.NoError(t, err)

		var rows []api.TranscriptionRow
		err = transcriptions.MarsDB.Select(
			&rows,
			`select * from "LiveTranscription" where "userID" = '1'`,
		)

		assert.NoError(t, err)
		assert.Equal(t, 1, len(rows))

		text := rows[0].Text
		assert.Equal(t, "It is roaring really easy.", text)

		var speakers []api.SpeakerRow
		err = transcriptions.MarsDB.Select(
			&speakers,
			`select * from "Speakers" where "speakerUID" = '100'`,
		)

		assert.NoError(t, err)
		assert.Equal(t, 1, len(speakers))

		assert.Equal(t, 1, mock.GetCalendarAuthTokenCalled)
		assert.Equal(t, 1, mock.RetrieveBotCalled)

		// Send in the done event
		webhookReq3 := api.RecallTranscriptionsWebhookRequest{
			Token: ourToken,
			Event: "bot.status_change",
			Data: api.RecallTranscriptionsWebhookData{
				BotID: "test-bot-1",
				Status: &api.RecallTranscriptionsWebhookStatus{
					Code:        "done",
					SubCode:     "",
					Message:     "",
					RecordingID: "rec-456",
					CreatedAt:   "2024-01-01T12:00:00Z",
				},
			},
		}

		err = transcriptions.RecallTranscriptionsWebhook(context.Background(), webhookReq3)
		assert.NoError(t, err)

		assert.Equal(t, 2, mock.GetCalendarAuthTokenCalled)
		assert.Equal(t, 2, mock.RetrieveBotCalled)

		var rows2 []api.TranscriptionRow
		err = transcriptions.MarsDB.Select(
			&rows2,
			`select * from "LiveTranscription" where "batchID" is not null and "userID" = '1'`,
		)
		assert.NoError(t, err)
		if assert.Equal(t, 1, len(rows2)) {
			reqs, reqsErr := meetings.GetAllAccessRequests(context.Background(), &mapi.GetAccessRequests{
				SessionID:           rows2[0].SessionID,
				SessionRecurrenceID: rows2[0].SessionRecurrenceID,
				RestrictionStatus:   "granted",
			})
			if assert.NoError(t, reqsErr) {
				if assert.Equal(t, 1, len(reqs.Data.AccessRules)) {
					assert.Equal(t, mapi.AccessRuleEmail, reqs.Data.AccessRules[0].Type)
					assert.Equal(t, "<EMAIL>", reqs.Data.AccessRules[0].Value)
				}
			}
		}
	})

	t.Run("Empty transcript speaker test", func(t *testing.T) {
		webhookReq := api.RecallTranscriptionsWebhookRequest{
			Token: ourToken,
			Event: "bot.status_change",
			Data: api.RecallTranscriptionsWebhookData{
				BotID: "test-bot-1",
				Status: &api.RecallTranscriptionsWebhookStatus{
					Code:        "in_call_recording",
					SubCode:     "active",
					Message:     "Bot is recording the call",
					RecordingID: "rec-456",
					CreatedAt:   "2024-01-01T12:00:00Z",
				},
			},
		}

		err := transcriptions.RecallTranscriptionsWebhook(context.Background(), webhookReq)
		assert.NoError(t, err)

		reqJson := "{\"Token\":\"\",\"SvixID\":\"\",\"SvixTimestamp\":0,\"SvixSignature\":\"\",\"event\":\"bot.transcription\",\"data\":{\"bot_id\":\"test-bot-1\",\"transcript\":{\"speaker_id\":100,\"original_transcript_id\":105,\"words\":[{\"text\":\"It\",\"start_time\":287.6953,\"end_time\":287.77344},{\"text\":\"is\",\"start_time\":287.77344,\"end_time\":288.08594},{\"text\":\"roaring\",\"start_time\":288.08594,\"end_time\":288.58594},{\"text\":\"really\",\"start_time\":288.9453,\"end_time\":289.2578},{\"text\":\"easy.\",\"start_time\":289.2578,\"end_time\":289.64844}],\"is_final\":true}}}"
		var webhookReqTranscriptEmpty api.RecallTranscriptionsWebhookRequest
		err = json.Unmarshal([]byte(reqJson), &webhookReqTranscriptEmpty)
		assert.NoError(t, err)

		webhookReqTranscriptEmpty.Token = ourToken
		err = transcriptions.RecallTranscriptionsWebhook(context.Background(), webhookReqTranscriptEmpty)
		assert.NoError(t, err)
	})
}

func TestBotResponse(t *testing.T) {
	responseJson := "{\"id\":\"f5f325e2-95cf-4ea4-82e4-2c0aa6bc64c4\",\"meeting_url\":{\"meeting_id\":\"qhg-ofcc-yyc\",\"platform\":\"google_meet\"},\"bot_name\":\"Rumi Notetaker\",\"join_at\":\"2025-01-23T11:58:00Z\",\"video_url\":\"\",\"media_retention_end\":\"2025-01-30T12:04:05.801345Z\",\"status_changes\":[{\"code\":\"ready\",\"message\":null,\"created_at\":\"2025-01-23T11:47:30.323851Z\",\"sub_code\":null},{\"code\":\"joining_call\",\"message\":null,\"created_at\":\"2025-01-23T11:58:00.741157Z\",\"sub_code\":null},{\"code\":\"ready\",\"message\":null,\"created_at\":\"2025-01-23T11:58:06.415098Z\",\"sub_code\":null},{\"code\":\"ready\",\"message\":null,\"created_at\":\"2025-01-23T11:58:12.034475Z\",\"sub_code\":null},{\"code\":\"in_waiting_room\",\"message\":null,\"created_at\":\"2025-01-23T11:58:21.518205Z\",\"sub_code\":null},{\"code\":\"in_call_not_recording\",\"message\":null,\"created_at\":\"2025-01-23T11:59:40.876096Z\",\"sub_code\":null},{\"code\":\"in_call_recording\",\"message\":null,\"created_at\":\"2025-01-23T11:59:40.889751Z\",\"sub_code\":null},{\"code\":\"call_ended\",\"message\":null,\"created_at\":\"2025-01-23T12:04:01.598873Z\",\"sub_code\":\"timeout_exceeded_everyone_left\"},{\"code\":\"recording_done\",\"message\":null,\"created_at\":\"2025-01-23T12:04:05.801331Z\",\"sub_code\":null},{\"code\":\"done\",\"message\":null,\"created_at\":\"2025-01-23T12:04:05.801345Z\",\"sub_code\":null}],\"meeting_metadata\":null,\"meeting_participants\":[{\"id\":100,\"name\":\"Michael\",\"events\":[{\"code\":\"join\",\"created_at\":\"2025-01-23T11:59:41.042896Z\"},{\"code\":\"leave\",\"created_at\":\"2025-01-23T12:03:57.324463Z\"}],\"is_host\":true,\"platform\":\"unknown\",\"extra_data\":null},{\"id\":200,\"name\":\"Sean\",\"events\":[{\"code\":\"join\",\"created_at\":\"2025-01-23T12:01:17.825627Z\"},{\"code\":\"video_on\",\"created_at\":\"2025-01-23T12:01:19.911860Z\"},{\"code\":\"leave\",\"created_at\":\"2025-01-23T12:03:58.126121Z\"},{\"code\":\"video_off\",\"created_at\":\"2025-01-23T12:03:58.128146Z\"}],\"is_host\":false,\"platform\":\"unknown\",\"extra_data\":null}],\"real_time_transcription\":{\"destination_url\":\"https://wormhole.develop.waitroom.com/v1.0/transcriptions/bot/webhook?token=CP79rfhTjf3KZOMJqCxPYhhD06BU8LMG\",\"partial_results\":false,\"enhanced_diarization\":true},\"transcription_options\":{\"provider\":\"deepgram\",\"use_separate_streams_when_available\":false},\"recording_mode\":\"speaker_view\",\"recordings\":[{\"id\":\"8f40ae3c-5dca-4d62-abf4-955a43ca5688\",\"created_at\":\"2025-01-23T11:58:00.399746Z\",\"started_at\":\"2025-01-23T11:59:40.889751Z\",\"completed_at\":\"2025-01-23T12:04:05.801331Z\"}],\"automatic_leave\":{\"waiting_room_timeout\":1200,\"noone_joined_timeout\":1200,\"everyone_left_timeout\":2,\"in_call_not_recording_timeout\":3600,\"recording_permission_denied_timeout\":3600,\"silence_detection\":{\"timeout\":3600,\"activate_after\":1200},\"bot_detection\":{\"using_participant_events\":{\"timeout\":600,\"activate_after\":1200}}},\"calendar_meetings\":[{\"id\":\"48576918-7736-4367-bf34-1984a10f6b67\",\"start_time\":\"2025-01-23T12:00:00Z\",\"end_time\":\"2025-01-23T12:30:00Z\",\"calendar_user\":{\"id\":\"37ed59d0-e721-4c1d-9924-ba7bac8b707a\",\"external_id\":\"973815071664768540\"}}],\"metadata\":{},\"recording\":\"8f40ae3c-5dca-4d62-abf4-955a43ca5688\"}"

	var botResponse recallai.BotResponse
	err := json.Unmarshal([]byte(responseJson), &botResponse)
	assert.NoError(t, err)
}
