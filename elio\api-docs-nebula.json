{"openapi": "3.0.0", "tags": [{"name": "memory"}, {"name": "post-session-summaries"}, {"name": "meeting-metadata"}, {"name": "meeting-suggestions"}, {"name": "x-ray"}], "info": {"title": "Neb<PERSON>", "description": "", "version": "1.0", "contact": {}}, "servers": [], "paths": {"/v1.0/memory/ask/": {"post": {"operationId": "MemoryController_askV2", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AskAIRequestBodyDTO"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DummyResponse"}}}}}}}, "/v1.0/memory/ask-ai-sync": {"post": {"operationId": "MemoryController_askAISync", "parameters": [{"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AskAIRequestBodyDTO"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AskAISyncResponseDTO"}}}}, "401": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDTO"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDTO"}}}}, "504": {"description": "Gateway timeout", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponseDTO"}}}}}}}, "/v1.0/memory/suggestions": {"get": {"operationId": "MemoryController_getSuggestions", "parameters": [{"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "integer"}}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "skip", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetSuggestionsResponse"}}}}}}}, "/v1.0/memory/stop": {"post": {"operationId": "MemoryController_stopRun", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StopRunBodyDTO"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/StopRunResponse"}}}}}}}, "/v1.0/memory/feedback": {"patch": {"operationId": "MemoryController_feedback", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFeedbackRequestDTO"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DummyResponse"}}}}}}}, "/v1.0/memory/threads": {"get": {"operationId": "MemoryController_getUserThreads", "parameters": [{"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "integer"}}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "skip", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetThreadsResponse"}}}}}}}, "/v1.0/memory/threads/{threadID}": {"get": {"operationId": "MemoryController_getThreadByID", "parameters": [{"name": "threadID", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetThreadByIdResponse"}}}}}}, "delete": {"operationId": "MemoryController_deleteThreadByID", "parameters": [{"name": "threadID", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteThreadResponse"}}}}}}}, "/v1.0/memory/threads/{threadID}/messages": {"get": {"operationId": "MemoryController_getThreadMessages", "parameters": [{"name": "threadID", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "string"}}, {"in": "query", "name": "limit", "required": false, "schema": {"type": "string"}}, {"in": "query", "name": "skip", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetThreadMessagesResponse"}}}}}}}, "/v1.0/memory/teams": {"post": {"operationId": "MemoryController_postTeam", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostTeamRequestBody"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DummyResponse"}}}, "description": "Successful operation"}}}}, "/v1.0/memory/teams/{team_id}/members": {"post": {"operationId": "MemoryController_postTeamMember", "parameters": [{"name": "team_id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostTeamMemberRequestBody"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DummyResponse"}}}}}}}, "/v1.0/memory/teams/{team_id}/members/{team_member_id}": {"delete": {"operationId": "MemoryController_deleteTeamMember", "parameters": [{"name": "team_id", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "team_member_id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DummyResponse"}}}, "description": "OK"}}}}, "/v1.0/recurrences/{recurrence_id}/access": {"patch": {"operationId": "MemoryController_patchRecurrenceTeamAccess", "parameters": [{"name": "recurrence_id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchRecurrenceTeamAccessBody"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchRecurrenceTeamAccessResponse"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PatchRecurrenceTeamAccessResponse"}}}}}}}, "/v1.0/post-session-summaries/{sessionID}": {"get": {"tags": ["post-session-summaries"], "summary": "Get By Id", "description": "API for getting post session summary by id.\n\n:param sessionID: int\n:param formats: str | None\n:return: JSONResponse", "operationId": "get_by_id_v1_0_post_session_summaries__sessionID__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "sessionID", "in": "path", "required": true, "schema": {"type": "integer", "title": "Sessionid"}}, {"name": "formats", "in": "query", "required": false, "schema": {"type": "string", "title": "Formats"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostSessionSummaryDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/post-session-summaries/by-session-ids/{sessionID}": {"get": {"tags": ["post-session-summaries"], "summary": "Get By Session Ids", "description": "API to get post session summary by session id and session recurrence id.\n\n:param sessionID: str\n:param sessionRecurrenceID: str\n\n:return: JSONResponse", "operationId": "get_by_session_ids_v1_0_post_session_summaries_by_session_ids__sessionID__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "sessionID", "in": "path", "required": true, "schema": {"type": "string", "title": "Sessionid"}}, {"name": "sessionRecurrenceID", "in": "query", "required": true, "schema": {"type": "string", "title": "Sessionrecurrenceid"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostSessionSummaryDTO"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/meeting-metadata/{metadata_id}": {"get": {"tags": ["meeting-metadata"], "summary": "Get Metadata By Id", "description": "Get meeting metadata by ID.\n\n:param metadata_id: The ID of the metadata record\n:return: JSONResponse with metadata", "operationId": "get_metadata_by_id_v1_0_meeting_metadata__metadata_id__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "metadata_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Metadata Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingMetadataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/meeting-metadata/by-session/{session_id}/{recurrence_id}": {"get": {"tags": ["meeting-metadata"], "summary": "Get Metadata By Session Id", "description": "Get meeting metadata by session ID and recurrence ID.\n\n:param session_id: The session ID\n:param recurrence_id: The session recurrence ID\n:return: JSONResponse with metadata", "operationId": "get_metadata_by_session_id_v1_0_meeting_metadata_by_session__session_id___recurrence_id__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "recurrence_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Recurrence Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MeetingMetadataResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/suggestions/{session_id}/{recurrence_id}": {"get": {"tags": ["meeting-suggestions"], "summary": "Get meeting suggestions", "description": "Retrieve meeting suggestions for a specific session, recurrence, and user", "operationId": "get_meeting_suggestions_v1_0_suggestions__session_id___recurrence_id__get", "security": [{"HTTPBasic": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "recurrence_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Recurrence Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetMeetingSuggestionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/create/step1": {"post": {"tags": ["x-ray", "x-ray"], "summary": "Create Xray Step1", "description": "Step 1 of X-Ray creation: Generate X-Ray type and prompt from description", "operationId": "create_xray_step1_v1_0_x_ray_create_step1_post", "parameters": [{"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayCreateStep1Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayCreateStep1Response"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/create/step2": {"post": {"tags": ["x-ray", "x-ray"], "summary": "Create Xray Step2", "description": "Step 2 of X-Ray creation: Generate title, emoji, and short summary from X-Ray type and prompt", "operationId": "create_xray_step2_v1_0_x_ray_create_step2_post", "parameters": [{"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayCreateStep2Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayCreateStep2Response"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/create/step3": {"post": {"tags": ["x-ray", "x-ray"], "summary": "Create Xray Step3", "description": "Step 3 of X-Ray creation: Create X-Ray record with all generated data", "operationId": "create_xray_step3_v1_0_x_ray_create_step3_post", "parameters": [{"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayCreateStep3Body"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/": {"get": {"tags": ["x-ray", "x-ray"], "summary": "List Xrays", "description": "List X-Rays with pagination, filtering, and sorting", "operationId": "list_xrays_v1_0_x_ray__get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "type_filter", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/XRayTypeFilter", "title": "Type Filter"}}, {"name": "sort_by", "in": "query", "required": false, "schema": {"$ref": "#/components/schemas/XRaySortBy", "default": "last_updated"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/templates": {"get": {"tags": ["x-ray", "x-ray"], "summary": "List Xray Templates", "description": "List X-Ray templates with pagination (Rumi-generated templates only)", "operationId": "list_xray_templates_v1_0_x_ray_templates_get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayTemplateListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/xray-templates/{template_id}": {"get": {"tags": ["x-ray", "x-ray"], "summary": "<PERSON>ray Template", "description": "Get an X-Ray template by ID (Rumi-generated templates only)", "operationId": "get_xray_template_v1_0_x_ray_xray_templates__template_id__get", "parameters": [{"name": "template_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Template Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/{xray_id}": {"patch": {"tags": ["x-ray", "x-ray"], "summary": "Update <PERSON><PERSON>", "description": "Update an X-Ray", "operationId": "update_xray_v1_0_x_ray__xray_id__patch", "parameters": [{"name": "xray_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Xray Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayUpdateRequestBody"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["x-ray", "x-ray"], "summary": "Get <PERSON>ray", "description": "Get a single X-Ray by ID", "operationId": "get_xray_v1_0_x_ray__xray_id__get", "parameters": [{"name": "xray_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Xray Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["x-ray", "x-ray"], "summary": "Delete Xray", "description": "Delete an X-Ray by ID", "operationId": "delete_xray_v1_0_x_ray__xray_id__delete", "parameters": [{"name": "xray_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Xray Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/{xray_id}/share": {"post": {"tags": ["x-ray", "x-ray"], "summary": "Share Xray As Template", "description": "Share an X-Ray as a template. Creates a template from the X-Ray's settings.\nReturns existing template if already shared (idempotent behavior).", "operationId": "share_xray_as_template_v1_0_x_ray__xray_id__share_post", "parameters": [{"name": "xray_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Xray Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayTemplateResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/{xray_id}/notifications": {"get": {"tags": ["x-ray", "x-ray"], "summary": "Get Xray Notifications", "description": "Get notifications for a specific X-Ray with pagination", "operationId": "get_xray_notifications_v1_0_x_ray__xray_id__notifications_get", "parameters": [{"name": "xray_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Xray Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 30, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/XRayNotificationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v1.0/x-ray/{xray_id}/notifications/mark-seen": {"patch": {"tags": ["x-ray", "x-ray"], "summary": "<PERSON> Notifications Seen", "description": "Mark all unseen notifications for an X-Ray as seen", "operationId": "mark_xray_notifications_seen_v1_0_x_ray__xray_id__notifications_mark_seen_patch", "parameters": [{"name": "xray_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Xray Id"}}, {"name": "x-user-id", "in": "header", "required": true, "schema": {"type": "string", "title": "X-User-Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MarkNotificationsSeenResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"DummyResponse": {"type": "object", "properties": {}}, "UserFeedbackRequestDTO": {"type": "object", "properties": {"messageId": {"type": "string"}, "feedback": {"type": "integer"}}, "required": ["messageId", "feedback"]}, "StopRunBodyDTO": {"type": "object", "parameters": [{"name": "X-User-Id", "required": true, "in": "header", "schema": {"type": "integer"}}], "properties": {"threadId": {"type": "string"}}}, "GetSuggestionsResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"total": {"type": "integer"}, "suggestions": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}, "text": {"type": "string"}, "short": {"type": "string"}, "explanation": {"type": "string"}, "createdAt": {"type": "number", "x-go-type": "int"}, "updatedAt": {"type": "number", "x-go-type": "int"}, "sources": {"type": "object", "additionalProperties": true}}}}}, "required": ["total", "suggestions"]}, "message": {"type": "string"}}, "required": ["message"]}, "GetThreadsResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"total": {"type": "integer"}, "hasMore": {"type": "boolean"}, "threads": {"type": "array", "items": {"$ref": "#/components/schemas/ThreadDTO"}}}, "required": ["total", "hasMore", "threads"]}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["message", "data", "success"]}, "GetThreadByIdResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"thread": {"$ref": "#/components/schemas/ThreadDTO"}}, "required": ["thread"]}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["message", "data", "success"]}, "GetThreadMessagesResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"total": {"type": "integer"}, "hasMore": {"type": "boolean"}, "messages": {"type": "array", "items": {"$ref": "#/components/schemas/MessageDTO"}}}, "required": ["messages", "hasMore", "total"]}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["message", "data", "success"]}, "DeleteThreadResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"threadId": {"type": "string"}}, "required": ["messages", "hasMore", "total"]}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["message", "data", "success"]}, "StopRunResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"threadId": {"type": "string"}, "providerThreadId": {"type": "string"}, "runId": {"type": "string"}}}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["message", "success"]}, "AskAIRequestBodyDTO": {"type": "object", "properties": {"query": {"type": "string"}, "requestId": {"type": "string"}, "recurrenceIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "tz": {"type": "string", "nullable": true}, "threadId": {"type": "string", "nullable": true}, "messageId": {"type": "string", "nullable": true}, "refresh": {"type": "boolean", "nullable": true}, "retry": {"type": "boolean", "nullable": true}, "timeout": {"type": "number", "minimum": 1.0, "maximum": 300.0, "default": 60.0, "description": "Timeout in seconds for the sync request", "nullable": true}}, "required": ["requestId"]}, "ThreadDTO": {"type": "object", "properties": {"id": {"type": "string"}, "title": {"type": "string"}, "lastMessageAt": {"type": "number", "x-go-type": "int"}, "createdAt": {"type": "number", "x-go-type": "int"}, "updatedAt": {"type": "number", "x-go-type": "int"}}, "required": ["id", "title", "lastMessageAt", "createdAt", "updatedAt"]}, "MessageItemSourceDataDTO": {"type": "object", "properties": {"recurrenceId": {"type": "string"}, "sessionId": {"type": "string"}, "title": {"type": "string"}, "about": {"type": "string", "nullable": true}, "participants": {"type": "array", "items": {"type": "string"}}, "startedAt": {"type": "integer"}, "endedAt": {"type": "integer"}, "organizer": {"type": "string", "nullable": true}}, "required": ["recurrenceId", "sessionId", "title", "participants", "startedAt", "endedAt"]}, "MessageItemSourceDTO": {"type": "object", "properties": {"type": {"type": "string", "enum": ["session"]}, "data": {"$ref": "#/components/schemas/MessageItemSourceDataDTO"}}, "required": ["type", "data"]}, "MessageDTO": {"type": "object", "properties": {"id": {"type": "string"}, "role": {"type": "string", "enum": ["user", "ai", "tool"]}, "threadId": {"type": "string"}, "content": {"type": "string"}, "feedback": {"type": "integer", "enum": [0, 1, 2]}, "sources": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/MessageItemSourceDTO"}}, "nullable": true}, "createdAt": {"type": "integer"}, "updatedAt": {"type": "integer"}}, "required": ["id", "role", "threadId", "content", "feedback", "createdAt", "updatedAt"]}, "PatchRecurrenceTeamAccessBody": {"type": "object", "properties": {"teamId": {"type": "string"}, "value": {"type": "boolean"}}, "required": ["teamId", "value"]}, "PatchRecurrenceTeamAccessResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}}, "required": ["success", "message"]}, "PostTeamRequestBody": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "memberIds": {"type": "array", "items": {"type": "string"}}}, "required": ["name", "id"]}, "PostTeamMemberRequestBody": {"type": "object", "properties": {"userId": {"type": "string"}}, "required": ["userId"]}, "PostSessionSummaryDTO": {"properties": {"message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/PostSessionSummaryData"}}, "type": "object", "required": ["message", "data"], "title": "PostSessionSummaryDTO", "description": "Response model for post session summary."}, "PostSessionSummaryData": {"properties": {"id": {"type": "integer", "title": "Id"}, "session_id": {"type": "string", "title": "Session Id"}, "session_recurrence_id": {"type": "string", "title": "Session Recurrence Id"}, "html": {"type": "string", "nullable": true, "title": "Html"}, "md": {"type": "string", "nullable": true, "title": "Md"}, "tldr": {"type": "string", "nullable": true, "title": "Tldr"}, "content": {"type": "object", "nullable": true, "title": "Content"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "session_id", "session_recurrence_id", "html", "md", "tldr", "content", "created_at", "updated_at"], "title": "PostSessionSummaryData"}, "HTTPValidationError": {"title": "HTTPValidationError", "type": "object", "properties": {"detail": {"title": "Detail", "type": "array", "items": {"$ref": "#/components/schemas/ValidationError"}}}}, "ValidationError": {"title": "ValidationError", "type": "object", "properties": {"loc": {"title": "Location", "type": "array", "items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}}, "msg": {"title": "Message", "type": "string"}, "type": {"title": "Error Type", "type": "string"}}, "required": ["loc", "msg", "type"]}, "AskAISyncResponseDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "data": {"type": "object", "properties": {"content": {"type": "string"}, "sources": {"type": "object", "additionalProperties": true}, "threadId": {"type": "string", "nullable": true}}}, "message": {"type": "string"}}, "required": ["success", "message"]}, "ErrorResponseDTO": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string"}, "data": {"type": "object", "nullable": true}, "sources": {"type": "object", "additionalProperties": true, "nullable": true}, "threadId": {"type": "string", "nullable": true}}, "required": ["success", "message"]}, "MeetingMetadataResponse": {"type": "object", "properties": {"data": {"type": "object", "properties": {"metadata": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "start_time": {"type": "string"}, "end_time": {"type": "string"}, "location": {"type": "string"}, "participants": {"type": "array", "items": {"type": "string"}}}}}}, "message": {"type": "string"}}, "required": ["message", "data"]}, "GetMeetingSuggestionsResponse": {"properties": {"data": {"$ref": "#/components/schemas/MeetingSuggestionsDTO", "nullable": true}, "message": {"type": "string", "title": "Message"}}, "type": "object", "required": ["message"], "title": "GetMeetingSuggestionsResponse", "description": "Response for getting meeting suggestions."}, "MeetingSuggestionsDTO": {"properties": {"id": {"type": "integer", "title": "Id"}, "session_id": {"type": "string", "title": "Session Id"}, "session_recurrence_id": {"type": "string", "title": "Session Recurrence Id"}, "user_id": {"type": "string", "title": "User Id"}, "suggestions": {"items": {"type": "string"}, "type": "array", "title": "Suggestions"}, "created_at": {"type": "string", "title": "Created At"}, "updated_at": {"type": "string", "title": "Updated At"}}, "type": "object", "required": ["id", "session_id", "session_recurrence_id", "user_id", "suggestions", "created_at", "updated_at"], "title": "MeetingSuggestionsDTO", "description": "Complete meeting suggestions DTO."}, "XRayCreateStep1Body": {"type": "object", "properties": {"description": {"type": "string", "title": "Description"}}, "required": ["description"], "title": "XRayCreateStep1Body", "description": "Request model for X-Ray description generation"}, "XRayCreateStep1Response": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayCreateStep1ResponseData"}}, "required": ["success", "message"], "title": "XRayCreateStep1Response", "description": "Response model for X-Ray xrayType and prompt generation"}, "XRayCreateStep1ResponseData": {"type": "object", "properties": {"xrayType": {"type": "string", "title": "XrayType"}, "prompt": {"type": "string", "title": "Prompt"}}, "required": ["xrayType", "prompt"], "title": "XRayCreateStep1ResponseData"}, "XRayCreateStep2Body": {"type": "object", "properties": {"xrayType": {"type": "string", "title": "XrayType"}, "prompt": {"type": "string", "title": "Prompt"}}, "required": ["xrayType", "prompt"], "title": "XRayCreateStep2Body", "description": "Request model for X-Ray title, emoji, and short summary generation"}, "XRayCreateStep2Response": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayCreateStep2ResponseData"}}, "required": ["success", "message"], "title": "XRayCreateStep2Response", "description": "Response model for X-Ray title, emoji, and short summary generation"}, "XRayCreateStep2ResponseData": {"type": "object", "properties": {"title": {"type": "string", "title": "Title"}, "emoji": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "shortSummary": {"type": "string", "title": "ShortSummary"}}, "required": ["title", "emoji", "shortSummary"], "title": "XRayCreateStep2ResponseData"}, "XRayCreateStep3Body": {"type": "object", "properties": {"description": {"type": "string", "title": "Description"}, "xrayType": {"type": "string", "title": "XrayType"}, "prompt": {"type": "string", "title": "Prompt"}, "title": {"type": "string", "title": "Title"}, "emoji": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}, "shortSummary": {"type": "string", "title": "ShortSummary"}, "frequency": {"type": "string", "nullable": true, "title": "Frequency", "description": "Cron expression for digest scheduling (required for digest type X-Rays)"}, "timezone": {"type": "string", "nullable": true, "title": "Timezone", "description": "Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC')"}, "alertChannels": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true, "title": "AlertChannels"}}, "required": ["description", "xrayType", "prompt", "title", "emoji", "shortSummary"], "title": "XRayCreateStep3Body", "description": "Request model for creating an X-Ray with all generated data"}, "XRayUpdateRequestBody": {"type": "object", "properties": {"title": {"type": "string", "nullable": true, "title": "Title"}, "alertChannels": {"type": "object", "additionalProperties": {"type": "boolean"}, "nullable": true, "title": "AlertChannels"}, "isActive": {"type": "boolean", "nullable": true, "title": "IsActive"}, "frequency": {"type": "string", "nullable": true, "title": "Frequency", "description": "Cron expression for digest scheduling (only applicable for digest type X-Rays)"}, "timezone": {"type": "string", "nullable": true, "title": "Timezone", "description": "Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC')"}}, "title": "XRayUpdateRequestBody", "description": "Request model for updating an X-Ray"}, "XRayResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayResponseData"}}, "required": ["success", "message"], "title": "XRayResponse", "description": "Response model for X-Ray operations"}, "XRayDeleteResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}}, "required": ["success", "message"], "title": "XRayDeleteResponse", "description": "Response model for X-Ray delete operation"}, "XRayResponseData": {"type": "object", "properties": {"xray": {"$ref": "#/components/schemas/XRayDTO"}}, "required": ["xray"], "title": "XRayResponseData"}, "XRayDTO": {"type": "object", "properties": {"id": {"type": "integer", "title": "Id"}, "ownerId": {"type": "integer", "title": "OwnerId"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "prompt": {"type": "string", "title": "Prompt"}, "icon": {"type": "string", "title": "Icon"}, "shortSummary": {"type": "string", "title": "ShortSummary"}, "currentCommitId": {"type": "integer", "nullable": true, "title": "CurrentCommitId"}, "currentCommit": {"$ref": "#/components/schemas/XRayDocCommit", "nullable": true}, "alertChannels": {"type": "object", "additionalProperties": {"type": "boolean"}, "title": "AlertChannels"}, "isActive": {"type": "boolean", "title": "IsActive"}, "visibility": {"type": "string", "title": "Visibility"}, "xrayType": {"type": "string", "title": "XrayType"}, "scope": {"type": "string", "title": "<PERSON><PERSON>"}, "unreadNotificationsCount": {"type": "integer", "nullable": true, "title": "UnreadNotificationsCount"}, "createdAt": {"type": "integer", "title": "CreatedAt"}, "updatedAt": {"type": "integer", "title": "UpdatedAt"}, "frequency": {"type": "string", "nullable": true, "title": "Frequency", "description": "Cron expression for digest scheduling (only present for digest type X-Rays)"}, "timezone": {"type": "string", "nullable": true, "title": "Timezone", "description": "Timezone identifier for digest scheduling (e.g., 'America/New_York', 'UTC')"}, "lastDigestAt": {"type": "integer", "nullable": true, "title": "LastDigestAt", "description": "Timestamp when the digest was last generated (only present for digest type X-Rays)"}}, "required": ["id", "ownerId", "title", "description", "prompt", "icon", "shortSummary", "alertChannels", "isActive", "visibility", "xrayType", "scope", "createdAt", "updatedAt"], "title": "XRayDTO", "description": "X-Ray data transfer object"}, "XRayDocCommit": {"type": "object", "properties": {"id": {"type": "integer", "title": "Id"}, "xrayId": {"type": "integer", "title": "XrayId"}, "content": {"type": "string", "title": "Content"}, "authorId": {"type": "integer", "title": "AuthorId"}, "createdAt": {"type": "integer", "title": "CreatedAt"}, "updatedAt": {"type": "integer", "title": "UpdatedAt"}}, "required": ["id", "xrayId", "content", "authorId", "createdAt", "updatedAt"], "title": "XRayDocCommit", "description": "X-Ray document commit"}, "XRayListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayListResponseData"}}, "required": ["success", "message"], "title": "XRayListResponse", "description": "Response model for X-Ray list operations"}, "XRayListResponseData": {"type": "object", "properties": {"xrays": {"type": "array", "items": {"$ref": "#/components/schemas/XRayDTO"}, "title": "<PERSON><PERSON><PERSON>"}, "total": {"type": "integer", "title": "Total"}, "hasMore": {"type": "boolean", "title": "HasMore"}}, "required": ["xrays", "total", "hasMore"], "title": "XRayListResponseData"}, "XRayTypeFilter": {"type": "string", "enum": ["build", "monitor", "digest"], "title": "XRayTypeFilter", "description": "Filter X-Rays by type"}, "XRaySortBy": {"type": "string", "enum": ["last_updated", "alphabetical"], "title": "XRaySortBy", "description": "Sort options for X-Rays"}, "XRayTemplateListResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayTemplateListResponseData"}}, "required": ["success", "message"], "title": "XRayTemplateListResponse", "description": "Response model for X-Ray template list operations"}, "XRayTemplateListResponseData": {"type": "object", "properties": {"templates": {"type": "array", "items": {"$ref": "#/components/schemas/XRayTemplateDTO"}, "title": "Templates"}, "total": {"type": "integer", "title": "Total"}, "hasMore": {"type": "boolean", "title": "HasMore"}}, "required": ["templates", "total", "hasMore"], "title": "XRayTemplateListResponseData"}, "XRayTemplateResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayTemplateResponseData"}}, "required": ["success", "message"], "title": "XRayTemplateResponse", "description": "Response model for X-Ray template operations"}, "XRayTemplateResponseData": {"type": "object", "properties": {"template": {"$ref": "#/components/schemas/XRayTemplateDTO"}}, "required": ["template"], "title": "XRayTemplateResponseData"}, "XRayTemplateDTO": {"type": "object", "properties": {"id": {"type": "integer", "title": "Id"}, "ownerId": {"type": "integer", "title": "OwnerId"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "prompt": {"type": "string", "title": "Prompt"}, "icon": {"type": "string", "title": "Icon"}, "shortSummary": {"type": "string", "title": "ShortSummary"}, "xrayType": {"type": "string", "title": "XrayType"}, "createdAt": {"type": "integer", "title": "CreatedAt"}, "updatedAt": {"type": "integer", "title": "UpdatedAt"}}, "required": ["id", "ownerId", "title", "description", "prompt", "icon", "shortSummary", "xrayType", "createdAt", "updatedAt"], "title": "XRayTemplateDTO", "description": "X-Ray template data transfer object"}, "XRayNotificationResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/XRayNotificationResponseData", "nullable": true}}, "required": ["success", "message"], "title": "XRayNotificationResponse", "description": "Response model for X-Ray notifications"}, "XRayNotificationResponseData": {"type": "object", "properties": {"notifications": {"type": "array", "items": {"$ref": "#/components/schemas/XRayNotificationDTO"}, "title": "Notifications"}, "total": {"type": "integer", "title": "Total"}, "hasMore": {"type": "boolean", "title": "HasMore"}}, "required": ["notifications", "total", "hasMore"], "title": "XRayNotificationResponseData"}, "XRayNotificationDTO": {"type": "object", "properties": {"id": {"type": "integer", "title": "Id"}, "xrayDocCommitId": {"type": "integer", "title": "XrayDocCommitId"}, "userId": {"type": "integer", "title": "UserId"}, "title": {"type": "string", "title": "Title"}, "content": {"type": "string", "title": "Content"}, "source": {"type": "object", "additionalProperties": true, "title": "Source"}, "seen": {"type": "boolean", "title": "Seen"}, "createdAt": {"type": "integer", "title": "CreatedAt"}, "updatedAt": {"type": "integer", "title": "UpdatedAt"}}, "required": ["id", "xrayDocCommitId", "userId", "title", "seen", "content", "source", "createdAt", "updatedAt"], "title": "XRayNotificationDTO", "description": "X-Ray notification data transfer object"}, "MarkNotificationsSeenResponse": {"type": "object", "properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"$ref": "#/components/schemas/MarkNotificationsSeenResponseData", "nullable": true}}, "required": ["success", "message"], "title": "MarkNotificationsSeenResponse", "description": "Response model for marking notifications as seen"}, "MarkNotificationsSeenResponseData": {"type": "object", "properties": {"markedCount": {"type": "integer", "title": "Marked<PERSON>ount"}}, "required": ["markedCount"], "title": "MarkNotificationsSeenResponseData"}, "Notification": {"properties": {"id": {"type": "integer", "title": "Id"}, "xrayDocCommitId": {"type": "integer", "title": "Xraydoccommitid"}, "userId": {"type": "integer", "title": "Userid"}, "seen": {"type": "boolean", "title": "Seen"}, "content": {"type": "string", "title": "Content"}, "source": {"type": "object", "title": "Source"}, "createdAt": {"type": "integer", "title": "Createdat"}, "updatedAt": {"type": "integer", "title": "Updatedat"}}, "type": "object", "required": ["id", "xrayDocCommitId", "userId", "seen", "content", "source", "createdAt", "updatedAt"], "title": "Notification"}, "XRayTemplate": {"properties": {"id": {"type": "integer", "title": "Id"}, "ownerId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Ownerid"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "prompt": {"type": "string", "title": "Prompt"}, "shortSummary": {"type": "string", "title": "Shortsummary"}, "xrayType": {"type": "string", "title": "Xraytype"}, "icon": {"type": "string", "title": "Icon"}, "createdAt": {"type": "integer", "title": "Createdat"}, "updatedAt": {"type": "integer", "title": "Updatedat"}}, "type": "object", "required": ["id", "ownerId", "title", "description", "prompt", "shortSummary", "xrayType", "icon", "createdAt", "updatedAt"], "title": "XRayTemplate"}, "XRay": {"properties": {"id": {"type": "integer", "title": "Id"}, "ownerId": {"type": "integer", "title": "Ownerid"}, "title": {"type": "string", "title": "Title"}, "description": {"type": "string", "title": "Description"}, "prompt": {"type": "string", "title": "Prompt"}, "icon": {"type": "string", "title": "Icon"}, "shortSummary": {"type": "string", "title": "Shortsummary"}, "currentCommitId": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Currentcommitid"}, "currentCommit": {"anyOf": [{"$ref": "#/components/schemas/XRayDocCommit"}, {"type": "null"}]}, "alertChannels": {"additionalProperties": {"type": "boolean"}, "type": "object", "title": "Alertchannels"}, "isActive": {"type": "boolean", "title": "Isactive"}, "visibility": {"type": "string", "title": "Visibility"}, "xrayType": {"type": "string", "title": "Xraytype"}, "scope": {"type": "string", "title": "<PERSON><PERSON>"}, "unreadNotificationsCount": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Unreadnotificationscount"}, "frequency": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Frequency"}, "timezone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Timezone"}, "lastDigestAt": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lastdigestat"}, "createdAt": {"type": "integer", "title": "Createdat"}, "updatedAt": {"type": "integer", "title": "Updatedat"}}, "type": "object", "required": ["id", "ownerId", "title", "description", "prompt", "icon", "shortSummary", "alertChannels", "isActive", "visibility", "xrayType", "scope", "createdAt", "updatedAt"], "title": "XRay"}}}}