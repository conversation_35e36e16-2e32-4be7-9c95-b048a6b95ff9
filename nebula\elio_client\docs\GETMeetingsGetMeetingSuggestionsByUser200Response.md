# GETMeetingsGetMeetingSuggestionsByUser200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**GETMeetingsGetMeetingSuggestionsByUser200ResponseData**](GETMeetingsGetMeetingSuggestionsByUser200ResponseData.md) |  | 
**message** | **str** |  | 

## Example

```python
from elio_client.models.get_meetings_get_meeting_suggestions_by_user200_response import GETMeetingsGetMeetingSuggestionsByUser200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETMeetingsGetMeetingSuggestionsByUser200Response from a JSON string
get_meetings_get_meeting_suggestions_by_user200_response_instance = GETMeetingsGetMeetingSuggestionsByUser200Response.from_json(json)
# print the JSON string representation of the object
print(GETMeetingsGetMeetingSuggestionsByUser200Response.to_json())

# convert the object into a dict
get_meetings_get_meeting_suggestions_by_user200_response_dict = get_meetings_get_meeting_suggestions_by_user200_response_instance.to_dict()
# create an instance of GETMeetingsGetMeetingSuggestionsByUser200Response from a dict
get_meetings_get_meeting_suggestions_by_user200_response_from_dict = GETMeetingsGetMeetingSuggestionsByUser200Response.from_dict(get_meetings_get_meeting_suggestions_by_user200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


