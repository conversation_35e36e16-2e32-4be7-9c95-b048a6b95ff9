package services

import (
	"context"
	"encore.app/hubble/database"
	"strconv"

	"encore.app/hubble/models"
	"encore.app/hubble/repositories"
	"encore.app/shared"
	"encore.dev/rlog"
	"gorm.io/gorm"
)

func UpdateUserRoleTransaction(ctx context.Context, auth *repositories.AuthUserRepository, userID string,
	role models.USER_ROLE) (
	models.AuthUser, error) {
	dbInstance := auth.DB
	authUser := models.AuthUser{}
	err := dbInstance.Transaction(func(tx *gorm.DB) error {
		var err error
		authUser.ID, err = strconv.ParseInt(userID, 10, 64)
		if err != nil {
			rlog.Error("UpdateUserRoleTransaction:: Error while parsing userID", "err", err)
			return err
		}

		authUserModel, err := auth.GetAuthUserByID(ctx, authUser.ID)
		if err != nil {
			return err
		}

		updatedRoles := append(authUserModel.RoleIDs, string(role))
		dbInstance.WithContext(ctx).Model(authUser).Updates(models.AuthUser{
			RoleIDs: updatedRoles,
		})
		authUser = *authUserModel
		authUser.RoleIDs = updatedRoles

		err = database.UpdateUserRoleIDsAppendRole(ctx, userID, role)
		if err != nil {
			return err
		}

		return nil
	})

	return authUser, err
}

func MapAuthUserDTO(user models.AuthUser) shared.UserDTO {
	id := strconv.FormatInt(user.ID, 10)
	return shared.UserDTO{
		AppleID:  user.AppleID,
		Email:    &user.Email,
		GoogleID: user.GoogleID,
		Id:       &id,
	}
}
