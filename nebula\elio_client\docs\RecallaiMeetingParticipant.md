# RecallaiMeetingParticipant


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**id** | **int** |  | 
**is_host** | **bool** |  | 
**name** | **str** |  | 
**platform** | **str** |  | 

## Example

```python
from elio_client.models.recallai_meeting_participant import RecallaiMeetingParticipant

# TODO update the JSON string below
json = "{}"
# create an instance of RecallaiMeetingParticipant from a JSON string
recallai_meeting_participant_instance = RecallaiMeetingParticipant.from_json(json)
# print the JSON string representation of the object
print(RecallaiMeetingParticipant.to_json())

# convert the object into a dict
recallai_meeting_participant_dict = recallai_meeting_participant_instance.to_dict()
# create an instance of RecallaiMeetingParticipant from a dict
recallai_meeting_participant_from_dict = RecallaiMeetingParticipant.from_dict(recallai_meeting_participant_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


