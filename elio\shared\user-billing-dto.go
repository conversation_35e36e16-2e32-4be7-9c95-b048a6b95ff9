package shared

import (
	"encoding/json"
)

// UserSubscriptionPlanDTOCollectionMode defines model for UserSubscriptionPlanDTO.CollectionMode.
type UserSubscriptionPlanDTOCollectionMode string

// Defines values for UserSubscriptionPlanDTOCollectionMode.
const (
	Automatic UserSubscriptionPlanDTOCollectionMode = "automatic"
	Manual    UserSubscriptionPlanDTOCollectionMode = "manual"
)

// BillingCycleDTO defines model for BillingCycleDTO.
type BillingCycleDTO struct {
	Frequency *float32 `json:"frequency,omitempty" encore:"optional"`
	Interval  *string  `json:"interval,omitempty" encore:"optional"`
}

// PriceDTOStatus defines model for PriceDTO.Status.
type PriceDTOStatus string

// Defines values for PriceDTOStatus.
const (
	PriceDTOStatusActive   PriceDTOStatus = "active"
	PriceDTOStatusArchived PriceDTOStatus = "archived"
)

// PriceDTOTaxMode defines model for PriceDTO.TaxMode.
type PriceDTOTaxMode string

const (
	AccountSetting PriceDTOTaxMode = "account_setting"
	External       PriceDTOTaxMode = "external"
	Internal       PriceDTOTaxMode = "internal"
)

// SubscriptionItemDTOStatus defines model for SubscriptionItemDTO.Status.
type SubscriptionItemDTOStatus string

// Defines values for SubscriptionItemDTOStatus.
const (
	SubscriptionItemDTOStatusActive   SubscriptionItemDTOStatus = "active"
	SubscriptionItemDTOStatusInactive SubscriptionItemDTOStatus = "inactive"
	SubscriptionItemDTOStatusTrialing SubscriptionItemDTOStatus = "trialing"
)

// UnitPriceDTO defines model for UnitPriceDTO.
type UnitPriceDTO struct {
	Amount       string `json:"amount" encore:"optional"`
	CurrencyCode string `json:"currencyCode" encore:"optional"`
}

// UnitPriceOverrideDTO defines model for UnitPriceOverrideDTO.
type UnitPriceOverrideDTO struct {
	CountryCodes []string     `json:"countryCodes" encore:"optional"`
	UnitPrice    UnitPriceDTO `json:"unitPrice" encore:"optional"`
}

type PriceQuantityDTO struct {
	Minimum float32 `json:"minimum" encore:"optional"`
	Maximum float32 `json:"maximum" encore:"optional"`
}

type PriceDTOTrialPeriod struct {
	Frequency *float32 `json:"frequency,omitempty" encore:"optional"`
	Interval  *string  `json:"interval,omitempty" encore:"optional"`
}

// PriceDTO defines model for PriceDTO.
type PriceDTO struct {
	BillingCycle       *BillingCycleDTO        `json:"billingCycle,omitempty" encore:"optional"`
	Id                 *string                 `json:"id,omitempty" encore:"optional"`
	ProductID          *string                 `json:"productID,omitempty" encore:"optional"`
	Status             *PriceDTOStatus         `json:"status,omitempty" encore:"optional"`
	TaxMode            *PriceDTOTaxMode        `json:"taxMode,omitempty" encore:"optional"`
	TrialPeriod        *PriceDTOTrialPeriod    `json:"trialPeriod" encore:"optional"`
	UnitPrice          *UnitPriceDTO           `json:"unitPrice,omitempty" encore:"optional"`
	UnitPriceOverrides *[]UnitPriceOverrideDTO `json:"unitPriceOverrides,omitempty" encore:"optional"`
	Quantity           *PriceQuantityDTO       `json:"quantity" encore:"optional"`
}

// SubscriptionItemDTO defines model for SubscriptionItemDTO.
type SubscriptionItemDTO struct {
	CreatedAt          string                    `json:"createdAt" encore:"optional"`
	CustomData         *json.RawMessage          `json:"customData,omitempty" copier:"-"  encore:"optional"`
	NextBilledAt       *string                   `json:"nextBilledAt"  encore:"optional"`
	PreviouslyBilledAt *string                   `json:"previouslyBilledAt" encore:"optional"`
	Price              PriceDTO                  `json:"price" encore:"optional"`
	Quantity           float32                   `json:"quantity" encore:"optional"`
	Recurring          bool                      `json:"recurring" encore:"optional"`
	Status             SubscriptionItemDTOStatus `json:"status" encore:"optional"`
	TrialDates         *struct {
		EndsAt   *string `json:"endsAt,omitempty" encore:"optional"`
		StartsAt *string `json:"startsAt,omitempty" encore:"optional"`
	} `json:"trialDates" encore:"optional"`
	UpdatedAt string `json:"updatedAt" encore:"optional"`
}

// SubscriptionPlanConfigDTOSupportType defines model for SubscriptionPlanConfigDTO.Support.Type.
type SubscriptionPlanConfigDTOSupportType string

// Defines values for SubscriptionPlanConfigDTOSupportType.
const (
	Basic   SubscriptionPlanConfigDTOSupportType = "basic"
	Premium SubscriptionPlanConfigDTOSupportType = "premium"
)

// SubscriptionPlanConfigDTO defines the DTO for the SubscriptionPlanConfig entity.
type SubscriptionPlanConfigDTO struct {
	AiFeed struct {
		Enabled bool `json:"enabled"`
	} `json:"aiFeed"`
	CustomFeedItems struct {
		Enabled bool `json:"enabled"`
	} `json:"customFeedItems"`
	CustomIntegrations struct {
		Enabled bool `json:"enabled"`
	} `json:"customIntegrations"`
	Id           string `json:"id"`
	Integrations struct {
		Apps    []string `json:"apps" encore:"optional"`
		Enabled bool     `json:"enabled"`
	} `json:"integrations"`
	MeetingMemory struct {
		Enabled bool `json:"enabled"`
	} `json:"meetingMemory"`
	MeetingSummary struct {
		Enabled bool `json:"enabled"`
	} `json:"meetingSummary"`
	MeetingTemplates struct {
		Enabled bool `json:"enabled"`
	} `json:"meetingTemplates"`
	MeetingWorkflows struct {
		Enabled bool `json:"enabled"`
	} `json:"meetingWorkflows"`
	Meetings struct {
		Enabled bool    `json:"enabled"`
		Max     float32 `json:"max"`
	} `json:"meetings"`
	ModelSegregation struct {
		Enabled bool `json:"enabled"`
	} `json:"modelSegregation"`
	OffTheRecord struct {
		Enabled bool `json:"enabled"`
	} `json:"offTheRecord"`
	PaddleProductID   string `json:"paddleProductID"`
	PaddleProductName string `json:"paddleProductName"`
	QueueMode         struct {
		Enabled bool `json:"enabled"`
	} `json:"queueMode"`
	Recording struct {
		Enabled bool  `json:"enabled"`
		Local   *bool `json:"local,omitempty" encore:"optional"`
	} `json:"recording"`
	Stream struct {
		Enabled bool    `json:"enabled"`
		Quality float32 `json:"quality"`
	} `json:"stream"`
	Support struct {
		Enabled bool                                 `json:"enabled"`
		Type    SubscriptionPlanConfigDTOSupportType `json:"type"`
	} `json:"support"`
	Crm struct {
		Enabled bool `json:"enabled"`
	} `json:"crm"`
	Bots struct {
		Enabled bool `json:"enabled"`
	} `json:"bots"`
	TimeLimit struct {
		Enabled bool `json:"enabled"`
		Max     int  `json:"max"`
	} `json:"timeLimit"`
	CreatedAt int64 `json:"createdAt"`
	UpdatedAt int64 `json:"updatedAt"`
}

// UserSubscriptionPlanDTOScheduledChangeAction defines model for UserSubscriptionPlanDTO.ScheduledChange.Action.
type UserSubscriptionPlanDTOScheduledChangeAction string

// Defines values for UserSubscriptionPlanDTOScheduledChangeAction.
const (
	Cancel UserSubscriptionPlanDTOScheduledChangeAction = "cancel"
	Pause  UserSubscriptionPlanDTOScheduledChangeAction = "pause"
	Resume UserSubscriptionPlanDTOScheduledChangeAction = "resume"
)

// UserSubscriptionPlanDTOStatus defines model for UserSubscriptionPlanDTO.Status.
type UserSubscriptionPlanDTOStatus string

// Defines values for UserSubscriptionPlanDTOStatus.
const (
	Active       UserSubscriptionPlanDTOStatus = "active"
	Canceled     UserSubscriptionPlanDTOStatus = "canceled"
	PastDue      UserSubscriptionPlanDTOStatus = "past_due"
	TrialExpired UserSubscriptionPlanDTOStatus = "trial_expired"
	Trialing     UserSubscriptionPlanDTOStatus = "trialing"
)

// UserSubscriptionPlanDTO defines model for UserSubscriptionPlanDTO.
type UserSubscriptionPlanDTO struct {
	AddressID      *string          `json:"addressID,omitempty" encore:"optional"`
	BillingCycle   *BillingCycleDTO `json:"billingCycle,omitempty" encore:"optional"`
	BillingDetails *struct {
		AdditionalInformation *string `json:"additionalInformation,omitempty" encore:"optional"`
		EnableCheckout        *bool   `json:"enableCheckout,omitempty" encore:"optional"`
		PurchaseOrderMinimum  *string `json:"purchaseOrderMinimum,omitempty" encore:"optional"`
	} `json:"billingDetails,omitempty" encore:"optional"`
	BusinessID           *string                                `json:"businessID,omitempty" encore:"optional"`
	CanceledAt           *string                                `json:"canceledAt,omitempty" encore:"optional"`
	CollectionMode       *UserSubscriptionPlanDTOCollectionMode `json:"collectionMode,omitempty" encore:"optional"`
	CreatedAt            int64                                  `json:"createdAt" encore:"optional"`
	CurrencyCode         *string                                `json:"currencyCode,omitempty" encore:"optional"`
	CurrentBillingPeriod *struct {
		EndsAt   *string `json:"endsAt,omitempty" encore:"optional"`
		StartsAt *string `json:"startsAt,omitempty" encore:"optional"`
	} `json:"currentBillingPeriod,omitempty" encore:"optional"`
	CustomData     *json.RawMessage       `json:"customData,omitempty" copier:"-" encore:"optional"`
	CustomerID     *string                `json:"customerID,omitempty" encore:"optional"`
	FirstBilledAt  *string                `json:"firstBilledAt,omitempty" encore:"optional"`
	Id             string                 `json:"id" encore:"optional"`
	Items          *[]SubscriptionItemDTO `json:"items,omitempty" encore:"optional"`
	ManagementUrls *struct {
		Cancel              *string `json:"cancel,omitempty" encore:"optional"`
		UpdatePaymentMethod *string `json:"updatePaymentMethod,omitempty" encore:"optional"`
	} `json:"managementUrls,omitempty" encore:"optional"`
	NextBilledAt         *string                    `json:"nextBilledAt,omitempty" encore:"optional"`
	PaddleSubscriptionID *string                    `json:"paddleSubscriptionID,omitempty" encore:"optional"`
	PausedAt             *string                    `json:"pausedAt,omitempty" encore:"optional"`
	PlanConfig           *SubscriptionPlanConfigDTO `json:"planConfig,omitempty" encore:"optional"`
	PlanConfigOverrides  *json.RawMessage           `json:"planConfigOverrides,omitempty" copier:"-" encore:"optional"`
	ScheduledChange      *struct {
		Action      *UserSubscriptionPlanDTOScheduledChangeAction `json:"action,omitempty" encore:"optional"`
		EffectiveAt *string                                       `json:"effectiveAt,omitempty" encore:"optional"`
		ResumeAt    *string                                       `json:"resumeAt,omitempty" encore:"optional"`
	} `json:"scheduledChange,omitempty" encore:"optional"`
	StartedAt   *string                       `json:"startedAt,omitempty" encore:"optional"`
	Status      UserSubscriptionPlanDTOStatus `json:"status" encore:"optional"`
	TrialEndsAt *int                          `json:"trialEndsAt,omitempty" encore:"optional"`
	UpdatedAt   int64                         `json:"updatedAt" encore:"optional"`
	UserID      string                        `json:"userID" encore:"optional"`
}

// SessionSubscriptionPlanDTO defines model for SessionSubscriptionPlanDTO.
type SessionSubscriptionPlanDTO struct {
	PlanConfig          SubscriptionPlanConfigDTO `json:"planConfig" encore:"optional"`
	PlanConfigOverrides *json.RawMessage          `json:"planConfigOverrides,omitempty" copier:"-" encore:"optional"`
}
