package comms_test

import (
	"context"
	"testing"

	"encore.app/meetings"
	meetings_api "encore.app/meetings/api"
	"encore.app/shared"
	"encore.app/transcriptions"
	ltapi "encore.app/transcriptions/api"
	"encore.app/wormhole"
	wormhole_api "encore.app/wormhole/api"
	nebula_api "encore.app/wormhole/services/nebula-api"
	"encore.dev/et"

	"encore.app/comms"
	api "encore.app/comms/api"
	"github.com/samber/lo"
	"github.com/stretchr/testify/assert"
)

func TestPostSessionSummaryReady(t *testing.T) {
	setUp()

	// Mock meeting suggestions endpoint for personalized suggestions
	et.MockEndpoint(meetings.GetMeetingSuggestionsByUserInternal, func(ctx context.Context, req *meetings_api.GetMeetingSuggestionsInternalRequest) (*meetings_api.GetMeetingSuggestionsResponse, error) {
		// Return mock suggestions based on user ID
		var suggestions []meetings_api.SuggestionResponseDTO
		switch req.UserID {
		case "1": // owner user
			suggestions = []meetings_api.SuggestionResponseDTO{
				{
					Id:         "1",
					Content:    "How effectively did I facilitate today's planning session?",
					Category:   "self_review",
					IsPersonal: true,
					CreatedAt:  1234567890,
				},
				{
					Id:         "2",
					Content:    "What are my action items from the quarterly review?",
					Category:   "action_items",
					IsPersonal: true,
					CreatedAt:  1234567891,
				},
				{
					Id:         "3",
					Content:    "How can I improve my presentation skills for future meetings?",
					Category:   "content_feedback",
					IsPersonal: true,
					CreatedAt:  1234567892,
				},
			}
		case "2": // guest user
			suggestions = []meetings_api.SuggestionResponseDTO{
				{
					Id:         "4",
					Content:    "How well did I contribute to the technical discussions?",
					Category:   "self_review",
					IsPersonal: true,
					CreatedAt:  1234567893,
				},
				{
					Id:         "5",
					Content:    "What follow-up tasks do I have from this meeting?",
					Category:   "action_items",
					IsPersonal: true,
					CreatedAt:  1234567894,
				},
				{
					Id:         "6",
					Content:    "What feedback can I provide on the proposed solutions?",
					Category:   "content_feedback",
					IsPersonal: true,
					CreatedAt:  1234567895,
				},
			}
		default:
			suggestions = []meetings_api.SuggestionResponseDTO{} // No suggestions for unknown users
		}
		
		return &meetings_api.GetMeetingSuggestionsResponse{
			Data: struct {
				Suggestions []meetings_api.SuggestionResponseDTO `json:"suggestions"`
				Total       int                                 `json:"total"`
			}{
				Suggestions: suggestions,
				Total:       len(suggestions),
			},
			Message: "Successfully retrieved meeting suggestions",
		}, nil
	})

	et.MockEndpoint(transcriptions.GetSpeakersAsSessionGuestsBySessionID, func(ctx context.Context, p *ltapi.GetSessionSpeakersAsGuestsBySessionIDRequest) (*ltapi.GetSessionSpeakersAsGuestsBySessionIDResponse, error) {
		if p.SessionRecurrenceID == "3" {
			return &ltapi.GetSessionSpeakersAsGuestsBySessionIDResponse{
				Guests: []shared.GuestDTO{
					{Email: "<EMAIL>"},
					{Email: "<EMAIL>"},
					{Email: "<EMAIL>"},
				},
			}, nil
		}
		if p.SessionRecurrenceID == "1" {
			return &ltapi.GetSessionSpeakersAsGuestsBySessionIDResponse{
				Guests: []shared.GuestDTO{
					{Email: "<EMAIL>"},
				},
			}, nil
		}
		if p.SessionRecurrenceID == "2" {
			return &ltapi.GetSessionSpeakersAsGuestsBySessionIDResponse{
				Guests: []shared.GuestDTO{
					{Email: "<EMAIL>"},
					{Email: "<EMAIL>"},
				},
			}, nil
		}
		return &ltapi.GetSessionSpeakersAsGuestsBySessionIDResponse{
			Guests: make([]shared.GuestDTO, 0),
		}, nil
	})

	et.MockEndpoint(wormhole.GetMeetingMetadataById, func(ctx context.Context, p *wormhole_api.GetMeetingMetadataRequest) (*nebula_api.MeetingMetadataResponse, error) {
		return &nebula_api.MeetingMetadataResponse{
			Data: struct {
				Metadata *struct {
					Description  *string   `json:"description,omitempty"`
					EndTime      *string   `json:"end_time,omitempty"`
					Location     *string   `json:"location,omitempty"`
					Participants *[]string `json:"participants,omitempty"`
					StartTime    *string   `json:"start_time,omitempty"`
					Title        *string   `json:"title,omitempty"`
				} `json:"metadata,omitempty"`
			}{
				Metadata: &struct {
					Description  *string   `json:"description,omitempty"`
					EndTime      *string   `json:"end_time,omitempty"`
					Location     *string   `json:"location,omitempty"`
					Participants *[]string `json:"participants,omitempty"`
					StartTime    *string   `json:"start_time,omitempty"`
					Title        *string   `json:"title,omitempty"`
				}{
					Title: lo.ToPtr("Fake LLM generated title"),
				},
			},
		}, nil
	})

	t.Run("PostSessionSummaryReady default email recipients (everyone)", func(t *testing.T) {
		resp, err := comms.PostSessionSummaryReady(context.Background(), &api.PostSessionSummaryReadyRequest{
			SessionID:           "1",
			SessionRecurrenceID: "3",
		})
		assert.NoError(t, err)
		assert.NotNil(t, resp)

		assert.Equal(t, 3, len(resp.Recipients))
		assert.Equal(t, "<EMAIL>", resp.Recipients[0])
		assert.Equal(t, "<EMAIL>", resp.Recipients[1])
		assert.Equal(t, "<EMAIL>", resp.Recipients[2])
	})

	t.Run("PostSessionSummaryReady owner only email recipients", func(t *testing.T) {
		resp, err := comms.PostSessionSummaryReady(context.Background(), &api.PostSessionSummaryReadyRequest{
			SessionID:           "1",
			SessionRecurrenceID: "1",
		})
		assert.NoError(t, err)
		assert.NotNil(t, resp)

		assert.Equal(t, 1, len(resp.Recipients))
		assert.Equal(t, "<EMAIL>", resp.Recipients[0])
	})

	t.Run("PostSessionSummaryReady same domain email recipients", func(t *testing.T) {
		resp, err := comms.PostSessionSummaryReady(context.Background(), &api.PostSessionSummaryReadyRequest{
			SessionID:           "1",
			SessionRecurrenceID: "2",
		})
		assert.NoError(t, err)
		assert.NotNil(t, resp)

		assert.Equal(t, 2, len(resp.Recipients))
		assert.Equal(t, "<EMAIL>", resp.Recipients[0])
		assert.Equal(t, "<EMAIL>", resp.Recipients[1])
	})
}
