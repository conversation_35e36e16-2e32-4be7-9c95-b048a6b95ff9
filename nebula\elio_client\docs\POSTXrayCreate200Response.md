# POSTXrayCreate200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**ApiXRayDTO**](ApiXRayDTO.md) |  | 
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.post_xray_create200_response import POSTXrayCreate200Response

# TODO update the JSON string below
json = "{}"
# create an instance of POSTXrayCreate200Response from a JSON string
post_xray_create200_response_instance = POSTXrayCreate200Response.from_json(json)
# print the JSON string representation of the object
print(POSTXrayCreate200Response.to_json())

# convert the object into a dict
post_xray_create200_response_dict = post_xray_create200_response_instance.to_dict()
# create an instance of POSTXrayCreate200Response from a dict
post_xray_create200_response_from_dict = POSTXrayCreate200Response.from_dict(post_xray_create200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


