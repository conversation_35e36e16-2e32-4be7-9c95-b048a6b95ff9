# SharedPriceDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**billing_cycle** | [**SharedBillingCycleDTO**](SharedBillingCycleDTO.md) |  | [optional] 
**id** | **str** |  | [optional] 
**product_id** | **str** |  | [optional] 
**quantity** | [**SharedPriceQuantityDTO**](SharedPriceQuantityDTO.md) |  | [optional] 
**status** | **str** |  | [optional] 
**tax_mode** | **str** |  | [optional] 
**trial_period** | [**SharedPriceDTOTrialPeriod**](SharedPriceDTOTrialPeriod.md) |  | [optional] 
**unit_price** | [**SharedUnitPriceDTO**](SharedUnitPriceDTO.md) |  | [optional] 
**unit_price_overrides** | [**List[SharedUnitPriceOverrideDTO]**](SharedUnitPriceOverrideDTO.md) |  | [optional] 

## Example

```python
from elio_client.models.shared_price_dto import SharedPriceDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedPriceDTO from a JSON string
shared_price_dto_instance = SharedPriceDTO.from_json(json)
# print the JSON string representation of the object
print(SharedPriceDTO.to_json())

# convert the object into a dict
shared_price_dto_dict = shared_price_dto_instance.to_dict()
# create an instance of SharedPriceDTO from a dict
shared_price_dto_from_dict = SharedPriceDTO.from_dict(shared_price_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


