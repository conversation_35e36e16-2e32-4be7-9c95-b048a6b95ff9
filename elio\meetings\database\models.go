package database

import (
	"context"
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"encore.app/meetings/api"
	"github.com/jmoiron/sqlx"

	"encore.dev/rlog"
	"github.com/samber/lo"

	"encore.app/pkg/database"
	"encore.app/shared"
	"github.com/jinzhu/copier"
	"github.com/lib/pq"
)

type MeetingTypeRow struct {
	Id   int64  `db:"id"`
	Name string `db:"name"`
}

type AccessRuleRow struct {
	Id                  int64          `db:"id"`
	SessionID           int64          `db:"sessionID"`
	SessionRecurrenceID int64          `db:"sessionRecurrenceID"`
	AccessType          string         `db:"accessType"`
	Type                string         `db:"type"`
	Value               string         `db:"value"`
	RestrictionStatus   string         `db:"restrictionStatus"`
	RequestMessage      sql.NullString `db:"requestMessage"`
	IsExternalRequest   bool           `db:"isExternalRequest"`
	GuestSurrogateID    sql.NullInt64  `db:"guestSurrogateID"`
	GuestFullName       sql.NullString `db:"guestFullName"`
	GuestEmail          sql.NullString `db:"guestEmail"`
	CreatedAt           int64          `db:"createdAt"`
	UpdatedAt           int64          `db:"updatedAt"`
}

func (rule AccessRuleRow) ToAccessRequestDTO() api.InReviewAccessRequestDTO {
	row := api.InReviewAccessRequestDTO{}
	copier.Copy(&row, rule)
	row.Id = shared.FormatInt(rule.Id)
	row.SessionID = shared.FormatInt(rule.SessionID)
	row.SessionRecurrenceID = shared.FormatInt(rule.SessionRecurrenceID)
	if rule.Type == "guest" && rule.GuestFullName.Valid {
		row.User.FirstName = rule.GuestFullName.String
	}
	return row
}

func (rule AccessRuleRow) ToSessionAccessDTO() api.SessionAccessDTO {
	sessionAccessDTO := api.SessionAccessDTO{
		ID:                  shared.FormatInt(rule.Id),
		SessionID:           shared.FormatInt(rule.SessionID),
		SessionRecurrenceID: shared.FormatInt(rule.SessionRecurrenceID),
		RestrictionStatus:   api.RestrictionStatus(rule.RestrictionStatus),
		Type:                api.AccessRuleType(rule.Type),
	}
	if rule.RequestMessage.Valid {
		sessionAccessDTO.RequestMessage = rule.RequestMessage.String
	}
	if sessionAccessDTO.Type == api.AccessRuleDomain {
		sessionAccessDTO.Value = rule.Value
	}
	if sessionAccessDTO.Type == api.AccessRuleGuest {
		sessionAccessDTO.User = &api.UserSummaryDTO{
			FullName: rule.GuestFullName.String,
			Email:    rule.GuestEmail.String,
		}
	}
	if sessionAccessDTO.Type == api.AccessRuleEmail {
		sessionAccessDTO.User = &api.UserSummaryDTO{
			Email: rule.Value,
		}
	}
	return sessionAccessDTO
}

type UserSummaryRow struct {
	Id        int64   `db:"id"`
	Avatar    *string `db:"avatar"`
	FirstName string  `db:"firstName"`
	LastName  string  `db:"lastName"`
	Email     string  `db:"email"`
}

func (u *UserSummaryRow) FullName() string {
	name := ""
	if u.FirstName != "" {
		name = u.FirstName
	}
	if u.LastName != "" {
		name += " " + u.LastName
	}
	return name
}

type SessionUsersRow struct {
	Id                    int64          `db:"id"`
	UserID                *int64         `db:"userID"`
	SessionID             int64          `db:"sessionID"`
	SessionRecurrenceID   int64          `db:"sessionRecurrenceID"`
	Fingerprint           pq.StringArray `db:"fingerprint"`
	RoleIDs               pq.StringArray `db:"roleIDs"`
	CreatedAt             int64          `db:"createdAt"`
	UpdatedAt             int64          `db:"updatedAt"`
	IsViewerAccessRevoked bool           `db:"isViewerAccessRevoked"`
	Joined                bool           `db:"joined"`
	GuestSurrogateID      *int64         `db:"guestSurrogateID"`
	GuestFullName         *string        `db:"guestFullName"`
	GuestEmail            *string        `db:"guestEmail"`
	// Legacy fields
	AgoraRecordingToken int64 `db:"agoraRecordingToken"`
}

type SessionStateUpdatedAt []shared.StateUpdatedAt

func (j *SessionStateUpdatedAt) Scan(src interface{}) error {
	return database.JSONScan(j, src)
}

type SessionRow struct {
	About        sql.NullString `db:"about"`
	AccessStatus string         `db:"accessStatus"`
	Avatar       sql.NullString `db:"avatar"`
	/** @deprecated - should be removed if possible */
	BranchURL                  sql.NullString        `db:"branchURL"`
	CalendarEventEditURL       sql.NullString        `db:"calendarEventEditURL"`
	CalendarID                 sql.NullString        `db:"calendarID"`
	CalendarType               sql.NullString        `db:"calendarType"`
	Category                   sql.NullInt64         `db:"category"`
	Cover                      sql.NullString        `db:"cover"`
	CreatedAt                  int64                 `db:"createdAt"`
	CreatorUserID              sql.NullInt64         `db:"creatorUserID"`
	DataVisibility             shared.DataVisibility `db:"dataVisibility"`
	EnableBots                 bool                  `db:"enableBots"`
	EndTimestamp               sql.NullInt64         `db:"endTimestamp"`
	EndedAt                    sql.NullInt64         `db:"endedAt"`
	HasPastRecordings          bool                  `db:"hasPastRecordings"`
	HostSessionUserIDs         pq.Int64Array         `db:"hostSessionUserIDs"`
	HostUserIDs                pq.Int64Array         `db:"hostUserIDs"`
	ID                         int64                 `db:"id"`
	IsFeatured                 bool                  `db:"isFeatured"`
	IsLiveStreamEnabled        bool                  `db:"isLiveStreamEnabled"`
	IsPubliclyVisible          bool                  `db:"isPubliclyVisible"`
	IsRecordingProcessed       bool                  `db:"isRecordingProcessed"`
	IsViewerAccessRestricted   bool                  `db:"isViewerAccessRestricted"`
	LegacyHostSessionUserIDs   pq.StringArray        `db:"legacyHostSessionUserIDs"`
	LegacyHostUserIDs          pq.StringArray        `db:"legacyHostUserIDs"`
	LegacyPrimaryHostUserID    sql.NullString        `db:"legacyPrimaryHostUserID"`
	LegacySessionCreatorUserID sql.NullString        `db:"legacySessionCreatorUserID"`
	LegacySessionID            sql.NullString        `db:"legacySessionID"`
	LegacySessionRecurrenceID  sql.NullString        `db:"legacySessionRecurrenceID"`
	MeetingType                shared.SessionDTOType `db:"meetingType"`
	OgMetadata                 database.JSONB        `db:"ogMetadata"`
	PrimaryHostUserID          int64                 `db:"primaryHostUserID"`
	ProducerSessionUserIDs     pq.Int64Array         `db:"producerSessionUserIDs"`
	ProducerUserIDs            pq.Int64Array         `db:"producerUserIDs"`
	RecurrenceID               int64                 `db:"recurrenceID"`
	RowVersion                 int                   `db:"rowversion"`
	Settings                   database.JSONB        `db:"settings"`
	StartTimestamp             sql.NullInt64         `db:"startTimestamp"`
	StartedAt                  sql.NullInt64         `db:"startedAt"`
	State                      shared.SessionState   `db:"state"`
	StateUpdatedAt             SessionStateUpdatedAt `db:"stateUpdatedAt"`
	ScreenSharingSettings      database.JSONB        `db:"screenSharingSettings"`
	Tags                       pq.StringArray        `db:"tags"`
	Title                      string                `db:"title"`
	UpdatedAt                  int64                 `db:"updatedAt"`
	URL                        string                `db:"url"`
	UserMeetingType            sql.NullInt64         `db:"user_meeting_type"`
	LobbyID                    sql.NullInt64         `db:"lobbyID"`
	UMTCreatedBy               sql.NullInt64         `db:"umt_created_by"`
	UMTDescription             sql.NullString        `db:"umt_description"`
	UMTTitle                   sql.NullString        `db:"umt_title"`
	UMTPromptTemplate          sql.NullString        `db:"umt_prompt_template"`
}

func GetHostUserIDs(sessions []SessionRow) []string {
	return lo.Uniq(lo.FlatMap(sessions, func(item SessionRow, index int) []string {
		return lo.Map(item.HostUserIDs, func(userID int64, index int) string {
			return shared.FormatInt(userID)
		})
	}))
}

func (s SessionRow) ToSessionDTOWithRelations(ctx context.Context) shared.SessionDTO {
	t := s.ToSessionDTO()
	if err := PopulateHostsWithRelations(ctx, &t); err != nil {
		rlog.Error("error populating hosts with relations", "err", err)
	}
	if err := PopulateSessionPlan(ctx, nil, &t); err != nil {
		rlog.Error("error populating session plan", "err", err)
	}
	return t
}

func (s SessionRow) ToSessionDTOWithHosts(hosts map[int64]UserRow, hostTeams map[int64]TeamWithUserRole) shared.SessionDTO {
	t := s.ToSessionDTO()
	if hosts == nil {
		return t
	}
	t.Hosts = make([]shared.UserDTO, 0, len(s.HostUserIDs))
	for _, hostID := range s.HostUserIDs {
		if host, hostOk := hosts[hostID]; hostOk {
			userDTO := host.ToUserDTO()
			if hostTeams != nil {
				if team, teamOk := hostTeams[hostID]; teamOk {
					teamDTO := shared.TeamWithInlineRelationsDTO{
						ID:        strconv.FormatInt(team.ID, 10),
						Name:      team.Name,
						CreatedAt: team.CreatedAt,
						UpdatedAt: team.UpdatedAt,
						Domains:   []string{},
					}
					userDTO.Team = &teamDTO
				}
			}
			t.Hosts = append(t.Hosts, *userDTO)
		}
	}
	return t
}

func (s SessionRow) ToSessionDTO() shared.SessionDTO {
	t := shared.SessionDTO{}
	t.Hosts = make([]shared.UserDTO, 0)
	if s.BranchURL.Valid {
		t.BranchURL = s.BranchURL.String
	}
	if s.Avatar.Valid {
		t.Avatar = lo.ToPtr(s.Avatar.String)
	}
	if s.Cover.Valid {
		t.Cover = lo.ToPtr(s.Cover.String)
	}
	if s.About.Valid {
		t.About = s.About.String
	}
	if s.CalendarEventEditURL.Valid {
		t.CalendarEventEditURL = lo.ToPtr(s.CalendarEventEditURL.String)
	}
	if s.CalendarID.Valid {
		t.CalendarID = lo.ToPtr(s.CalendarID.String)
	}
	if s.CalendarType.Valid {
		t.CalendarType = lo.ToPtr(s.CalendarType.String)
	}
	t.CreateTimestamp = lo.ToPtr(int(s.CreatedAt))
	if s.StartTimestamp.Valid {
		t.StartTimestamp = lo.ToPtr(int(s.StartTimestamp.Int64))
	}
	if s.EndTimestamp.Valid {
		t.EndTimestamp = lo.ToPtr(int(s.EndTimestamp.Int64))
	}
	if s.StartedAt.Valid {
		t.StartedAt = lo.ToPtr(int(s.StartedAt.Int64))
	}
	if s.EndedAt.Valid {
		t.EndedAt = lo.ToPtr(int(s.EndedAt.Int64))
	}
	if s.LobbyID.Valid {
		t.SessionID = shared.FormatInt(s.LobbyID.Int64)
	}
	t.HasPastRecordings = lo.ToPtr(s.HasPastRecordings)
	t.IsLiveStreamEnabled = lo.ToPtr(s.IsLiveStreamEnabled)
	t.IsRecordingProcessed = lo.ToPtr(s.IsRecordingProcessed)
	metadataJson, _ := json.Marshal(s.OgMetadata)
	if len(metadataJson) > 0 {
		t.OgMetadata = lo.ToPtr(json.RawMessage(metadataJson))
	}
	t.PrimaryHostUserID = shared.FormatInt(s.PrimaryHostUserID)
	t.PublicURL = s.URL
	t.IsViewerAccessRestricted = s.IsViewerAccessRestricted
	t.DataVisibility = s.DataVisibility
	t.SessionCreatorUserID = shared.FormatInt(s.CreatorUserID.Int64)
	t.SessionID = shared.FormatInt(s.ID)
	t.SessionRecurrenceID = shared.FormatInt(s.RecurrenceID)
	settingsJson, _ := json.Marshal(s.Settings)
	if len(settingsJson) > 0 {
		t.SessionSettings = lo.ToPtr(json.RawMessage(settingsJson))
	}
	t.SessionState = s.State
	t.SessionTags = s.Tags
	t.SessionTitle = s.Title
	t.StateUpdatedAt = s.StateUpdatedAt
	t.UpdatedAt = int(s.UpdatedAt)
	t.Rowversion = s.RowVersion
	if s.UserMeetingType.Valid {
		t.UserMeetingType = &shared.UserMeetingTypeDTO{
			Description:    s.UMTDescription.String,
			Id:             shared.FormatInt(s.UserMeetingType.Int64),
			PromptTemplate: s.UMTPromptTemplate.String,
			Title:          s.UMTTitle.String,
		}
		if s.UMTCreatedBy.Valid {
			t.UserMeetingType.CreatedBy = lo.ToPtr(int(s.UMTCreatedBy.Int64))
		}
	}
	t.AccessStatus = shared.SessionDTOAccessStatus(s.AccessStatus)
	t.MeetingType = s.MeetingType

	return t
}
func PopulateHostsWithRelations(ctx context.Context, t *shared.SessionDTO) error {
	// TODO support multiple hosts
	host, err := GetUserByID(ctx, t.PrimaryHostUserID)
	if err != nil {
		return err
	}
	if host != nil {
		hostDTO := *host.ToUserDTO()
		if err = host.PopulateRelations(ctx, &hostDTO); err != nil {
			return err
		}
		t.Hosts = []shared.UserDTO{hostDTO}
	}
	return nil
}

func PopulateSessionPlan(ctx context.Context, optionalTx *sqlx.Tx, t *shared.SessionDTO) error {
	// When the session owner is part of a team, automatically upgrade them to premium
	host, foundHost := lo.Find(t.Hosts, func(item shared.UserDTO) bool {
		if item.Id == nil || t.PrimaryHostUserID == "" {
			return false
		}
		return *item.Id == t.PrimaryHostUserID
	})
	if foundHost && host.Team != nil {
		configRow, err := getPremiumPlan(ctx)
		if err != nil {
			return err
		}
		t.SubscriptionPlan = configRow.ToSessionSubscriptionDTO()
		return nil
	}
	if t.PrimaryHostUserID == "" {
		return nil
	}
	row := shared.SubscriptionPlanConfigRow{}
	usrArgs := database.Args{"userID": t.PrimaryHostUserID}
	var paddleCustomerID sql.NullString
	err := GetPaddleCustomerByUserID.InTx(optionalTx).GetValues(ctx, usrArgs, &paddleCustomerID)

	if err == nil && paddleCustomerID.Valid {
		args := database.Args{"customerID": paddleCustomerID.String}
		err = SubscriptionPlanConfigByCustomerID.InTx(optionalTx).GetStruct(ctx, args, &row)
	}
	if errors.Is(err, sql.ErrNoRows) {
		// When there's no active plan for the team/customer, defer to the user plan table
		err = SubscriptionPlanConfigByUserID.InTx(optionalTx).GetStruct(ctx, usrArgs, &row)
		if errors.Is(err, sql.ErrNoRows) {
			return nil // No active subscription plan
		}
	}
	if err != nil {
		return fmt.Errorf("error getting subscription plan config: %w", err)
	}
	t.SubscriptionPlan = row.ToSessionSubscriptionDTO()
	return nil
}

func (s SessionRow) ToSessionEventDTO() *shared.SessionEventDTO {
	var t shared.SessionEventDTO

	if s.About.Valid {
		t.About = s.About.String
	}
	t.AccessStatus = s.AccessStatus
	if s.Avatar.Valid {
		t.Avatar = s.Avatar.String
	}
	if s.BranchURL.Valid {
		t.BranchURL = s.BranchURL.String
	}
	if s.CalendarEventEditURL.Valid {
		t.CalendarEventEditURL = s.CalendarEventEditURL.String
	}
	if s.CalendarID.Valid {
		t.CalendarID = s.CalendarID.String
	}
	if s.CalendarType.Valid {
		t.CalendarType = s.CalendarType.String
	}
	if s.Category.Valid {
		t.Category = shared.FormatInt(s.Category.Int64)
	}
	t.CreatedAt = int(s.CreatedAt)
	if s.CreatorUserID.Valid {
		t.CreatorUserID = shared.FormatInt(s.CreatorUserID.Int64)
	}
	t.DataVisibility = string(s.DataVisibility)
	t.EnableBots = s.EnableBots
	if s.EndTimestamp.Valid {
		t.EndTimestamp = int(s.EndTimestamp.Int64)
	}
	t.HasPastRecordings = s.HasPastRecordings
	if len(s.HostSessionUserIDs) > 0 {
		t.HostSessionUserIDs = []string{}
		for _, v := range s.HostSessionUserIDs {
			if value := shared.FormatInt(v); value != "" {
				t.HostSessionUserIDs = append(t.HostSessionUserIDs, value)
			}
		}
	}
	if len(s.HostUserIDs) > 0 {
		t.HostUserIDs = []string{}
		for _, v := range s.HostUserIDs {
			if value := shared.FormatInt(v); value != "" {
				t.HostUserIDs = append(t.HostUserIDs, value)
			}
		}
	}
	t.ID = shared.FormatInt(s.ID)
	t.IsFeatured = s.IsFeatured
	t.IsLiveStreamEnabled = s.IsLiveStreamEnabled
	t.IsPubliclyVisible = s.IsPubliclyVisible
	t.IsRecordingProcessed = s.IsRecordingProcessed
	t.IsViewerAccessRestricted = s.IsViewerAccessRestricted
	t.MeetingType = string(s.MeetingType)
	s.OgMetadata.Scan(t.OGMetadata)
	t.PrimaryHostUserID = shared.FormatInt(s.PrimaryHostUserID)
	if len(s.ProducerSessionUserIDs) > 0 {
		t.ProducerSessionUserIDs = []string{}
		for i, v := range s.ProducerSessionUserIDs {
			t.ProducerSessionUserIDs[i] = shared.FormatInt(v)
		}
	}
	if len(s.ProducerUserIDs) > 0 {
		t.ProducerUserIDs = []string{}
		for i, v := range s.ProducerUserIDs {
			t.ProducerUserIDs[i] = shared.FormatInt(v)
		}
	}
	t.RecurrenceID = shared.FormatInt(s.RecurrenceID)
	t.Rowversion = s.RowVersion
	s.Settings.Scan(t.Settings)
	if s.StartTimestamp.Valid {
		t.StartTimestamp = int(s.StartTimestamp.Int64)
	}
	if s.StartedAt.Valid {
		t.StartedAt = int(s.StartedAt.Int64)
	}
	if s.EndedAt.Valid {
		t.EndedAt = int(s.EndedAt.Int64)
	}
	t.State = s.State
	t.StateUpdatedAt = s.StateUpdatedAt
	s.Tags.Scan(t.Tags)
	t.Title = s.Title
	t.UpdatedAt = int(s.UpdatedAt)
	t.URL = s.URL

	if s.UserMeetingType.Valid {
		t.UserMeetingType = shared.FormatInt(s.UserMeetingType.Int64)
	}

	return &t
}

type UserRow struct {
	ID                   int64                       `db:"id" copier:"-"`
	Email                sql.NullString              `db:"email"`
	FirstName            sql.NullString              `db:"firstName"`
	LastName             sql.NullString              `db:"lastName"`
	SignUpTimestamp      int64                       `db:"signUpTimestamp"`
	Fingerprint          pq.StringArray              `db:"fingerprint"`
	GoogleOauthMetadata  sql.NullString              `db:"googleOauthMetadata"`
	AppleID              sql.NullString              `db:"appleID"`
	GoogleID             sql.NullString              `db:"googleID"`
	RoleIDs              pq.StringArray              `db:"roleIDs"`
	About                sql.NullString              `db:"about"`
	Avatar               sql.NullString              `db:"avatar"`
	Social               shared.Socials              `db:"social"`
	Timezone             sql.NullString              `db:"timezone"`
	MarketingOptIn       bool                        `db:"marketingOptIn"`
	CreatedAt            int64                       `db:"createdAt"`
	UpdatedAt            int64                       `db:"updatedAt"`
	NotificationSettings shared.NotificationSettings `db:"notificationSettings"`
	IsTestUser           bool                        `db:"isTestUser"`
	PaddleCustomerID     sql.NullString              `db:"paddleCustomerID"`
	Onboarding           shared.OnboardingFlags      `db:"onboarding"`
	Lobbies              shared.LobbyDTOArray        `db:"lobbies"`
}

func (u *UserRow) ToUserDTO() *shared.UserDTO {
	var target shared.UserDTO
	copier.Copy(&target, u)
	target.Id = lo.ToPtr(shared.FormatInt(u.ID))
	if u.Email.Valid {
		target.Email = lo.ToPtr(u.Email.String)
	}
	if u.FirstName.Valid {
		target.FirstName = lo.ToPtr(u.FirstName.String)
	}
	if u.LastName.Valid {
		target.LastName = lo.ToPtr(u.LastName.String)
	}
	if u.Avatar.Valid {
		target.Avatar = lo.ToPtr(u.Avatar.String)
	}
	if u.About.Valid {
		target.About = lo.ToPtr(u.About.String)
	}
	if u.Timezone.Valid {
		target.Timezone = lo.ToPtr(u.Timezone.String)
	}
	target.Lobbies = nil
	if u.Lobbies.Valid {
		target.Lobbies = &u.Lobbies.Value
	}
	target.Onboarding = &u.Onboarding
	if u.PaddleCustomerID.Valid {
		target.CustomerID = lo.ToPtr(u.PaddleCustomerID.String)
	}

	return &target
}

var ErrGetUserSubscriptionPlanConfig = errors.New("error getting user subscription plan config")
var ErrGetUserSubscriptionPlan = errors.New("error getting user subscription plan")

// Populates user subscription plan
func (u UserRow) PopulatePlan(ctx context.Context, dest *shared.UserDTO) error {
	team, teamErr := GetTeamWithUserRoleByUserID(ctx, u.ID)
	if teamErr != nil && !errors.Is(teamErr, sql.ErrNoRows) {
		return fmt.Errorf("error getting team for user: %w", teamErr)
	}

	// When the session owner is part of a team, automatically upgrade the session to premium
	if team != nil {
		// User will NOT have a user subscription plan in this case so we need to create an empty user subscription plan DTO
		ts := time.Now().Unix()
		p := NewUserSubscriptionPlanDTO(0, u.ID, shared.Active, ts, ts)
		configRow, err := getPremiumPlan(ctx)
		if err != nil {
			return err
		}
		sessionPlan := configRow.ToSessionSubscriptionDTO()
		p.PlanConfig = &sessionPlan.PlanConfig
		p.PlanConfigOverrides = sessionPlan.PlanConfigOverrides
		dest.SubscriptionPlan = p
		return nil
	}

	row := UserSubscriptionPlan{}
	configRow := shared.SubscriptionPlanConfigRow{}
	// User is not part of a team
	var err = sql.ErrNoRows
	if u.PaddleCustomerID.Valid {
		args := database.Args{"customerID": u.PaddleCustomerID.String}
		err = SubscriptionPlanConfigByCustomerID.GetStruct(ctx, args, &configRow)
	}

	if errors.Is(err, sql.ErrNoRows) {
		usrArgs := database.Args{"userID": shared.FormatInt(u.ID)}
		// When there's no active plan for the team/customer, defer to the user plan table
		err = SubscriptionPlanConfigByUserID.GetStruct(ctx, usrArgs, &configRow)
		if errors.Is(err, sql.ErrNoRows) {
			return nil // No active subscription plan
		}
	}
	if err != nil {
		return fmt.Errorf("%w: %w", ErrGetUserSubscriptionPlanConfig, err)
	}
	err = GetUserSubscriptionPlan.GetStruct(ctx, database.Args{"planID": configRow.PlanID}, &row)
	if err != nil {
		return fmt.Errorf("%w: %w", ErrGetUserSubscriptionPlan, err)
	}
	p := row.ToUserSubscriptionPlanDTO()
	sessionPlan := configRow.ToSessionSubscriptionDTO()
	p.PlanConfig = &sessionPlan.PlanConfig
	p.PlanConfigOverrides = sessionPlan.PlanConfigOverrides
	dest.SubscriptionPlan = p
	return nil
}

func getPremiumPlan(ctx context.Context) (*shared.SubscriptionPlanConfigRow, error) {
	configRow := shared.SubscriptionPlanConfigRow{}
	// Get premium plan (subscription plan config)
	args := database.Args{"paddleProductName": "premium"}
	err := SubscriptionPlanConfigByPaddleProductName.GetStruct(ctx, args, &configRow)
	if err != nil {
		return nil, fmt.Errorf("%w: %w", ErrGetUserSubscriptionPlanConfig, err)
	}
	return &configRow, nil
}

// Populates all user relations
func (u UserRow) PopulateRelations(ctx context.Context, dest *shared.UserDTO) error {
	if err := u.PopulatePlan(ctx, dest); err != nil {
		return shared.LogAndWrapError("failed to populate user plan", err, "userID", u.ID)
	}

	if err := u.PopulateTeam(ctx, dest); err != nil {
		return shared.LogAndWrapError("failed to populate user team", err, "userID", u.ID)
	}

	return nil
}

// Populates the team information if user is member of a team
func (u UserRow) PopulateTeam(ctx context.Context, dest *shared.UserDTO) error {
	// Get team with user role
	teamWithUserRole, err := GetTeamWithUserRoleByUserID(ctx, u.ID)
	if errors.Is(err, sql.ErrNoRows) {
		return nil
	}
	if err != nil {
		return shared.LogAndWrapError("error getting team with user role", err, "userID", u.ID)
	}

	teamID := shared.FormatInt(teamWithUserRole.ID)

	teamMembersCount, err := CountTeamMembersByTeamID(ctx, &CountTeamMembersByTeamIDRequest{
		TeamID: teamID,
	})
	if err != nil {
		return shared.LogAndWrapError("error getting team members count", err, "teamID", teamID)
	}

	team := shared.TeamWithInlineRelationsDTO{
		ID:           teamID,
		Name:         teamWithUserRole.Name,
		Domains:      teamWithUserRole.Domains,
		CreatedAt:    teamWithUserRole.CreatedAt,
		UpdatedAt:    teamWithUserRole.UpdatedAt,
		MembersCount: &teamMembersCount,
		Role:         &teamWithUserRole.Role,
	}

	dest.Team = &team
	return nil
}

var Statements = database.Statements{}

type UserSubscriptionPlan struct {
	PlanConfigID         int64             `db:"planConfigID"`
	PlanID               int64             `db:"planID"`
	PaddleSubscriptionID sql.NullString    `db:"paddleSubscriptionID"`
	UserID               int64             `db:"userID"`
	PlanConfigOverrides  database.JSONBRaw `db:"planConfigOverrides"`
	TrialEndsAt          sql.NullInt64     `db:"trialEndsAt"`
	Status               string            `db:"status"`
	CustomerID           sql.NullString    `db:"customerID"`
	AddressID            sql.NullString    `db:"addressID"`
	BusinessID           sql.NullString    `db:"businessID"`
	CurrencyCode         sql.NullString    `db:"currencyCode"`
	StartedAt            sql.NullString    `db:"startedAt"`
	FirstBilledAt        sql.NullString    `db:"firstBilledAt"`
	NextBilledAt         sql.NullString    `db:"nextBilledAt"`
	PausedAt             sql.NullString    `db:"pausedAt"`
	CanceledAt           sql.NullString    `db:"canceledAt"`
	CollectionMode       sql.NullString    `db:"collectionMode"`
	BillingDetails       database.JSONBRaw `db:"billingDetails"`
	CurrentBillingPeriod database.JSONBRaw `db:"currentBillingPeriod"`
	BillingCycle         database.JSONBRaw `db:"billingCycle"`
	ScheduledChange      database.JSONBRaw `db:"scheduledChange"`
	Items                database.JSONBRaw `db:"items"`
	CustomData           database.JSONBRaw `db:"customData"`
	CreatedAt            int64             `db:"createdAt"`
	UpdatedAt            int64             `db:"updatedAt"`
}

func NewUserSubscriptionPlanDTO(id, userID int64, status shared.UserSubscriptionPlanDTOStatus, createdAt, updatedAt int64) *shared.UserSubscriptionPlanDTO {
	return &shared.UserSubscriptionPlanDTO{
		Id:        shared.FormatInt(id),
		UserID:    shared.FormatInt(userID),
		Status:    status,
		CreatedAt: createdAt,
		UpdatedAt: updatedAt,
	}
}

func (row UserSubscriptionPlan) ToUserSubscriptionPlanDTO() *shared.UserSubscriptionPlanDTO {
	p := NewUserSubscriptionPlanDTO(row.PlanID, row.UserID, shared.UserSubscriptionPlanDTOStatus(row.Status), row.CreatedAt, row.UpdatedAt)

	if len(row.PlanConfigOverrides) > 0 {
		p.PlanConfigOverrides = lo.ToPtr(json.RawMessage(row.PlanConfigOverrides))
	}
	if row.PaddleSubscriptionID.Valid {
		p.PaddleSubscriptionID = &row.PaddleSubscriptionID.String
	}
	if row.CustomerID.Valid {
		p.CustomerID = &row.CustomerID.String
	}
	if row.AddressID.Valid {
		p.AddressID = &row.AddressID.String
	}
	if row.BusinessID.Valid {
		p.BusinessID = &row.BusinessID.String
	}
	if row.CurrencyCode.Valid {
		p.CurrencyCode = &row.CurrencyCode.String
	}
	if row.StartedAt.Valid {
		p.StartedAt = &row.StartedAt.String
	}
	if row.FirstBilledAt.Valid {
		p.FirstBilledAt = &row.FirstBilledAt.String
	}
	if row.NextBilledAt.Valid {
		p.NextBilledAt = &row.NextBilledAt.String
	}
	if row.PausedAt.Valid {
		p.PausedAt = &row.PausedAt.String
	}
	if row.CanceledAt.Valid {
		p.CanceledAt = &row.CanceledAt.String
	}
	if row.CollectionMode.Valid {
		p.CollectionMode = lo.ToPtr(shared.UserSubscriptionPlanDTOCollectionMode(row.CollectionMode.String))
	}
	json.Unmarshal(row.BillingDetails, &p.BillingDetails)
	json.Unmarshal(row.CurrentBillingPeriod, &p.CurrentBillingPeriod)
	json.Unmarshal(row.BillingCycle, &p.BillingCycle)
	json.Unmarshal(row.ScheduledChange, &p.ScheduledChange)
	json.Unmarshal(row.Items, &p.Items)
	json.Unmarshal(row.CustomData, &p.CustomData)

	return p
}

type UserSessionRow struct {
	SessionUserID         int64          `db:"sessionUserID"`
	Joined                bool           `db:"joined"`
	IsViewerAccessRevoked bool           `db:"isViewerAccessRevoked"`
	RoleID                pq.StringArray `db:"roleIDs"`
	UserID                sql.NullInt64  `db:"userID"`
	Email                 sql.NullString `db:"email"`
	FirstName             sql.NullString `db:"firstName"`
	LastName              sql.NullString `db:"lastName"`
	Avatar                sql.NullString `db:"avatar"`
	GuestSurrogateID      sql.NullInt64  `db:"guestSurrogateID"`
	GuestFullName         sql.NullString `db:"guestFullName"`
	GuestEmail            sql.NullString `db:"guestEmail"`
}

func (r UserSessionRow) ToSessionUserDTO() api.SessionUserDTO {
	user := api.SessionUserDTO{}
	user.SessionUserID = shared.FormatInt(r.SessionUserID)
	user.Joined = r.Joined
	user.IsViewerAccessRevoked = r.IsViewerAccessRevoked
	user.RoleIDs = r.RoleID
	if r.UserID.Valid {
		user.User = &api.SessionUser{
			UserID:    shared.FormatInt(r.UserID.Int64),
			Email:     r.Email.String,
			FirstName: r.FirstName.String,
			LastName:  r.LastName.String,
			Avatar:    r.Avatar.String,
		}
	}
	if r.GuestSurrogateID.Valid {
		user.Guest = &api.SessionGuest{
			SurrogateID: shared.FormatInt(r.GuestSurrogateID.Int64),
			FullName:    r.GuestFullName.String,
			Email:       r.GuestEmail.String,
		}
	}
	return user
}

// MeetingSuggestionRow represents a meeting suggestion record in the database
type MeetingSuggestionRow struct {
	Id                  int64  `db:"id"`
	SessionId           string `db:"session_id"`
	SessionRecurrenceId string `db:"session_recurrence_id"`
	UserId              string `db:"user_id"`
	Category            string `db:"category"`
	Prompt              string `db:"prompt"`
	CreatedAt           int64  `db:"created_at"`
	UpdatedAt           int64  `db:"updated_at"`
}

// ToMeetingSuggestionDTO converts the row to a DTO
func (r MeetingSuggestionRow) ToMeetingSuggestionDTO() api.MeetingSuggestionDTO {
	return api.MeetingSuggestionDTO{
		Id:                  shared.FormatInt(r.Id),
		SessionId:           r.SessionId,
		SessionRecurrenceId: r.SessionRecurrenceId,
		UserId:              r.UserId,
		Category:            r.Category,
		Prompt:              r.Prompt,
		CreatedAt:           r.CreatedAt,
		UpdatedAt:           r.UpdatedAt,
	}
}
