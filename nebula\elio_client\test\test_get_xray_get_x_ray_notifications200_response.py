# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_xray_get_x_ray_notifications200_response import GETXrayGetXRayNotifications200Response

class TestGETXrayGetXRayNotifications200Response(unittest.TestCase):
    """GETXrayGetXRayNotifications200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETXrayGetXRayNotifications200Response:
        """Test GETXrayGetXRayNotifications200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETXrayGetXRayNotifications200Response`
        """
        model = GETXrayGetXRayNotifications200Response()
        if include_optional:
            return GETXrayGetXRayNotifications200Response(
                data = elio_client.models.api/get_x_ray_notifications_response_data.api.GetXRayNotificationsResponseData(
                    has_more = True, 
                    notifications = [
                        elio_client.models.api/x_ray_notification_dto.api.XRayNotificationDTO(
                            content = '', 
                            created_at = 56, 
                            id = 56, 
                            seen = True, 
                            source = elio_client.models.source.source(), 
                            updated_at = 56, 
                            user_id = 56, 
                            xray_doc_commit_id = 56, )
                        ], 
                    total_count = 56, ),
                message = '',
                success = True
            )
        else:
            return GETXrayGetXRayNotifications200Response(
                data = elio_client.models.api/get_x_ray_notifications_response_data.api.GetXRayNotificationsResponseData(
                    has_more = True, 
                    notifications = [
                        elio_client.models.api/x_ray_notification_dto.api.XRayNotificationDTO(
                            content = '', 
                            created_at = 56, 
                            id = 56, 
                            seen = True, 
                            source = elio_client.models.source.source(), 
                            updated_at = 56, 
                            user_id = 56, 
                            xray_doc_commit_id = 56, )
                        ], 
                    total_count = 56, ),
                message = '',
                success = True,
        )
        """

    def testGETXrayGetXRayNotifications200Response(self):
        """Test GETXrayGetXRayNotifications200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
