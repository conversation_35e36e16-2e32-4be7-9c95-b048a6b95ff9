# POSTXrayGenerateXRayInfoRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**prompt** | **str** |  | 
**type** | **str** |  | 

## Example

```python
from elio_client.models.post_xray_generate_x_ray_info_request import POSTXrayGenerateXRayInfoRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTXrayGenerateXRayInfoRequest from a JSON string
post_xray_generate_x_ray_info_request_instance = POSTXrayGenerateXRayInfoRequest.from_json(json)
# print the JSON string representation of the object
print(POSTXrayGenerateXRayInfoRequest.to_json())

# convert the object into a dict
post_xray_generate_x_ray_info_request_dict = post_xray_generate_x_ray_info_request_instance.to_dict()
# create an instance of POSTXrayGenerateXRayInfoRequest from a dict
post_xray_generate_x_ray_info_request_from_dict = POSTXrayGenerateXRayInfoRequest.from_dict(post_xray_generate_x_ray_info_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


