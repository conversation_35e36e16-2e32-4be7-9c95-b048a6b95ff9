package hubble

import (
	"context"
	"encoding/json"
	"encore.app/hubble/database"
	"errors"
	"fmt"
	"github.com/samber/lo"
	"net/http"
	"strconv"
	"strings"
	"time"

	"encore.app/hubble/api"
	hubble_api "encore.app/hubble/gen"
	"encore.app/hubble/models"
	"encore.app/hubble/repositories"
	"encore.app/hubble/services"
	"encore.app/hubble/utils"
	"encore.app/shared"
	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"encore.dev/storage/cache"
)

//encore:api auth method=GET path=/v1.0/auth/is-authorized-feature tag:trixta
func (hubble *Hubble) IsAuthorizedToSessionFeature(c context.Context, params api.AuthControllerIsAuthorizedToSessionFeatureRequest) (*api.AuthControllerIsAuthorizedToSessionFeatureResponse, error) {
	r, err := hubble.MarsService.MarsGetSession(c, params.SessionID)
	if err != nil || r == nil || r.Session == nil {
		return nil, shared.HttpResponseError(utils.HttpMessages[utils.SessionNotFound], utils.SessionNotFound, http.StatusNotFound)
	}

	session := r.Session

	if session.SubscriptionPlan == nil {
		return nil, shared.HttpResponseError("session subscription plan not found", utils.InternalServerError, http.StatusInternalServerError)
	}

	authorized := false

	switch hubble_api.AuthControllerIsAuthorizedToSessionFeatureParamsFeature(params.Feature) {
	case hubble_api.QueueMode:
		if session.SubscriptionPlan.PlanConfig.QueueMode.Enabled {
			authorized = true
		}
	case hubble_api.AiFeed:
		if session.SubscriptionPlan.PlanConfig.AiFeed.Enabled {
			authorized = true
		}
	case hubble_api.Recording:
		if session.SubscriptionPlan.PlanConfig.Recording.Enabled {
			authorized = true
		}
	case hubble_api.OffTheRecord:
		if session.SubscriptionPlan.PlanConfig.OffTheRecord.Enabled {
			authorized = true
		}
	case hubble_api.MeetingSummary:
		if session.SubscriptionPlan.PlanConfig.MeetingSummary.Enabled {
			authorized = true
		}
	case hubble_api.MeetingMemory:
		if session.SubscriptionPlan.PlanConfig.MeetingMemory.Enabled {
			authorized = true
		}
	case hubble_api.MeetingTemplates:
		if session.SubscriptionPlan.PlanConfig.MeetingTemplates.Enabled {
			authorized = true
		}
	case hubble_api.MeetingWorkflows:
		if session.SubscriptionPlan.PlanConfig.MeetingWorkflows.Enabled {
			authorized = true
		}
	case hubble_api.Stream:
		if session.SubscriptionPlan.PlanConfig.Stream.Enabled {
			authorized = true
		}
	case hubble_api.ModelSegregation:
		if session.SubscriptionPlan.PlanConfig.ModelSegregation.Enabled {
			authorized = true
		}
	case hubble_api.CustomFeedItems:
		if session.SubscriptionPlan.PlanConfig.CustomFeedItems.Enabled {
			authorized = true
		}
	case hubble_api.CustomIntegrations:
		if session.SubscriptionPlan.PlanConfig.CustomIntegrations.Enabled {
			authorized = true
		}
	case hubble_api.Meetings:
		if session.SubscriptionPlan.PlanConfig.Meetings.Enabled {
			authorized = true
		}
	}

	planJson, _ := json.Marshal(session.SubscriptionPlan)
	planJsonRaw := &json.RawMessage{}
	err = planJsonRaw.UnmarshalJSON(planJson)
	if err != nil {
		planJsonRaw = nil
	}

	response := api.AuthControllerIsAuthorizedToSessionFeatureResponse{}
	response.Data.IsAuthorized = authorized
	response.Data.SubscriptionPlan = planJsonRaw

	return &response, nil
}

//encore:api private method=GET path=/v1.0/auth/is-authorized-integration
func (hubble *Hubble) IsAuthorizedToIntegration(c context.Context, params api.AuthControllerIsAuthorizedToIntegrationRequest) (*api.AuthControllerIsAuthorizedToIntegrationResponse, error) {

	user, err := hubble.MarsService.MarsGetUser(c, params.UserID)

	if err != nil || user == nil {
		return nil, shared.HttpResponseError(utils.HttpMessages[utils.UserNotFound], utils.UserNotFound, http.StatusNotFound)
	}

	var authorized bool

	if !user.SubscriptionPlan.PlanConfig.Integrations.Enabled {
		authorized = false
	} else {
		// Apps being nil means all integrations are allowed on a plan
		if user.SubscriptionPlan.PlanConfig.Integrations.Apps == nil {
			authorized = true
		} else {
			apps := *user.SubscriptionPlan.PlanConfig.Integrations.Apps

			for _, providerName := range apps {
				if providerName == params.ProviderName {
					authorized = true
				}
			}
		}
	}

	planJson, _ := json.Marshal(user.SubscriptionPlan)
	planJsonRaw := &json.RawMessage{}
	err = planJsonRaw.UnmarshalJSON(planJson)
	if err != nil {
		planJsonRaw = nil
	}

	response := api.AuthControllerIsAuthorizedToIntegrationResponse{}
	response.Data.IsAuthorized = authorized
	response.Data.SubscriptionPlan = planJsonRaw

	return &response, nil
}

//encore:api private method=GET path=/v1.0/auth/user
func (hubble *Hubble) UserByToken(ctx context.Context, params *api.GetUserByTokenRequest) (*api.GetUserByTokenResponse, error) {
	tokenSplit := strings.Fields(params.Token)
	if len(tokenSplit) != 2 || strings.ToLower(tokenSplit[0]) != "bearer" {
		return nil, errs.WrapCode(errors.New("invalid token"), errs.Unauthenticated, "invalid token")
	}
	claims, err := hubble.AuthenticationService.VerifyAuthToken(ctx, tokenSplit[1])
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, "error VerifyAuthToken", utils.ValidationError, http.StatusUnauthorized)
	}

	if claims.UserID == "" {
		return nil, shared.B().WithCode(shared.Unauthorized)
	}
	user, err := hubble.AuthUserRepository.GetAuthUserByID(ctx, shared.ParseInt(claims.UserID))
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, "failed to get auth user details", utils.Unauthorized, http.StatusForbidden)
	}
	if user == nil {
		rlog.Info("User not found", "userID", claims.UserID)
		return nil, shared.LogErrorHttpResponse(fmt.Errorf("user not found in db"), "failed to get auth user details", utils.Unauthorized, http.StatusForbidden)
	}

	response := &api.GetUserByTokenResponse{}
	mapperUser := services.MapAuthUserDTO(*user)
	response.Data.AuthUser = &mapperUser
	return response, nil
}

func (hubble *Hubble) enforceSubjects(subs []string, obj string, act shared.AuthPolicyActions) (bool, error) {
	for _, sub := range subs {
		authed, err := hubble.Enforcer.Enforce(sub, obj, string(act))
		if err != nil {
			return false, err
		}
		if authed {
			return true, nil
		}
	}
	return false, nil
}

//encore:api auth method=POST path=/v1.0/auth/user tag:trixta
func (hubble *Hubble) UpsertAuthUsers(ctx context.Context, params *api.CreateAuthUsersRequest) (*api.CreateAuthUsersResponse, error) {
	upserted := []models.AuthUser{}

	for _, user := range params.Users {
		userID, err := strconv.ParseInt(user.ID, 10, 64)
		if err != nil {
			msg := "failed to parse user.ID as int64"
			rlog.Error(msg, "userID", user.ID, "email", user.Email, "err", err)
			return nil, shared.HttpResponseError(msg, shared.InternalServerError, http.StatusInternalServerError)
		}

		authUser, err := hubble.AuthUserRepository.GetAuthUserByID(ctx, userID)
		if err != nil {
			msg := "failed to get auth user by id"
			rlog.Error(msg, "userID", user.ID, "email", user.Email, "err", err)
			return nil, shared.HttpResponseError(msg, shared.InternalServerError, http.StatusInternalServerError)
		}

		if authUser == nil {
			// Create
			authUser, err := hubble.AuthUserRepository.CreateAuthUser(ctx, &models.User{
				ID:          userID,
				Email:       user.Email,
				RoleIDs:     user.RoleIDs,
				TeamID:      user.TeamID,
				TeamRoleIDs: user.TeamRoleIDs,
			})

			if err != nil && shared.IsSQLErr(err, shared.SQLUniqueViolation) {
				rlog.Info("auth user already exists. skipping...", "userID", user.ID, "email", user.Email)
				continue
			}

			if err != nil {
				msg := "failed to create auth user"
				rlog.Error(msg, "userID", user.ID, "email", user.Email, "err", err)
				return nil, shared.HttpResponseError(msg, shared.InternalServerError, http.StatusInternalServerError)
			}

			if authUser == nil {
				msg := "Auth user must not be nil"
				rlog.Error(msg)
				return nil, errors.New(msg)
			}

			upserted = append(upserted, *authUser)
			rlog.Info("auth user successfully created", "authUser", authUser)
		} else {
			// Update
			authUser, err = hubble.AuthUserRepository.UpdateAuthUserByID(ctx, authUser.ID, models.AuthUser{
				ID:          userID,
				Email:       user.Email,
				RoleIDs:     user.RoleIDs,
				TeamID:      user.TeamID,
				TeamRoleIDs: user.TeamRoleIDs,
			})
			if err != nil {
				msg := "failed to update auth user"
				rlog.Error(msg, "userID", user.ID, "email", user.Email, "err", err)
				return nil, shared.HttpResponseError(msg, shared.InternalServerError, http.StatusInternalServerError)
			}

			if authUser == nil {
				msg := "Auth user must not be nil"
				rlog.Error(msg)
				return nil, errors.New(msg)
			}
			upserted = append(upserted, *authUser)
			rlog.Info("auth user already exists", "authUser", authUser)
		}
	}

	response := &api.CreateAuthUsersResponse{}
	response.Success = true
	response.Message = "auth users successfully created"
	response.AuthUsers = upserted
	return response, nil
}

//encore:api auth method=PATCH path=/v1.0/auth/user tag:trixta
func (hubble *Hubble) UpdateAuthUser(ctx context.Context, params *api.UpdateAuthUserRequest) (*api.UpdateAuthUsersResponse, error) {
	userID, err := strconv.ParseInt(params.ID, 10, 64)
	if err != nil {
		msg := "failed to parse user.ID as int64"
		rlog.Error(msg, "userID", params.ID, "email", "err", err)
		return nil, shared.HttpResponseError(msg, shared.InternalServerError, http.StatusInternalServerError)
	}

	updatePayload := models.AuthUser{}
	if params.Email != nil {
		updatePayload.Email = *params.Email
	}

	if params.RoleIDs != nil {
		updatePayload.RoleIDs = *params.RoleIDs
	}

	if params.TeamID != nil {
		updatePayload.TeamID = params.TeamID
	}

	if params.TeamRoleIDs != nil {
		updatePayload.TeamRoleIDs = *params.TeamRoleIDs
	}

	authUser, err := hubble.AuthUserRepository.UpdateAuthUserByID(ctx, userID, updatePayload)
	if err != nil {
		msg := "failed to update auth user"
		rlog.Error(msg, "userID", params.ID, "email", "err", err)
		return nil, shared.HttpResponseError(msg, shared.InternalServerError, http.StatusInternalServerError)
	}

	rlog.Info("auth user successfully updated", "authUser", authUser)

	invalidated, err := hubble.AuthenticationService.InvalidateAllTokensByUserID(params.ID)
	if err != nil {
		rlog.Error("failed to invalidate all tokens by user id", "err", err)
		return nil, err
	}

	rlog.Info("successfully invalidated all tokens by user id", invalidated)

	response := &api.UpdateAuthUsersResponse{}
	response.Success = true
	response.Message = "auth user successfully updated"
	return response, nil
}

// IsAuthorized checks if the user is authorized to perform a specified action.
// If the request is related to a session (i.e., the object is a session),
// and the initial authorization check fails, the function will attempt to
// populate session-specific policies, invalidate the cache, and then retry
// the authorization check.
//
//encore:api private method=POST path=/v1.0/auth/is-authorized
func (hubble *Hubble) IsAuthorized(ctx context.Context, params *api.IsAuthorizedRequest) error {
	b := shared.B().Meta("sub", strings.Join(params.Sub, ","), "obj", params.Obj, "act", string(params.Act))
	if len(params.Sub) == 0 {
		return b.WithCode(shared.Unauthorized)
	}
	authed, err := hubble.enforceSubjects(params.Sub, params.Obj, params.Act)
	if err != nil {
		return b.Cause(err).Msg("failed to enforce policy").Err()
	}
	// If auth is true, short-circuit
	if authed {
		return nil
	}

	isSessionObject, sessionID := IsSessionObject(params.Obj)

	if isSessionObject {
		err = PopulateSessionPolicies(ctx, &api.PopulateSessionPoliciesRequest{
			SessionID:    sessionID,
			RecurrenceID: RecurrenceIDFromObjectString(params.Obj),
		})
		if err != nil {
			return b.Cause(err).Msg("failed to populate session policies").Err()
		}

		if authed, err = hubble.enforceSubjects(params.Sub, params.Obj, params.Act); err != nil {
			return b.Cause(err).Msg("failed to enforce policy").Err()
		}
		if authed {
			return nil
		}
	}

	return b.WithCode(shared.Unauthorized)
}

//encore:api private method=POST path=/v1.0/auth/is-authorized-batch
func (hubble *Hubble) IsAuthorizedBatch(ctx context.Context, params *api.IsAuthorizedBatchRequest) (*api.IsAuthorizedBatchResponse, error) {
	sub := params.Sub
	if !strings.Contains(params.Sub, "/") {
		sub = "user/id/" + params.Sub
	}

	var batchRequests [][]interface{}
	for _, obj := range params.Objs {
		i := make([]interface{}, 3)
		i[0] = sub
		i[1] = obj
		i[2] = params.Act
		batchRequests = append(batchRequests, i)
	}
	isAuthorizedResult, err := hubble.Enforcer.BatchEnforce(batchRequests)
	if err != nil {
		rlog.Error("failed to parse userID", "err", err, "userID", params.Sub)
		return nil, shared.HttpResponseError("internal service error", utils.InternalServerError, http.StatusInternalServerError)
	}

	response := &api.IsAuthorizedBatchResponse{}
	for i, req := range batchRequests {
		response.Data = append(response.Data, shared.IsAuthorizedBatchResponseDTO{
			Sub:          req[0].(string),
			Obj:          req[1].(string),
			Act:          req[2].(string),
			IsAuthorized: isAuthorizedResult[i],
		})
	}

	return response, nil
}

//encore:api public method=POST path=/v1.0/auth/request-otp
func (hubble *Hubble) RequestOTP(ctx context.Context, request *api.OTPRequest) error {
	err := utils.ErrorOnExceededRateLimitOtpRequests(request.Email, request.IP, time.Now().Unix())
	if err != nil {
		return shared.HttpResponseError("rate limit exceeded", utils.ValidationError, http.StatusTooManyRequests)
	}

	if IsEmailBlacklisted(ctx, request.Email) {
		return shared.HttpResponseError("invalid email address", utils.ValidationError, http.StatusBadRequest)
	}

	authUser, err := hubble.AuthUserRepository.GetAuthUserByEmail(ctx, request.Email)
	if err != nil {
		return shared.LogErrorHttpResponse(err, "failed to get auth user details", utils.InternalServerError, http.StatusInternalServerError)
	}

	if !request.IsSignup && authUser == nil {
		rlog.Info("User not found")
		return shared.HttpResponseError("user not found", utils.UserNotFound, http.StatusNotFound)
	}

	if request.ClientID != "" {
		client, err := hubble.OAuthRepository.GetByID(ctx, request.ClientID)
		if err != nil {
			return shared.LogErrorHttpResponse(err, "failed to get client details", utils.InternalServerError, http.StatusInternalServerError)
		}
		if client == nil {
			return shared.HttpResponseError("client not found", utils.Unauthorized, http.StatusUnauthorized)
		}
		if !client.IsRedirectURIValid(request.RedirectUri) {
			rlog.Error("Invalid redirect uri", "clientID", request.ClientID, "redirectUri", request.RedirectUri)
			return shared.HttpResponseError("invalid redirect uri", utils.ValidationError, http.StatusBadRequest)
		}
	}

	if authUser != nil && authUser.DeletedAt.Valid {
		rlog.Info("User is deleted but trying to request token", "email", request.Email, "userID", authUser.ID)
		// Pretend we sent the email to avoid leaking user existence
		utils.ResetOtpVerifyAttempts(request.Email)
		return nil
	}

	otp := ""
	if request.IsSignup && authUser == nil {
		otp, err = hubble.AuthenticationService.GenerateSignupEmailVerificationOTP(ctx, services.GenerateSignUpEmailVerificationOTPParams{
			Email:          request.Email,
			FirstName:      *request.FirstName,
			LastName:       *request.LastName,
			Timezone:       request.Timezone,
			MarketingOptIn: request.MarketingOptIn,
			Fingerprint:    request.Fingerprint,
		}, request.ClientID, request.RedirectUri)
		if err != nil {
			return shared.LogErrorHttpResponse(err, "failed to generate signup email verification otp", utils.InternalServerError, http.StatusInternalServerError)
		}
	} else {
		otp, err = hubble.AuthenticationService.GenerateLoginEmailVerificationOTP(ctx, request.Email, request.ClientID, request.RedirectUri)
		if err != nil {
			return shared.LogErrorHttpResponse(err, "failed to generate login email verification otp", utils.InternalServerError, http.StatusInternalServerError)
		}
	}

	utils.ResetOtpVerifyAttempts(request.Email)

	/**
	 * Send mail in background
	 */
	go func() {
		err = hubble.EmailService.TriggerAuthEmail(secrets.PostmarkServerToken, request.Email, otp, request.IsSignup)
		if err != nil {
			rlog.Error("Error sending otp postmark email", "err", err)
		}
	}()

	return nil
}

//encore:api public method=POST path=/v1.0/auth/verify-otp
func (hubble *Hubble) VerifyEmailOTP(ctx context.Context, request *api.VerifyOTPRequest) (*api.VerifyOTPResponse, error) {
	subscribeUserOnMailjet := false

	err := utils.ErrorOnExceededRateLimitOtpVerify(request.Email, request.OTP, request.IP)
	if err != nil {
		return nil, shared.HttpResponseError("rate limit exceeded", utils.ValidationError, http.StatusTooManyRequests)
	}

	authenticationData, err := hubble.AuthenticationService.VerifyOTPForEmailVerification(ctx, request.Email, request.OTP, request.ClientID)
	if err != nil {
		errMsg := err.Error()
		if errMsg == "OTP not found" {
			utils.IncrementOtpVerifyAttempts(request.Email)
			return nil, shared.HttpResponseError(err.Error(), utils.OTPExpired, http.StatusUnavailableForLegalReasons)
		}
		if errMsg == "invalid OTP" {
			utils.IncrementOtpVerifyAttempts(request.Email)
			return nil, shared.HttpResponseError(err.Error(), utils.InvalidOTP, http.StatusMethodNotAllowed)
		}

		return nil, shared.LogErrorHttpResponse(err, "failed to verify otp", utils.InternalServerError, http.StatusBadRequest)
	}

	authUser, err := hubble.AuthUserRepository.GetAuthUserByEmail(ctx, request.Email)
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, "failed to get auth user details", utils.InternalServerError, http.StatusInternalServerError)
	}

	// If signup is false i.e logging in and auth user doesn't exists then throw error
	if !authenticationData.IsSignUp && authUser == nil {
		return nil, shared.HttpResponseError(utils.HttpMessages[utils.UserNotFound], utils.UserNotFound, http.StatusNotFound)
	}

	// If signup is true and auth user doesn't exists then register user on mars and create auth user for newly created user
	if authenticationData.IsSignUp && authUser == nil {
		newUserIDPtr, err := database.InsertUser(ctx, database.InsertUserRequest{
			Email:           request.Email,
			FirstName:       authenticationData.FirstName,
			LastName:        lo.ToPtr(authenticationData.LastName),
			MarketingOptIn:  lo.FromPtr(authenticationData.MarketingOptIn),
			Timezone:        authenticationData.Timezone,
			SignUpTimestamp: time.Now().Unix(),
		})
		if err != nil {
			return nil, shared.LogErrorHttpResponse(err, "failed to create user", utils.InternalServerError, http.StatusInternalServerError)
		}
		newUserID := *newUserIDPtr

		authUser, err = hubble.AuthUserRepository.CreateAuthUser(ctx, &models.User{
			ID:          newUserID,
			Email:       request.Email,
			TeamID:      nil,
			TeamRoleIDs: []string{},
		})
		if err != nil {
			return nil, shared.LogErrorHttpResponse(err, "failed to create auth user", utils.InternalServerError, http.StatusInternalServerError)
		}

		if *authenticationData.MarketingOptIn {
			subscribeUserOnMailjet = true
		}
	}

	userID := strconv.FormatInt(authUser.ID, 10)
	authenticationToken, refreshToken, err := hubble.AuthenticationService.GenerateAuthRefreshPair(authUser)
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, err.Error(), utils.InternalServerError, http.StatusInternalServerError)
	}

	utils.ResetAllForEmail(request.Email)

	response := &api.VerifyOTPResponse{}
	response.Data.AuthToken = authenticationToken
	response.Data.RefreshToken = refreshToken
	response.Data.UserID = userID
	response.Data.RedirectURI = authenticationData.RedirectUri

	if subscribeUserOnMailjet {
		go func() {
			backgroundCtx := context.Background()
			utils.SubscribeUserOnMailjet(backgroundCtx, Config.Mailjet, secrets.MailjetAPIKey, secrets.MailjetAPISecret, authUser.Email)
		}()
	}

	return response, nil
}

//encore:api public method=PUT path=/v1.0/auth/refresh-auth-token
func (hubble *Hubble) RefreshAuthToken(ctx context.Context, request *api.RefreshTokenRequest) (*api.RefreshTokenResponse, error) {
	/**
	 * Verify refresh token
	 */
	decodedRefreshToken, err := hubble.AuthenticationService.VerifyRefreshToken(ctx, request.RefreshToken)
	// Give the user a chance to use the cached response if the token is invalidated (multiple tabs requesting at the same time bug)
	if errors.Is(err, services.InvalidatedTokenErr) && decodedRefreshToken != nil {
		if cached, err := CachedRefreshResponse.Get(ctx, repositories.TokenIDKey{TokenID: decodedRefreshToken.ID}); err == nil && cached.Data.AuthToken != "" {
			return &cached, nil
		}
	}
	if err != nil {
		tokenID := ""
		userID := ""
		if decodedRefreshToken != nil {
			tokenID = decodedRefreshToken.ID
			userID = decodedRefreshToken.UserID
		}
		rlog.Error("failed to verify refresh token", "err", err, "tokenID", tokenID, "userID", userID)
		return nil, shared.HttpResponseError(err.Error(), utils.Unauthorized, http.StatusUnauthorized)
	}
	if decodedRefreshToken == nil {
		return nil, shared.HttpResponseError("invalid refresh token", utils.Unauthorized, http.StatusUnauthorized)
	}
	if err = CachedRefreshResponse.SetIfNotExists(ctx, repositories.TokenIDKey{TokenID: decodedRefreshToken.ID}, api.RefreshTokenResponse{}); err != nil {
		if !errors.Is(err, cache.KeyExists) {
			return nil, err
		}
		<-time.After(200 * time.Millisecond) // Give the other request a chance to set the cache
		if cached, err := CachedRefreshResponse.Get(ctx, repositories.TokenIDKey{TokenID: decodedRefreshToken.ID}); err == nil && cached.Data.AuthToken != "" {
			return &cached, nil
		}
	}

	userID := decodedRefreshToken.UserID
	wasSuccess := false

	defer func(skip *bool) {
		if !*skip {
			CachedRefreshResponse.Delete(ctx, repositories.TokenIDKey{TokenID: decodedRefreshToken.ID})
		}
	}(&wasSuccess)

	authenticationToken := ""
	refreshToken := ""
	if decodedRefreshToken.GuestSurrogateID != 0 {
		// Refresh Guest token
		builder := utils.NewJWTTokenBuilder(secrets.JWTSecret, secrets.JWTRefreshSecret)
		builder.GuestToken(decodedRefreshToken.GuestSurrogateID, decodedRefreshToken.GuestFullName, decodedRefreshToken.GuestEmail).NewTokenID()
		authenticationToken, refreshToken, err = builder.Build()
		if err != nil {
			return nil, shared.LogErrorHttpResponse(err, err.Error(), utils.InternalServerError, http.StatusInternalServerError)
		}
	}
	if decodedRefreshToken.UserID != "" {
		// User existence check
		authUserID, err := strconv.ParseInt(userID, 10, 64)
		if err != nil {
			return nil, shared.HttpResponseError(err.Error(), utils.InternalServerError, http.StatusInternalServerError)
		}
		authUser, err := hubble.AuthUserRepository.GetAuthUserByID(ctx, authUserID)
		if err != nil {
			return nil, shared.HttpResponseError("user not found", utils.Unauthorized, http.StatusUnauthorized)
		}
		if authUser == nil {
			return nil, shared.HttpResponseError("user not found", utils.Unauthorized, http.StatusUnauthorized)
		}

		if IsEmailBlacklisted(ctx, authUser.Email) {
			return nil, shared.HttpResponseError("invalid email address", utils.ValidationError, http.StatusBadRequest)
		}

		/**
		 * Generate new authentication token and refresh token
		 */
		authenticationToken, refreshToken, err = hubble.AuthenticationService.GenerateAuthRefreshPair(authUser)
		if err != nil {
			return nil, shared.LogErrorHttpResponse(err, err.Error(), utils.InternalServerError, http.StatusInternalServerError)
		}
	}
	// Invalidate previous tokens
	err = hubble.AuthenticationService.InvalidateRefreshToken(ctx, decodedRefreshToken.ID)
	if err != nil {
		rlog.Warn("Failed to invalidate old refresh token", "err", err)
	}

	response := &api.RefreshTokenResponse{}
	response.Data.AuthToken = authenticationToken
	response.Data.RefreshToken = refreshToken
	wasSuccess = true
	CachedRefreshResponse.Set(ctx, repositories.TokenIDKey{TokenID: decodedRefreshToken.ID}, *response)

	return response, nil
}

func postSocialLoginTasks(auroraService *services.AuroraService, subscribeUserOnMailjet bool, socialAvatar *string, userID string, email string) {
	backgroundCtx := context.Background()
	if subscribeUserOnMailjet {
		err := utils.SubscribeUserOnMailjet(backgroundCtx, Config.Mailjet, secrets.MailjetAPIKey, secrets.MailjetAPISecret, email)
		if err != nil {
			rlog.Error("failed to subscribe user on mailjet", "err", err, "userID", userID, "email", email)
		}
	}

	if socialAvatar != nil && *socialAvatar != "" {
		err := auroraService.UpdateAvatarForMarsUser(backgroundCtx, userID, *socialAvatar)
		if err != nil {
			rlog.Error("failed to upload avatar for user via social login", "err", err, "userID", userID, "socialAvatar", socialAvatar)
		}
	}
}

//encore:api public method=POST path=/v1.0/auth/social-token
func (hubble *Hubble) SocialTokenLogin(ctx context.Context, request *api.SocialLoginRequest) (*api.SocialLoginResponse, error) {
	// Step 1: Verify social auth token by calling mars
	apiResponse, err := services.SocialLoginTokenVerification(ctx, Config.Mars, secrets.MarsUser, secrets.MarsPassword, request)
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, "error while decoding social token", utils.ValidationError, http.StatusBadRequest)
	}
	if !apiResponse.Success {
		httpErrCode, respStatusCode, err := func() (shared.HttpErrorCode, int, error) {
			statusCode := apiResponse.Error.StatusCode
			errCode := utils.InternalServerError

			switch apiResponse.Error.ErrCode {
			case 2023:
				errCode = utils.GoogleVerificationFailed
			case 2024:
				errCode = utils.AppleVerificationFailed
			case 2025:
				errCode = utils.UnsupportedAuthVerificationMethod
			}

			return errCode, statusCode, fmt.Errorf(apiResponse.Error.Message)
		}()

		return nil, shared.HttpResponseError(err.Error(), httpErrCode, respStatusCode)
	}

	// Step 2: Create or update user/authUser
	createOrUpdateUserFromSocialLogin, err := services.CreateOrUpdateUserFromSocialLogin(ctx, hubble.MarsService, hubble.AuthUserRepository, apiResponse.Data)
	if err != nil {
		return nil, err
	}

	authUser := createOrUpdateUserFromSocialLogin.AuthUser
	userID := strconv.FormatInt(authUser.ID, 10)
	authenticationToken, refreshToken, err := hubble.AuthenticationService.GenerateAuthRefreshPair(authUser)
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, err.Error(), utils.InternalServerError, http.StatusInternalServerError)
	}

	// Background tasks
	go postSocialLoginTasks(hubble.AuroraService, createOrUpdateUserFromSocialLogin.SubscribeUserOnMailjet, createOrUpdateUserFromSocialLogin.SocialAvatar,
		userID,
		authUser.Email)

	response := api.SocialLoginResponse{}
	response.Data.AuthToken = authenticationToken
	response.Data.RefreshToken = refreshToken
	response.Data.UserID = userID
	response.Data.AgentID = userID

	return &response, nil
}

//encore:api public method=POST path=/v1.0/auth/social-code
func (hubble *Hubble) SocialCodeLogin(ctx context.Context, request *api.SocialLoginViaCodeRequest) (*api.SocialLoginResponse, error) {
	apiResponse, err := services.SocialLoginCodeVerification(ctx, Config.Mars, secrets.MarsUser, secrets.MarsPassword, *request)
	if err != nil {
		return nil, err
	}
	if !apiResponse.Success {
		httpErrCode, respStatusCode := func() (shared.HttpErrorCode, int) {
			statusCode := apiResponse.Error.StatusCode
			errCode := utils.InternalServerError

			switch apiResponse.Error.ErrCode {
			case 2023:
				errCode = utils.GoogleVerificationFailed
			case 2024:
				errCode = utils.AppleVerificationFailed
			case 2025:
				errCode = utils.UnsupportedAuthVerificationMethod
			}

			return errCode, statusCode
		}()

		return nil, shared.HttpResponseError(apiResponse.Error.Message, httpErrCode, respStatusCode)
	}

	// Step 2: Create or update user/authUser
	createOrUpdateUserFromSocialLogin, err := services.CreateOrUpdateUserFromSocialLogin(ctx, hubble.MarsService, hubble.AuthUserRepository, apiResponse.Data)
	if err != nil {
		return nil, err
	}

	authUser := createOrUpdateUserFromSocialLogin.AuthUser
	userID := strconv.FormatInt(authUser.ID, 10)
	authenticationToken, refreshToken, err := hubble.AuthenticationService.GenerateAuthRefreshPair(authUser)
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, err.Error(), utils.InternalServerError, http.StatusInternalServerError)
	}

	// Background tasks
	go postSocialLoginTasks(hubble.AuroraService, createOrUpdateUserFromSocialLogin.SubscribeUserOnMailjet, createOrUpdateUserFromSocialLogin.SocialAvatar,
		userID,
		authUser.Email)

	response := api.SocialLoginResponse{}
	response.Data.AuthToken = authenticationToken
	response.Data.RefreshToken = refreshToken
	response.Data.UserID = userID
	response.Data.AgentID = userID

	return &response, nil
}

//encore:api auth method=POST path=/v1.0/auth/logout
func (hubble *Hubble) Logout(ctx context.Context) (*api.LogoutResponse, error) {
	err := hubble.AuthenticationService.InvalidateTokenPair(ctx, auth.Data().(*shared.DecodedToken).ID)
	if err != nil {
		return nil, shared.LogErrorHttpResponse(err, "failed to expire token", utils.Unauthorized, http.StatusUnauthorized)
	}

	return &api.LogoutResponse{}, nil
}

//encore:api auth method=PATCH path=/v1.0/auth/user/host-opt-in
func (hubble *Hubble) HostOptIn(ctx context.Context) (*api.OptInResponse, error) {
	claims := auth.Data()
	if claims == nil {
		return nil, shared.HttpResponseError("failed to decode claims", utils.InternalServerError, http.StatusInternalServerError)
	}
	claimsMap, ok := claims.(*shared.DecodedToken)
	if !ok {
		return nil, shared.LogErrorHttpResponse(errors.New("cannot decode claims"), "failed to decode claims", utils.InternalServerError, http.StatusInternalServerError)
	}

	// Iterate and check if role exists in token
	roleFound := false
	for _, role := range claimsMap.RoleIDs {
		if role == string(models.USER_ROLE_SESSION_OWNER) {
			roleFound = true
		}
	}

	response := api.OptInResponse{}
	response.Data.UserID = claimsMap.UserID

	// If user doesn't have the session owner role this
	// 1. Appends the role
	// 2. Generates new auth and refresh tokens
	// 3. Expires previous tokens
	// 4. Returns
	if !roleFound {
		authUser, err := services.UpdateUserRoleTransaction(ctx, hubble.AuthUserRepository, claimsMap.UserID, models.USER_ROLE_SESSION_OWNER)
		if err != nil {
			errMsg := err.Error()
			if errMsg == "2090" {
				return nil, shared.HttpResponseError(utils.HttpMessages[utils.FailedToAppendRoles], utils.FailedToAppendRoles, http.StatusBadRequest)
			}

			return nil, shared.HttpResponseError(err.Error(), utils.InternalServerError, http.StatusInternalServerError)
		}

		authenticationToken, refreshToken, err := hubble.AuthenticationService.GenerateAuthRefreshPair(&authUser)
		if err != nil {
			return nil, shared.LogErrorHttpResponse(err, err.Error(), utils.InternalServerError, http.StatusInternalServerError)
		}
		response.Data.AuthToken = authenticationToken
		response.Data.RefreshToken = refreshToken

		err = hubble.AuthenticationService.InvalidateTokenPair(ctx, auth.Data().(*shared.DecodedToken).ID)
		if err != nil {
			rlog.Warn("failed to invalidate tokens on role change", "err", err)
		}
	}

	// Else do nothing
	return &response, nil
}
