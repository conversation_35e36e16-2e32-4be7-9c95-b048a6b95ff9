# GETMeetingsListUserTransactions200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**List[ApiListUserTransactionsResponseData]**](ApiListUserTransactionsResponseData.md) |  | 
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.get_meetings_list_user_transactions200_response import GETMeetingsListUserTransactions200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETMeetingsListUserTransactions200Response from a JSON string
get_meetings_list_user_transactions200_response_instance = GETMeetingsListUserTransactions200Response.from_json(json)
# print the JSON string representation of the object
print(GETMeetingsListUserTransactions200Response.to_json())

# convert the object into a dict
get_meetings_list_user_transactions200_response_dict = get_meetings_list_user_transactions200_response_instance.to_dict()
# create an instance of GETMeetingsListUserTransactions200Response from a dict
get_meetings_list_user_transactions200_response_from_dict = GETMeetingsListUserTransactions200Response.from_dict(get_meetings_list_user_transactions200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


