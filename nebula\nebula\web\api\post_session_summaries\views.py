import json

from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from starlette import status

from nebula.services.post_session_summaries.post_session_summaries_service import (
    get_post_session_summary_by_id,
    get_post_session_summary_by_session_ids,
)
from nebula.web.api.auth import authorize
from nebula.web.api.post_session_summaries.schema import (
    PostSessionSummaryDTO,
)
from nebula.web.api.post_session_summaries.utils import (
    post_session_summary_formats_from_qstr,
)

router = APIRouter()


@router.get(
    "/{sessionID}",
    response_model=PostSessionSummaryDTO,
    dependencies=[Depends(authorize)],
)
async def get_by_id(
    sessionID: int,
    formats: str | None = None,
) -> JSONResponse:
    """
    API for getting post session summary by id.

    :param sessionID: int
    :param formats: str | None
    :return: JSONResponse
    """
    summary = await get_post_session_summary_by_id(sessionID)

    if not summary:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"message": "Post session summary not found"},
        )

    md, html, json_dict = post_session_summary_formats_from_qstr(summary)

    return JSONResponse(
        status_code=status.HTTP_200_OK if summary.content else status.HTTP_202_ACCEPTED,
        content=json.loads(
            PostSessionSummaryDTO(
                message="Successfully retrieved post session summary",
                data=PostSessionSummaryDTO.Data(
                    id=summary.id,
                    session_id=summary.session_id,
                    session_recurrence_id=summary.session_recurrence_id,
                    html=html,
                    tldr=summary.tldr,
                    md=md,
                    content=json_dict,
                    created_at=summary.created_at.isoformat(),
                    updated_at=summary.updated_at.isoformat(),
                ),
            ).to_json()
        ),
    )


@router.get(
    "/by-session-ids/{sessionID}",
    response_model=PostSessionSummaryDTO,
    dependencies=[Depends(authorize)],
)
async def get_by_session_ids(
    sessionID: str,
    sessionRecurrenceID: str,
) -> JSONResponse:
    """
    API to get post session summary by session id and session recurrence id.

    :param sessionID: str
    :param sessionRecurrenceID: str

    :return: JSONResponse
    """
    summary = await get_post_session_summary_by_session_ids(
        sessionID,
        sessionRecurrenceID,
    )

    if not summary:
        return JSONResponse(
            status_code=status.HTTP_404_NOT_FOUND,
            content={"message": "Post session summary not found"},
        )

    md, _, json_dict = post_session_summary_formats_from_qstr(summary)

    return JSONResponse(
        status_code=status.HTTP_200_OK if summary.content else status.HTTP_202_ACCEPTED,
        content=json.loads(
            PostSessionSummaryDTO(
                message="Successfully retrieved post session summary",
                data=PostSessionSummaryDTO.Data(
                    id=summary.id,
                    session_id=summary.session_id,
                    session_recurrence_id=summary.session_recurrence_id,
                    md=md,
                    tldr=summary.tldr,
                    html=None,
                    content=json_dict,
                    created_at=summary.created_at.isoformat(),
                    updated_at=summary.updated_at.isoformat(),
                ),
            ).to_json(),
        ),
    )
