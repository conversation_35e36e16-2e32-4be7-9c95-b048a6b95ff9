from typing import Optional, Dict
from enum import Enum
from pydantic import BaseModel, ConfigDict
from nebula.utils import to_camel


class XRayTypeFilter(str, Enum):
    """Filter enum for X-Ray types"""

    BUILD = "build"
    MONITOR = "monitor"
    DIGEST = "digest"


class XRaySortBy(str, Enum):
    """Sort options for X-Rays"""

    LAST_UPDATED = "last_updated"
    ALPHABETICAL = "alphabetical"


class XRayUpdateData(BaseModel):
    """Typed update data for X-Ray"""

    model_config = ConfigDict(
        from_attributes=True, populate_by_name=True, alias_generator=to_camel
    )

    title: Optional[str] = None
    alert_channels: Optional[Dict[str, bool]] = None
    is_active: Optional[bool] = None
    # Digest-specific fields
    frequency: Optional[str] = None  # Cron expression for digest scheduling
    timezone: Optional[str] = None  # Timezone identifier


class XRayModel(BaseModel):
    """Pydantic model representing an X-Ray for temporal workflows."""

    id: int
    owner_id: int
    title: str
    description: str
    prompt: str
    icon: str
    short_summary: Optional[str]
    xray_type: str  # "build", "monitor", "digest"
    scope: str  # "personal", "team", "all"
    visibility: str  # "user", "team"
    is_active: bool
    current_commit_id: Optional[int]
    created_at: str
    updated_at: str

    # Digest-specific fields
    frequency: Optional[str] = None
    timezone: Optional[str] = None
    last_digest_at: Optional[int]
