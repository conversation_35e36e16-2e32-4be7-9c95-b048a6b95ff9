package api

import "errors"

type CreateBotRequest struct {
	MeetingLink string `json:"meetingLink"`
}

type GenerateBotAuthURLResponse struct {
	AuthURL string `json:"authURL"`
}

type RecallTranscriptionsWebhookTranscript struct {
	Speaker                      *string      `json:"speaker,omitempty"`
	SpeakerID                    *int64       `json:"speaker_id,omitempty"`
	TranscriptionProviderSpeaker *string      `json:"transcription_provider_speaker,omitempty"`
	Language                     *string      `json:"language,omitempty"`
	OriginalTranscriptID         int64        `json:"original_transcript_id"`
	Words                        []RecallWord `json:"words"`
	IsFinal                      bool         `json:"is_final"`
}

type RecallTranscriptionsWebhookSearch struct {
	Speaker              *string           `json:"speaker,omitempty"`
	OriginalTranscriptID int64             `json:"original_transcript_id"`
	Hits                 []RecallSearchHit `json:"hits"`
}

type RecallTranscriptionsWebhookStatus struct {
	Code        string `json:"code"`
	SubCode     string `json:"sub_code,omitempty"`
	Message     string `json:"message,omitempty"`
	RecordingID string `json:"recording_id,omitempty"`
	CreatedAt   string `json:"created_at,omitempty"`
}

type RecallTranscriptionsWebhookLog struct {
	Level     string `json:"level"`
	Message   string `json:"message"`
	CreatedAt string `json:"created_at"`
	OutputId  string `json:"output_id"`
}

type RecallTranscriptionsWebhookData struct {
	BotID      string                                 `json:"bot_id"`
	Transcript *RecallTranscriptionsWebhookTranscript `json:"transcript,omitempty"`
	Search     *RecallTranscriptionsWebhookSearch     `json:"search,omitempty"`
	Status     *RecallTranscriptionsWebhookStatus     `json:"status,omitempty"`
	Log        *RecallTranscriptionsWebhookLog        `json:"log,omitempty"`
}

type RecallTranscriptionsWebhookRequest struct {
	Token         string                          `query:"token" encore:"sensitive"`
	SvixID        string                          `header:"X-Svix-Id" encore:"sensitive"`
	SvixTimestamp int64                           `header:"X-Svix-Timestamp" encore:"sensitive"`
	SvixSignature string                          `header:"X-Svix-Signature" encore:"sensitive"`
	Event         string                          `json:"event"`
	Data          RecallTranscriptionsWebhookData `json:"data"`
}

type RecallWord struct {
	Text      string  `json:"text" encore:"sensitive"`
	StartTime float64 `json:"start_time"`
	EndTime   float64 `json:"end_time"`
}

type RecallSearchHit struct {
	Text       string  `json:"text" encore:"sensitive"`
	StartTime  float64 `json:"start_time"`
	EndTime    float64 `json:"end_time"`
	Confidence float64 `json:"confidence"`
}

type RecallProcessRecordingRequest struct {
	VideoURL     string `json:"video_url"`
	SessionID    string `json:"session_id"`
	RecurrenceID string `json:"recurrence_id"`
	BotID        string `json:"bot_id"`
}

var EphemeralEnvOnly = errors.New("should be running in ephemeral environment")
