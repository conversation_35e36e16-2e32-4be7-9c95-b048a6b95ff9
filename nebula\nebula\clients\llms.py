import cohere
from openai import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>

from nebula.settings import settings

mm_openai_client = AsyncOpenAI(api_key=settings.openai_mm_api_key)
pms_openai_client = AsyncOpenAI(api_key=settings.openai_pms_api_key)
ai_feed_openai_client = AsyncOpenAI(api_key=settings.openai_feed_api_key)
metadata_openai_client = AsyncOpenAI(api_key=settings.openai_metadata_api_key)
xray_openai_client = AsyncOpenAI(api_key=settings.openai_xray_api_key)
cohere_client = cohere.Client(settings.cohere_api_key)


class ThreadRunNotFound(Exception):
    pass
