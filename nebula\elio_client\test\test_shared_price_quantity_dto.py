# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.shared_price_quantity_dto import SharedPriceQuantityDTO

class TestSharedPriceQuantityDTO(unittest.TestCase):
    """SharedPriceQuantityDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> SharedPriceQuantityDTO:
        """Test SharedPriceQuantityDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `SharedPriceQuantityDTO`
        """
        model = SharedPriceQuantityDTO()
        if include_optional:
            return SharedPriceQuantityDTO(
                maximum = 1.337,
                minimum = 1.337
            )
        else:
            return SharedPriceQuantityDTO(
        )
        """

    def testSharedPriceQuantityDTO(self):
        """Test SharedPriceQuantityDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
