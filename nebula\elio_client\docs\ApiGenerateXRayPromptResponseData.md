# ApiGenerateXRayPromptResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**prompt** | **str** |  | 
**type** | **str** |  | 

## Example

```python
from elio_client.models.api_generate_x_ray_prompt_response_data import ApiGenerateXRayPromptResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiGenerateXRayPromptResponseData from a JSON string
api_generate_x_ray_prompt_response_data_instance = ApiGenerateXRayPromptResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiGenerateXRayPromptResponseData.to_json())

# convert the object into a dict
api_generate_x_ray_prompt_response_data_dict = api_generate_x_ray_prompt_response_data_instance.to_dict()
# create an instance of ApiGenerateXRayPromptResponseData from a dict
api_generate_x_ray_prompt_response_data_from_dict = ApiGenerateXRayPromptResponseData.from_dict(api_generate_x_ray_prompt_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


