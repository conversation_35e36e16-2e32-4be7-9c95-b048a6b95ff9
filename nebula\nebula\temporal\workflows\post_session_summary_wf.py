from temporalio import workflow
from datetime import timedelta
import json

with workflow.unsafe.imports_passed_through():
    from temporalio.workflow import ParentClosePolicy
    from temporalio.exceptions import ChildWorkflowError, TimeoutError
    from nebula.temporal.activities.post_session_summary_act import PostSessionSummaryActivities
    from nebula.temporal.workflows.meeting_suggestions_wf import MeetingSuggestionsWorkflow
    from nebula.temporal.types import (
        PostSessionSummaryWorkflowParams,
        PostSessionSummaryWorkflowResult,
        GeneratePMSInput,
        CreateOrUpdatePostSessionSummaryInput,
        GetActionItemsInput,
        NotifyDraconidsInput,
        MeetingSuggestionsWorkflowParams,
    )
    from nebula.settings import settings


@workflow.defn(name="PostSessionSummaryGeneration")
class PostSessionSummaryWorkflow:
    """Workflow for post session summary generation from meeting transcripts"""

    @workflow.run
    async def run(
        self, session_info: PostSessionSummaryWorkflowParams
    ) -> PostSessionSummaryWorkflowResult:
        """
        Run the post session summary generation workflow.

        Args:
            session_info: Session identification information containing session_id and recurrence_id

        Returns:
            PostSessionSummaryWorkflowResult containing the results of summary generation
        """
        workflow.logger.info(
            f"Starting post session summary workflow for session {session_info.session_id}, "
            f"recurrence {session_info.recurrence_id}"
        )

        # Step 1: Get transcription batches
        transcription_result = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.get_transcription_batches_for_pss,  # type: ignore
            session_info,
            start_to_close_timeout=timedelta(seconds=60),
        )

        if len(transcription_result.transcription_batches) == 0:
            workflow.logger.warn(
                f"No transcription batches found for session {session_info.session_id}, "
                f"recurrence {session_info.recurrence_id}"
            )
            return PostSessionSummaryWorkflowResult(
                done=True,
                message="No transcription batches found",
            )

        # Step 2: Get session data  
        session_data = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.get_session_data_for_pss,  # type: ignore
            session_info,
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 3: Process transcript
        processed_transcript = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.process_transcript_for_pss,  # type: ignore
            transcription_result,
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 4: Check transcript length
        if len(processed_transcript.transcript.split()) < 100:
            workflow.logger.info(
                f"[summary_worker] session {session_info.session_id} recurrence {session_info.recurrence_id} - skipping summary generation. transcripts too short"
            )
            return PostSessionSummaryWorkflowResult(
                done=True,
                message="Transcript too short",
            )

        # Step 5: Create initial post session summary record
        summary_record = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.create_or_update_post_session_summary,  # type: ignore
            CreateOrUpdatePostSessionSummaryInput(
                session_id=session_info.session_id,
                recurrence_id=session_info.recurrence_id,
                content=None,
                tldr=None,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        workflow.logger.info(
            f"[summary_worker] started for session {session_info.session_id} recurrence {session_info.recurrence_id}"
        )

        # Step 6: Generate main post meeting summary
        start_time = workflow.now().timestamp()
        pms_input = GeneratePMSInput(
            transcript=processed_transcript.transcript,
            recurrence_id=session_info.recurrence_id,
            locale=processed_transcript.locale,
            meeting_type=session_data.meeting_type,
        )
        
        pms_result = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.generate_post_meeting_summary,  # type: ignore
            pms_input,
            start_to_close_timeout=timedelta(minutes=3),
        )

        # Step 8: Check if summary generation was successful
        if not pms_result.summary:
            workflow.logger.error(
                f"Post meeting summary generation failed for session {session_info.session_id}"
            )
            return PostSessionSummaryWorkflowResult(
                done=False,
                message="Post meeting summary generation failed",
                summary_id=summary_record.summary_id,
            )

        # Step 9: Get action items for the session
        action_items = await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.get_action_items_for_pss,  # type: ignore
            GetActionItemsInput(
                session_id=session_info.session_id,
                recurrence_id=session_info.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 10: Combine summary with action items and participants
        final_summary = pms_result.summary
        final_summary["action_items"] = action_items
        final_summary["participants"] = session_data.participants

        workflow.logger.info(
            f"Added the {len(session_data.participants)} participants in the summary {session_info.session_id} recurrence {session_info.recurrence_id}"
        )

        # Step 11: Update the post session summary record with content
        await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.create_or_update_post_session_summary,  # type: ignore
            CreateOrUpdatePostSessionSummaryInput(
                session_id=session_info.session_id,
                recurrence_id=session_info.recurrence_id,
                content=json.dumps(final_summary),
                tldr=None,
            ),
            start_to_close_timeout=timedelta(seconds=30),
        )

        # Step 12: Execute meeting suggestions generation as child workflow with timeout
        workflow.logger.info(
            f"Starting meeting suggestions child workflow for session {session_info.session_id}, "
            f"recurrence {session_info.recurrence_id}"
        )
        
        try:
            # Wait for meeting suggestions to be generated (max 5 minutes)
            suggestions_result = await workflow.execute_child_workflow(
                MeetingSuggestionsWorkflow.run,
                MeetingSuggestionsWorkflowParams(
                    session_id=session_info.session_id,
                    recurrence_id=session_info.recurrence_id,
                ),
                id=f"meeting-suggestions-{session_info.session_id}-{session_info.recurrence_id}",
                task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
                execution_timeout=timedelta(minutes=5),
                parent_close_policy=ParentClosePolicy.ABANDON,  
            )
            workflow.logger.info(
                f"Meeting suggestions completed in ≤ 5 min: {suggestions_result.message}"
            )
        except (ChildWorkflowError, TimeoutError) as e:
            workflow.logger.warn(
                f"Meeting suggestions child workflow failed or timed out after 5 min for session "
                f"{session_info.session_id}, recurrence {session_info.recurrence_id}: {e}. "
                f"Continuing with PSS workflow."
                )
        except Exception as e:
            workflow.logger.error(
                f"Unexpected error in meeting suggestions child workflow for session "
                f"{session_info.session_id}, recurrence {session_info.recurrence_id}: {e}. "
                f"Continuing with PSS workflow."
            )

        # Step 13: Notify Draconids (AFTER meeting suggestions are generated)
        await workflow.execute_activity(  # type: ignore
            PostSessionSummaryActivities.notify_draconids_summary_ready,  # type: ignore
            NotifyDraconidsInput(
                session_id=session_info.session_id,
                recurrence_id=session_info.recurrence_id,
            ),
            start_to_close_timeout=timedelta(seconds=30),
                )

        workflow.logger.info(
            f"[summary_worker] session {session_info.session_id} recurrence {session_info.recurrence_id} - committing kafka message. JSON key len: {len(final_summary.keys())}"
        )

        processing_time = workflow.now().timestamp() - start_time

        workflow.logger.info(
            f"[summary_worker] finished for session {session_info.session_id} recurrence {session_info.recurrence_id}. Time taken: {processing_time:.2f} seconds"
        )

        return PostSessionSummaryWorkflowResult(
            done=True,
            message="Successfully generated post session summary",
            summary_id=summary_record.summary_id,
            processing_time_seconds=processing_time,
        ) 