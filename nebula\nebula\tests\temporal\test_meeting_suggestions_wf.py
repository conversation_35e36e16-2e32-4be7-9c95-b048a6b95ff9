import pytest
from temporalio.testing import WorkflowEnvironment
from nebula.temporal.converter import pydantic_data_converter

from nebula.temporal.workflows.meeting_suggestions_wf import MeetingSuggestionsWorkflow
from nebula.temporal.types import (
    MeetingSuggestionsWorkflowParams,
    MeetingSuggestionsWorkflowResult,
)
from nebula.tests.helpers.temporal import create_ai_end_meet_worker_for_tests
from nebula.settings import settings


@pytest.mark.asyncio
async def test_meeting_suggestions_workflow_success():
    """Test the meeting suggestions workflow with mocked activities."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        # Use the standard task queue from settings
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(env.client, task_queue):
            # Execute the workflow
            result = await env.client.execute_workflow(
                MeetingSuggestionsWorkflow.run,
                MeetingSuggestionsWorkflowParams(
                    session_id="123",
                    recurrence_id="456"
                ),
                id="test-meeting-suggestions-workflow",
                task_queue=task_queue,
            )

            # Verify the result
            assert isinstance(result, MeetingSuggestionsWorkflowResult)
            assert result.done is True
            assert "Successfully generated 9 meeting suggestions for 3 participants" in result.message
            assert result.processing_time_seconds is not None
            assert result.processing_time_seconds > 0 