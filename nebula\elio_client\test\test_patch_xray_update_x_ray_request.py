# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.patch_xray_update_x_ray_request import PATCHXrayUpdateXRayRequest

class TestPATCHXrayUpdateXRayRequest(unittest.TestCase):
    """PATCHXrayUpdateXRayRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> PATCHXrayUpdateXRayRequest:
        """Test PATCHXrayUpdateXRayRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `PATCHXrayUpdateXRayRequest`
        """
        model = PATCHXrayUpdateXRayRequest()
        if include_optional:
            return PATCHXrayUpdateXRayRequest(
                alert_channels = {
                    'key' : True
                    },
                frequency = '',
                icon = '',
                is_active = True,
                prompt = '',
                title = ''
            )
        else:
            return PATCHXrayUpdateXRayRequest(
                alert_channels = {
                    'key' : True
                    },
                frequency = '',
                icon = '',
                is_active = True,
                prompt = '',
                title = '',
        )
        """

    def testPATCHXrayUpdateXRayRequest(self):
        """Test PATCHXrayUpdateXRayRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
