# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_meetings_get_meeting_suggestions_by_session_api200_response import POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response

class TestPOSTMeetingsGetMeetingSuggestionsBySessionAPI200Response(unittest.TestCase):
    """POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response:
        """Test POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response`
        """
        model = POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response()
        if include_optional:
            return POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response(
                message = '',
                success = True,
                suggestions = [
                    elio_client.models.api/meeting_suggestion_dto.api.MeetingSuggestionDTO(
                        category = '', 
                        created_at = 56, 
                        id = '', 
                        prompt = '', 
                        session_id = '', 
                        session_recurrence_id = '', 
                        updated_at = 56, 
                        user_id = '', )
                    ]
            )
        else:
            return POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response(
                message = '',
                success = True,
                suggestions = [
                    elio_client.models.api/meeting_suggestion_dto.api.MeetingSuggestionDTO(
                        category = '', 
                        created_at = 56, 
                        id = '', 
                        prompt = '', 
                        session_id = '', 
                        session_recurrence_id = '', 
                        updated_at = 56, 
                        user_id = '', )
                    ],
        )
        """

    def testPOSTMeetingsGetMeetingSuggestionsBySessionAPI200Response(self):
        """Test POSTMeetingsGetMeetingSuggestionsBySessionAPI200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
