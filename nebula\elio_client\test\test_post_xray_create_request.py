# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_xray_create_request import POSTXrayCreateRequest

class TestPOSTXrayCreateRequest(unittest.TestCase):
    """POSTXrayCreateRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTXrayCreateRequest:
        """Test POSTXrayCreateRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTXrayCreateRequest`
        """
        model = POSTXrayCreateRequest()
        if include_optional:
            return POSTXrayCreateRequest(
                alert_channels = {
                    'key' : True
                    },
                description = '',
                frequency = '',
                icon = '',
                prompt = '',
                short_summary = '',
                title = '',
                type = ''
            )
        else:
            return POSTXrayCreateRequest(
                alert_channels = {
                    'key' : True
                    },
                description = '',
                frequency = '',
                icon = '',
                prompt = '',
                short_summary = '',
                title = '',
                type = '',
        )
        """

    def testPOSTXrayCreateRequest(self):
        """Test POSTXrayCreateRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
