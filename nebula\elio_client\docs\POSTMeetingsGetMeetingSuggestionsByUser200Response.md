# POSTMeetingsGetMeetingSuggestionsByUser200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**message** | **str** |  | 
**success** | **bool** |  | 
**suggestions** | **List[str]** |  | 

## Example

```python
from elio_client.models.post_meetings_get_meeting_suggestions_by_user200_response import POSTMeetingsGetMeetingSuggestionsByUser200Response

# TODO update the JSON string below
json = "{}"
# create an instance of POSTMeetingsGetMeetingSuggestionsByUser200Response from a JSON string
post_meetings_get_meeting_suggestions_by_user200_response_instance = POSTMeetingsGetMeetingSuggestionsByUser200Response.from_json(json)
# print the JSON string representation of the object
print(POSTMeetingsGetMeetingSuggestionsByUser200Response.to_json())

# convert the object into a dict
post_meetings_get_meeting_suggestions_by_user200_response_dict = post_meetings_get_meeting_suggestions_by_user200_response_instance.to_dict()
# create an instance of POSTMeetingsGetMeetingSuggestionsByUser200Response from a dict
post_meetings_get_meeting_suggestions_by_user200_response_from_dict = POSTMeetingsGetMeetingSuggestionsByUser200Response.from_dict(post_meetings_get_meeting_suggestions_by_user200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


