# GETMeetingsGetMeetingSuggestionsByUserInternal200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData**](GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData.md) |  | 
**message** | **str** |  | 

## Example

```python
from elio_client.models.get_meetings_get_meeting_suggestions_by_user_internal200_response import GETMeetingsGetMeetingSuggestionsByUserInternal200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETMeetingsGetMeetingSuggestionsByUserInternal200Response from a JSON string
get_meetings_get_meeting_suggestions_by_user_internal200_response_instance = GETMeetingsGetMeetingSuggestionsByUserInternal200Response.from_json(json)
# print the JSON string representation of the object
print(GETMeetingsGetMeetingSuggestionsByUserInternal200Response.to_json())

# convert the object into a dict
get_meetings_get_meeting_suggestions_by_user_internal200_response_dict = get_meetings_get_meeting_suggestions_by_user_internal200_response_instance.to_dict()
# create an instance of GETMeetingsGetMeetingSuggestionsByUserInternal200Response from a dict
get_meetings_get_meeting_suggestions_by_user_internal200_response_from_dict = GETMeetingsGetMeetingSuggestionsByUserInternal200Response.from_dict(get_meetings_get_meeting_suggestions_by_user_internal200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


