package shared

import (
	"encoding/json"
	"encore.app/pkg/database"
	"encore.dev/rlog"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/samber/lo"
)

type BaseFeatureConfig struct {
	Enabled bool
}

// SubscriptionPlanConfig is an entity that represents the configuration for a subscription plan.
type SubscriptionPlanConfig struct {
	AiFeed             BaseFeatureConfig
	CustomFeedItems    BaseFeatureConfig
	CustomIntegrations BaseFeatureConfig
	Id                 string
	Integrations       struct {
		BaseFeatureConfig
		Apps []string
	}
	MeetingMemory    BaseFeatureConfig
	MeetingSummary   BaseFeatureConfig
	MeetingTemplates BaseFeatureConfig
	MeetingWorkflows BaseFeatureConfig
	Meetings         struct {
		BaseFeatureConfig
		Max float32
	}
	ModelSegregation  BaseFeatureConfig
	OffTheRecord      BaseFeatureConfig
	PaddleProductID   string
	PaddleProductName string
	QueueMode         BaseFeatureConfig
	Recording         struct {
		BaseFeatureConfig
		Local *bool
	}
	Stream struct {
		BaseFeatureConfig
		Quality float32
	}
	Support struct {
		BaseFeatureConfig
		Type SubscriptionPlanConfigDTOSupportType
	}
	Crm  BaseFeatureConfig
	Bots BaseFeatureConfig
}

func (spc *SubscriptionPlanConfig) ToDTO() SubscriptionPlanConfigDTO {
	return SubscriptionPlanConfigDTO{
		AiFeed: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.AiFeed.Enabled},
		CustomFeedItems: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.CustomFeedItems.Enabled},
		CustomIntegrations: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.CustomIntegrations.Enabled},
		Id: spc.Id,
		Integrations: struct {
			Apps    []string `json:"apps" encore:"optional"`
			Enabled bool     `json:"enabled"`
		}{
			Apps:    spc.Integrations.Apps,
			Enabled: spc.Integrations.Enabled,
		},
		MeetingMemory: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.MeetingMemory.Enabled},
		MeetingSummary: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.MeetingSummary.Enabled},
		MeetingTemplates: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.MeetingTemplates.Enabled},
		MeetingWorkflows: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.MeetingWorkflows.Enabled},
		Meetings: struct {
			Enabled bool    `json:"enabled"`
			Max     float32 `json:"max"`
		}{
			Enabled: spc.Meetings.Enabled,
			Max:     spc.Meetings.Max,
		},
		ModelSegregation: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.ModelSegregation.Enabled},
		OffTheRecord: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.OffTheRecord.Enabled},
		PaddleProductID:   spc.PaddleProductID,
		PaddleProductName: spc.PaddleProductName,
		QueueMode: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.QueueMode.Enabled},
		Recording: struct {
			Enabled bool  `json:"enabled"`
			Local   *bool `json:"local,omitempty" encore:"optional"`
		}{
			Enabled: spc.Recording.Enabled,
			Local:   spc.Recording.Local,
		},
		Stream: struct {
			Enabled bool    `json:"enabled"`
			Quality float32 `json:"quality"`
		}{
			Enabled: spc.Stream.Enabled,
			Quality: spc.Stream.Quality,
		},
		Support: struct {
			Enabled bool                                 `json:"enabled"`
			Type    SubscriptionPlanConfigDTOSupportType `json:"type"`
		}{
			Enabled: spc.Support.Enabled,
			Type:    spc.Support.Type,
		},
		Crm: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.Crm.Enabled},
		Bots: struct {
			Enabled bool `json:"enabled"`
		}{Enabled: spc.Bots.Enabled},
	}
}

// FeatureConfigRow represents the standard structure for feature configuration
type FeatureConfigRow struct {
	Valid   bool    `json:"valid"`
	Enabled bool    `json:"enabled"`
	Max     float32 `json:"max"`
	Local   bool    `json:"local"`
	Quality float32 `json:"quality"`
	Type    string  `json:"type"`
}

// Scan implements sql.Scanner interface
func (n *FeatureConfigRow) Scan(value interface{}) error {
	if value == nil {
		n.Valid = false
		return nil
	}
	n.Valid = true
	return json.Unmarshal(value.([]byte), &n)
}

// SubscriptionPlanConfig represents the full subscription plan configuration
type SubscriptionPlanConfigRow struct {
	ID                  int64             `db:"id"`
	PlanConfigID        int64             `db:"planConfigID"`
	PlanID              int64             `db:"planID"`
	PaddleProductID     string            `db:"paddleProductID"`
	PaddleProductName   string            `db:"paddleProductName"`
	PlanConfigOverrides database.JSONBRaw `db:"planConfigOverrides"`

	// Required JSONB fields
	Meetings     FeatureConfigRow `db:"meetings"`
	QueueMode    FeatureConfigRow `db:"queueMode"`
	OffTheRecord FeatureConfigRow `db:"offTheRecord"`

	// Nullable JSONB fields
	AIFeed             FeatureConfigRow `db:"aiFeed"`
	CustomFeedItems    FeatureConfigRow `db:"customFeedItems"`
	CustomIntegrations FeatureConfigRow `db:"customIntegrations"`
	Integrations       FeatureConfigRow `db:"integrations"`
	MeetingMemory      FeatureConfigRow `db:"meetingMemory"`
	MeetingSummary     FeatureConfigRow `db:"meetingSummary"`
	MeetingTemplates   FeatureConfigRow `db:"meetingTemplates"`
	MeetingWorkflows   FeatureConfigRow `db:"meetingWorkflows"`
	ModelSegregation   FeatureConfigRow `db:"modelSegregation"`
	Recording          FeatureConfigRow `db:"recording"`
	Stream             FeatureConfigRow `db:"stream"`
	Support            FeatureConfigRow `db:"support"`
	Crm                FeatureConfigRow `db:"crm"`
	Bots               FeatureConfigRow `db:"bots"`
	TimeLimit          FeatureConfigRow `db:"timeLimit"`

	CreatedAt int64 `db:"createdAt"`
	UpdatedAt int64 `db:"updatedAt"`
}

func (row SubscriptionPlanConfigRow) ToSessionSubscriptionDTO() *SessionSubscriptionPlanDTO {
	p := &SessionSubscriptionPlanDTO{}
	if err := copier.Copy(&p.PlanConfig, row); err != nil {
		rlog.Error("error copying subscription plan config: %w", err)
		return nil
	}
	p.PlanConfig.Id = FormatInt(row.PlanConfigID)
	if len(row.PlanConfigOverrides) > 0 {
		p.PlanConfigOverrides = lo.ToPtr(json.RawMessage(row.PlanConfigOverrides))
	}
	return p
}

func (row SubscriptionPlanConfigRow) ToSubscriptionPlanConfig() (*SubscriptionPlanConfig, error) {
	p := &SubscriptionPlanConfig{}
	if err := copier.CopyWithOption(&p, row, copier.Option{DeepCopy: true}); err != nil {
		err = fmt.Errorf("error copying subscription plan config: %w", err)
		return nil, err
	}
	p.Id = FormatInt(row.ID)
	return p, nil
}
