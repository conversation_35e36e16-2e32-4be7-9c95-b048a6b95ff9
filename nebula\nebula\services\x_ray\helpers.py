"""Helper functions for X-Ray functionality."""

from typing import Optional
from nebula.db.models.xray import XRay
from nebula.db.models.xray_doc_commit import XRayDocumentCommit
from nebula.services.x_ray.types import XRayModel


def xray_to_xray_model(
    xray: XRay, commit: Optional[XRayDocumentCommit] = None
) -> XRayModel:
    """Convert XRay database model to Pydantic model for temporal workflows."""
    return XRayModel(
        id=xray.id,
        owner_id=xray.owner_id,
        title=xray.title,
        description=xray.description,
        prompt=xray.prompt,
        icon=xray.icon,
        short_summary=xray.short_summary,
        xray_type=xray.xray_type,
        scope=xray.scope,
        visibility=xray.visibility,
        is_active=xray.is_active,
        current_commit_id=xray.current_commit_id,
        created_at=xray.created_at.isoformat() if xray.created_at else "",
        updated_at=xray.updated_at.isoformat() if xray.updated_at else "",
        # Digest-specific fields
        frequency=xray.frequency,
        timezone=xray.timezone,
        last_digest_at=int(xray.last_digest_at.timestamp())
        if xray.last_digest_at
        else None,
    )
