# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_generate_x_ray_prompt_response_data import ApiGenerateXRayPromptResponseData

class TestApiGenerateXRayPromptResponseData(unittest.TestCase):
    """ApiGenerateXRayPromptResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiGenerateXRayPromptResponseData:
        """Test ApiGenerateXRayPromptResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiGenerateXRayPromptResponseData`
        """
        model = ApiGenerateXRayPromptResponseData()
        if include_optional:
            return ApiGenerateXRayPromptResponseData(
                prompt = '',
                type = ''
            )
        else:
            return ApiGenerateXRayPromptResponseData(
                prompt = '',
                type = '',
        )
        """

    def testApiGenerateXRayPromptResponseData(self):
        """Test ApiGenerateXRayPromptResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
