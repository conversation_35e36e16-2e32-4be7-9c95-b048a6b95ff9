import asyncio
import time
import traceback

from temporalio import activity

from nebula.services.elio.service import (
    get_session_participants,
    trigger_elio_meeting_suggestions_upsert,
)
from nebula.services.meeting_suggestions.models import (
    ParticipantInfo,
)
from elio_client.models.api_meeting_suggestion_input import ApiMeetingSuggestionInput
from nebula.temporal.types import (
    MeetingSuggestionsWorkflowParams,
    AllUsersMeetingSuggestionsRecordResult,
    ParticipantsResult,
    ExtractAllUsersMeetingSuggestionsInput,
    CreateAllUsersMeetingSuggestionsInput,
    AllUsersMeetingSuggestionsExtraction,
    ProcessedTranscriptWithUsersResult,
    TranscriptionBatchesResult,
)


class MeetingSuggestionsActivities:


    @activity.defn
    async def get_all_participants(
        self, session_info: MeetingSuggestionsWorkflowParams
    ) -> ParticipantsResult:
        """
        Activity to get all meeting participants with their user IDs and names.

        Args:
            session_info: Session identification information

        Returns:
            ParticipantsResult containing list of participants with IDs and names
        """
        try:
            # Get participants
            participants = await asyncio.to_thread(
                get_session_participants,
                session_info.session_id, session_info.recurrence_id
            )

            participant_list = []
            for participant in participants:
                if participant.joined is False:
                    continue

                # Handle user participants
                if participant.user is not None:
                    user_id = participant.user.user_id
                    if not user_id:
                        continue

                    # Build full name
                    fullname = (
                        f"{participant.user.first_name} {participant.user.last_name}"
                        if participant.user.first_name and participant.user.last_name
                        else participant.user.first_name or participant.user.last_name or "Unknown User"
                    )

                    participant_list.append(ParticipantInfo(
                        user_id=user_id,
                        name=fullname
                    ))

                # Handle guest participants
                elif participant.guest is not None:
                    guest_id = participant.guest.surrogate_id
                    if not guest_id:
                        continue

                    # Use guest full name
                    fullname = participant.guest.full_name or "Unknown Guest"

                    participant_list.append(ParticipantInfo(
                        user_id=guest_id,
                        name=fullname
                    ))

            activity.logger.info(
                f"Retrieved {len(participant_list)} participants for session {session_info.session_id}"
            )

            return ParticipantsResult(participants=participant_list)

        except Exception as e:
            activity.logger.error(
                f"Error retrieving participants: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def create_all_users_meeting_suggestions_record(
        self, input: CreateAllUsersMeetingSuggestionsInput
    ) -> AllUsersMeetingSuggestionsRecordResult:
        """
        Activity to create meeting suggestions records by calling Elio service.

        Args:
            input: CreateAllUsersMeetingSuggestionsInput containing session info and suggestions for all users

        Returns:
            AllUsersMeetingSuggestionsRecordResult containing statistics about created records
        """
        try:
            participant_ids = set()
            suggestions_for_elio = []
            
            # Convert suggestions to Elio format
            for suggestion_item in input.suggestions.user_suggestions:
                suggestions_for_elio.append(ApiMeetingSuggestionInput(
                    userId=suggestion_item.user_id,
                    category=suggestion_item.category,
                    prompt=suggestion_item.prompt,
                ))
                participant_ids.add(suggestion_item.user_id)

            # Call Elio to store the suggestions
            await asyncio.to_thread(
                trigger_elio_meeting_suggestions_upsert,
                session_id=input.session_info.session_id,
                recurrence_id=input.session_info.recurrence_id,
                suggestions=suggestions_for_elio
            )

            activity.logger.info(
                f"Successfully sent {len(suggestions_for_elio)} meeting suggestion records to Elio for {len(participant_ids)} participants "
                f"for session {input.session_info.session_id}, recurrence {input.session_info.recurrence_id}"
            )

            return AllUsersMeetingSuggestionsRecordResult(
                total_records_created=len(suggestions_for_elio),
                participant_count=len(participant_ids),
            )

        except Exception as e:
            activity.logger.error(
                f"Error sending meeting suggestions to Elio: {str(e)}\n{traceback.format_exc()}"
            )
            raise



    @activity.defn
    async def process_transcript_with_user_ids(
        self, transcript_batches: TranscriptionBatchesResult
    ) -> ProcessedTranscriptWithUsersResult:
        """
        Activity to process transcript batches with user IDs for multi-user processing.

        Args:
            transcript_batches: Transcription batches to process

        Returns:
            ProcessedTranscriptWithUsersResult containing transcript and unique users
        """
        try:
            from nebula.shared.transcriptions import get_all_transcriptions_with_user_ids
            
            # Get transcript with user IDs
            transcripts, locale, unique_users = get_all_transcriptions_with_user_ids(
                transcript_batches.transcription_batches
            )
            transcript = "\n".join(transcripts)

            # Convert set to list for JSON serialization
            participants = [
                ParticipantInfo(user_id=user_id, name=name) 
                for user_id, name in unique_users
                if user_id  # Only include users with valid IDs
            ]

            activity.logger.info(
                f"Processed transcript with user IDs: {len(transcript)} characters, "
                f"{len(participants)} unique participants, locale={locale}"
            )

            return ProcessedTranscriptWithUsersResult(
                transcript=transcript, 
                locale=locale or "en-US",
                participants=participants
            )

        except Exception as e:
            activity.logger.error(
                f"Error processing transcript with user IDs: {str(e)}\n{traceback.format_exc()}"
            )
            raise

    @activity.defn
    async def extract_all_users_meeting_suggestions(
        self, input: ExtractAllUsersMeetingSuggestionsInput
    ) -> AllUsersMeetingSuggestionsExtraction:
        """
        Activity to extract meeting suggestions for all users using a single LLM call.

        Args:
            input: ExtractAllUsersMeetingSuggestionsInput containing meeting data

        Returns:
            AllUsersMeetingSuggestionsExtraction containing suggestions for all users
        """
        try:
            from nebula.services.meeting_suggestions.meeting_suggestions_service import (
                generate_multi_user_meeting_suggestions_with_llm
            )
            
            # Generate suggestions for all users in one call
            start_time = time.time()
            all_suggestions = await generate_multi_user_meeting_suggestions_with_llm(input)
            processing_time = time.time() - start_time

            activity.logger.info(
                f"Generated suggestions for {len(input.participants)} users "
                f"in single LLM call in {processing_time:.2f} seconds"
            )

            return all_suggestions

        except Exception as e:
            activity.logger.error(
                f"Error extracting multi-user meeting suggestions: {str(e)}\n{traceback.format_exc()}"
            )
            raise 