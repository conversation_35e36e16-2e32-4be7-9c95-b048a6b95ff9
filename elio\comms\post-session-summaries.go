package comms

import (
	"context"
	_ "embed"
	"errors"
	"fmt"
	"net/url"
	"strings"
	"time"

	"encore.app/comms/template"
	"encore.app/pkg/notify"
	wormhole_api "encore.app/wormhole/api"

	"encore.app/comms/api"
	"encore.app/meetings"
	meetings_api "encore.app/meetings/api"
	"encore.app/shared"
	"encore.app/transcriptions"
	transcriptions_api "encore.app/transcriptions/api"
	wormhole "encore.app/wormhole"
	"encore.dev/rlog"
	"github.com/samber/lo"
)

// PostSessionSummaryReady: Receive a request from nebula letting us know that the PSS was generated
// TODO when tag:internal is merged, is that instead of tag:trixta
//
//encore:api auth method=GET tag:trixta
func (c *Comms) PostSessionSummaryReady(ctx context.Context, req *api.PostSessionSummaryReadyRequest) (*api.PostSessionSummaryReadyResponse, error) {
	// 1. Fetch the session recurrence
	sessionResp, err := meetings.GetSessionByID(ctx, &meetings_api.GetSessionByIDRequest{
		SessionID:    req.SessionID,
		RecurrenceID: req.SessionRecurrenceID,
	})
	if err != nil {
		return nil, err
	}
	session := sessionResp.Session

	// 2. Fetch the session users
	sessionUsersResp, err := meetings.GetSessionUsersBySessionID(ctx, &meetings_api.GetSessionUsersBySessionIDRequest{
		SessionID:           req.SessionID,
		SessionRecurrenceID: req.SessionRecurrenceID,
	})
	if err != nil {
		return nil, err
	}
	sessionUsers := sessionUsersResp.SessionUsers

	speakersAsGuests, err := transcriptions.GetSpeakersAsSessionGuestsBySessionID(ctx, &transcriptions_api.GetSessionSpeakersAsGuestsBySessionIDRequest{
		SessionID:           req.SessionID,
		SessionRecurrenceID: req.SessionRecurrenceID,
	})
	if err != nil {
		return nil, err
	}
	speakerEmails := lo.Map(speakersAsGuests.Guests, func(g shared.GuestDTO, _ int) string {
		return g.Email
	})

	// 3. Fetch the session access rules
	accessRulesResp, err := meetings.GetSessionAccessRules(ctx, &meetings_api.GetSessionAccessRulesRequest{
		SessionID:           req.SessionID,
		SessionRecurrenceID: req.SessionRecurrenceID,
	})
	if err != nil {
		return nil, err
	}
	accessRules := accessRulesResp.Data

	// 4. Define recipients based on settings
	owner, found := lo.Find(sessionUsers, func(su meetings_api.SessionUserDTO) bool {
		if su.User == nil {
			return false
		}
		return su.User.UserID == session.SessionCreatorUserID
	})
	if !found {
		return nil, errors.New("owner not found")
	}

	// Get the owner's user details, we need the timezone:
	ownerUserResp, err := meetings.GetUserByID(ctx, &meetings_api.GetUserByIDRequest{
		UserID: owner.User.UserID,
	})
	if err != nil {
		return nil, err
	}
	userTimezone := ownerUserResp.Timezone
	if userTimezone == nil {
		userTimezone = lo.ToPtr("America/New_York")
	}

	recipients := []string{owner.User.Email}
	pssEmailRule := shared.PSSEmailRecipientRuleEveryone
	sessionSettings := session.Settings()
	if sessionSettings != nil && sessionSettings.PostSessionSummary != nil {
		pssEmailRule = sessionSettings.PostSessionSummary.Recipients.Email
	}

	switch pssEmailRule {
	// case owner only is covered by default, since the owner always gets the PSS
	case shared.PSSEmailRecipientRuleSameDomain:
		// Get all session users with the same domain as the owner
		domain := strings.Split(owner.User.Email, "@")[1]
		usersWithSameDomain := lo.Filter(sessionUsers, func(su meetings_api.SessionUserDTO, _ int) bool {
			var email string
			if su.User == nil {
				email = su.Guest.Email
			} else {
				email = su.User.Email
			}
			if email == "" {
				return false
			}
			emailSplit := strings.Split(email, "@")
			if len(emailSplit) != 2 {
				return false
			}
			return emailSplit[1] == domain
		})
		recipients = append(recipients, lo.Map(usersWithSameDomain, func(su meetings_api.SessionUserDTO, _ int) string {
			if su.User == nil {
				return su.Guest.Email
			}
			return su.User.Email
		})...)
		sameDomainSpeakers := lo.Filter(speakersAsGuests.Guests, func(g shared.GuestDTO, _ int) bool {
			emailSplit := strings.Split(g.Email, "@")
			if len(emailSplit) != 2 {
				return false
			}
			return emailSplit[1] == domain
		})
		recipients = append(recipients, lo.Map(sameDomainSpeakers, func(g shared.GuestDTO, _ int) string {
			return g.Email
		})...)
	case shared.PSSEmailRecipientRuleEveryone:
		invited := lo.Filter(accessRules, func(r meetings_api.SessionAccessDTO, _ int) bool {
			return r.Type == "email"
		})
		invitedEmails := lo.Map(invited, func(r meetings_api.SessionAccessDTO, _ int) string {
			return r.Value
		})
		sessionEmails := lo.Map(sessionUsers, func(su meetings_api.SessionUserDTO, _ int) string {
			if su.User != nil {
				return su.User.Email
			}
			if su.Guest != nil {
				return su.Guest.Email
			}
			return ""
		})
		recipients = append(recipients, invitedEmails...)
		recipients = append(recipients, lo.Filter(sessionEmails, func(email string, _ int) bool {
			return email != ""
		})...)
		recipients = append(recipients, speakerEmails...)
	}

	// ensure unique recipients
	recipients = lo.Uniq(recipients)

	// Get the post-session summary from wormhole
	pssResp, err := wormhole.PostSessionSummary(ctx, &wormhole_api.PostSessionSummaryRequest{
		SessionRecurrenceID: session.SessionRecurrenceID,
		SessionID:           session.SessionID,
	})
	if err != nil {
		return nil, err
	}
	if pssResp.ErrCode != shared.HttpSuccessCode {
		return nil, errors.New("failed to get PSS from wormhole")
	}

	parsedPublicUrl, _ := url.Parse(session.PublicURL)
	recordingUrl := ""
	if parsedPublicUrl != nil {
		recordingUrl = fmt.Sprintf("https://%s/recordings/%s/%s", parsedPublicUrl.Hostname(), session.SessionID, session.SessionRecurrenceID)
	}

	// 5. Send personalized PSS to each recipient individually
	for _, recipient := range recipients {
		// Get personalized suggestions for this recipient
		var personalizedSuggestions []string
		
		// Find the user ID for this recipient
		var recipientUserID string
		recipientUser, found := lo.Find(sessionUsers, func(su meetings_api.SessionUserDTO) bool {
			if su.User != nil {
				return su.User.Email == recipient
			}
			if su.Guest != nil {
				return su.Guest.Email == recipient
			}
			return false
		})
		
		if found && recipientUser.User != nil {
			recipientUserID = recipientUser.User.UserID
			
			// Fetch personalized suggestions for this user
			personalizedSuggestions = getPersonalizedSuggestions(ctx, recipientUserID, req.SessionID, req.SessionRecurrenceID, recipient)
		} else {
			// For guests or users not found in session users, use empty suggestions
			personalizedSuggestions = []string{}
			rlog.Debug("no personalized suggestions for recipient", "email", recipient, "reason", "user not found in session or is guest")
		}
		
		// Generate HTML - use suggestions template only if we have suggestions
		var personalizedHTMLBuilder strings.Builder
		var err error
		
		if len(personalizedSuggestions) > 0 {
			personalizedHTMLBuilder, err = template.RenderPostSessionSummaryHTMLWithSuggestions(pssResp.Data.Content, personalizedSuggestions, recordingUrl)
			if err != nil {
				rlog.Error("failed to render HTML with suggestions, falling back to template without suggestions", "error", err, "recipient", recipient)
				// Fallback to rendering without suggestions
				personalizedHTMLBuilder, err = template.RenderPostSessionSummaryHTML(pssResp.Data.Content)
				if err != nil {
					return nil, err
				}
			}
		} else {
			// No suggestions, use the regular template
			personalizedHTMLBuilder, err = template.RenderPostSessionSummaryHTML(pssResp.Data.Content)
			if err != nil {
				return nil, err
			}
		}
		
		// Create personalized context for this recipient
		personalizedContext := notify.PostSessionSummaryEmailReadyContext{
			Title:        session.SessionTitle,
			RecordingURL: recordingUrl,
			HTML:         personalizedHTMLBuilder.String(),
			Suggestions:  personalizedSuggestions,
		}
		
		// Set time formatting (same for all users but using owner's timezone)
		startedAtTimestamp := session.StartedAt
		if startedAtTimestamp == nil {
			startedAtTimestamp = session.StartTimestamp
		}
		if startedAtTimestamp != nil {
			zone, _ := time.LoadLocation(*userTimezone)
			t := time.Unix(int64(*startedAtTimestamp), 0).In(zone)
			personalizedContext.MeetingDate = t.Format("2 January")
			personalizedContext.MeetingTime = t.Format("3:04 pm MST")
			endedAtTimestamp := session.EndedAt
			if endedAtTimestamp == nil {
				endedAtTimestamp = session.EndTimestamp
			}
			if endedAtTimestamp != nil {
				personalizedContext.CalcDuration(t, time.Unix(int64(*endedAtTimestamp), 0))
			}
		}
		
		// Send individual email with personalized content
		c.Notify.Session(func(events notify.SessionEvents) {
			events.PostSessionSummaryEmailReady([]string{recipient}, personalizedContext)
		})
		
		rlog.Debug("sent personalized post-session summary email", "recipient", recipient, "sessionID", req.SessionID, "recurrenceID", req.SessionRecurrenceID, "suggestionsCount", len(personalizedSuggestions))
	}

	response := api.PostSessionSummaryReadyResponse{
		Message:    "Post session summary emails triggered",
		Code:       200,
		Recipients: recipients,
	}
	return &response, nil
}
