# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_transcriptions_create_bot_for_meeting_request import POSTTranscriptionsCreateBotForMeetingRequest

class TestPOSTTranscriptionsCreateBotForMeetingRequest(unittest.TestCase):
    """POSTTranscriptionsCreateBotForMeetingRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTTranscriptionsCreateBotForMeetingRequest:
        """Test POSTTranscriptionsCreateBotForMeetingRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTTranscriptionsCreateBotForMeetingRequest`
        """
        model = POSTTranscriptionsCreateBotForMeetingRequest()
        if include_optional:
            return POSTTranscriptionsCreateBotForMeetingRequest(
                meeting_link = ''
            )
        else:
            return POSTTranscriptionsCreateBotForMeetingRequest(
                meeting_link = '',
        )
        """

    def testPOSTTranscriptionsCreateBotForMeetingRequest(self):
        """Test POSTTranscriptionsCreateBotForMeetingRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
