# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_suggestion_response_dto import ApiSuggestionResponseDTO

class TestApiSuggestionResponseDTO(unittest.TestCase):
    """ApiSuggestionResponseDTO unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiSuggestionResponseDTO:
        """Test ApiSuggestionResponseDTO
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiSuggestionResponseDTO`
        """
        model = ApiSuggestionResponseDTO()
        if include_optional:
            return ApiSuggestionResponseDTO(
                category = '',
                content = '',
                created_at = 56,
                id = '',
                is_personal = True
            )
        else:
            return ApiSuggestionResponseDTO(
                category = '',
                content = '',
                created_at = 56,
                id = '',
                is_personal = True,
        )
        """

    def testApiSuggestionResponseDTO(self):
        """Test ApiSuggestionResponseDTO"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
