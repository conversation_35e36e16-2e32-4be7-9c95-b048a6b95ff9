# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_x_ray_doc_commit import ApiXRayDocCommit

class TestApiXRayDocCommit(unittest.TestCase):
    """ApiXRayDocCommit unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiXRayDocCommit:
        """Test ApiXRayDocCommit
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiXRayDocCommit`
        """
        model = ApiXRayDocCommit()
        if include_optional:
            return ApiXRayDocCommit(
                author_id = 56,
                content = '',
                created_at = 56,
                id = 56,
                updated_at = 56,
                xray_id = 56
            )
        else:
            return ApiXRayDocCommit(
                author_id = 56,
                content = '',
                created_at = 56,
                id = 56,
                updated_at = 56,
                xray_id = 56,
        )
        """

    def testApiXRayDocCommit(self):
        """Test ApiXRayDocCommit"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
