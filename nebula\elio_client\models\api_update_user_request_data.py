# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from elio_client.models.shared_onboarding_flags import SharedOnboardingFlags
from elio_client.models.shared_user_social_dto import SharedUserSocialDTO
from typing import Optional, Set
from typing_extensions import Self

class ApiUpdateUserRequestData(BaseModel):
    """
    ApiUpdateUserRequestData
    """ # noqa: E501
    about: Optional[StrictStr] = None
    avatar: Optional[StrictStr] = None
    first_name: Optional[StrictStr] = Field(default=None, alias="firstName")
    last_name: Optional[StrictStr] = Field(default=None, alias="lastName")
    onboarding: Optional[SharedOnboardingFlags] = None
    social: Optional[List[SharedUserSocialDTO]] = None
    __properties: ClassVar[List[str]] = ["about", "avatar", "firstName", "lastName", "onboarding", "social"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ApiUpdateUserRequestData from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of onboarding
        if self.onboarding:
            _dict['onboarding'] = self.onboarding.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in social (list)
        _items = []
        if self.social:
            for _item_social in self.social:
                if _item_social:
                    _items.append(_item_social.to_dict())
            _dict['social'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ApiUpdateUserRequestData from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "about": obj.get("about"),
            "avatar": obj.get("avatar"),
            "firstName": obj.get("firstName"),
            "lastName": obj.get("lastName"),
            "onboarding": SharedOnboardingFlags.from_dict(obj["onboarding"]) if obj.get("onboarding") is not None else None,
            "social": [SharedUserSocialDTO.from_dict(_item) for _item in obj["social"]] if obj.get("social") is not None else None
        })
        return _obj


