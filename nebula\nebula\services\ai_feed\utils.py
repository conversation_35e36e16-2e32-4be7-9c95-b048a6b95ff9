import re

from nebula.services.mars.schema import MarsGetUserResponseUserDTO
from nebula.services.ai_feed.models import UserBasic


def mars_user_dto_to_user_basic(user: MarsGetUserResponseUserDTO) -> UserBasic:
    userFirstName = user.get("userFirstName", "")
    userLastName = user.get("userLastName", "")
    return UserBasic(
        id=user["userID"],
        firstName=userFirstName,
        lastName=userLastName,
        fullName=f"{userFirstName} {userLastName}".strip(),
        avatar=user.get("avatar", None),
    )


def assignee_from_action_item_contents(value: str) -> str:
    pattern = r"assignee\s*[:|;\-\s]\s*(.*)"
    matches = re.findall(pattern, value, re.IGNORECASE)
    assignees = []

    for _, assignee in enumerate(matches, start=1):
        assignees.append(assignee)

    return ", ".join(assignees)
