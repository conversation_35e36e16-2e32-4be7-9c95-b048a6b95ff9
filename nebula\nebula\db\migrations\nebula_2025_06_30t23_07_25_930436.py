from piccolo.apps.migrations.auto.migration_manager import Migration<PERSON>anager
from piccolo.table import Table


# dummy table used for raw sql migrations
class RawTable(Table):
    pass


ID = "2025-06-30T23:07:25:930436"
VERSION = "1.23.0"
DESCRIPTION = "Add timezone, frequency, and last_digest_at columns to xrays table for digest scheduling functionality"

# Table names
xray_table_name = "xrays"


async def forwards():
    manager = MigrationManager(
        migration_id=ID,
        app_name="nebula",
        description=DESCRIPTION,
    )

    # Add timezone, frequency, and last_digest_at columns to xrays table
    async def add_digest_columns():
        # Add timezone column - stores timezone identifier (e.g., 'America/New_York', 'UTC')
        await RawTable.raw(f"""
            ALTER TABLE {xray_table_name} 
            ADD COLUMN timezone TEXT
        """)

        # Add frequency column - stores cron expression for digest scheduling
        await RawTable.raw(f"""
            ALTER TABLE {xray_table_name} 
            ADD COLUMN frequency TEXT
        """)

        # Add last_digest_at column - tracks when digest was last generated
        await RawTable.raw(f"""
            ALTER TABLE {xray_table_name} 
            ADD COLUMN last_digest_at TIMESTAMP WITH TIME ZONE
        """)

        # Create index on last_digest_at for efficient querying during digest scheduling
        await RawTable.raw(
            f"CREATE INDEX idx_{xray_table_name}_last_digest_at ON {xray_table_name} (last_digest_at)"
        )

    manager.add_raw(add_digest_columns)

    return manager


async def backwards():
    manager = MigrationManager(
        migration_id=ID,
        app_name="nebula",
        description=DESCRIPTION,
    )

    # Remove the added columns
    async def remove_digest_columns():
        # Drop the index first
        await RawTable.raw(f"DROP INDEX IF EXISTS idx_{xray_table_name}_last_digest_at")

        # Drop the columns
        await RawTable.raw(
            f"ALTER TABLE {xray_table_name} DROP COLUMN IF EXISTS last_digest_at"
        )
        await RawTable.raw(
            f"ALTER TABLE {xray_table_name} DROP COLUMN IF EXISTS frequency"
        )
        await RawTable.raw(
            f"ALTER TABLE {xray_table_name} DROP COLUMN IF EXISTS timezone"
        )

    manager.add_raw(remove_digest_columns)

    return manager
