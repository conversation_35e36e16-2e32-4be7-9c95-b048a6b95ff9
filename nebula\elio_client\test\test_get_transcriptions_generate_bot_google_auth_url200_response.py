# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_transcriptions_generate_bot_google_auth_url200_response import GETTranscriptionsGenerateBotGoogleAuthURL200Response

class TestGETTranscriptionsGenerateBotGoogleAuthURL200Response(unittest.TestCase):
    """GETTranscriptionsGenerateBotGoogleAuthURL200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETTranscriptionsGenerateBotGoogleAuthURL200Response:
        """Test GETTranscriptionsGenerateBotGoogleAuthURL200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETTranscriptionsGenerateBotGoogleAuthURL200Response`
        """
        model = GETTranscriptionsGenerateBotGoogleAuthURL200Response()
        if include_optional:
            return GETTranscriptionsGenerateBotGoogleAuthURL200Response(
                auth_url = ''
            )
        else:
            return GETTranscriptionsGenerateBotGoogleAuthURL200Response(
                auth_url = '',
        )
        """

    def testGETTranscriptionsGenerateBotGoogleAuthURL200Response(self):
        """Test GETTranscriptionsGenerateBotGoogleAuthURL200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
