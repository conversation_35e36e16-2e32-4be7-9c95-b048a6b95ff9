# ApiSuggestionResponseDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**category** | **str** |  | 
**content** | **str** |  | 
**created_at** | **int** |  | 
**id** | **str** |  | 
**is_personal** | **bool** |  | 

## Example

```python
from elio_client.models.api_suggestion_response_dto import ApiSuggestionResponseDTO

# TODO update the JSON string below
json = "{}"
# create an instance of ApiSuggestionResponseDTO from a JSON string
api_suggestion_response_dto_instance = ApiSuggestionResponseDTO.from_json(json)
# print the JSON string representation of the object
print(ApiSuggestionResponseDTO.to_json())

# convert the object into a dict
api_suggestion_response_dto_dict = api_suggestion_response_dto_instance.to_dict()
# create an instance of ApiSuggestionResponseDTO from a dict
api_suggestion_response_dto_from_dict = ApiSuggestionResponseDTO.from_dict(api_suggestion_response_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


