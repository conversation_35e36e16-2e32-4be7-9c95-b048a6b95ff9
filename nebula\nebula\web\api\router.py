from fastapi.routing import APIRouter

from nebula.settings import Settings
from nebula.web.api import (
    x_ray,
    ai_feed,
    docs,
    index,
    post_session_summaries,
    search,
    meeting_metadata,
)


api_router = APIRouter()
api_router.include_router(index.router)

if Settings().enable_swagger_docs:
    api_router.include_router(docs.router, prefix="/swagger", tags=["docs"])

api_router.include_router(ai_feed.router, prefix="/v1.0/ai-feed", tags=["ai-feed"])

api_router.include_router(
    post_session_summaries.router,
    prefix="/v1.0/post-session-summaries",
    tags=["post-session-summaries"],
)

api_router.include_router(
    search.router,
    prefix="/v1.0/memory",
    tags=["memory"],
)
api_router.include_router(
    x_ray.router,
    prefix="/v1.0/x-ray",
    tags=["x-ray"],
)


api_router.include_router(
    meeting_metadata.router,
    prefix="/v1.0/meeting-metadata",
    tags=["meeting-metadata"],
)
