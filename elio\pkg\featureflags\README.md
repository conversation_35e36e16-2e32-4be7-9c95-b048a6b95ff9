# PostHog Feature Flags Utility

A utility package for PostHog feature flags in the Elio backend service. This is a simple Go package, not a service.

## Setup

Set up the PostHog API key secret:

```bash
# Required: PostHog Project API Key
encore secret set --type dev <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
encore secret set --type prod <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
```

Get your **Project API Key** from: PostHog project settings → API Keys

## Usage

```go
import "encore.app/pkg/featureflags"

// Check if a feature is enabled
if featureflags.IsEnabled("new-feature-enabled", userID) {
    // Show new feature
}

// Get typed values with fallbacks
theme := featureflags.GetStringValue("ui-theme", userID, "default")
maxLimit := featureflags.GetIntValue("max-connections", userID, 100)
betaEnabled := featureflags.GetBoolValue("beta-features", userID, false)

// Get all flags for a user
allFlags := featureflags.GetAllFlags(userID)
```

## Shutdown Handling

**Important**: Add `featureflags.Close()` to your service's `Shutdown` method:

```go
// In your service (e.g., hubble/hubble.go, meetings/meetings.go, etc.)
func (s *YourService) Shutdown(force context.Context) {
    // Close PostHog feature flags client to flush buffered events
    featureflags.Close()

    // ... other cleanup code
}
```

**Why this matters**: PostHog buffers events for performance. Without calling `Close()` during shutdown, you may lose recent events when the service terminates. Encore automatically calls `Shutdown()` during graceful shutdowns.

**Note**: `featureflags.Close()` is safe to call even if feature flags were never used (client never initialized). It will simply do nothing in that case.

## Features

- ✅ **Lazy initialization**: Client initializes automatically on first use
- ✅ **Safe fallbacks**: Returns defaults when PostHog is unavailable
- ✅ **Thread-safe**: Uses sync.Once for initialization
- ✅ **Typed helpers**: String, int, bool value getters with defaults
- ✅ **Error handling**: Logs errors and returns safe defaults
- ✅ **Graceful shutdown**: Call `Close()` to flush buffered events

## Adding Feature Flags

1. Create the flag in your PostHog dashboard
2. Add a constant for the flag key:
   ```go
   const MyNewFeature = "my-new-feature-enabled"
   ```
3. Use it in your code:
   ```go
   if featureflags.IsEnabled(MyNewFeature, userID) {
       // Feature logic here
   }
   ```
