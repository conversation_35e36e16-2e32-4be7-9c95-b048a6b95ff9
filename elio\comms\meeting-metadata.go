package comms

import (
	"context"
	"strings"

	"encore.app/comms/api"
	"encore.app/meetings"
	meetings_api "encore.app/meetings/api"
	"encore.app/shared"
)

func isInstantMeeting(session shared.SessionDTO) bool {
	// Some dark logic to match the magic wand emoji at the start of the title for instant meetings.
	return strings.HasPrefix(session.SessionTitle, "🪄 Meeting with")
}

// MeetingMetadataReady is a endpoint to notify <PERSON><PERSON> that a specific session's metadata has been generated.
// Elio will then store the metadata title if it's a Recall or Listening Mode session.
//
//encore:api auth method=POST tag:trixta
func (c *Comms) MeetingMetadataReady(ctx context.Context, req *api.MeetingMetadataReadyRequest) error {
	sessionID := req.SessionID
	recurrenceID := req.SessionRecurrenceID
	metadata := req.Metadata

	sessionResp, err := meetings.GetSessionByID(ctx, &meetings_api.GetSessionByIDRequest{
		SessionID:    sessionID,
		RecurrenceID: recurrenceID,
	})
	if err != nil {
		return err
	}

	session := sessionResp.Session

	meetingType := session.MeetingType
	if meetingType == shared.SessionDTORecallBot || meetingType == shared.SessionDTOListeningMode || isInstantMeeting(session) {
		// Update session with the new title (if we used a generic title before.)
		_, err = meetings.UpdateSession(ctx, req.SessionID, req.SessionRecurrenceID, &meetings_api.UpdateSessionRequest{
			Title: &metadata.Title,
		})
		if err != nil {
			return err
		}
	}

	return nil
}
