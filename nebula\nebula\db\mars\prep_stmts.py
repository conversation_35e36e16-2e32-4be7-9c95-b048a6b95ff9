from typing import Dict

from nebula.db.prepare import NamedStatement


def create_prepare_get_past_team_visible_recurrences_by_team_id():
    return """PREPARE get_past_team_visible_recurrences_by_team_id (bigint, int, int) AS
        WITH ranked_sessions AS (
            SELECT DISTINCT ON (sr."recurrenceID")
                sr."id" AS "sessionID",
                sr."recurrenceID" AS "recurrenceID",
                sr."title" AS "title",
                sr."about" AS "about",
                sr."createdAt" AS "createdAt",
                sr."updatedAt" AS "updatedAt",
                sr."stateUpdatedAt" AS "stateUpdatedAt",
                sr."dataVisibility" AS "dataVisibility",
                cu.id AS "creatorID",
                cu."firstName" AS "creatorFirstName",
                cu."lastName" AS "creatorLastName",
                cu."avatar" AS "creatorAvatar",
                (
                    SELECT elem->>'updatedAt'
                    FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
                    WHERE elem->>'state' = 'ended'
                    ORDER BY elem->>'updatedAt' DESC
                    LIMIT 1
                ) AS "latestStateUpdatedAt"
            FROM "teams" tm
            LEFT JOIN "team_members" tmem ON tmem."teamID" = tm.id
            LEFT JOIN "session_users" su ON su."userID" = tmem."userID"
            LEFT JOIN users us ON us.id = su."userID"
            LEFT JOIN sessions_recurrences sr ON sr."id" = su."sessionID" AND sr."recurrenceID" = su."sessionRecurrenceID"
            LEFT JOIN users cu ON cu.id = sr."creatorUserID"
            WHERE tm.id = $1
              AND sr."dataVisibility" = 'team-visible'
              AND sr."settings"->>'summaAI' = 'true'
              AND sr.state = 'ended'
              AND sr."stateUpdatedAt" @> '[{"state":"active"}]'
            ORDER BY sr."recurrenceID", (
                SELECT elem->>'updatedAt'
                FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
                WHERE elem->>'state' = 'ended'
                ORDER BY elem->>'updatedAt' DESC
                LIMIT 1
            ) DESC
        )
        SELECT *
        FROM ranked_sessions
        ORDER BY "latestStateUpdatedAt" DESC
        LIMIT $2
        OFFSET $3;
    """


def create_prepare_get_past_team_visible_recurrences_since_unix_ts_by_team_id():
    return """PREPARE get_past_team_visible_recurrences_since_unix_ts_by_team_id (bigint, int, int) AS
        WITH ranked_sessions AS (
            SELECT DISTINCT ON (sr."recurrenceID")
                sr."id" AS "sessionID",
                sr."recurrenceID" AS "recurrenceID",
                sr."title" AS "title",
                sr."about" AS "about",
                sr."createdAt" AS "createdAt",
                sr."updatedAt" AS "updatedAt",
                sr."stateUpdatedAt" AS "stateUpdatedAt",
                sr."dataVisibility" AS "dataVisibility",
                cu.id AS "creatorID",
                cu."firstName" AS "creatorFirstName",
                cu."lastName" AS "creatorLastName",
                cu."avatar" AS "creatorAvatar",
                (
                    SELECT elem->>'updatedAt'
                    FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
                    WHERE elem->>'state' = 'ended'
                    ORDER BY elem->>'updatedAt' DESC
                    LIMIT 1
                ) AS "latestStateUpdatedAt"
            FROM "teams" tm
            LEFT JOIN "team_members" tmem ON tmem."teamID" = tm.id
            LEFT JOIN "session_users" su ON su."userID" = tmem."userID"
            LEFT JOIN users us ON us.id = su."userID"
            LEFT JOIN sessions_recurrences sr ON sr."id" = su."sessionID" AND sr."recurrenceID" = su."sessionRecurrenceID"
            LEFT JOIN users cu ON cu.id = sr."creatorUserID"
            WHERE tm.id = $1
              AND sr."dataVisibility" = 'team-visible'
              AND sr."settings"->>'summaAI' = 'true'
              AND sr.state = 'ended'
              AND sr."stateUpdatedAt" @> '[{"state":"active"}]'
              AND COALESCE(sr."startedAt", sr."startTimestamp") >= $4
              AND COALESCE(sr."startedAt", sr."startTimestamp") < EXTRACT(EPOCH FROM NOW())
            ORDER BY sr."recurrenceID", (
                SELECT elem->>'updatedAt'
                FROM jsonb_array_elements(sr."stateUpdatedAt") AS elem
                WHERE elem->>'state' = 'ended'
                ORDER BY elem->>'updatedAt' DESC
                LIMIT 1
            ) DESC
        )
        SELECT *
        FROM ranked_sessions
        ORDER BY "latestStateUpdatedAt" DESC
        LIMIT $2
        OFFSET $3;
    """


GET_PAST_TEAM_VISIBLE_RECURRENCES_BY_TEAM_ID_STMT_KEY = (
    "get_past_team_visible_recurrences_by_team_id"
)
GET_PAST_TEAM_VISIBLE_RECURRENCES_SINCE_UNIX_TS_BY_TEAM_ID_STMT_KEY = (
    "get_past_team_visible_recurrences_since_unix_ts_by_team_id"
)


MARS_PREPARED_STMTS: Dict[str, NamedStatement] = {
    GET_PAST_TEAM_VISIBLE_RECURRENCES_BY_TEAM_ID_STMT_KEY: {
        "stmt": create_prepare_get_past_team_visible_recurrences_by_team_id(),
    },
    GET_PAST_TEAM_VISIBLE_RECURRENCES_SINCE_UNIX_TS_BY_TEAM_ID_STMT_KEY: {
        "stmt": create_prepare_get_past_team_visible_recurrences_since_unix_ts_by_team_id(),
    },
}
