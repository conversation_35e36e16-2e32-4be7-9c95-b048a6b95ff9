# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_list_user_transactions_response_data import ApiListUserTransactionsResponseData

class TestApiListUserTransactionsResponseData(unittest.TestCase):
    """ApiListUserTransactionsResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiListUserTransactionsResponseData:
        """Test ApiListUserTransactionsResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiListUserTransactionsResponseData`
        """
        model = ApiListUserTransactionsResponseData()
        if include_optional:
            return ApiListUserTransactionsResponseData(
                invoice_url = '',
                transaction = elio_client.models.shared/billing_transaction_dto.shared.BillingTransactionDTO(
                    custom_data = elio_client.models.custom_data.customData(), 
                    items = [
                        elio_client.models.shared/billing_transaction_item_dto.shared.BillingTransactionItemDTO(
                            price = elio_client.models.shared/billing_price_dto.shared.BillingPriceDTO(), )
                        ], 
                    payments = [
                        elio_client.models.shared/billing_transaction_payment_attempt_dto.shared.BillingTransactionPaymentAttemptDTO(
                            method_details = elio_client.models.shared/billing_payment_method_details_dto.shared.BillingPaymentMethodDetailsDTO(
                                card = elio_client.models.shared/billing_card.shared.BillingCard(), 
                                type = '', ), )
                        ], )
            )
        else:
            return ApiListUserTransactionsResponseData(
                invoice_url = '',
                transaction = elio_client.models.shared/billing_transaction_dto.shared.BillingTransactionDTO(
                    custom_data = elio_client.models.custom_data.customData(), 
                    items = [
                        elio_client.models.shared/billing_transaction_item_dto.shared.BillingTransactionItemDTO(
                            price = elio_client.models.shared/billing_price_dto.shared.BillingPriceDTO(), )
                        ], 
                    payments = [
                        elio_client.models.shared/billing_transaction_payment_attempt_dto.shared.BillingTransactionPaymentAttemptDTO(
                            method_details = elio_client.models.shared/billing_payment_method_details_dto.shared.BillingPaymentMethodDetailsDTO(
                                card = elio_client.models.shared/billing_card.shared.BillingCard(), 
                                type = '', ), )
                        ], ),
        )
        """

    def testApiListUserTransactionsResponseData(self):
        """Test ApiListUserTransactionsResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
