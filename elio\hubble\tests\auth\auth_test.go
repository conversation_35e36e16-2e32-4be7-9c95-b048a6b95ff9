package auth

import (
	"context"
	_ "embed"
	hubble_db "encore.app/hubble/database"
	"encore.app/hubble/models"
	"encore.app/hubble/repositories"
	"encore.app/meetings"
	meetings_api "encore.app/meetings/api"
	"encore.dev/beta/auth"
	"encore.dev/storage/sqldb"
	"errors"
	"fmt"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/jmoiron/sqlx"
	"github.com/samber/lo"
	"testing"

	"encore.app/hubble"
	"encore.app/hubble/api"
	"encore.app/pkg/database"
	"encore.app/shared"
	"encore.dev/et"
	"github.com/stretchr/testify/assert"
)

var testDBMars *sqldb.Database
var testDBHubble *sqldb.Database

func setUp() {
	et.EnableServiceInstanceIsolation()

	var err error

	testDBHubble, err = et.NewTestDatabase(context.Background(), "hubble")
	if err != nil {
		panic(err)
	}

	testDBMars, err = et.NewTestDatabase(context.Background(), "mars")
	if err != nil {
		panic(err)
	}

	hubble.MarsDB = sqlx.NewDb(testDBMars.Stdlib(), "pgx")
	hubble.HubbleDB = testDBHubble

	meetings.MarsDB = sqlx.NewDb(testDBMars.Stdlib(), "pgx")

	shared.SeedDBTestDataFromFixture(hubble.HubbleDB, shared.GetFixtureByName("seed_auth_users.sql"))

	shared.SeedDBTestDataFromFixtureX(hubble.MarsDB, shared.GetFixtureByName("seed_users.sql"))
	shared.SeedDBTestDataFromFixtureX(hubble.MarsDB, shared.GetFixtureByName("user_subscription_plans.sql"))
	shared.SeedDBTestDataFromFixtureX(hubble.MarsDB, shared.GetFixtureByName("seed_categories.sql"))
	shared.SeedDBTestDataFromFixtureX(hubble.MarsDB, shared.GetFixtureByName("seed_basic_session.sql"))
	shared.SeedDBTestDataFromFixtureX(hubble.MarsDB, shared.GetFixtureByName("teams.sql"))
}

func TestIsAuthorized(t *testing.T) {
	setUp()

	t.Run("Create action", func(t *testing.T) {
		err := hubble.IsAuthorized(context.Background(), &api.IsAuthorizedRequest{
			Sub: []string{shared.GetUserSubjectByIDRole("1", shared.UserSubjectFree)},
			Obj: "session/id/*",
			Act: shared.AuthPolicyActionCreate,
		})

		database.AssertXNoOpenConns(t, hubble.MarsDB)
		assert.NoError(t, err)
	})

	t.Run("Update action", func(t *testing.T) {
		object := fmt.Sprintf("session/id/%s/recurrence/id/%s", "1", "1")

		err := hubble.IsAuthorized(context.Background(), &api.IsAuthorizedRequest{
			Sub: []string{shared.GetUserSubjectByIDRole("1", shared.UserSubjectFree)},
			Obj: object,
			Act: shared.AuthPolicyActionUpdate,
		})

		database.AssertXNoOpenConns(t, hubble.MarsDB)
		assert.NoError(t, err)
	})

	t.Run("Get action", func(t *testing.T) {
		object := fmt.Sprintf("session/id/%s", "1")

		err := hubble.IsAuthorized(context.Background(), &api.IsAuthorizedRequest{
			Sub: []string{shared.GetUserSubjectByEmailRole("<EMAIL>", shared.UserSubjectFree)},
			Obj: object,
			Act: shared.AuthPolicyActionGet,
		})

		database.AssertXNoOpenConns(t, hubble.MarsDB)
		assert.NoError(t, err)
	})
}

func TestHostOptIn(t *testing.T) {
	setUp()

	t.Run("User does not have the session owner role", func(t *testing.T) {
		t.Run("Adds the session owner role to the user", func(t *testing.T) {
			// Arrange
			uid := "1"
			et.OverrideAuthInfo(auth.UID(uid), &shared.DecodedToken{
				UserID:  uid,
				ID:      uid,
				Email:   "<EMAIL>",
				RoleIDs: []string{},
			},
			)

			// Act
			_, err := hubble.HostOptIn(context.Background())

			// Assert
			userDTO, err := meetings.GetUserByID(context.Background(), &meetings_api.GetUserByIDRequest{UserID: uid})
			assert.NoError(t, err)
			roleIDs := *userDTO.RoleIDs
			assert.Contains(t, roleIDs, "session_owner")
		})
	})
}

func TestVerifyEmailOTP(t *testing.T) {
	setUp()

	t.Run("Valid OTP", func(t *testing.T) {
		t.Run("User does not exist", func(t *testing.T) {
			t.Run("Creates user", func(t *testing.T) {
				// Arrange
				signUpEmailVerificationKey := repositories.EmailOTPKey{Email: "<EMAIL>"}
				signUpEmailVerificationValue := repositories.SignUpEmailVerificationOTP{
					OTP:            "0000",
					IsSignUp:       true,
					FirstName:      "New",
					LastName:       "User",
					MarketingOptIn: lo.ToPtr(false),
					Timezone:       lo.ToPtr("UTC+1"),
				}

				hubble.RedisRepo.VerifyEmailOTP.Set(context.Background(), signUpEmailVerificationKey, signUpEmailVerificationValue)

				// Act
				res, err := hubble.VerifyEmailOTP(context.Background(), &api.VerifyOTPRequest{
					Email: signUpEmailVerificationKey.Email,
					OTP:   signUpEmailVerificationValue.OTP,
				})

				// Assert
				assert.NoError(t, err)

				uid := res.Data.UserID

				userDTOPtr, err := meetings.GetUserByID(context.Background(), &meetings_api.GetUserByIDRequest{
					UserID: uid,
				})
				assert.NoError(t, err)
				user := *userDTOPtr
				assert.Equal(t, signUpEmailVerificationKey.Email, *user.Email)
				assert.Equal(t, signUpEmailVerificationValue.FirstName, *user.FirstName)
				assert.Equal(t, signUpEmailVerificationValue.LastName, *user.LastName)
				assert.Equal(t, *signUpEmailVerificationValue.MarketingOptIn, *user.MarketingOptIn)
				assert.Equal(t, *signUpEmailVerificationValue.Timezone, *user.Timezone)
			})
		})
	})
}

func TestInsertUser(t *testing.T) {
	setUp()

	if err := hubble_db.MarsStatements.ValidateAll(context.Background(), hubble.MarsDB, "mars"); err != nil {
		panic(err)
	}

	t.Run("User already exists - errors with code 23505", func(t *testing.T) {
		// Arrange
		// This test isn't picking up the seed data, so we need to insert a user first
		// idk why :shrug:
		createUserRequest := hubble_db.InsertUserRequest{
			Email:          "<EMAIL>",
			FirstName:      "John",
			LastName:       lo.ToPtr("Doe"),
			RoleIDs:        []models.USER_ROLE{models.USER_ROLE_SESSION_OWNER},
			MarketingOptIn: true,
		}

		_, err := hubble_db.InsertUser(context.Background(), createUserRequest)
		assert.NoError(t, err)

		// Act
		_, err = hubble_db.InsertUser(context.Background(), createUserRequest)

		// Assert
		assert.Error(t, err)
		var dbErr *pgconn.PgError
		errors.As(err, &dbErr)
		assert.Equal(t, "23505", dbErr.Code)
		assert.Equal(t, "USER_EMAIL_INDEX", dbErr.ConstraintName)
	})
}
