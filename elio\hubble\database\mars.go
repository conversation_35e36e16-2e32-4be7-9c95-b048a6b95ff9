package database

import (
	"context"
	"encore.app/hubble/models"
	"encore.app/pkg/database"
	"github.com/samber/lo"
	"strings"
	"time"
)

var RecurrenceByRecurrenceIDQry = database.NewStatement(`
	SELECT "id", "recurrenceID", "dataVisibility" FROM sessions_recurrences WHERE "recurrenceID"=:recurrenceID
`)

var SessionBySessionIDQry = database.NewStatement(`
	SELECT "id", "recurrenceID", "dataVisibility" FROM sessions WHERE id=:sessionID
`)

var TeamByRecurrenceIDQry = database.NewStatement(`
	SELECT t.id from sessions_recurrences sr
	LEFT JOIN users us ON us.id = sr."primaryHostUserID"
	LEFT JOIN team_members tm ON tm."userID" = us.id
	LEFT JOIN teams t ON t.id = tm."teamID"
	WHERE sr."recurrenceID" = :recurrenceID
`)

var HostsQry = database.NewStatement(`
	SELECT "hostUserIDs", "recurrenceID" FROM sessions WHERE "id"=:sessionID
`)

var TeamMemberUserIdsByTeamIDQry = database.NewStatement(`
	SELECT tm."userID", tm.role FROM team_members tm WHERE tm."teamID" = :teamID
`)

var AccessQry = database.NewStatement(`
	SELECT "restrictionStatus", "type", "value" FROM session_access_rules WHERE "sessionID"=:sessionID AND "sessionRecurrenceID"=:recurrenceID
`)

var IDQry = database.NewStatement(`
	SELECT id_generator()
`)

var UpdateUserRoleIDsAppendRoleCmd = database.NewStatement(`
	UPDATE users SET
		"roleIDs" = array_append("roleIDs", :roleID),
		"updatedAt" = :updatedAt
	WHERE id = :id
`)

var InsertUserCmd = database.NewStatement(`
	INSERT INTO users (
		id, email, "firstName", "lastName", "marketingOptIn", "timezone", "signUpTimestamp", "roleIDs",
	  "createdAt", "updatedAt"
	) VALUES (
		:id, :email, :firstName, :lastName, :marketingOptIn, :timezone, :signUpTimestamp, array[:roleIDs],
		:createdAt, :updatedAt
	)
`)

var MarsStatements = database.Statements{
	SessionBySessionIDQry,
	RecurrenceByRecurrenceIDQry,
	TeamByRecurrenceIDQry,
	HostsQry,
	TeamMemberUserIdsByTeamIDQry,
	AccessQry,
	IDQry,
	UpdateUserRoleIDsAppendRoleCmd,
	InsertUserCmd,
}

type Args = map[string]interface{}

func UpdateUserRoleIDsAppendRole(ctx context.Context, userID string, roleID models.USER_ROLE) error {
	_, err := UpdateUserRoleIDsAppendRoleCmd.Execx(ctx, Args{
		"id":        userID,
		"roleID":    roleID,
		"updatedAt": time.Now().Unix(),
	})
	return err
}

type InsertUserRequest struct {
	Email           string
	FirstName       string
	LastName        *string
	MarketingOptIn  bool
	Timezone        *string
	SignUpTimestamp int64
	RoleIDs         []models.USER_ROLE
}

func InsertUser(ctx context.Context, req InsertUserRequest) (*int64, error) {
	id, err := GenID(ctx)
	if err != nil {
		return nil, err
	}

	roleIDs := strings.Join(lo.Map(req.RoleIDs, func(role models.USER_ROLE, _ int) string {
		return string(role)
	}), ",")

	_, err = InsertUserCmd.Execx(ctx, database.Args{
		"id":              &id,
		"email":           req.Email,
		"firstName":       req.FirstName,
		"lastName":        req.LastName,
		"marketingOptIn":  req.MarketingOptIn,
		"timezone":        req.Timezone,
		"signUpTimestamp": req.SignUpTimestamp,
		"roleIDs":         roleIDs,
		"createdAt":       time.Now().Unix(),
		"updatedAt":       time.Now().Unix(),
	})
	if err != nil {
		return nil, err
	}

	return id, nil
}

func GenID(ctx context.Context) (*int64, error) {
	var id int64
	err := IDQry.GetValues(ctx, database.Args{}, &id)
	if err != nil {
		return nil, err
	}
	return &id, nil
}
