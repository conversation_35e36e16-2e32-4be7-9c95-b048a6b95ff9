{% assign suggestions_data = suggestions %}
{% if response.data.content.suggestions %}
  {% assign suggestions_data = response.data.content.suggestions %}
{% endif %}

{% if suggestions_data and suggestions_data.size > 0 %}
<!-- Suggested Queries Section -->
<div style="background:#ffffff;background-color:#ffffff;margin:0px auto;max-width:600px;margin-top:20px;">
  <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:#ffffff;background-color:#ffffff;width:100%;">
    <tbody>
      <tr>
        <td align="left" style="font-size:0px;padding:10px 25px;word-break:break-word;">
          
          <!-- Title -->
          <div style="font-family:Poppins, Helvetica, Arial, sans-serif;font-size:18px;font-weight:600;line-height:24px;text-align:left;color:#000000;margin-bottom:16px;">
            Try with Meeting Memory™:
          </div>
          
          <!-- Simple List of Suggestions -->
          <div style="font-family:Poppins, Helvetica, Arial, sans-serif;font-size:16px;font-weight:400;line-height:28px;text-align:left;color:#000000;">
            <ul style="margin-top:2px;margin-bottom:8px;">
              {% for suggestion in suggestions_data %}
              <li>
                <a href="{{recording_url}}?prompt={{ suggestion | url_encode }}" style="color:#000000;text-decoration:underline;" target="_blank">
                  {{ suggestion }}
                </a>
              </li>
              {% endfor %}
            </ul>
          </div>
          
        </td>
      </tr>
    </tbody>
  </table>
</div>
{% endif %} 