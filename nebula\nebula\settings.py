import enum
from pathlib import Path
from tempfile import gettempdir
from typing import Optional

from dotenv import find_dotenv, load_dotenv
from pydantic_settings import BaseSettings
import base64

TEMP_DIR = Path(gettempdir())

load_dotenv(find_dotenv(".env"))


class LogLevel(str, enum.Enum):  # noqa: WPS600
    """Possible log levels."""

    NOTSET = "NOTSET"
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    FATAL = "FATAL"

class Settings(BaseSettings):
    """
    Application settings.

    These parameters can be configured
    with environment variables.
    """

    host: str = "127.0.0.1"
    port: int = 8000

    # quantity of workers for uvicorn
    workers_count: int = 3
    # Enable uvicorn reloading
    reload: bool = False

    # Current environment
    environment: str = "local"

    enable_swagger_docs: bool = True

    log_level: LogLevel = LogLevel.INFO

    basic_auth_username: str = "nebula"
    basic_auth_password: str = "nebula_pass"

    # Variables for the database
    postgres_host: str = "localhost"
    postgres_port: str = "5432"
    postgres_user: str = "nebula"
    postgres_pass: str = "nebula"
    postgres_db: str = "nebula"
    postgres_schema: str = "public"
    postgres_echo: bool = False
    db_require_ssl: bool = True  # Default to True for security, disable only for local/testing

    kafka_bootstrap_servers: str = "localhost:9092"
    kafka_ssl: bool = False
    kafka_sasl_username: Optional[str] = None
    kafka_sasl_password: Optional[str] = None
    kafka_sasl_mechanism: str = "PLAIN"
    kafka_topic_batch_transcriptions: str = "sessions-live-transcriptions-batch-local"

    luxor_db_user: str = "default_user"
    luxor_db_password: str = "default_password"
    luxor_db_database: str = "default_database"
    luxor_db_host: str = "default_host"
    luxor_db_schema: str = "public"

    mars_db_user: str = "default_user"
    mars_db_password: str = "default_password"
    mars_db_host: str = "default_host"
    mars_db_database: str = "mars"
    mars_db_schema: str = "public"  # Neon default schema

    temporal_host: Optional[str] = "localhost"
    temporal_port: int = 7233

    temporal_namespace: str = "default"

    # Temporal encryption key for securing workflow data
    temporal_encryption_key: Optional[str] = None

    # Not needed for local development
    # On remote environments, these are base64 encoded certs
    temporal_client_cert: Optional[str] = None
    temporal_client_private_key: Optional[str] = None
    temporal_server_root_ca_cert: Optional[str] = None

    temporal_ai_end_meeting_queue: str = "ai-end-meeting"
    temporal_queue_prefix: Optional[str] = None

    draconids_auth_username: str = "default_user"
    draconids_auth_password: str = "default_password"
    draconids_base_url: str = "default_url"

    luxor_base_url: str = ""
    luxor_basic_auth_username: str = "trixta"
    luxor_basic_auth_password: str = "password"

    elio_base_url: str = "http://localhost:4000"
    web_app_base_url: str = "https://rumi.ai"

    aws_region: str = "us-east-1"
    aws_access_key_id: Optional[str] = None
    aws_secret_access_key: Optional[str] = None

    # OpenAI API keys for different product features in order to track usage
    openai_feed_api_key: str = ""
    openai_pms_api_key: str = ""
    openai_mm_api_key: str = "default_mm_api_key"
    openai_metadata_api_key: str = "default_metadata_api_key"
    openai_xray_api_key: str = "default_xray_api_key"

    openai_ai_feed_model: str = "gpt-4.1"
    openai_pms_model: str = "gpt-4.1"
    openai_rag_model: str = "gpt-4.1"
    openai_rag_steps_model: str = "gpt-4.1-nano"
    openai_metadata_model: str = "gpt-4.1"
    openai_xray_model: str = "gpt-4.1"

    typesense_protocol: str = "http"
    typesense_host: str = "localhost"
    typesense_port: str = "8108"
    typesense_admin_api_key: str = "xyz"
    typesense_connection_timeout_secs: int = 2
    typesense_rag_top_k: int = 950
    embeddings_model_name: str = "all-mpnet-base-v2"  # sentence-transformer model

    cohere_api_key: str = "defualt-key"
    cohere_embed_model: str = "embed-multilingual-v3.0"
    cohere_rerank_model: str = "rerank-v3.5"
    cohere_chat_model: str = "command-r-plus"
    cohere_rerank_top_n: int = 100

    mars_base_url: str = ""
    mars_basic_auth_username: str = ""
    mars_basic_auth_password: str = ""

    @property
    def kafka_bootstrap_servers_list(self) -> list[str]:
        """
        Kafka bootstrap servers as list.

        :return: list of kafka bootstrap servers.
        """
        return self.kafka_bootstrap_servers.split(",")

    @property
    def elio_basic_auth_header(self) -> str:
        """
        Basic auth base64 encoded string for Elio.

        :return: base64 encoded string.
        """
        return (
            "Basic "
            + base64.b64encode(
                f"{self.luxor_basic_auth_username}:{self.luxor_basic_auth_password}".encode()
            ).decode()
        )

    @property
    def temporal_ai_end_meeting_queue_with_prefix(self) -> str:
        """
        Temporal AI end meeting queue name with prefix if available.

        :return: prefixed queue name or original queue name.
        """
        if self.temporal_queue_prefix:
            return f"{self.temporal_queue_prefix}-{self.temporal_ai_end_meeting_queue}"
        return self.temporal_ai_end_meeting_queue

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "allow"


settings = Settings()
