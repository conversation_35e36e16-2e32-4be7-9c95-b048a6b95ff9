# GETXrayGetXRayNotifications200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**data** | [**ApiGetXRayNotificationsResponseData**](ApiGetXRayNotificationsResponseData.md) |  | 
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.get_xray_get_x_ray_notifications200_response import GETXrayGetXRayNotifications200Response

# TODO update the JSON string below
json = "{}"
# create an instance of GETXrayGetXRayNotifications200Response from a JSON string
get_xray_get_x_ray_notifications200_response_instance = GETXrayGetXRayNotifications200Response.from_json(json)
# print the JSON string representation of the object
print(GETXrayGetXRayNotifications200Response.to_json())

# convert the object into a dict
get_xray_get_x_ray_notifications200_response_dict = get_xray_get_x_ray_notifications200_response_instance.to_dict()
# create an instance of GETXrayGetXRayNotifications200Response from a dict
get_xray_get_x_ray_notifications200_response_from_dict = GETXrayGetXRayNotifications200Response.from_dict(get_xray_get_x_ray_notifications200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


