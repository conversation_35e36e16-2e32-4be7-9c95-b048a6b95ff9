package controllers

import (
	"encoding/base64"
	"encoding/json"
	"encore.app/hubble"
	"encore.app/hubble/api"
	"encore.app/shared"
	mars_api "encore.app/wormhole/services/mars-api"
	"errors"
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

type BillingController struct {
	marsClient   *mars_api.Client
	marsHost     string
	marsUser     string
	marsPassword string
}

type BillingProxyDestinationEndpoint struct {
	Pattern *regexp.Regexp
	Method  string
	Public  bool
}

var allowedBillingEndpoints = []BillingProxyDestinationEndpoint{
	{
		Pattern: regexp.MustCompile("/v1.0/billing/plans"),
		Method:  "GET",
		Public:  true,
	},
	{
		Pattern: regexp.MustCompile("\\bbilling/plans/\\w+"),
		Method:  "GET",
		Public:  true,
	},
}

func (c *BillingController) Init(marsClient *mars_api.Client, host, user, password string) *BillingController {
	c.marsClient = marsClient
	c.marsHost = host
	c.marsUser = user
	c.marsPassword = password
	return c
}

func (c *BillingController) getEndpointIfWhitelisted(url *url.URL) *BillingProxyDestinationEndpoint {
	for i := 0; i < len(allowedBillingEndpoints); i++ {
		if allowedBillingEndpoints[i].Pattern.MatchString(url.Path) {
			return &allowedBillingEndpoints[i]
		}
	}

	return nil
}

// ProxyMars Proxy responsible for proxying all requests prefixed with "billing" to Mars
func (c *BillingController) ProxyMars(ctx *gin.Context) {
	endpoint := c.getEndpointIfWhitelisted(ctx.Request.URL)

	if endpoint == nil {
		ctx.AbortWithStatusJSON(http.StatusNotFound, gin.H{
			"message": "Bad request",
		})
		return
	}

	isAuthorizedResult, err := c.authorizeBillingProxyEndpoint(ctx, *endpoint, c.getBillingProxyEndpointUserAction(ctx))

	if err != nil && !c.isUnauthorizedErr(err) {
		log.Logger.Error().Err(err).Msg("Authorizing billing mars request failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	if !isAuthorizedResult.isAuthorized {
		ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return
	}

	remote, err := url.Parse(c.marsHost)
	if err != nil {
		log.Logger.Error().Err(err).Msg("Error parsing mars host url")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	proxy := httputil.NewSingleHostReverseProxy(remote)
	// Define the director func
	proxy.Director = func(req *http.Request) {
		req.Host = remote.Host
		req.URL.Scheme = remote.Scheme
		req.URL.Host = remote.Host
		req.URL.Path = ctx.Copy().Request.URL.Path
		req.URL.RawQuery = ctx.Copy().Request.URL.RawQuery

		if ctx.Request.Method != "GET" {
			req.Body = ctx.Copy().Request.Body
		}

		// Only include whitelisted headers
		for h := range req.Header {
			if !whitelistedHeaders[h] {
				req.Header.Del(h)
			}
		}

		authCredentials := c.marsUser + ":" + c.marsPassword
		authHeaderValue := fmt.Sprintf("Basic %s", base64.StdEncoding.EncodeToString([]byte(authCredentials)))
		req.Header.Add("Authorization", authHeaderValue)
	}

	ctx.Writer.Header().Del("Access-Control-Allow-Origin") // Clients do not accept duplicates.
	proxy.ServeHTTP(ctx.Writer, ctx.Request)
}

// Individual requests

func (c *BillingController) ActivateTrial(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	// Hubble HTTP: Append session_owner role to the user
	hostOptInResponseBody, err := hubble.HostOptIn(ctx)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Activate trial host opt in request failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	var marsErr *shared.HttpErrorResp
	e := errors.As(err, &marsErr)
	if e && marsErr.ErrorDetail.StatusCode >= http.StatusBadRequest {
		ctx.AbortWithStatusJSON(marsErr.ErrorDetail.StatusCode, marsErr)
		return
	}

	userID := hostOptInResponseBody.Data.UserID

	// Mars HTTP: Activate trial
	activateTrialResponse, err := c.marsClient.UserControllerActivateTrial(ctx, hostOptInResponseBody.Data.UserID)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", userID).Msg("Activating trial failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if activateTrialResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(activateTrialResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	activateTrialResponseBody, err := mars_api.ParseUserControllerActivateTrialResponse(activateTrialResponse)
	if err != nil || activateTrialResponseBody == nil {
		log.Logger.Error().Err(err).Str("UserID", userID).Msg("Parsing activate trial response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message": activateTrialResponseBody.JSON200.Message,
		"success": activateTrialResponseBody.JSON200.Success,
		"data": gin.H{
			"userSubscriptionPlan": activateTrialResponseBody.JSON200.Data,
			"authToken":            hostOptInResponseBody.Data.AuthToken,
			"refreshToken":         hostOptInResponseBody.Data.RefreshToken,
			"userID":               hostOptInResponseBody.Data.UserID,
		},
	})
}

func (c *BillingController) GetUserSubscriptionPlan(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	parsedParams := mars_api.UserControllerGetUserSubscriptionPlanParams{}
	includeQueryParam := ctx.Query("include")
	if includeQueryParam != "" {
		parsedParams.Include = &includeQueryParam
	}

	userSubscriptionPlanResponse, err := c.marsClient.UserControllerGetUserSubscriptionPlan(ctx, authorizeResult.userID, &parsedParams)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Getting user subscription plan failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if userSubscriptionPlanResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(userSubscriptionPlanResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	userSubscriptionPlanResponseBody, err := mars_api.ParseUserControllerGetUserSubscriptionPlanResponse(userSubscriptionPlanResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing get user subscription plan response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}
	ctx.JSON(http.StatusOK, userSubscriptionPlanResponseBody.JSON200)
}

func (c *BillingController) CancelUserSubscriptionPlan(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	cancelUserSubscriptionPlanResponse, err := c.marsClient.UserControllerCancelUserSubscriptionPlan(ctx, authorizeResult.userID)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Canceling user subscription plan failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if cancelUserSubscriptionPlanResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(cancelUserSubscriptionPlanResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	cancelUserSubscriptionPlanResponseBody, err := mars_api.ParseUserControllerCancelUserSubscriptionPlanResponse(cancelUserSubscriptionPlanResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing cancel user subscription plan response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}
	ctx.JSON(http.StatusOK, cancelUserSubscriptionPlanResponseBody.JSON200)
}

func (c *BillingController) ReactivateUserSubscriptionPlan(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	reactivateUserSubscriptionPlanResponse, err := c.marsClient.UserControllerReactivateUserSubscriptionPlan(ctx, authorizeResult.userID)

	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Reactivating user subscription plan failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if reactivateUserSubscriptionPlanResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(reactivateUserSubscriptionPlanResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	reactivateUserSubscriptionPlanResponseBody, err := mars_api.ParseUserControllerReactivateUserSubscriptionPlanResponse(reactivateUserSubscriptionPlanResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing reactivate user subscription plan response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}
	ctx.JSON(http.StatusOK, reactivateUserSubscriptionPlanResponseBody.JSON200)
}

func (c *BillingController) UpdateUserSubscriptionPlan(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	reqBody := mars_api.UserControllerUpdateUserSubscriptionPlanJSONRequestBody{}
	requestBodyBytes, err := ctx.GetRawData()
	err = json.Unmarshal(requestBodyBytes, &reqBody)

	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing update user subscription plan request body failed")
		ctx.AbortWithStatus(http.StatusInternalServerError)
		return
	}

	updateUserSubscriptionPlanResponse, err := c.marsClient.UserControllerUpdateUserSubscriptionPlan(ctx, authorizeResult.userID, reqBody)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Updating user subscription plan failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if updateUserSubscriptionPlanResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(updateUserSubscriptionPlanResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	updateUserSubscriptionPlanResponseBody, err := mars_api.ParseUserControllerUpdateUserSubscriptionPlanResponse(updateUserSubscriptionPlanResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing update user subscription plan response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}
	ctx.JSON(http.StatusOK, updateUserSubscriptionPlanResponseBody.JSON200)
}

func (c *BillingController) GetUserPaymentMethodDetails(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	userPaymentMethodDetailsResponse, err := c.marsClient.UserControllerGetUserPaymentMethodDetails(ctx, authorizeResult.userID)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Getting user payment method details failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if userPaymentMethodDetailsResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(userPaymentMethodDetailsResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	userPaymentMethodDetailsResponseBody, err := mars_api.ParseUserControllerGetUserPaymentMethodDetailsResponse(userPaymentMethodDetailsResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing get user payment method details response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	ctx.JSON(http.StatusOK, userPaymentMethodDetailsResponseBody.JSON200)
}

func (c *BillingController) GetUpdatePaymentMethodTransaction(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	getUpdatePaymentMethodTransactionResponse, err := c.marsClient.UserControllerGetUpdatePaymentMethodTransaction(ctx, authorizeResult.userID)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Getting update payment method transaction failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if getUpdatePaymentMethodTransactionResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(getUpdatePaymentMethodTransactionResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	getUpdatePaymentMethodTransactionResponseBody, err := mars_api.ParseUserControllerGetUpdatePaymentMethodTransactionResponse(getUpdatePaymentMethodTransactionResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing get update payment method transaction response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	ctx.JSON(http.StatusOK, getUpdatePaymentMethodTransactionResponseBody.JSON200)
}

func (c *BillingController) GetUserTransactions(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	includeInvoicesQueryParam := ctx.Query("includeInvoices")
	limitQueryParam := ctx.Query("limit")
	cursorParam := ctx.Query("cursor")

	params := mars_api.UserControllerGetUserTransactionsParams{}

	if includeInvoicesQueryParam != "" {
		params.IncludeInvoices = &includeInvoicesQueryParam
	}

	if limitQueryParam != "" {
		params.Limit = &limitQueryParam
	}

	if cursorParam != "" {
		params.Cursor = &cursorParam
	}

	userTransactionsResponse, err := c.marsClient.UserControllerGetUserTransactions(ctx, authorizeResult.userID, &params)

	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Getting user transactions failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}

	if userTransactionsResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(userTransactionsResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	userTransactionsResponseBody, err := mars_api.ParseUserControllerGetUserTransactionsResponse(userTransactionsResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing get user transactions response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	ctx.JSON(http.StatusOK, userTransactionsResponseBody.JSON200)
}

func (c *BillingController) GetUserTransactionInvoice(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	transactionID := ctx.Param("transactionID")
	if !authorizeResult.isAuthorized {
		return
	}

	getTransactionInvoiceResponse, err := c.marsClient.UserControllerGetUserTransactionInvoice(ctx, authorizeResult.userID, transactionID)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Getting user transaction invoice failed")
		ctx.AbortWithError(http.StatusInternalServerError, err)
		return
	}
	if getTransactionInvoiceResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(getTransactionInvoiceResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	getUserTransactionInvoiceResponseBody, err := mars_api.ParseUserControllerGetUserTransactionInvoiceResponse(getTransactionInvoiceResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing get user transaction invoice response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	ctx.JSON(http.StatusOK, getUserTransactionInvoiceResponseBody.JSON200)
}

func (c *BillingController) CreateCustomer(ctx *gin.Context) {
	authorizeResult := c.authorizeUserBillingEndpoint(ctx, shared.AuthPolicyActionUpdate)
	if !authorizeResult.isAuthorized {
		return
	}

	createCustomerResponse, err := c.marsClient.UserControllerCreateCustomer(ctx, authorizeResult.userID)

	if err != nil {
		ctx.AbortWithError(http.StatusInternalServerError, err)
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Creating customer failed")
		return
	}
	if createCustomerResponse.StatusCode >= http.StatusBadRequest {
		errResp := mars_api.MarsErrorMapping{}
		errResp.Map(createCustomerResponse)
		ctx.AbortWithStatusJSON(errResp.StatusCode, errResp.Error)
		return
	}

	createCustomerResponseBody, err := mars_api.ParseUserControllerCreateCustomerResponse(createCustomerResponse)
	if err != nil {
		log.Logger.Error().Err(err).Str("UserID", authorizeResult.userID).Msg("Parsing create customer response failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return
	}

	ctx.JSON(http.StatusOK, createCustomerResponseBody.JSON200)
}

// Helpers

type AuthorizeResult struct {
	isAuthorized bool
	userID       string
}

func (c *BillingController) authorize(ctx *gin.Context, userAction shared.AuthPolicyActions, userID *string) (AuthorizeResult, error) {
	userByToken, _ := hubble.UserByToken(ctx.Request.Context(), &api.GetUserByTokenRequest{Token: ctx.Request.Header.Get("Authorization")})
	if userByToken == nil {
		return AuthorizeResult{isAuthorized: false, userID: ""}, errors.New("unauthorized")
	}

	if userID != nil && *userID != *userByToken.Data.AuthUser.Id {
		return AuthorizeResult{isAuthorized: false, userID: *userID}, errors.New("unauthorized")
	}

	err := hubble.IsAuthorized(ctx.Request.Context(), &api.IsAuthorizedRequest{
		Sub: shared.GetUserSubjectWithRole(*userByToken.Data.AuthUser.Id),
		Obj: shared.GetUserBillingObjectByID(*userByToken.Data.AuthUser.Id),
		Act: userAction,
	})

	if err != nil {
		return AuthorizeResult{isAuthorized: false, userID: *userByToken.Data.AuthUser.Id}, err
	}

	return AuthorizeResult{isAuthorized: true, userID: *userByToken.Data.AuthUser.Id}, err
}

func (c *BillingController) authorizeBillingProxyEndpoint(ctx *gin.Context, endpoint BillingProxyDestinationEndpoint, userAction shared.AuthPolicyActions) (AuthorizeResult, error) {
	if endpoint.Public {
		// userByTokenResponse := hubble_api.GetUserByToken(ctx, c.hubbleClient)
		return AuthorizeResult{isAuthorized: true, userID: ""}, nil
	}
	return c.authorize(ctx, userAction, nil)
}

func (c *BillingController) authorizeUserBillingEndpoint(ctx *gin.Context, userAction shared.AuthPolicyActions) AuthorizeResult {
	userID := ctx.Param("userID")
	if userID == "" {
		return AuthorizeResult{isAuthorized: false, userID: ""}
	}

	authorizeResult, err := c.authorize(ctx, userAction, &userID)

	if err != nil && !c.isUnauthorizedErr(err) {
		log.Logger.Error().Err(err).Msg("Authorizing billing mars request failed")
		ctx.AbortWithStatusJSON(http.StatusInternalServerError, gin.H{
			"message": "Internal server error",
		})
		return authorizeResult
	}

	if !authorizeResult.isAuthorized || authorizeResult.userID == "" {
		ctx.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
			"message": "Unauthorized",
		})
		return authorizeResult
	}

	return authorizeResult
}

func (c *BillingController) getBillingProxyEndpointUserAction(ctx *gin.Context) shared.AuthPolicyActions {
	if ctx.Request.Method == "DELETE" {
		return shared.AuthPolicyActionDelete
	}

	return shared.AuthPolicyActionGet
}

func (c *BillingController) isUnauthorizedErr(err error) bool {
	return strings.Contains(err.Error(), "unauthorized")
}
