package recallai

import "time"

type RecallSDK struct {
	Token          string // Our authentication token, from our `Config()`
	RegionEndpoint string // We use us-east-1 since thats where our account was provisioned. Ex: "https://us-east-1.recall.ai/api/"
}

type DeepgramOptions struct {
	Punctuate      bool     `json:"punctuate"`       // Add punctuation to transcribed text
	Encoding       string   `json:"encoding"`        // Audio encoding format (e.g., "linear16")
	SampleRate     int      `json:"sample_rate"`     // Audio sample rate in Hz (e.g., 96000)
	Model          string   `json:"model"`           // Deepgram model to use (e.g., "nova-3")
	Language       string   `json:"language"`        // Language code for transcription (e.g., "en-US")
	SmartFormat    bool     `json:"smart_format"`    // Apply smart formatting to output
	InterimResults bool     `json:"interim_results"` // Send partial transcription results while speaking
	Channels       int      `json:"channels"`        // Number of audio channels
	VadEvents      bool     `json:"vad_events"`      // Enable voice activity detection events
	Endpointing    int      `json:"endpointing"`     // Milliseconds of silence before finalizing utterance
	FillerWords    bool     `json:"filler_words"`    // Include filler words like "um", "uh" in transcript
	Replace        []string `json:"replace"`         // Word replacement pairs (e.g., ["oldword:newword"])
}

type TranscriptionOptions struct {
	Provider string          `json:"provider"`           // In our case it's always "deepgram", this is a required field.
	Deepgram DeepgramOptions `json:"deepgram,omitempty"` // Deepgram specific options
}

type RealTimeTranscriptionConfig struct {
	DestinationUrl      string `json:"destination_url"`                // the endpoint receiving the transcription updates.  This is a required field
	PartialResults      bool   `json:"partial_results,omitempty"`      // should we receive results even if the speaker is still monolging?
	EnhancedDiarization bool   `json:"enhanced_diarization,omitempty"` // Required for us to take advantage of Deepgram integration
}

type GoogleMeetOptions struct {
	LoginRequired      bool   `json:"login_required"`        // Whether the bot requires login to join the meeting
	GoogleLoginGroupId string `json:"google_login_group_id"` // The Google login group ID to use for the bot
}

type CreateBotParams struct {
	BotName               string                      `json:"bot_name,omitempty"`                // The name, used when joining other meetings.
	JoinAt                string                      `json:"join_at,omitempty"`                 // An ISO formatted string representing the time to join a scheduled meeting
	RealTimeTranscription RealTimeTranscriptionConfig `json:"real_time_transcription,omitempty"` // Required to turn on transcriptions from the Bot
	TranscriptionOptions  TranscriptionOptions        `json:"transcription_options,omitempty"`   // Required to turn on transcriptions from the Bot
	GoogleMeet            GoogleMeetOptions           `json:"google_meet,omitempty"`             // Required to turn on Google Meet integration
	MeetingURL            string                      `json:"meeting_url,omitempty"`             // The URL of the meeting to join
}

type UpdateBotParams struct {
	Id    string          `json:"id"`
	Patch CreateBotParams `json:"patch"` // The changes you want to effect on the already created bot
}

type DeleteBotParams struct {
	Id string `json:"id"`
}

type CalendarAuthTokenParams struct {
	UserId string `json:"user_id"` // This needs to be a unique id representing a Rumi user.
}

type CalendarMeetingParams struct {
	Id string `json:"id"`
}

// Response data shapes:
// https://docs.recall.ai/reference/bot_create
type MeetingMetadata struct {
	Title           string `json:"title"`
	ZoomMeetingUUID string `json:"zoom_meeting_uuid,omitempty"` // Only included if it's a Zoom meeting
}

type MeetingParticipant struct {
	Id       int64  `json:"id"`
	Name     string `json:"name"`
	IsHost   bool   `json:"is_host"`
	Platform string `json:"platform"`
}

type RecallConnection struct {
	Connected bool   `json:"connected"`
	Platform  string `json:"platform"`
	Email     string `json:"email"`
}

type CalendarUser struct {
	Id          string               `json:"id"`
	ExternalId  string               `json:"external_id"`
	Connections []RecallConnection   `json:"connections"`
	Preferences RecordingPreferences `json:"preferences"`
}

type CalendarMeeting struct {
	Id           string       `json:"id"`
	StartTime    string       `json:"start_time"`
	EndTime      string       `json:"end_time"`
	CalendarUser CalendarUser `json:"calendar_user"`
}

type BotMeetingLink struct {
	MeetingId string `json:"meeting_id"`
	Platform  string `json:"platform"`
}

type BotResponse struct {
	Id                  string               `json:"id"`
	MeetingUrl          BotMeetingLink       `json:"meeting_url"`
	MeetingMetadata     MeetingMetadata      `json:"meeting_metadata"`
	MeetingParticipants []MeetingParticipant `json:"meeting_participants"`
	CalendarMeetings    []CalendarMeeting    `json:"calendar_meetings"`
	VideoUrl            string               `json:"video_url"`
}

type CalendarAuthTokenResponse struct {
	Token string `json:"token"` // Auth token associated with the user specified. Last for a day starting at creation time.
}

type ZoomInvite struct {
	MeetingId       string `json:"meeting_id"`
	MeetingPassword string `json:"meeting_password,omitempty"`
}

type TeamsInvite struct {
	OrganizerId string `json:"organizer_id"`
	TenantId    string `json:"tenant_id"`
	MessageId   string `json:"message_id"`
	ThreadId    string `json:"thread_id"`
}

type MeetInvite struct {
	MeetingId string `json:"meeting_id"`
}

type MeetingAttendee struct {
	Name        string `json:"name"`
	Email       string `json:"email"`
	IsOrganizer string `json:"is_organizer"`
	Status      string `json:"status"` // One of: ["accepted", "declined", "tentative", "not_available"]
}

// About `CalendarMeetingResponse.Visibility`:
// For Google calendar meetings('default' | 'public' | 'private' | 'confidential').
// For Microsoft Outlook('normal' | 'personal' | 'private' | 'confidential').
// Additionally, this field may contain 'null' value.
type CalendarMeetingResponse struct {
	Id               string            `json:"id"`
	Title            string            `json:"title"`
	WillRecord       bool              `json:"will_record"`
	WillRecordReason string            `json:"will_record_reason"`
	StartTime        time.Time         `json:"start_time"` // the docs say this is a "date-time"? What does that mean for us, a string or time?
	EndTime          time.Time         `json:"end_time"`
	Platform         string            `json:"platform"`
	MeetingPlatform  string            `json:"meeting_platform"`
	CalendarPlatform string            `json:"calendar_platform"`
	ZoomInvite       ZoomInvite        `json:"zoom_invite,omitempty"`
	TeamsInvite      TeamsInvite       `json:"teams_invite,omitempty"`
	MeetInvite       MeetInvite        `json:"meet_invite,omitempty"`
	BotId            string            `json:"bot_id,omitempty"` // This field can be null, which we omit.
	IsExternal       bool              `json:"is_external"`
	IsHostedByMe     bool              `json:"is_hosted_by_me"`
	IsRecurring      bool              `json:"is_recurring"`
	OrganizerEmail   string            `json:"organizer_email"`
	AttendeeEmails   []string          `json:"attendee_emails"`
	Attendees        []MeetingAttendee `json:"attendees"`
	ICalUid          string            `json:"ical_uid"`
	Visibility       string            `json:"visibility,omitempty"`
}

type DisconnectCalendarPlatformParams struct {
	// Should be the string 'google' or 'microsoft'
	Platform string `json:"platform"`
}

type RecordingPreferences struct {
	RecordNonHost   bool   `json:"record_non_host"`
	RecordRecurring bool   `json:"record_recurring"`
	RecordExternal  bool   `json:"record_external"`
	RecordInternal  bool   `json:"record_internal"`
	RecordConfirmed bool   `json:"record_confirmed"`
	RecordOnlyHost  bool   `json:"record_only_host"`
	BotName         string `json:"bot_name"`
}

type RecordingPreferencesParams struct {
	ExternalId  string               `json:"external_id,omitempty"`
	Preferences RecordingPreferences `json:"preferences"`
}

type GetCalendarUserParams struct {
	UserId string `json:"user_id"`
}
