# ApiGetUserPaymentMethodDetailsResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**last_payment** | [**SharedBillingPaymentResultDTO**](SharedBillingPaymentResultDTO.md) |  | 

## Example

```python
from elio_client.models.api_get_user_payment_method_details_response_data import ApiGetUserPaymentMethodDetailsResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiGetUserPaymentMethodDetailsResponseData from a JSON string
api_get_user_payment_method_details_response_data_instance = ApiGetUserPaymentMethodDetailsResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiGetUserPaymentMethodDetailsResponseData.to_json())

# convert the object into a dict
api_get_user_payment_method_details_response_data_dict = api_get_user_payment_method_details_response_data_instance.to_dict()
# create an instance of ApiGetUserPaymentMethodDetailsResponseData from a dict
api_get_user_payment_method_details_response_data_from_dict = ApiGetUserPaymentMethodDetailsResponseData.from_dict(api_get_user_payment_method_details_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


