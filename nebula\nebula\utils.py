import datetime
import decimal
import os
import random
from typing import Any, AsyncIterable, AsyncIterator, <PERSON>ple

import arrow

from nebula.constants import PROJECT_ROOT_MARKER


def float_to_decimal(dict_obj: dict[Any, Any]) -> dict[Any, Any]:
    """
    Convert all float values in a dictionary to Decimal values.

    :param dict_obj: The dictionary object

    :returns: The dictionary object with all float values converted to Decimal values
    """
    for key, value in dict_obj.items():
        if isinstance(value, float):
            dict_obj[key] = decimal.Decimal(str(value))
        elif isinstance(value, dict):
            dict_obj[key] = float_to_decimal(value)
        elif isinstance(value, list):
            for i, v in enumerate(value):  # noqa: WPS111
                if isinstance(v, dict):
                    value[i] = float_to_decimal(v)
    return dict_obj


def relative_tz_dt_from_unix(unix_ts: int, iana: str):
    utc = arrow.get(unix_ts)
    return utc.to(iana).format("YYYY-MM-DD HH:mm:ss")


def format_unix_ts(unix_timestamp: int, format: str = "%Y-%m-%d %H:%M:%S") -> str:
    try:
        # Throws value error if unix timestamp with miliseconds
        return datetime.datetime.fromtimestamp(unix_timestamp).strftime(format=format)  # type: ignore
    except ValueError:
        return datetime.datetime.fromtimestamp(unix_timestamp / 1000).strftime(
            format=format  # type: ignore
        )


def duration_str_from_secs(duration_seconds: int) -> str:
    hours, remainder = divmod(duration_seconds, 3600)
    minutes = divmod(remainder, 60)
    return f"{hours} hr, {minutes} minutes"


def find_project_root(current_dir):
    """
    Search for a directory containing the .project_root marker file,
    moving up the directory structure from the current directory.
    """
    while not os.path.exists(os.path.join(current_dir, PROJECT_ROOT_MARKER)):
        current_dir = os.path.dirname(current_dir)
        if current_dir == "" or os.path.ismount(current_dir):
            raise FileNotFoundError(f"Couldn't find the {PROJECT_ROOT_MARKER} marker.")
    return current_dir


def get_new_rag_doc_file_path(filename: str):
    """
    Generate the full path for a file in the rag directory.
    This function finds the project root and then appends the path to the rag dir.
    """
    # Start from the directory of this script
    test_dir = os.path.dirname(os.path.abspath(__file__))

    # Find the project root
    project_root = find_project_root(test_dir)

    # Construct the full path to the file
    return os.path.join(project_root, "rag", "index", filename)


def get_new_rag_file(filename: str):
    """
    Generate the full path for a file in the rag directory.
    This function finds the project root and then appends the path to the rag dir.
    """
    # Start from the directory of this script
    test_dir = os.path.dirname(os.path.abspath(__file__))

    # Find the project root
    project_root = find_project_root(test_dir)

    # Construct the full path to the file
    return os.path.join(project_root, "rag", filename)


def backoff_hdlr(details):
    print(
        "Backing off {wait:0.1f} seconds after {tries} tries "
        "calling function {target} with args {args} and kwargs "
        "{kwargs}".format(**details)
    )


def str_is_json(value: str) -> bool:
    return value.startswith("{") and value.endswith("}")


def rand_int(bits: int = 64) -> int:
    return random.getrandbits(bits)


def datetime_to_human_readable(dt: datetime.datetime):
    return dt.strftime("%A, %B %d, %Y at %I:%M %p")


def int_or_none_from_any(s: Any) -> int | None:
    try:
        v = int(s)
        return v
    except Exception:
        return None


async def async_enumerate(
    async_iterable: AsyncIterable, start: int = 0
) -> AsyncIterator[Tuple[int, Any]]:
    index = start
    async for item in async_iterable:
        yield index, item
        index += 1


def str_shrink_to_bytes(s: str, size: int):
    """
    Shrinks the input string `s` to fit within the specified byte size when encoded in UTF-8.

    :param s: The input string to be shrunk.
    :param size: The maximum size in bytes that the string should occupy when encoded in UTF-8.

    :returns: The shrunk string that fits within the specified byte size.
    """
    # If the string is already within the size, return it as is
    if len(s.encode("utf-8")) <= size:
        return s

    # Binary search for the maximum length that fits within `size`
    low, high = 0, len(s)
    while low < high:
        mid = (low + high + 1) // 2
        if len(s[:mid].encode("utf-8")) <= size:
            low = mid
        else:
            high = mid - 1

    return s[:low]


def to_camel(string: str) -> str:
    parts = string.split("_")
    return parts[0] + "".join(word.capitalize() for word in parts[1:])
