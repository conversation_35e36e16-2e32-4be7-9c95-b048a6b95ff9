# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr
from typing import Any, ClassVar, Dict, List
from typing import Optional, Set
from typing_extensions import Self

class POSTXrayCreateRequest(BaseModel):
    """
    POSTXrayCreateRequest
    """ # noqa: E501
    alert_channels: Dict[str, StrictBool] = Field(alias="alertChannels")
    description: StrictStr
    frequency: StrictStr
    icon: StrictStr
    prompt: StrictStr
    short_summary: StrictStr = Field(alias="shortSummary")
    title: StrictStr
    type: StrictStr
    __properties: ClassVar[List[str]] = ["alertChannels", "description", "frequency", "icon", "prompt", "shortSummary", "title", "type"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of POSTXrayCreateRequest from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of POSTXrayCreateRequest from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "alertChannels": obj.get("alertChannels"),
            "description": obj.get("description"),
            "frequency": obj.get("frequency"),
            "icon": obj.get("icon"),
            "prompt": obj.get("prompt"),
            "shortSummary": obj.get("shortSummary"),
            "title": obj.get("title"),
            "type": obj.get("type")
        })
        return _obj


