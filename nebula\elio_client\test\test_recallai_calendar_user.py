# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.recallai_calendar_user import RecallaiCalendarUser

class TestRecallaiCalendarUser(unittest.TestCase):
    """RecallaiCalendarUser unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> RecallaiCalendarUser:
        """Test RecallaiCalendarUser
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `RecallaiCalendarUser`
        """
        model = RecallaiCalendarUser()
        if include_optional:
            return RecallaiCalendarUser(
                connections = [
                    elio_client.models.recallai/recall_connection.recallai.RecallConnection(
                        connected = True, 
                        email = '', 
                        platform = '', )
                    ],
                external_id = '',
                id = '',
                preferences = elio_client.models.recallai/recording_preferences.recallai.RecordingPreferences(
                    bot_name = '', 
                    record_confirmed = True, 
                    record_external = True, 
                    record_internal = True, 
                    record_non_host = True, 
                    record_only_host = True, 
                    record_recurring = True, )
            )
        else:
            return RecallaiCalendarUser(
                connections = [
                    elio_client.models.recallai/recall_connection.recallai.RecallConnection(
                        connected = True, 
                        email = '', 
                        platform = '', )
                    ],
                external_id = '',
                id = '',
                preferences = elio_client.models.recallai/recording_preferences.recallai.RecordingPreferences(
                    bot_name = '', 
                    record_confirmed = True, 
                    record_external = True, 
                    record_internal = True, 
                    record_non_host = True, 
                    record_only_host = True, 
                    record_recurring = True, ),
        )
        """

    def testRecallaiCalendarUser(self):
        """Test RecallaiCalendarUser"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
