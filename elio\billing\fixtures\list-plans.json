{"success": true, "message": "Data fetched successfully", "data": [{"id": "pro_01hn31q5grky5xcyp58401kp1v", "name": "Premium", "description": "Premium ", "status": "active", "prices": [{"billingCycle": {"frequency": 1, "interval": "month"}, "id": "pri_01jby4g699bhkr9xt77yq8m7b8", "productID": "pro_01hn31q5grky5xcyp58401kp1v", "status": "active", "taxMode": "account_setting", "trialPeriod": {"frequency": 30, "interval": "day"}, "unitPrice": {"amount": "2900", "currencyCode": "USD"}, "unitPriceOverrides": [], "quantity": null}, {"billingCycle": {"frequency": 1, "interval": "month"}, "id": "pri_01hn32p3k1rcgzevmqjz1t9nej", "productID": "pro_01hn31q5grky5xcyp58401kp1v", "status": "active", "taxMode": "account_setting", "trialPeriod": null, "unitPrice": {"amount": "2900", "currencyCode": "USD"}, "unitPriceOverrides": [], "quantity": null}, {"billingCycle": {"frequency": 1, "interval": "month"}, "id": "pri_01hn32p3h4dze6k5bx5s9tznqc", "productID": "pro_01hn31q5grky5xcyp58401kp1v", "status": "archived", "taxMode": "account_setting", "trialPeriod": null, "unitPrice": {"amount": "999", "currencyCode": "USD"}, "unitPriceOverrides": [], "quantity": null}, {"billingCycle": {"frequency": 1, "interval": "year"}, "id": "pri_01hn32kvf4t0qqwdqeqpwcxrvt", "productID": "pro_01hn31q5grky5xcyp58401kp1v", "status": "active", "taxMode": "account_setting", "trialPeriod": null, "unitPrice": {"amount": "28800", "currencyCode": "USD"}, "unitPriceOverrides": [], "quantity": null}, {"billingCycle": {"frequency": 1, "interval": "month"}, "id": "pri_01hn32hdtj4pc4g2f6qha69wzn", "productID": "pro_01hn31q5grky5xcyp58401kp1v", "status": "active", "taxMode": "account_setting", "trialPeriod": {"frequency": 14, "interval": "day"}, "unitPrice": {"amount": "2900", "currencyCode": "USD"}, "unitPriceOverrides": [], "quantity": null}, {"billingCycle": {"frequency": 1, "interval": "year"}, "id": "pri_01hn32e9sswpvkm4kwbpx1shj9", "productID": "pro_01hn31q5grky5xcyp58401kp1v", "status": "active", "taxMode": "account_setting", "trialPeriod": {"frequency": 14, "interval": "day"}, "unitPrice": {"amount": "28800", "currencyCode": "USD"}, "unitPriceOverrides": [], "quantity": null}], "config": {"aiFeed": {"enabled": true}, "customFeedItems": {"enabled": false}, "customIntegrations": {"enabled": false}, "id": "943369326642071161", "integrations": {"apps": null, "enabled": true}, "meetingMemory": {"enabled": true}, "meetingSummary": {"enabled": true}, "meetingTemplates": {"enabled": false}, "meetingWorkflows": {"enabled": false}, "meetings": {"enabled": true, "max": 0}, "modelSegregation": {"enabled": false}, "offTheRecord": {"enabled": true}, "paddleProductID": "pro_01hn31q5grky5xcyp58401kp1v", "paddleProductName": "premium", "queueMode": {"enabled": true}, "recording": {"enabled": true, "local": true}, "stream": {"enabled": true, "quality": 1080}, "support": {"enabled": true, "type": "premium"}, "crm": {"enabled": false}, "bots": {"enabled": false}, "timeLimit": {"enabled": false, "max": 0}, "createdAt": 0, "updatedAt": 0}}]}