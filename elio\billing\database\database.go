package database

import (
	"context"
	"encore.app/pkg/database"
	"encore.app/shared"
	"github.com/jmoiron/sqlx"
)

// ListSubscriptionPlanConfigsByPaddleProductIDsQry
// This query should NOT be validated.
// It is handled differently due to the `IN` clause.
var ListSubscriptionPlanConfigsByPaddleProductIDsQry = `
	SELECT * FROM "subscription_plan_configs"
	WHERE "paddleProductID" IN (?)
`

var Statements = database.Statements{}

func ListSubscriptionPlanConfigsByPaddleProductIDs(ctx context.Context, db *sqlx.DB, paddleProductIDs []string) (*[]shared.SubscriptionPlanConfigRow, error) {
	var err error
	spcrs := []shared.SubscriptionPlanConfigRow{}

	query, args, err := sqlx.In(ListSubscriptionPlanConfigsByPaddleProductIDsQry, paddleProductIDs)
	query = db.Rebind(query)
	rows, err := db.QueryxContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	yield := func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var spcr shared.SubscriptionPlanConfigRow
		if scanErr := r.StructScan(&spcr); scanErr != nil {
			err = scanErr
			return false
		}

		spcrs = append(spcrs, spcr)
		return true
	}

	for rows.Next() {
		if !yield(rows, nil) {
			break
		}
	}

	return &spcrs, err
}
