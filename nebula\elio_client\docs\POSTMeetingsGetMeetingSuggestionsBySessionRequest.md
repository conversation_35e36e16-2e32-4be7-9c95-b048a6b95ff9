# POSTMeetingsGetMeetingSuggestionsBySessionRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**recurrence_id** | **str** |  | 
**session_id** | **str** |  | 

## Example

```python
from elio_client.models.post_meetings_get_meeting_suggestions_by_session_request import POSTMeetingsGetMeetingSuggestionsBySessionRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTMeetingsGetMeetingSuggestionsBySessionRequest from a JSON string
post_meetings_get_meeting_suggestions_by_session_request_instance = POSTMeetingsGetMeetingSuggestionsBySessionRequest.from_json(json)
# print the JSON string representation of the object
print(POSTMeetingsGetMeetingSuggestionsBySessionRequest.to_json())

# convert the object into a dict
post_meetings_get_meeting_suggestions_by_session_request_dict = post_meetings_get_meeting_suggestions_by_session_request_instance.to_dict()
# create an instance of POSTMeetingsGetMeetingSuggestionsBySessionRequest from a dict
post_meetings_get_meeting_suggestions_by_session_request_from_dict = POSTMeetingsGetMeetingSuggestionsBySessionRequest.from_dict(post_meetings_get_meeting_suggestions_by_session_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


