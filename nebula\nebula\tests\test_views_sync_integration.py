import asyncio
from unittest.mock import patch

import pytest
from fastapi import FastAP<PERSON>
from fastapi.testclient import TestClient

from nebula.web.api.search.views import router
from nebula.web.api.auth import authorize


# Fixture to create a test client
@pytest.fixture
def app():
    app = FastAPI()
    # Override auth dependency for testing
    app.dependency_overrides[authorize] = lambda: None
    app.include_router(router)
    return app


@pytest.fixture
def client(app):
    return TestClient(app)


def test_ask_ai_sync_integration(client):
    async def mock_rag(body, send, user_id):
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"text_generation_delta","data":{"content":"Hello"}}',
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"text_generation_delta","data":{"content":" world!"}}',
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"sources_updated","data":{"sources":[{"recurrenceId":123}]}}',
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"stream_ended"}',
            }
        )

    test_body = {
        "requestId": "test-integration-request",
        "query": "integration test query",
        "threadId": "integration-thread-123",
    }

    with patch("nebula.web.api.search.sync.rag", new=mock_rag):
        response = client.post(
            "/ask-ai-sync?timeout=10.0", json=test_body, headers={"x-user-id": "123"}
        )

        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["content"] == "Hello world!"
        assert response_data["data"]["sources"] == [{"recurrenceId": 123}]
        assert response_data["message"] == "Success"


def test_ask_ai_sync_integration_error(client):
    async def mock_rag_error(body, send, user_id):
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"stream_failed","code":400,"data":{"content":"Integration error"}}',
            }
        )

    test_body = {
        "requestId": "test-integration-error",
        "query": "error test query",
        "threadId": "integration-thread-error",
    }

    with patch("nebula.web.api.search.sync.rag", new=mock_rag_error):
        response = client.post(
            "/ask-ai-sync", json=test_body, headers={"x-user-id": "123"}
        )

        assert response.status_code == 400
        response_data = response.json()
        assert response_data["success"] is False
        assert response_data["message"] == "Integration error"
        assert "sources" in response_data


def test_ask_ai_sync_integration_timeout(client):
    async def mock_rag_timeout(body, send, user_id):
        await asyncio.sleep(10.0)

    test_body = {
        "requestId": "test-integration-timeout",
        "query": "timeout test query",
        "threadId": "integration-thread-timeout",
    }

    with patch("nebula.web.api.search.sync.rag", new=mock_rag_timeout):
        with patch("asyncio.wait_for", side_effect=asyncio.TimeoutError()):
            response = client.post(
                "/ask-ai-sync?timeout=1.0", json=test_body, headers={"x-user-id": "123"}
            )

            assert response.status_code == 504
            response_data = response.json()
            assert response_data["success"] is False
            assert "timed out" in response_data["message"].lower()


def test_ask_ai_sync_integration_missing_user_id(client):
    test_body = {
        "requestId": "test-missing-user",
        "query": "user id test query",
        "threadId": "integration-thread-user",
    }

    response = client.post("/ask-ai-sync", json=test_body)

    assert response.status_code == 401
    response_data = response.json()
    assert response_data["message"] == "User id is missing"
    assert response_data["data"] is None


def test_ask_ai_sync_integration_invalid_json(client):
    response = client.post(
        "/ask-ai-sync",
        content=b'{"invalid json":',
        headers={"x-user-id": "123", "Content-Type": "application/json"},
    )

    assert response.status_code in [400, 422]
