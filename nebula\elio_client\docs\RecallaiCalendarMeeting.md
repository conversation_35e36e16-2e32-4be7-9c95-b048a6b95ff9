# RecallaiCalendarMeeting


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**calendar_user** | [**RecallaiCalendarUser**](RecallaiCalendarUser.md) |  | 
**end_time** | **str** |  | 
**id** | **str** |  | 
**start_time** | **str** |  | 

## Example

```python
from elio_client.models.recallai_calendar_meeting import RecallaiCalendarMeeting

# TODO update the JSON string below
json = "{}"
# create an instance of RecallaiCalendarMeeting from a JSON string
recallai_calendar_meeting_instance = RecallaiCalendarMeeting.from_json(json)
# print the JSON string representation of the object
print(RecallaiCalendarMeeting.to_json())

# convert the object into a dict
recallai_calendar_meeting_dict = recallai_calendar_meeting_instance.to_dict()
# create an instance of Recal<PERSON><PERSON>alendarMeeting from a dict
recallai_calendar_meeting_from_dict = RecallaiCalendarMeeting.from_dict(recallai_calendar_meeting_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


