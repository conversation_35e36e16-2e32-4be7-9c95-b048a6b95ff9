package database

import (
	"context"
	"time"

	"encore.app/meetings/api"
	"encore.app/pkg/database"
	"encore.dev/rlog"
	"github.com/jmoiron/sqlx"
)

// SQL statements for meeting suggestions
var (
	upsertMeetingSuggestion = database.NewStatement(`
		INSERT INTO meeting_suggestions (
			session_id, session_recurrence_id, user_id, category, prompt, created_at, updated_at
		) VALUES (
			:sessionId, :recurrenceId, :userId, :category, :prompt, :createdAt, :updatedAt
		)
		ON CONFLICT (session_id, session_recurrence_id, user_id, category)
		DO UPDATE SET 
			prompt = EXCLUDED.prompt,
			updated_at = EXCLUDED.updated_at
	`)

	getMeetingSuggestionsByUser = database.NewStatement(`
		SELECT * FROM meeting_suggestions 
		WHERE session_id = :sessionId AND session_recurrence_id = :recurrenceId AND user_id = :userId
		ORDER BY category ASC
	`)

	getMeetingSuggestionsBySession = database.NewStatement(`
		SELECT * FROM meeting_suggestions 
		WHERE session_id = :sessionId AND session_recurrence_id = :recurrenceId
		ORDER BY user_id ASC, category ASC
	`)

	deleteMeetingSuggestionsBySession = database.NewStatement(`
		DELETE FROM meeting_suggestions 
		WHERE session_id = :sessionId AND session_recurrence_id = :recurrenceId
	`)
)

func init() {
	Statements = append(Statements, database.Statements{
		upsertMeetingSuggestion,
		getMeetingSuggestionsByUser,
		getMeetingSuggestionsBySession,
		deleteMeetingSuggestionsBySession,
	}...)
}

// UpsertMeetingSuggestions inserts or updates meeting suggestions for a session
func UpsertMeetingSuggestions(ctx context.Context, sessionID, recurrenceID string, suggestions []api.MeetingSuggestionInput) error {
	now := time.Now().Unix()

	for _, suggestion := range suggestions {
		args := database.Args{
			"sessionId":    sessionID,
			"recurrenceId": recurrenceID,
			"userId":       suggestion.UserId,
			"category":     suggestion.Category,
			"prompt":       suggestion.Prompt,
			"createdAt":    now,
			"updatedAt":    now,
		}

		_, err := upsertMeetingSuggestion.Execx(ctx, args)
		if err != nil {
			rlog.Error("Failed to upsert meeting suggestion", "err", err, "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", suggestion.UserId, "category", suggestion.Category)
			return err
		}
	}

	rlog.Debug("Successfully upserted meeting suggestions", "sessionID", sessionID, "recurrenceID", recurrenceID, "count", len(suggestions))
	return nil
}

// GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user in a session
func GetMeetingSuggestionsByUser(ctx context.Context, sessionID, recurrenceID, userID string) ([]string, error) {
	var suggestions []string
	var err error

	args := database.Args{
		"sessionId":    sessionID,
		"recurrenceId": recurrenceID,
		"userId":       userID,
	}

	getMeetingSuggestionsByUser.Queryx(ctx, args, func(rows *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var row MeetingSuggestionRow
		if scanErr := rows.StructScan(&row); scanErr != nil {
			err = scanErr
			return false
		}

		suggestions = append(suggestions, row.Prompt)
		return true
	})

	if err != nil {
		rlog.Error("Failed to get meeting suggestions by user", "err", err, "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", userID)
		return nil, err
	}

	rlog.Debug("Retrieved meeting suggestions by user", "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", userID, "count", len(suggestions))
	return suggestions, nil
}

// GetMeetingSuggestionRowsByUser retrieves meeting suggestion rows for a specific user in a session
func GetMeetingSuggestionRowsByUser(ctx context.Context, sessionID, recurrenceID, userID string) ([]MeetingSuggestionRow, error) {
	var suggestions []MeetingSuggestionRow
	var err error

	args := database.Args{
		"sessionId":    sessionID,
		"recurrenceId": recurrenceID,
		"userId":       userID,
	}

	getMeetingSuggestionsByUser.Queryx(ctx, args, func(rows *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var row MeetingSuggestionRow
		if scanErr := rows.StructScan(&row); scanErr != nil {
			err = scanErr
			return false
		}

		suggestions = append(suggestions, row)
		return true
	})

	if err != nil {
		rlog.Error("Failed to get meeting suggestion rows by user", "err", err, "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", userID)
		return nil, err
	}

	rlog.Debug("Retrieved meeting suggestion rows by user", "sessionID", sessionID, "recurrenceID", recurrenceID, "userID", userID, "count", len(suggestions))
	return suggestions, nil
}

// GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session
func GetMeetingSuggestionsBySession(ctx context.Context, sessionID, recurrenceID string) ([]MeetingSuggestionRow, error) {
	var suggestions []MeetingSuggestionRow
	var err error

	args := database.Args{
		"sessionId":    sessionID,
		"recurrenceId": recurrenceID,
	}

	getMeetingSuggestionsBySession.Queryx(ctx, args, func(rows *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var row MeetingSuggestionRow
		if scanErr := rows.StructScan(&row); scanErr != nil {
			err = scanErr
			return false
		}

		suggestions = append(suggestions, row)
		return true
	})

	if err != nil {
		rlog.Error("Failed to get meeting suggestions by session", "err", err, "sessionID", sessionID, "recurrenceID", recurrenceID)
		return nil, err
	}

	rlog.Debug("Retrieved meeting suggestions by session", "sessionID", sessionID, "recurrenceID", recurrenceID, "count", len(suggestions))
	return suggestions, nil
}

// DeleteMeetingSuggestionsBySession deletes all meeting suggestions for a session
func DeleteMeetingSuggestionsBySession(ctx context.Context, sessionID, recurrenceID string) error {
	args := database.Args{
		"sessionId":    sessionID,
		"recurrenceId": recurrenceID,
	}

	_, err := deleteMeetingSuggestionsBySession.Execx(ctx, args)
	if err != nil {
		rlog.Error("Failed to delete meeting suggestions by session", "err", err, "sessionID", sessionID, "recurrenceID", recurrenceID)
		return err
	}

	rlog.Debug("Deleted meeting suggestions by session", "sessionID", sessionID, "recurrenceID", recurrenceID)
	return nil
} 