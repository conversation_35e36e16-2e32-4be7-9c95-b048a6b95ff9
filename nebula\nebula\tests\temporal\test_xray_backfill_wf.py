import pytest
from datetime import timedelta
from temporalio.testing import WorkflowEnvironment
from temporalio.exceptions import ApplicationError

from nebula.temporal.types import XRayBackfillScanWorkflowParams
from nebula.tests.helpers.temporal import create_ai_end_meet_worker_for_tests
from nebula.temporal.workflows.xray_backfill_wf import XRayBackfillScanWorkflow
from nebula.temporal.converter import pydantic_data_converter
from nebula.settings import settings


@pytest.mark.asyncio
async def test_xray_backfill_workflow_success():
    """Test the XRay backfill workflow executes successfully."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            result = await client.execute_workflow(
                XRayBackfillScanWorkflow.run,
                arg=XRayBackfillScanWorkflowParams(xray_id=1, user_id=12345, tz="UTC"),
                id="test-xray-backfill-workflow-success",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=10),
            )

            # Verify workflow execution succeeded
            assert isinstance(result, dict)
            assert result["success"] is True
            assert "all_user_recurrences" in result
            assert "duration" in result
            assert "initial_len" in result
            assert "processed_Len" in result

            # Verify recurrences were processed
            assert len(result["all_user_recurrences"]) >= 0
            assert result["duration"] > 0


@pytest.mark.asyncio
async def test_xray_backfill_workflow_with_invalid_user():
    """Test the XRay backfill workflow with invalid user ID."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            with pytest.raises(Exception) as exc_info:
                await client.execute_workflow(
                    XRayBackfillScanWorkflow.run,
                    arg=XRayBackfillScanWorkflowParams(
                        xray_id=1,
                        user_id=-1,  # Invalid user ID
                        tz="UTC",
                    ),
                    id="test-xray-backfill-workflow-invalid-user",
                    task_queue=task_queue,
                    execution_timeout=timedelta(minutes=5),
                )

            # Verify the correct error message
            assert "User does not exist" in str(exc_info.value)


@pytest.mark.asyncio
async def test_xray_backfill_workflow_with_invalid_xray():
    """Test the XRay backfill workflow with invalid XRay ID."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            with pytest.raises(Exception) as exc_info:
                await client.execute_workflow(
                    XRayBackfillScanWorkflow.run,
                    arg=XRayBackfillScanWorkflowParams(
                        xray_id=-1,  # Invalid XRay ID
                        user_id=12345,
                        tz="UTC",
                    ),
                    id="test-xray-backfill-workflow-invalid-xray",
                    task_queue=task_queue,
                    execution_timeout=timedelta(minutes=5),
                )

            # Verify the correct error message
            assert "XRay document does not exist" in str(exc_info.value)


@pytest.mark.asyncio
async def test_xray_backfill_workflow_with_no_recurrences():
    """Test the XRay backfill workflow when user has no past recurrences."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that returns no recurrences
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_backfill_wf import XRayBackfillScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from temporalio import activity

        class NoRecurrencesMock(MockXRayActivities):
            @activity.defn
            async def get_past_user_session_recurrences_by_user_id(
                self, user_id: int
            ) -> list:
                """Mock that returns no recurrences"""
                return []

        mock_xray_activities = NoRecurrencesMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayBackfillScanWorkflow],
            activities=[
                # Include all the necessary activities
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
                mock_xray_activities.get_user_by_id,
                mock_xray_activities.get_past_user_session_recurrences_by_user_id,
            ],
        ):
            result = await client.execute_workflow(
                XRayBackfillScanWorkflow.run,
                arg=XRayBackfillScanWorkflowParams(xray_id=1, user_id=12345, tz="UTC"),
                id="test-xray-backfill-workflow-no-recurrences",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify workflow completed successfully even with no recurrences
            assert isinstance(result, dict)
            assert result["success"] is True
            assert len(result["all_user_recurrences"]) == 0
            assert result["initial_len"] == 0
            assert result["processed_Len"] == 0


@pytest.mark.asyncio
async def test_xray_backfill_workflow_with_activity_failure():
    """Test the XRay backfill workflow handles activity failures correctly."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that fails on get_user_by_id
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_backfill_wf import XRayBackfillScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from temporalio import activity

        class FailingBackfillMock(MockXRayActivities):
            @activity.defn
            async def get_user_by_id(self, user_id: int) -> dict | None:
                """Mock that fails to get user"""
                raise ApplicationError("Database connection failed", non_retryable=True)

        mock_xray_activities = FailingBackfillMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayBackfillScanWorkflow],
            activities=[
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
                mock_xray_activities.get_user_by_id,
                mock_xray_activities.get_past_user_session_recurrences_by_user_id,
            ],
        ):
            with pytest.raises(Exception) as exc_info:
                await client.execute_workflow(
                    XRayBackfillScanWorkflow.run,
                    arg=XRayBackfillScanWorkflowParams(
                        xray_id=1, user_id=12345, tz="UTC"
                    ),
                    id="test-xray-backfill-workflow-failure",
                    task_queue=task_queue,
                    execution_timeout=timedelta(minutes=5),
                )

            # Verify the error propagated correctly
            assert "Database connection failed" in str(exc_info.value)


@pytest.mark.asyncio
async def test_xray_backfill_workflow_parameters():
    """Test the XRay backfill workflow with different parameter combinations."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        async with create_ai_end_meet_worker_for_tests(client, task_queue):
            # Test with different XRay ID
            result = await client.execute_workflow(
                XRayBackfillScanWorkflow.run,
                arg=XRayBackfillScanWorkflowParams(
                    xray_id=2, user_id=12345, tz="America/New_York"
                ),
                id="test-xray-backfill-workflow-different-params",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            assert isinstance(result, dict)
            assert result["success"] is True

            # Test with different user ID and timezone
            result = await client.execute_workflow(
                XRayBackfillScanWorkflow.run,
                arg=XRayBackfillScanWorkflowParams(
                    xray_id=1, user_id=67890, tz="Europe/London"
                ),
                id="test-xray-backfill-workflow-different-user",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            assert isinstance(result, dict)
            assert result["success"] is True


@pytest.mark.asyncio
async def test_xray_backfill_workflow_deduplication():
    """Test that the backfill workflow properly deduplicates recurrences."""
    async with await WorkflowEnvironment.start_local(
        data_converter=pydantic_data_converter
    ) as env:
        client = env.client
        task_queue = settings.temporal_ai_end_meeting_queue_with_prefix

        # Create custom mock that returns duplicate recurrences
        from temporalio.worker import Worker
        from nebula.temporal.workflows.xray_backfill_wf import XRayBackfillScanWorkflow
        from nebula.tests.mocks.mock_xray_activities import MockXRayActivities
        from temporalio import activity

        class DuplicateRecurrencesMock(MockXRayActivities):
            @activity.defn
            async def get_past_user_session_recurrences_by_user_id(
                self, user_id: int
            ) -> list:
                """Mock that returns duplicate recurrences"""
                return [
                    {
                        "recurrenceID": 1001,
                        "sessionID": 2001,
                        "title": "Weekly Team Standup",
                        "startTimestamp": 1640995200,
                        "endTimestamp": 1640998800,
                        "createdAt": 1640995200,
                    },
                    {
                        "recurrenceID": 1001,  # Duplicate
                        "sessionID": 2001,  # Duplicate
                        "title": "Weekly Team Standup",
                        "startTimestamp": 1640995200,
                        "endTimestamp": 1640998800,
                        "createdAt": 1640995200,
                    },
                    {
                        "recurrenceID": 1002,
                        "sessionID": 2002,
                        "title": "Project Review Meeting",
                        "startTimestamp": 1641081600,
                        "endTimestamp": 1641085200,
                        "createdAt": 1641081600,
                    },
                ]

        mock_xray_activities = DuplicateRecurrencesMock()

        async with Worker(
            client,
            task_queue=task_queue,
            workflows=[XRayBackfillScanWorkflow],
            activities=[
                mock_xray_activities.get_xrays,
                mock_xray_activities.get_transcript_by_recurrence_id,
                mock_xray_activities.quick_scan,
                mock_xray_activities.get_xray_by_id,
                mock_xray_activities.deep_scan,
                mock_xray_activities.get_user_by_id,
                mock_xray_activities.get_past_user_session_recurrences_by_user_id,
            ],
        ):
            result = await client.execute_workflow(
                XRayBackfillScanWorkflow.run,
                arg=XRayBackfillScanWorkflowParams(xray_id=1, user_id=12345, tz="UTC"),
                id="test-xray-backfill-workflow-deduplication",
                task_queue=task_queue,
                execution_timeout=timedelta(minutes=5),
            )

            # Verify deduplication worked
            assert isinstance(result, dict)
            assert result["success"] is True
            assert result["initial_len"] == 3  # 3 items before deduplication
            assert result["processed_Len"] == 2  # 2 items after deduplication
            assert len(result["all_user_recurrences"]) == 2
