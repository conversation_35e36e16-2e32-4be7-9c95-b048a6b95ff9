import json
from typing import Optional, List
from pydantic import BaseModel

from nebula.db.models.ai_feed import AIFeed


class UserBasic(BaseModel):
    id: str
    firstName: str
    lastName: Optional[str]
    fullName: Optional[str]
    avatar: Optional[str]


class PostAIFeedActionItemsRequestDTO(BaseModel):
    """Post AI Feed Action Items Request DTO."""

    sessionID: str
    sessionRecurrenceID: str
    batchID: str
    lang: Optional[str]

    class Metadata(BaseModel):
        """Batch metadata schema."""

        firstTranscriptionID: int
        firstTimeUnix: int
        lastTranscriptionID: int
        lastTimeUnix: int
        batchDuration: int

    batchMeta: Metadata

    class ActionItem(BaseModel):
        """Action items schema."""

        assignee: str
        actionItem: str
        context: str
        SMARTCriteriaMet: List[str]

    actionItems: List[ActionItem]


class PostAIFeedCatchupSummaryRequestDTO(BaseModel):
    """Post AI Feed Catchup Summary Request DTO."""

    sessionID: str
    sessionRecurrenceID: str
    batchID: str
    lang: Optional[str]

    class Metadata(BaseModel):
        """Batch metadata schema."""

        firstTranscriptionID: int
        firstTimeUnix: int
        lastTranscriptionID: int
        lastTimeUnix: int
        batchDuration: int

    batchMeta: Metadata

    summary: str
    context: Optional[str]


class PostAIFeedInsightsRequestDTO(BaseModel):
    """Post AI Feed Insights Request DTO."""

    sessionID: str
    sessionRecurrenceID: str
    batchID: str
    lang: Optional[str] = "en-US"

    class Metadata(BaseModel):
        """Batch metadata schema."""

        firstTranscriptionID: int
        firstTimeUnix: int
        lastTranscriptionID: int
        lastTimeUnix: int
        batchDuration: int

    batchMeta: Metadata

    class Insight(BaseModel):
        """Insight schema."""

        insight: str
        context: Optional[str] = None

    insights: List[Insight]


def patch_ai_feed_json_fields(ai_feed: AIFeed):
    """Patch AI feed JSON fields that might be stored as strings."""
    if isinstance(ai_feed["msg_dynamic_fields"], str):
        ai_feed["msg_dynamic_fields"] = json.loads(ai_feed["msg_dynamic_fields"])
    if isinstance(ai_feed["metadata_dynamic_fields"], str):
        ai_feed["metadata_dynamic_fields"] = json.loads(
            ai_feed["metadata_dynamic_fields"],
        )
    if isinstance(ai_feed["links"], str):
        ai_feed["links"] = json.loads(
            ai_feed["links"],
        )

    return ai_feed 