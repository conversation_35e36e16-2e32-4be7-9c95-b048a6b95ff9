# ApiMeetingSuggestionDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**category** | **str** |  | 
**created_at** | **int** |  | 
**id** | **str** |  | 
**prompt** | **str** |  | 
**session_id** | **str** |  | 
**session_recurrence_id** | **str** |  | 
**updated_at** | **int** |  | 
**user_id** | **str** |  | 

## Example

```python
from elio_client.models.api_meeting_suggestion_dto import ApiMeetingSuggestionDTO

# TODO update the JSON string below
json = "{}"
# create an instance of ApiMeetingSuggestionDTO from a JSON string
api_meeting_suggestion_dto_instance = ApiMeetingSuggestionDTO.from_json(json)
# print the JSON string representation of the object
print(ApiMeetingSuggestionDTO.to_json())

# convert the object into a dict
api_meeting_suggestion_dto_dict = api_meeting_suggestion_dto_instance.to_dict()
# create an instance of ApiMeetingSuggestionDTO from a dict
api_meeting_suggestion_dto_from_dict = ApiMeetingSuggestionDTO.from_dict(api_meeting_suggestion_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


