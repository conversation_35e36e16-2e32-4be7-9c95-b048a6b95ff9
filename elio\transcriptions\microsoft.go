package transcriptions

import (
	"net/http"
	"net/url"
)

// Needed to verify domains for Microsoft OAuth2 integration
//
//encore:api raw public path=/.well-known/microsoft-identity-association.json
func (t *Transcriptions) IdentityAssociated(w http.ResponseWriter, req *http.Request) {
	w.<PERSON>er().Set("Content-Type", "application/json; charset=UTF-8")
	w.Write([]byte(`{"associatedApplications":[{"applicationId":"` + Config.MicrosoftOAuth2ClientID() + `"}]}`))
}

// Needed redirect back to recall
//
//encore:api raw public path=/v1.0/calendar/ms_oauth_callback
func (t *Transcriptions) MicrosoftRedirectOfRedirect(w http.ResponseWriter, req *http.Request) {
	urlSystem, _ := url.Parse(t.RecallSDK.MicrosoftCallbackURL())
	urlSystem.RawQuery = req.URL.RawQuery
	w.Header().Set("Location", urlSystem.String())
	w.<PERSON>riteHeader(http.StatusTemporaryRedirect)
}
