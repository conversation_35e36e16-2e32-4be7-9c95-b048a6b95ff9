package api

import (
	"encoding/json"
	"fmt"

	"encore.app/hubble/models"
	"encore.app/shared"
)

// IsAuthorizedRequest Check if the subject, object, action is authorized
type IsAuthorizedRequest struct {
	Sub []string                 `json:"sub"` // subject to check authorization for
	Obj string                   `json:"obj"` // object to check authorization on
	Act shared.AuthPolicyActions `json:"act"` // action to check authorization for
}

func (request IsAuthorizedRequest) Validate() error {
	if request.Obj == "" {
		return fmt.Errorf("object cannot be empty")
	}
	if request.Act == "" {
		return fmt.Errorf("action cannot be empty")
	}
	return nil
}

// IsAuthorizedBatchRequest Check an array of objects is authorized
type IsAuthorizedBatchRequest struct {
	Sub  string                   `json:"sub"`  // subject to check authorization for
	Objs []string                 `json:"objs"` // objects to check authorization on
	Act  shared.AuthPolicyActions `json:"act"`  // action to check authorization for
}

func (request IsAuthorizedBatchRequest) Validate() error {
	if request.Sub == "" {
		return fmt.Errorf("subject cannot be empty")
	}
	if len(request.Objs) == 0 {
		return fmt.Errorf("objects cannot be empty")
	}
	for idx, obj := range request.Objs {
		if obj == "" {
			return fmt.Errorf("object %d cannot be empty", idx)
		}
	}
	if request.Act == "" {
		return fmt.Errorf("action cannot be empty")
	}
	return nil
}

// IsAuthorizedBatchResponse Reply to the IsAuthorizedBatchRequest
type IsAuthorizedBatchResponse struct {
	Data []shared.IsAuthorizedBatchResponseDTO `json:"data"` // array of authorization checks
}

// GetUserByTokenRequest Request to get a user by their JWT token
type GetUserByTokenRequest struct {
	Token string `json:"token"` // the JWT token
}

// GetUserByTokenResponse Contains the user based on the JWT token passed in the Authorization header
type GetUserByTokenResponse struct {
	Data struct {
		AuthUser *shared.UserDTO `json:"authUser"` // the user DTO
	}
}

// OTPRequest Request to send an OTP to a user via email optionally signing them up
type OTPRequest struct {
	IP             string  `header:"X-Forwarded-For" encore:"sensitive"` // the IP address of the user
	Email          string  `json:"email"`                                // the email to send the OTP to
	Fingerprint    *string `json:"fingerprint,omitempty"`                // the fingerprint of the device
	FirstName      *string `json:"firstName,omitempty"`                  // the first name of the user only required if signing up
	IsSignup       bool    `json:"isSignup"`                             // whether to sign the user up if they don't exist
	JoinSession    *bool   `json:"joinSession,omitempty"`                // whether to join the user to an existing session
	SessionID      *string `json:"sessionID,omitempty"`                  // the session ID to join the user to, required if joinSession is true
	LastName       *string `json:"lastName,omitempty"`                   // the last name of the user only required if signing up
	MarketingOptIn *bool   `json:"marketingOptIn,omitempty"`             // whether the user opted in to marketing, promise we don't spam you
	RedirectUri    string  `json:"redirect_uri,omitempty"`               // the URL to redirect the user to after they verify their OTP
	Timezone       *string `json:"timezone,omitempty"`                   // the timezone of the user only required if signing up
	ClientID       string  `json:"client_id,omitempty"`                  // ID of the client provider, only used for oauth requests
	State          string  `json:"state,omitempty"`                      // used for oAuth2 support, this would be passed on the auth request
}

func (request OTPRequest) Validate() error {
	if request.Email == "" {
		return fmt.Errorf("email cannot be empty")
	}
	return nil
}

// VerifyOTPRequest Request to verify an OTP
type VerifyOTPRequest struct {
	IP       string `header:"X-Forwarded-For" encore:"sensitive"` // the IP address of the user
	Email    string `json:"email"`                                // the email to verify the OTP for
	OTP      string `json:"otp"`                                  // the OTP to verify
	ClientID string `json:"client_id,omitempty"`                  // ID of the client provider, only used for oauth requests
	State    string `json:"state,omitempty"`                      // used for oAuth2 support, the requesting service will verify this value matches the request
}

func (request VerifyOTPRequest) Validate() error {
	if request.Email == "" {
		return fmt.Errorf("email query param missing")
	}

	if request.OTP == "" {
		return fmt.Errorf("otp query param missing")
	}
	return nil
}

// VerifyOTPResponse Response to the VerifyOTPRequest
type VerifyOTPResponse struct {
	Data struct {
		AuthToken    string `json:"authToken"`              // the JWT token for the user
		RefreshToken string `json:"refreshToken"`           // the refresh JWT token for the user
		UserID       string `json:"userID"`                 // the user ID of the user
		RedirectURI  string `json:"redirect_uri,omitempty"` // the redirect URI
	} `json:"data"` // the response data
}

// RefreshTokenRequest Request to refresh the JWT token
type RefreshTokenRequest struct {
	RefreshToken string `json:"refreshToken" encore:"sensitive"` // the refresh JWT token for the user
}

func (o RefreshTokenRequest) Validate() error {
	if o.RefreshToken == "" {
		return fmt.Errorf("refreshToken cannot be empty")
	}
	return nil
}

// OAuthRefreshTokenRequest Request to refresh the JWT token
type OAuthRefreshTokenRequest struct {
	RefreshToken string  `json:"refreshToken" encore:"sensitive"`           // the refresh JWT token for the user
	ClientID     string  `json:"client_id,omitempty"`                       // ID of the client provider, only used for oauth requests
	ClientSecret string  `json:"clientSecret,omitempty" encore:"sensitive"` // Secret of the client provider, only used for oauth requests
	GrantType    *string `form:"grant_type,omitempty"`                      // the grant type
}

func (o OAuthRefreshTokenRequest) Validate() error {
	if o.RefreshToken == "" {
		return fmt.Errorf("refreshToken cannot be empty")
	}
	if o.ClientID == "" {
		return fmt.Errorf("clientID cannot be empty")
	}
	return nil
}

// RefreshTokenResponse Response to the RefreshTokenRequest
type RefreshTokenResponse struct {
	Data struct {
		AuthToken    string `json:"authToken"`    // the JWT token for the user
		RefreshToken string `json:"refreshToken"` // the refresh JWT token for the user
	} `json:"data"` // the response data
}

// SocialLoginRequest Request to login with a social provider
type SocialLoginRequest struct {
	Token       string `json:"token" encore:"sensitive"` // the social token
	Type        string `json:"type"`                     // the social type
	UserDetails struct {
		FirstName string `json:"firstName"` // the first name of the user
		LastName  string `json:"lastName"`  // the last name of the user
		Nonce     string `json:"nonce"`     // the nonce of the user
	} `json:"userDetails"`                                        // the user details
	ClientID    string `json:"client_id,omitempty" copier:"-"`    // ID of the client provider, only used for oauth requests
	RedirectUri string `json:"redirect_uri,omitempty" copier:"-"` // the URL to redirect the user to after they verify their OTP
	State       string `json:"state,omitempty"`                   // used by OAuth requester to validate
}

// SocialLoginResponse Response to the SocialLoginRequest
type SocialLoginResponse struct {
	Data struct {
		AuthToken    string `json:"authToken"`    // the JWT token for the user
		RefreshToken string `json:"refreshToken"` // the refresh JWT token for the user
		UserID       string `json:"userID"`       // the user ID of the user
		AgentID      string `json:"agent_id"`     // the user ID of the user
	} `json:"data"` // the response data
}

// SocialLoginViaCodeRequest Request to login with a social provider via a code
type SocialLoginViaCodeRequest struct {
	Code        string  `json:"code"`
	Type        *string `json:"type,omitempty"`
	ClientID    string  `json:"client_id,omitempty" copier:"-"`    // ID of the client provider, only used for oauth requests
	RedirectUri string  `json:"redirect_uri,omitempty" copier:"-"` // the URL to redirect the user to after they verify their OTP
	State       string  `json:"state,omitempty"`                   // used by OAuth requester to validate
}

func (request SocialLoginViaCodeRequest) Validate() error {
	if request.Code == "" {
		return fmt.Errorf("code cannot be empty")
	}
	return nil
}

// LogoutResponse Response when a user logs out
type LogoutResponse struct {
	Data struct {
		//
	} `json:"data"` // the response data
}

// OptInResponse Response when a user opts in to be a meeting host
type OptInResponse struct {
	Data struct {
		UserID       string `json:"userID"`       // the user ID of the user
		AuthToken    string `json:"authToken"`    // the JWT token for the user
		RefreshToken string `json:"refreshToken"` // the refresh JWT token for the user
	} `json:"data"` // the response data
}

// AuthControllerIsAuthorizedToSessionFeatureRequest Request to check if a user is authorized to a session feature
type AuthControllerIsAuthorizedToSessionFeatureRequest struct {
	SessionID string `query:"sessionID"` // the session ID
	Feature   string `query:"feature"`   // the feature to check authorization for
}

func (request AuthControllerIsAuthorizedToSessionFeatureRequest) Validate() error {
	if request.SessionID == "" {
		return fmt.Errorf("sessionID query param missing")
	}

	if request.Feature == "" {
		return fmt.Errorf("feature query param missing")
	}
	return nil
}

// AuthControllerIsAuthorizedToSessionFeatureResponse Response to the AuthControllerIsAuthorizedToSessionFeatureRequest
type AuthControllerIsAuthorizedToSessionFeatureResponse struct {
	Data struct {
		IsAuthorized     bool             `json:"isAuthorized"`               // whether the user is authorized
		SubscriptionPlan *json.RawMessage `json:"subscriptionPlan,omitempty"` // the subscription plan of the users session
	} `json:"data"` // the response data
}

// AuthControllerIsAuthorizedToIntegrationRequest Request to check if a user is authorized to an integration
type AuthControllerIsAuthorizedToIntegrationRequest struct {
	UserID       string `form:"userID" json:"userID"`             // the user ID
	ProviderName string `form:"providerName" json:"providerName"` // the provider name
}

func (request AuthControllerIsAuthorizedToIntegrationRequest) Validate() error {
	if request.UserID == "" {
		return fmt.Errorf("UserID query param missing")
	}

	if request.ProviderName == "" {
		return fmt.Errorf("providerName query param missing")
	}
	return nil
}

// AuthControllerIsAuthorizedToIntegrationResponse Response to the AuthControllerIsAuthorizedToIntegrationRequest
type AuthControllerIsAuthorizedToIntegrationResponse struct {
	Data struct {
		IsAuthorized     bool             `json:"isAuthorized"`               // whether the user is authorized
		SubscriptionPlan *json.RawMessage `json:"subscriptionPlan,omitempty"` // the subscription plan of the user
	} `json:"data"` // the response data
}

// OAuthRedirectURIResponse returns the redirect URI after verifying the email OTP
type OAuthRedirectURIResponse struct {
	Data struct {
		RedirectUri string `json:"redirect_uri"`
	} `json:"data"`
}

// IssueNewOAuth2ClientCredentialsRequest defines model for IssueNewOAuth2ClientCredentialsRequest.
type IssueNewOAuth2ClientCredentialsRequest struct {
	ClientDescription *string `json:"clientDescription,omitempty"`
	ClientName        string  `json:"clientName"`
	RedirectURI       *string `json:"redirectURI,omitempty"`
}

// ClientCredentialsResponse defines model for ClientCredentialsDTO.
type ClientCredentialsResponse struct {
	Data struct {
		ClientDescription *string `json:"clientDescription,omitempty"`
		ClientID          string  `json:"clientID"`
		ClientName        string  `json:"clientName"`
		ClientSecret      string  `json:"clientSecret"  encore:"sensitive"`
		RedirectURI       *string `json:"redirectURI,omitempty"`
	} `json:"data"`
}

// OAuthExchangeAuthCodeForTokenRequest used to exchange oauth code for a JWT token
type OAuthExchangeAuthCodeForTokenRequest struct {
	ClientID     string  `form:"client_id"`
	ClientSecret string  `form:"client_secret" encore:"sensitive"`
	Code         string  `form:"code"`
	GrantType    *string `form:"grant_type,omitempty"`
	RedirectUri  string  `form:"redirect_uri"`
	State        string  `form:"state,omitempty"`
}

// OAuthExchangeAuthCodeForTokenResponse response to the OAuthExchangeAuthCodeForTokenRequest
type OAuthExchangeAuthCodeForTokenResponse struct {
	AccessToken               string `json:"access_token"`
	ExpiresIn                 int64  `json:"expires_in"`      // ExpiresIn the time in seconds until the token expires
	ExpiresAtTime             int64  `json:"expires_at_time"` // ExpiresAtTime the unix timestamp in seconds after which the token expires
	RefreshToken              string `json:"refresh_token"`
	RefreshTokenExpiresIn     int64  `json:"refresh_token_expires_in"`      // RefreshTokenExpiresIn the time in seconds until the refresh token expires
	RefreshTokenExpiresAtTime int64  `json:"refresh_token_expires_at_time"` // RefreshTokenExpiresAtTime the unix timestamp for refresh token expiry
	TokenType                 string `json:"token_type"`
}

type DeleteUserRequest struct {
	Email string `json:"email"`
}

type CreateAuthUserInput struct {
	ID          string   `json:"id"`
	Email       string   `json:"email"`
	TeamID      *string  `json:"teamID"`
	RoleIDs     []string `json:"roleIDs"`
	TeamRoleIDs []string `json:"teamRoleIDs"`
}

type CreateAuthUsersRequest struct {
	Users []CreateAuthUserInput `json:"users"`
}

type UpdateAuthUserRequest struct {
	ID          string    `json:"id"`
	Email       *string   `json:"email,omitempty"`
	TeamID      *string   `json:"teamID,omitempty"`
	RoleIDs     *[]string `json:"roleIDs,omitempty"`
	TeamRoleIDs *[]string `json:"teamRoleIDs,omitempty"`
}

type CreateAuthUsersResponse struct {
	Success   bool              `json:"success"`
	Message   string            `json:"message"`
	AuthUsers []models.AuthUser `json:"users"`
}

type UpdateAuthUsersResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type LoginGuestUserRequest struct {
	Email    string `json:"email,omitempty"`    // optional email of the guest user
	FullName string `json:"fullName,omitempty"` // optional full name of the guest user
}

type LoginGuestUserResponse struct {
	Data struct {
		AuthToken    string          `json:"authToken"`    // the JWT token for the user
		RefreshToken string          `json:"refreshToken"` // the refresh JWT token for the user
		Guest        shared.GuestDTO `json:"guest"`        // the guest user object
	} `json:"data"` // the response data
}

type PopulateSessionPoliciesRequest struct {
	SessionID    string `json:"sessionID"`
	RecurrenceID string `json:"recurrenceID"`
}
