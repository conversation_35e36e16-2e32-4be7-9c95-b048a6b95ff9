# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_meetings_get_meeting_suggestions_by_user_public200_response import GETMeetingsGetMeetingSuggestionsByUserPublic200Response

class TestGETMeetingsGetMeetingSuggestionsByUserPublic200Response(unittest.TestCase):
    """GETMeetingsGetMeetingSuggestionsByUserPublic200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETMeetingsGetMeetingSuggestionsByUserPublic200Response:
        """Test GETMeetingsGetMeetingSuggestionsByUserPublic200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETMeetingsGetMeetingSuggestionsByUserPublic200Response`
        """
        model = GETMeetingsGetMeetingSuggestionsByUserPublic200Response()
        if include_optional:
            return GETMeetingsGetMeetingSuggestionsByUserPublic200Response(
                suggestions = [
                    ''
                    ]
            )
        else:
            return GETMeetingsGetMeetingSuggestionsByUserPublic200Response(
                suggestions = [
                    ''
                    ],
        )
        """

    def testGETMeetingsGetMeetingSuggestionsByUserPublic200Response(self):
        """Test GETMeetingsGetMeetingSuggestionsByUserPublic200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
