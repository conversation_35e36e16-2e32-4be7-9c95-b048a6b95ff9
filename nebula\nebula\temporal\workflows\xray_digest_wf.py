from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from datetime import timedelta
    from nebula.temporal.activities.user_act import UserActivities
    from nebula.temporal.activities.xray_act import XRayActivities
    from nebula.temporal.types import (
        CreateDigestCommitInput,
        GenerateScheduledDigestContentInput,
        XRayDigestWorkflowParams,
        XRayDigestWorkflowResult,
    )


@workflow.defn(name="XRayDigest")
class XRayDigestWorkflow:
    """Workflow for generating digest content for digest-type X-Rays"""

    @workflow.run
    async def run(self, params: XRayDigestWorkflowParams) -> XRayDigestWorkflowResult:
        """
        Run the digest generation workflow.

        Args:
            params: XRayDigestWorkflowParams containing xray_id

        Returns:
            XRayDigestWorkflowResult containing the results of digest generation
        """
        start_time = workflow.now().timestamp()

        workflow.logger.info(f"Starting digest generation for X-Ray {params.xray_id}")

        try:
            # Step 1: Get the digest X-Ray and validate it
            xray = await workflow.execute_activity_method(
                XRayActivities.get_xray_by_id,
                params.xray_id,
                start_to_close_timeout=timedelta(minutes=5),
            )

            if not xray:
                return XRayDigestWorkflowResult(
                    success=False,
                    message=f"X-Ray {params.xray_id} not found",
                    processing_time_seconds=workflow.now().timestamp() - start_time,
                )

            if xray.xray_type != "digest":
                return XRayDigestWorkflowResult(
                    success=False,
                    message=f"X-Ray {params.xray_id} is not a digest type",
                    processing_time_seconds=workflow.now().timestamp() - start_time,
                )

            # Step 2: Get xray owner by id
            xray_owner = await workflow.execute_activity_method(
                UserActivities.get_user_by_id,
                xray.owner_id,
                start_to_close_timeout=timedelta(minutes=5),
            )

            # Extract team_id from the dictionary structure
            team_id = None
            if xray_owner and xray_owner.get("team"):
                team_id = xray_owner["team"].get("id")

            if not xray_owner:
                return XRayDigestWorkflowResult(
                    success=False,
                    message=f"X-Ray owner {xray.owner_id} not found",
                    processing_time_seconds=workflow.now().timestamp() - start_time,
                )

            # Step 3: Generate digest content
            digest_content = await workflow.execute_activity_method(
                XRayActivities.generate_scheduled_digest_content,
                GenerateScheduledDigestContentInput(
                    xray_id=params.xray_id,
                    team_id=int(team_id) if team_id else None,
                ),
                start_to_close_timeout=timedelta(minutes=10),
            )

            if not digest_content:
                return XRayDigestWorkflowResult(
                    success=False,
                    message=f"Failed to generate digest content: {digest_content}",
                    processing_time_seconds=workflow.now().timestamp() - start_time,
                )

            # Step 4: Create commit with digest content
            commit_id = await workflow.execute_activity_method(
                XRayActivities.create_digest_commit,
                CreateDigestCommitInput(
                    xray_id=params.xray_id,
                    digest_content=digest_content,
                    author_id=xray.owner_id,
                ),
                start_to_close_timeout=timedelta(minutes=5),
            )

            # Step 5: Update last_digest_at timestamp
            await workflow.execute_activity_method(
                XRayActivities.update_xray_last_digest_at,
                params.xray_id,
                start_to_close_timeout=timedelta(minutes=2),
            )
            processing_time = workflow.now().timestamp() - start_time

            workflow.logger.info(
                f"Successfully generated digest for X-Ray {params.xray_id} in {processing_time:.2f} seconds"
            )

            return XRayDigestWorkflowResult(
                success=True,
                message="Successfully generated digest content and created commit.",
                digest_content=digest_content,
                commit_id=commit_id,
                processing_time_seconds=processing_time,
            )

        except Exception as e:
            processing_time = workflow.now().timestamp() - start_time
            workflow.logger.error(
                f"Digest generation failed for X-Ray {params.xray_id}: {e}"
            )

            return XRayDigestWorkflowResult(
                success=False,
                message=f"Digest generation failed: {str(e)}",
                processing_time_seconds=processing_time,
            )
