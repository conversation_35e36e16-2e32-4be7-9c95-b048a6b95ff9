from nebula.db.models.xray import XRay
from piccolo.columns import BigSerial, Text, Timestamp, BigInt
from piccolo.columns.column_types import ForeignKey
from piccolo.columns.defaults.timestamp import TimestampNow
from piccolo.table import Table


class XRayDocumentCommit(Table, tablename="xray_doc_commits"):
    """
    Represents a commit to an XRay document.
    Each commit is a snapshot of the document at a specific point in time.
    """

    id = BigSerial(primary_key=True)
    xray_id = ForeignKey(references=XRay)
    author_id = BigInt(null=False)
    content = Text(null=False)  # Full document content at this commit
    created_at = Timestamp(default=TimestampNow())
    updated_at = Timestamp(default=TimestampNow())
