from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from typing import List
    from pydantic import BaseModel
    from datetime import timedelta
    from nebula.temporal.activities.xray_act import XRayActivities
    from nebula.temporal.types import (
        XRayDeepScanParams,
        XRayQuickScanActivityParams,
        XRayScanWorkflowParams,
        XRayScanWorkflowResult,
    )


@workflow.defn(name="XRayScan")
class XRayScanWorkflow:
    @workflow.run
    async def run(self, params: XRayScanWorkflowParams) -> XRayScanWorkflowResult:
        start_time = workflow.now().timestamp()

        try:
            # TODO: This should be paginated
            # All user's xrays except digests.
            # Digest specific workflows are registered when xray is created and are powered by Temporal's Schedules feature.
            # See https://docs.temporal.io/evaluate/development-production-features/schedules
            xrays = await workflow.execute_activity_method(
                XRayActivities.get_active_xrays_no_digests,
                params.user_id,
                start_to_close_timeout=timedelta(minutes=5),
            )

            # Get transcript
            transcript = await workflow.execute_activity_method(
                XRayActivities.get_transcript_by_recurrence_id,
                params.recurrence_id,
                start_to_close_timeout=timedelta(minutes=5),
            )
            if not transcript:
                return XRayScanWorkflowResult(
                    success=False,
                    message=f"No transcript found for recurrence ID {params.recurrence_id}",
                    xrays_processed=0,
                    processing_time_seconds=workflow.now().timestamp() - start_time,
                )

            # Run quick scan to find relevant xrays
            quick_scan_result = await workflow.execute_activity_method(  # type: ignore
                XRayActivities.quick_scan,
                XRayQuickScanActivityParams(
                    recurrence_id=params.recurrence_id,
                    transcript=transcript,
                    xrays=xrays,
                ),
                start_to_close_timeout=timedelta(minutes=5),
            )

            # If no xrays are relevant, return early
            if not quick_scan_result.xray_ids:
                processing_time = workflow.now().timestamp() - start_time
                return XRayScanWorkflowResult(
                    success=True,
                    message="No relevant xrays found",
                    xrays_processed=0,
                    processing_time_seconds=processing_time,
                )

            class ProcessedXRay(BaseModel):
                xray_id: int
                xray_title: str
                success: bool
                changed: bool
                message: str

            # Process each relevant xray
            processed_xrays: List[ProcessedXRay] = []

            for xray_id in quick_scan_result.xray_ids:
                # Get the xray
                xray = await workflow.execute_activity_method(
                    XRayActivities.get_xray_by_id,
                    xray_id,
                    start_to_close_timeout=timedelta(minutes=5),
                )

                if xray:
                    # Run deep scan for this document
                    deep_scan_result = await workflow.execute_activity_method(
                        XRayActivities.deep_scan,
                        XRayDeepScanParams(
                            recurrence_id=params.recurrence_id,
                            transcript=transcript,
                            xray=xray,
                            quick_result_scan=quick_scan_result,
                        ),
                        start_to_close_timeout=timedelta(minutes=5),
                    )

                    processed_xrays.append(
                        ProcessedXRay(
                            xray_id=xray_id,
                            xray_title=xray.title,
                            success=deep_scan_result.success,
                            changed=deep_scan_result.changed,
                            message=deep_scan_result.message,
                        )
                    )

            processing_time = workflow.now().timestamp() - start_time
            return XRayScanWorkflowResult(
                success=True,
                message=f"Successfully processed {len(processed_xrays)} documents",
                xrays_processed=len(processed_xrays),
                processing_time_seconds=processing_time,
            )
        except Exception as e:
            processing_time = workflow.now().timestamp() - start_time

            return XRayScanWorkflowResult(
                success=False,
                message=f"Workflow failed with error: {str(e)}",
                xrays_processed=0,
                processing_time_seconds=processing_time,
            )
