from temporalio import activity

from nebula.temporal.types import (
    MeetingSuggestionsWorkflowParams,
    TranscriptionBatchesResult,
    AllUsersMeetingSuggestionsRecordResult,
    ParticipantsResult,
    ExtractAllUsersMeetingSuggestionsInput,
    CreateAllUsersMeetingSuggestionsInput,
    AllUsersMeetingSuggestionsExtraction,
    ProcessedTranscriptWithUsersResult,
)
from nebula.services.meeting_suggestions.models import (
    ParticipantInfo,
    UserMeetingSuggestionItem,
)


class MockMeetingSuggestionsActivities:
    @activity.defn
    async def process_transcript_with_user_ids(
        self, transcript_batches: TranscriptionBatchesResult
    ) -> ProcessedTranscriptWithUsersResult:
        """Mock activity to process transcripts with user IDs for multi-user processing."""
        # Process transcript with user ID format and extract participants
        transcript_lines = []
        participants = set()
        
        for batch in transcript_batches.transcription_batches:
            user_id = batch.speaker_user_id
            
            if user_id:
                participants.add((user_id, batch.speaker_full_name))
            
            user_id_info = f" (userID: {user_id})" if user_id else ""
            transcript_lines.append(f"{batch.speaker_full_name}{user_id_info}: {batch.text}")
        
        # Convert participants to list
        participant_list = [
            ParticipantInfo(user_id=user_id, name=name)
            for user_id, name in participants
            if user_id
        ]
        
        return ProcessedTranscriptWithUsersResult(
            transcript="\n".join(transcript_lines),
            locale="en-US",
            participants=participant_list
        )

    @activity.defn
    async def get_all_participants(
        self, session_info: MeetingSuggestionsWorkflowParams
    ) -> ParticipantsResult:
        """Mock activity to get all participants."""
        # Return mock participants
        mock_participants = [
            ParticipantInfo(user_id="user_123", name="John Doe"),
            ParticipantInfo(user_id="user_456", name="Sarah Smith"),
            ParticipantInfo(user_id="user_789", name="Test User"),
        ]
        return ParticipantsResult(participants=mock_participants)

    @activity.defn
    async def extract_all_users_meeting_suggestions(
        self, input: ExtractAllUsersMeetingSuggestionsInput
    ) -> AllUsersMeetingSuggestionsExtraction:
        """Mock activity to extract meeting suggestions for all users using a single LLM call."""
        # Return mock suggestions for all participants (generated in single call simulation)
        mock_user_suggestions = []
        
        for participant in input.participants:
            # Generate 3 suggestions per user (simulating single LLM call response)
            mock_user_suggestions.extend([
                UserMeetingSuggestionItem(
                    user_id=participant.user_id,
                    category="self_review",
                    prompt=f"How effectively did I contribute to today's planning session?"
                ),
                UserMeetingSuggestionItem(
                    user_id=participant.user_id,
                    category="action_items",
                    prompt=f"What are my action items from the quarterly roadmap review?"
                ),
                UserMeetingSuggestionItem(
                    user_id=participant.user_id,
                    category="content_feedback",
                    prompt=f"How can I improve my presentation skills for future meetings?"
                )
            ])
        
        return AllUsersMeetingSuggestionsExtraction(user_suggestions=mock_user_suggestions)

    @activity.defn
    async def create_all_users_meeting_suggestions_record(
        self, input: CreateAllUsersMeetingSuggestionsInput
    ) -> AllUsersMeetingSuggestionsRecordResult:
        """Mock activity to create meeting suggestions records for all users."""
        # Calculate mock statistics
        total_suggestions = len(input.suggestions.user_suggestions)
        unique_users = len(set(suggestion.user_id for suggestion in input.suggestions.user_suggestions))
        
        return AllUsersMeetingSuggestionsRecordResult(
            total_records_created=total_suggestions,
            participant_count=unique_users,
        ) 