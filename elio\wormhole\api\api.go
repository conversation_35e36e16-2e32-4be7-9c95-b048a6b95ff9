package api

import (
	"encoding/json"
	"regexp"

	"encore.app/shared"

	luxor_api "encore.app/wormhole/services/luxor-api"
)

type UpsertRecordingRequest struct {
	luxor_api.UpsertRecordingDTO
}

type UpsertRecordingResponse struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID,omitempty"`
	PlaylistLandscape   string `json:"playlistLandscape,omitempty" encore:"optional"`
	PlaylistPortrait    string `json:"playlistPortrait,omitempty" encore:"optional"`
}

type MessageItemType string

const (
	ActionItems  MessageItemType = "action_items"
	PMS          MessageItemType = "pms"
	MeetingsList MessageItemType = "meetings_list"
	Text         MessageItemType = "text"
)

type MessageItemSourceData struct {
	RecurrenceID string   `json:"recurrenceId"`
	SessionID    string   `json:"sessionId"`
	Title        string   `json:"title"`
	About        *string  `json:"about,omitempty"`
	Participants []string `json:"participants"`
	StartedAt    int      `json:"startedAt"`
	EndedAt      int      `json:"endedAt"`
	Organizer    *string  `json:"organizer,omitempty"`
}

type MessageItemSource struct {
	Type string                `json:"type"`
	Data MessageItemSourceData `json:"data"`
}

type Sources map[string][]MessageItemSource

type MessageItemContentType string

const (
	Markdown MessageItemContentType = "markdown"
)

type MessageItem struct {
	Type        MessageItemType        `json:"type"`
	Content     string                 `json:"content"`
	ContentType MessageItemContentType `json:"contentType"`
}

type Role string

const (
	User Role = "user"
	AI   Role = "ai"
)

type Message struct {
	Id       string        `json:"id"`
	Role     Role          `json:"role"`
	Sources  *Sources      `json:"sources,omitempty"`
	Items    []MessageItem `json:"items"`
	Feedback int           `json:"feedback"`
}

type AskAIBody struct {
	Query    string  `json:"query"`
	ThreadID *string `json:"threadId,omitempty"`
	Refresh  bool    `json:"refresh"`
}

type UserFeedbackResponseData struct {
	Message Message `json:"message"`
}

type UserFeedbackResponseDTO struct {
	Data    *UserFeedbackResponseData `json:"data,omitempty"`
	Message string                    `json:"message"`
}

// PresenceResponse defines the response payload for the Presence endpoint, containing a list of users who are connected to this endpoint.
type PresenceResponse struct {
	Code shared.HttpErrorCode `json:"code"`
	Data []shared.UserDTO     `json:"data"`
}

// EndPresenceRequest disconnects users connected to the presence endpoint for a given session.
type EndPresenceRequest struct {
	SessionID string `json:"sessionID"`
}

type AIFeedEventType = string

const (
	PostingStarted AIFeedEventType = "posting_started"
	PostingEnded   AIFeedEventType = "posting_ended"
	EditingStarted AIFeedEventType = "editing_started"
	EditingEnded   AIFeedEventType = "editing_ended"
	WorkingStarted AIFeedEventType = "working_started"
	WorkingEnded   AIFeedEventType = "working_ended"
	FeedItemReady  AIFeedEventType = "feed_item_ready"
)

// AIFeedEvent defines the request payload for the AIFeedEvent endpoint.
type AIFeedEvent struct {
	EventType           AIFeedEventType `json:"eventType"`
	AIFeedID            int64           `json:"aiFeedID"`
	SessionID           string          `json:"sessionID"`
	SessionRecurrenceID string          `json:"sessionRecurrenceID"`
}

type IntegrationEventType string
type SalesforceEventType string
type HubSpotEventType string

const (
	SalesforceBindingCreated IntegrationEventType = "salesforce.binding.created"
	SalesforceBindingUpdated IntegrationEventType = "salesforce.binding.updated"
	SalesforceBindingDeleted IntegrationEventType = "salesforce.binding.deleted"
)

const (
	HubSpotBindingCreated IntegrationEventType = "hubspot.binding.created"
	HubSpotBindingUpdated IntegrationEventType = "hubspot.binding.updated"
	HubSpotBindingDeleted IntegrationEventType = "hubspot.binding.deleted"
)

type GetSalesforceBindingRequest struct {
}

type PostIntegrationEventRequest struct {
	EventType IntegrationEventType `json:"eventType"`
	Payload   json.RawMessage      `json:"payload"`
}

type BindingIntegrationEventData struct {
	ID                  string `json:"id,omitempty"`
	UserId              string `json:"userId,omitempty"`
	SessionID           string `json:"sessionId,omitempty"`
	SessionRecurrenceID string `json:"sessionRecurrenceId,omitempty"`
}

func (req PostIntegrationEventRequest) MatchesEvent(pattern string) bool {
	// Escape dot (.) and replace '*' with '.*' to match any characters
	regexPattern := "^" + regexp.QuoteMeta(pattern) + "$"
	regexPattern = regexp.MustCompile(`\\\*`).ReplaceAllString(regexPattern, ".*")
	matched, _ := regexp.MatchString(regexPattern, string(req.EventType))
	return matched
}

func (req PostIntegrationEventRequest) BindingIntegrationData() BindingIntegrationEventData {
	payload := BindingIntegrationEventData{}
	json.Unmarshal(req.Payload, &payload)
	return payload
}

func (req PostIntegrationEventRequest) Validate() error {
	if !req.MatchesEvent("*.binding.*") {
		return nil
	}
	payload := req.BindingIntegrationData()
	if shared.ParseInt(payload.SessionID) == 0 {
		return shared.B().WithCode(shared.ValidationErrorCode)
	}
	if shared.ParseInt(payload.SessionRecurrenceID) == 0 {
		return shared.B().WithCode(shared.ValidationErrorCode)
	}
	if shared.ParseInt(payload.UserId) == 0 {
		return shared.B().WithCode(shared.ValidationErrorCode)
	}
	return nil
}

type PostIntegrationEventResponse struct {
	Message string `json:"message"`
}

var PostIntegrationEventResponseOK = &PostIntegrationEventResponse{
	Message: "ok",
}

type AskAIResponseDTO struct {
	Data    *AskAIResponseData `json:"data,omitempty"`
	Message string             `json:"message"`
}

type AskAIResponseData struct {
	Messages []Message `json:"messages"`
	ThreadID *string   `json:"threadId,omitempty"`
}

// MM V2
type AskAIStreamEvent struct {
	Type       string           `json:"type"`
	Code       int              `json:"code"`
	RequestId  string           `json:"requestId"`
	IsFinished bool             `json:"isFinished"`
	Data       *json.RawMessage `json:"data"`
	Message    *string          `json:"message"`
}

type MMSuggestion struct {
	Id          int             `json:"id"`
	Text        string          `json:"text"`
	Short       string          `json:"short"`
	Explanation string          `json:"explanation"`
	Sources     json.RawMessage `json:"sources"`
	CreatedAt   int             `json:"createdAt"`
	UpdatedAt   int             `json:"updatedAt"`
}

type GetListParams struct {
	Limit string `query:"limit"`
	Skip  string `query:"skip"`
}

type GetMMSuggestionsResponseDTO struct {
	Data struct {
		Total       int            `json:"total"`
		Suggestions []MMSuggestion `json:"suggestions"`
	} `json:"data"`
	Message string `json:"message"`
}

type RecurSessionRequest struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
	Fingerprint         string `json:"fingerprint,omitempty"`
}

// TODO: List out the possible params
type CreateSessionRequest struct {
	Title string `json:"title"`
	About string `json:"about"`
}

type CreateSessionWithUserRequest struct {
	Title  string `json:"title"`
	About  string `json:"about"`
	UserID string `json:"userID"`
}

type UpdateSessionByIDRequest struct {
	Session shared.SessionDTO `json:"session"`
	UserID  string            `json:"userID"`
}

type ApproveOrDenySessionAccessRequestData struct {
	CallTrixtaAction  bool   `json:"callTrixtaAction,omitempty"`
	Id                string `json:"id"`
	RestrictionStatus string `json:"restrictionStatus"`
	Fingerprint       string `header:"X-Fingerprint"`
}

type ApproveOrDenySessionAccessResponseData struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
	UserEmail           string `json:"userEmail"`
}

type AccessControlItem struct {
	Type  *string `json:"type,omitempty"`
	Value *string `json:"value,omitempty"`
}

type AddSessionAccessControlRulesRequest struct {
	Domains             []string            `json:"domains"`
	Emails              []string            `json:"emails"`
	SessionID           string              `json:"sessionID"`
	SessionRecurrenceID string              `json:"sessionRecurrenceID"`
	Message             *string             `json:"message,omitempty"`
	OverrideRules       *bool               `json:"overrideRules,omitempty"`
	ViewerAccessControl []AccessControlItem `json:"viewerAccessControl"`
	Fingerprint         string              `header:"X-Fingerprint"`
}

type AddSessionAccessRulesResponse struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
	Data                struct {
		AccessRules []shared.SessionAccessRuleDTO `json:"accessRules"`
		UserEmails  []string                      `json:"userEmails"`
	} `json:"data"`
}

type RemoveSessionAccessRuleRequest struct {
	Id                  string `json:"id"`
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
}

type RemoveSessionAccessRuleResponse struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
	Id                  string `json:"id"`
}

type AskAIRequestBodyDTO struct {
	MessageId *string `json:"messageId"`
	Query     *string `json:"query,omitempty" encore:"sensitive"`
	Refresh   *bool   `json:"refresh"`
	RequestId string  `json:"requestId"`
	Retry     *bool   `json:"retry"`
	Sessions  *[]struct {
		Id            string   `json:"id"`
		RecurrenceIds []string `json:"recurrenceIds"`
	} `json:"sessions"`
	ThreadId *string  `json:"threadId"`
	Timeout  *float32 `json:"timeout,omitempty"`
	Tz       *string  `json:"tz"`
}

type PostSessionSummaryRequest struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
}

type PostSessionSummaryResponse struct {
	ErrCode shared.HttpErrorCode `json:"errCode"`
	Message string               `json:"message"`
	Data    struct {
		ID                  int64           `json:"id,omitempty"`
		SessionID           string          `json:"session_id,omitempty"`
		SessionRecurrenceID string          `json:"session_recurrence_id,omitempty"`
		Content             json.RawMessage `json:"content,omitempty" encore:"sensitive"`
		HTML                string          `json:"html,omitempty" encore:"sensitive"`
		MD                  string          `json:"md,omitempty" encore:"sensitive"`
		TLDR                string          `json:"tldr,omitempty" encore:"sensitive"`
		CreatedAt           string          `json:"created_at,omitempty"`
		UpdatedAt           string          `json:"updated_at,omitempty"`
	} `json:"data"`
}

// NewPostSessionSummaryResponse creates a new PostSessionSummaryResponse with the given error code
func NewPostSessionSummaryResponse(errCode shared.HttpErrorCode) *PostSessionSummaryResponse {
	return &PostSessionSummaryResponse{
		Message: shared.ErrorMessages[errCode],
		ErrCode: errCode,
	}
}

type AskAISyncResponseDTO struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    *struct {
		Content  string           `json:"content,omitempty" encore:"sensitive"`
		Sources  *json.RawMessage `json:"sources,omitempty" encore:"sensitive"`
		ThreadId string           `json:"threadId,omitempty"`
	} `json:"data,omitempty"`
}

type GetMeetingMetadataRequest struct {
	SessionID           string `json:"sessionID"`
	SessionRecurrenceID string `json:"sessionRecurrenceID"`
}
