# SharedBillingTransactionDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**custom_data** | **object** |  | 
**items** | [**List[SharedBillingTransactionItemDTO]**](SharedBillingTransactionItemDTO.md) |  | 
**payments** | [**List[SharedBillingTransactionPaymentAttemptDTO]**](SharedBillingTransactionPaymentAttemptDTO.md) |  | 

## Example

```python
from elio_client.models.shared_billing_transaction_dto import SharedBillingTransactionDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedBillingTransactionDTO from a JSON string
shared_billing_transaction_dto_instance = SharedBillingTransactionDTO.from_json(json)
# print the JSON string representation of the object
print(SharedBillingTransactionDTO.to_json())

# convert the object into a dict
shared_billing_transaction_dto_dict = shared_billing_transaction_dto_instance.to_dict()
# create an instance of SharedBillingTransactionDTO from a dict
shared_billing_transaction_dto_from_dict = SharedBillingTransactionDTO.from_dict(shared_billing_transaction_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


