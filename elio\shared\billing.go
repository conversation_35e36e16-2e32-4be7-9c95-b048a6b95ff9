package shared

import (
	"encoding/json"
	paddleSDK "github.com/PaddleHQ/paddle-go-sdk/v3"
	"github.com/jinzhu/copier"
	"github.com/stoewer/go-strcase"
	"strings"
)

type BillingProductDTO struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Description *string    `json:"description,omitempty" encore:"optional"`
	Status      string     `json:"status"`
	Prices      []PriceDTO `json:"prices"`
}

type BillingTransactionDTO struct {
	CustomData json.RawMessage                       `json:"customData"`
	Items      []BillingTransactionItemDTO           `json:"items"`
	Payments   []BillingTransactionPaymentAttemptDTO `json:"payments"`
	paddleSDK.Transaction
}

func (t BillingTransactionDTO) MarshalJSON() ([]byte, error) {
	type Alias BillingTransactionDTO

	bytes, err := json.Marshal(struct {
		Alias
	}{
		Alias(t),
	})

	data := convertPaddleKeysToRumiKeys(bytes)

	return data, err
}

func (t *BillingTransactionDTO) UnmarshalJSON(data []byte) error {
	type Alias BillingTransactionDTO

	var tAliased Alias

	data = convertRumiKeysToPaddleKeys(data)

	if err := json.Unmarshal(data, &tAliased); err != nil {
		return err
	}

	copier.Copy(&t, tAliased)

	return nil
}

type BillingPriceDTO struct {
	paddleSDK.Price
}

func (p BillingPriceDTO) MarshalJSON() ([]byte, error) {
	type Alias BillingPriceDTO

	bytes, err := json.Marshal(struct {
		Alias
	}{
		Alias(p),
	})

	return convertPaddleKeysToRumiKeys(bytes), err
}

func (p *BillingPriceDTO) UnmarshalJSON(data []byte) error {
	type Alias BillingPriceDTO

	var pAliased Alias

	data = convertRumiKeysToPaddleKeys(data)

	if err := json.Unmarshal(data, &pAliased); err != nil {
		return err
	}

	copier.Copy(&p, pAliased)

	return nil
}

type BillingTransactionItemDTO struct {
	Price BillingPriceDTO `json:"price"`
	paddleSDK.TransactionItem
}

func (p BillingTransactionItemDTO) MarshalJSON() ([]byte, error) {
	type Alias BillingTransactionItemDTO

	bytes, err := json.Marshal(struct {
		Alias
	}{
		Alias(p),
	})

	return convertPaddleKeysToRumiKeys(bytes), err
}

func (p *BillingTransactionItemDTO) UnmarshalJSON(data []byte) error {
	type Alias BillingTransactionItemDTO

	var pAliased Alias

	data = convertRumiKeysToPaddleKeys(data)

	if err := json.Unmarshal(data, &pAliased); err != nil {
		return err
	}

	copier.Copy(&p, pAliased)

	return nil
}

type BillingTransactionPaymentAttemptDTO struct {
	MethodDetails BillingPaymentMethodDetailsDTO `json:"method_details"`
	paddleSDK.TransactionPaymentAttempt
}

func (p BillingTransactionPaymentAttemptDTO) MarshalJSON() ([]byte, error) {
	type Alias BillingTransactionPaymentAttemptDTO

	bytes, err := json.Marshal(struct {
		Alias
	}{
		Alias(p),
	})

	return convertPaddleKeysToRumiKeys(bytes), err
}

func (p *BillingTransactionPaymentAttemptDTO) UnmarshalJSON(data []byte) error {
	type Alias BillingTransactionPaymentAttemptDTO

	var pAliased Alias

	data = convertRumiKeysToPaddleKeys(data)

	if err := json.Unmarshal(data, &pAliased); err != nil {
		return err
	}

	copier.Copy(&p, pAliased)

	return nil
}

type BillingPaymentResultDTOErrorCode string
type BillingPaymentResultDTOStatus string

type BillingPaymentResultDTO struct {
	Status    BillingPaymentResultDTOStatus    `json:"status"`
	ErrorCode BillingPaymentResultDTOErrorCode `json:"errorCode"`
}

type BillingPaymentMethodType string
type BillingPaymentMethodCardType string

type BillingCard struct {
	paddleSDK.Card
}

func (p BillingCard) MarshalJSON() ([]byte, error) {
	type Alias BillingCard

	bytes, err := json.Marshal(struct {
		Alias
	}{
		Alias(p),
	})

	return convertPaddleKeysToRumiKeys(bytes), err
}

func (p *BillingCard) UnmarshalJSON(data []byte) error {
	type Alias BillingCard

	var cAliased Alias

	data = convertRumiKeysToPaddleKeys(data)

	if err := json.Unmarshal(data, &cAliased); err != nil {
		return err
	}

	copier.Copy(&p, cAliased)

	return nil
}

type BillingPaymentMethodDetailsDTO struct {
	Type string       `json:"type"`
	Card *BillingCard `json:"card,omitempty"`
}

func (p BillingPaymentMethodDetailsDTO) MarshalJSON() ([]byte, error) {
	type Alias BillingPaymentMethodDetailsDTO

	bytes, err := json.Marshal(struct {
		Alias
	}{
		Alias(p),
	})

	return convertPaddleKeysToRumiKeys(bytes), err
}

func (p *BillingPaymentMethodDetailsDTO) UnmarshalJSON(data []byte) error {
	type Alias BillingPaymentMethodDetailsDTO

	var pAliased Alias

	data = convertRumiKeysToPaddleKeys(data)

	if err := json.Unmarshal(data, &pAliased); err != nil {
		return err
	}

	copier.Copy(&p, pAliased)

	return nil
}

func convertKeys(j json.RawMessage, fn func(string) string) json.RawMessage {
	m := make(map[string]json.RawMessage)
	if err := json.Unmarshal(j, &m); err != nil {
		// Not a JSON object
		return j
	}

	for k, v := range m {
		fixed := fn(k)
		delete(m, k)
		m[fixed] = convertKeys(v, fn)
	}

	b, err := json.Marshal(m)
	if err != nil {
		return j
	}

	return b
}

func convertPaddleKeysToRumiKeys(j json.RawMessage) json.RawMessage {
	return convertKeys(j, func(s string) string {
		s = strcase.LowerCamelCase(s)
		s = strings.ReplaceAll(s, "Id", "ID")
		return s
	})
}

func convertRumiKeysToPaddleKeys(j json.RawMessage) json.RawMessage {
	return convertKeys(j, func(s string) string {
		s = strcase.SnakeCase(s)
		s = strings.ReplaceAll(s, "ID", "Id")
		return s
	})
}
