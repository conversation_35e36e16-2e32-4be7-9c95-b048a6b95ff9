{"success": true, "message": "Data fetched successfully", "data": {"addressID": "add_01hnfk409pz4hc9ve4v9a2wvjn", "billingCycle": {"frequency": 1, "interval": "month"}, "canceledAt": "2024-02-14T11:06:00.679Z", "collectionMode": "automatic", "createdAt": **********, "currencyCode": "USD", "customerID": "ctm_01hnfk339kxyjc6504m6m6yp9s", "id": "2", "items": [{"createdAt": "2024-01-31T11:05:58.139Z", "nextBilledAt": null, "previouslyBilledAt": null, "price": {"billingCycle": {"frequency": 1, "interval": "month"}, "id": "pri_01hkpwf6jrrs65n0rk1wtktvqz", "productID": "pro_01hdd98039vq8qsd44rx9gvybh", "taxMode": "account_setting", "trialPeriod": {"frequency": 14, "interval": "day"}, "unitPrice": {"amount": "999", "currencyCode": "USD"}, "quantity": null}, "quantity": 1, "recurring": true, "status": "trialing", "trialDates": {"endsAt": "2024-02-14T11:05:07Z", "startsAt": "2024-01-31T11:05:58.139Z"}, "updatedAt": "2024-02-14T11:06:00.686Z"}], "paddleSubscriptionID": "sub_01hnfk4wkv9gbmrv72fhsq1mgt2", "planConfig": {"aiFeed": {"enabled": true}, "customFeedItems": {"enabled": false}, "customIntegrations": {"enabled": false}, "id": "943369326642071161", "integrations": {"apps": null, "enabled": true}, "meetingMemory": {"enabled": true}, "meetingSummary": {"enabled": true}, "meetingTemplates": {"enabled": false}, "meetingWorkflows": {"enabled": false}, "meetings": {"enabled": true, "max": 0}, "modelSegregation": {"enabled": false}, "offTheRecord": {"enabled": true}, "paddleProductID": "pro_01hn31q5grky5xcyp58401kp1v", "paddleProductName": "premium", "queueMode": {"enabled": true}, "recording": {"enabled": true, "local": true}, "stream": {"enabled": true, "quality": 1080}, "support": {"enabled": true, "type": "premium"}, "crm": {"enabled": false}, "bots": {"enabled": false}, "timeLimit": {"enabled": true, "max": 9999999}, "createdAt": 1706691387, "updatedAt": 1706691387}, "planConfigOverrides": {"foobar": "test"}, "startedAt": "2024-01-31T11:05:58.139Z", "status": "active", "updatedAt": 1707908762, "userID": "3"}}