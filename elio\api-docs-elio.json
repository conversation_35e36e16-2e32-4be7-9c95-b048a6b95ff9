{"components": {"responses": {"APIError": {"content": {"application/json": {"schema": {"externalDocs": {"url": "https://pkg.go.dev/encore.dev/beta/errs#Error"}, "properties": {"code": {"description": "Error code", "example": "not_found", "externalDocs": {"url": "https://pkg.go.dev/encore.dev/beta/errs#ErrCode"}, "type": "string"}, "details": {"description": "Error details", "type": "object"}, "message": {"description": "Error message", "type": "string"}}, "title": "APIError", "type": "object"}}}, "description": "Error response"}}, "schemas": {"api.AIFeedEventType": {"type": "string"}, "api.AccessControlItem": {"properties": {"type": {"type": "string"}, "value": {"type": "string"}}, "required": ["type", "value"], "type": "object"}, "api.AccessRuleType": {"type": "string"}, "api.AccessType": {"type": "string"}, "api.ChannelRole": {"type": "string"}, "api.CreateAuthUserInput": {"properties": {"email": {"type": "string"}, "id": {"type": "string"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "teamID": {"type": "string"}, "teamRoleIDs": {"items": {"type": "string"}, "type": "array"}}, "required": ["id", "email", "teamID", "roleIDs", "teamRoleIDs"], "type": "object"}, "api.GetUserPaymentMethodDetailsResponseData": {"properties": {"lastPayment": {"$ref": "#/components/schemas/shared.PaddlePaymentResultDTO"}}, "required": ["lastPayment"], "type": "object"}, "api.HeardSpeaker": {"properties": {"audioSourceUser": {"$ref": "#/components/schemas/shared.UserDTO"}, "firstUtterance": {"format": "date-time", "title": "FirstUtterance is the time when the speaker started speaking\n", "type": "string"}, "lastUtterance": {"format": "date-time", "title": "LastUtterance is the time when the speaker stopped speaking\n", "type": "string"}, "snippets": {"items": {"$ref": "#/components/schemas/api.SpeakerSnippet"}, "title": "Snippets is a collection of utterances from this speaker with their time ranges\n", "type": "array"}, "speakerIdentity": {"$ref": "#/components/schemas/api.IdentifiedSpeaker"}, "speakerName": {"title": "Speaker<PERSON><PERSON> is the name of the speaker, will be \"Speaker 1\" when unidentified\n", "type": "string"}, "speakerUID": {"title": "SpeakerUID is the unique identifier of the speaker contains the userID\n", "type": "string"}}, "required": ["audioSourceUser", "<PERSON><PERSON><PERSON>", "speakerUID", "speakerIdentity", "firstUtterance", "lastUtterance", "snippets"], "type": "object"}, "api.IdentifiedSpeaker": {"properties": {"speakerEmail": {"title": "SpeakerEmail optional: email address of the identified speaker\n", "type": "string"}, "speakerFullName": {"title": "Speaker<PERSON><PERSON><PERSON><PERSON> optional: first name of the identified speaker\n", "type": "string"}, "speakerUID": {"title": "SpeakerUID is the unique identifier of the speaker\n", "type": "string"}, "speakerUserID": {"title": "SpeakerUserID Voids other identifying fields when present\n", "type": "string"}}, "required": ["speakerUID", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "speakerEmail", "speakerUserID"], "type": "object"}, "api.InReviewAccessRequestDTO": {"properties": {"accessType": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"$ref": "#/components/schemas/api.RestrictionStatus"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "type": {"$ref": "#/components/schemas/api.AccessRuleType"}, "updatedAt": {"format": "int64", "type": "integer"}, "user": {"properties": {"avatar": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "lastName": {"type": "string"}}, "required": ["id", "avatar", "firstName", "lastName"], "type": "object"}, "value": {"type": "string"}}, "required": ["id", "restrictionStatus", "requestMessage", "sessionID", "sessionRecurrenceID", "type", "value", "accessType", "updatedAt", "createdAt", "user"], "type": "object"}, "api.IntegrationEventType": {"type": "string"}, "api.ListSessionsResponseData": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/shared.SessionDTO"}, "type": "array"}}, "required": ["sessions"], "type": "object"}, "api.ListUserTransactionsResponseData": {"properties": {"invoiceUrl": {"type": "string"}, "transaction": {"$ref": "#/components/schemas/shared.PaddleTransactionDTO"}}, "required": ["transaction", "invoiceUrl"], "type": "object"}, "api.LobbySummaryDTO": {"properties": {"about": {"title": "About Defaults owner bio\n", "type": "string"}, "avatarURL": {"title": "AvatarURL Defaults owner avatar URL\n", "type": "string"}, "isActive": {"type": "boolean"}, "isOwnerDetails": {"title": "Extra details for multi owner lobbies\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "name": {"title": "Name Defaults owner first name and last name\n", "type": "string"}, "owners": {"items": {"$ref": "#/components/schemas/shared.UserSocialDTO"}, "title": "TODO we might not even need this slice\n", "type": "array"}, "slug": {"type": "string"}}, "required": ["lobbyID", "slug", "isActive", "owners", "isOwnerDetails", "avatarURL", "name", "about"], "type": "object"}, "api.MMSuggestion": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "explanation": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "short": {"type": "string"}, "sources": {"type": "object"}, "text": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["id", "text", "short", "explanation", "sources", "createdAt", "updatedAt"], "type": "object"}, "api.MeetingMetadata": {"properties": {"key_decisions": {"items": {"type": "string"}, "type": "array"}, "keywords": {"items": {"type": "string"}, "type": "array"}, "meeting_id": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}, "short_summary": {"type": "string"}, "title": {"type": "string"}, "topics": {"items": {"type": "string"}, "type": "array"}, "type": {"type": "string"}}, "required": ["meeting_id", "title", "participants", "type", "topics", "short_summary", "key_decisions", "keywords"], "type": "object"}, "api.Message": {"properties": {"feedback": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "items": {"items": {"$ref": "#/components/schemas/api.MessageItem"}, "type": "array"}, "role": {"$ref": "#/components/schemas/api.Role"}, "sources": {"$ref": "#/components/schemas/api.Sources"}}, "required": ["id", "role", "sources", "items", "feedback"], "type": "object"}, "api.MessageItem": {"properties": {"content": {"type": "string"}, "contentType": {"$ref": "#/components/schemas/api.MessageItemContentType"}, "type": {"$ref": "#/components/schemas/api.MessageItemType"}}, "required": ["type", "content", "contentType"], "type": "object"}, "api.MessageItemContentType": {"type": "string"}, "api.MessageItemSource": {"properties": {"data": {"$ref": "#/components/schemas/api.MessageItemSourceData"}, "type": {"type": "string"}}, "required": ["type", "data"], "type": "object"}, "api.MessageItemSourceData": {"properties": {"about": {"type": "string"}, "endedAt": {"format": "int64", "type": "integer"}, "organizer": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}, "recurrenceId": {"type": "string"}, "sessionId": {"type": "string"}, "startedAt": {"format": "int64", "type": "integer"}, "title": {"type": "string"}}, "required": ["recurrenceId", "sessionId", "title", "about", "participants", "startedAt", "endedAt", "organizer"], "type": "object"}, "api.MessageItemType": {"type": "string"}, "api.RecallSearchHit": {"properties": {"confidence": {"type": "number"}, "end_time": {"type": "number"}, "start_time": {"type": "number"}, "text": {"type": "string"}}, "required": ["text", "start_time", "end_time", "confidence"], "type": "object"}, "api.RecallTranscriptionsWebhookData": {"properties": {"bot_id": {"type": "string"}, "log": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookLog"}, "search": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookSearch"}, "status": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookStatus"}, "transcript": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookTranscript"}}, "required": ["bot_id", "transcript", "search", "status", "log"], "type": "object"}, "api.RecallTranscriptionsWebhookLog": {"properties": {"created_at": {"type": "string"}, "level": {"type": "string"}, "message": {"type": "string"}, "output_id": {"type": "string"}}, "required": ["level", "message", "created_at", "output_id"], "type": "object"}, "api.RecallTranscriptionsWebhookSearch": {"properties": {"hits": {"items": {"$ref": "#/components/schemas/api.RecallSearchHit"}, "type": "array"}, "original_transcript_id": {"format": "int64", "type": "integer"}, "speaker": {"type": "string"}}, "required": ["speaker", "original_transcript_id", "hits"], "type": "object"}, "api.RecallTranscriptionsWebhookStatus": {"properties": {"code": {"type": "string"}, "created_at": {"type": "string"}, "message": {"type": "string"}, "recording_id": {"type": "string"}, "sub_code": {"type": "string"}}, "required": ["code", "sub_code", "message", "recording_id", "created_at"], "type": "object"}, "api.RecallTranscriptionsWebhookTranscript": {"properties": {"is_final": {"type": "boolean"}, "language": {"type": "string"}, "original_transcript_id": {"format": "int64", "type": "integer"}, "speaker": {"type": "string"}, "speaker_id": {"format": "int64", "type": "integer"}, "transcription_provider_speaker": {"type": "string"}, "words": {"items": {"$ref": "#/components/schemas/api.RecallWord"}, "type": "array"}}, "required": ["speaker", "speaker_id", "transcription_provider_speaker", "language", "original_transcript_id", "words", "is_final"], "type": "object"}, "api.RecallWord": {"properties": {"end_time": {"type": "number"}, "start_time": {"type": "number"}, "text": {"type": "string"}}, "required": ["text", "start_time", "end_time"], "type": "object"}, "api.RestrictionStatus": {"type": "string"}, "api.Role": {"type": "string"}, "api.SessionAccessDTO": {"properties": {"id": {"type": "string"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"$ref": "#/components/schemas/api.RestrictionStatus"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "type": {"$ref": "#/components/schemas/api.AccessRuleType"}, "user": {"$ref": "#/components/schemas/api.UserSummaryDTO"}, "value": {"title": "Value is the domain name when Type field is domain otherwise omitted\n", "type": "string"}}, "required": ["id", "sessionID", "sessionRecurrenceID", "restrictionStatus", "type"], "type": "object"}, "api.SessionAccessRulesDTO": {"properties": {"type": {"type": "string"}, "value": {"type": "string"}}, "required": ["type", "value"], "type": "object"}, "api.SessionDefaultGuest": {"properties": {"email": {"type": "string"}, "fullName": {"type": "string"}, "joined": {"type": "boolean"}, "surrogateID": {"type": "string"}}, "required": ["surrogateID", "fullName", "email", "joined"], "type": "object"}, "api.SessionDefaultUser": {"properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "joined": {"type": "boolean"}, "lastName": {"type": "string"}}, "required": ["email", "joined"], "type": "object"}, "api.SessionDefaultUsersDTO": {"properties": {"guests": {"items": {"$ref": "#/components/schemas/api.SessionDefaultGuest"}, "type": "array"}, "users": {"items": {"$ref": "#/components/schemas/api.SessionDefaultUser"}, "type": "array"}}, "type": "object"}, "api.SessionGuest": {"properties": {"email": {"type": "string"}, "fullName": {"type": "string"}, "surrogateID": {"type": "string"}}, "required": ["surrogateID", "fullName", "email"], "type": "object"}, "api.SessionRequestCount": {"properties": {"count": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "recurrenceID": {"type": "string"}}, "required": ["id", "recurrenceID", "count"], "type": "object"}, "api.SessionUser": {"properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "userID": {"type": "string"}}, "required": ["email"], "type": "object"}, "api.SessionUserDTO": {"properties": {"guest": {"$ref": "#/components/schemas/api.SessionGuest"}, "isViewerAccessRevoked": {"type": "boolean"}, "joined": {"type": "boolean"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "sessionUserID": {"type": "string"}, "user": {"$ref": "#/components/schemas/api.SessionUser"}}, "required": ["sessionUserID", "joined", "isViewerAccessRevoked", "roleIDs"], "type": "object"}, "api.Sources": {"additionalProperties": {"items": {"$ref": "#/components/schemas/api.MessageItemSource"}, "type": "array"}, "type": "object"}, "api.SpeakerSnippet": {"properties": {"createdAt": {"format": "date-time", "title": "CreatedAt is when this snippet was created\n", "type": "string"}, "endSec": {"title": "EndSec is the end time of the snippet in seconds\n", "type": "number"}, "startSec": {"title": "StartSec is the start time of the snippet in seconds\n", "type": "number"}, "text": {"title": "Text is the transcript of the snippet\n", "type": "string"}}, "required": ["text", "startSec", "endSec", "createdAt"], "type": "object"}, "api.SubscriptionPlanConfigDTOAndPaddleProductDTO": {"properties": {"config": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTO"}}, "required": ["config"], "type": "object"}, "api.TeamMemberWithRelationsDTO": {"properties": {"TeamMember": {"$ref": "#/components/schemas/shared.TeamMemberDTO"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["TeamMember", "user"], "type": "object"}, "api.TeamRelations": {"properties": {"members": {"items": {"$ref": "#/components/schemas/api.TeamMemberWithRelationsDTO"}, "type": "array"}, "membersCount": {"format": "int64", "type": "integer"}, "owner": {"$ref": "#/components/schemas/shared.UserDTO"}}, "type": "object"}, "api.TeamWithRelations": {"properties": {"team": {"$ref": "#/components/schemas/shared.Team"}, "teamRelations": {"$ref": "#/components/schemas/api.TeamRelations"}}, "required": ["team", "teamRelations"], "type": "object"}, "api.UpdateAccessRule": {"properties": {"accessType": {"$ref": "#/components/schemas/api.AccessType"}, "guestEmail": {"type": "string"}, "guestFullName": {"type": "string"}, "guestSurrogateID": {"format": "int64", "type": "integer"}, "id": {"title": "ID when present all fields except RestrictionStatus are ignored\n", "type": "string"}, "isExternalRequest": {"type": "boolean"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"$ref": "#/components/schemas/api.RestrictionStatus"}, "type": {"$ref": "#/components/schemas/api.AccessRuleType"}, "value": {"type": "string"}}, "type": "object"}, "api.UserFeedbackResponseData": {"properties": {"message": {"$ref": "#/components/schemas/api.Message"}}, "required": ["message"], "type": "object"}, "api.UserMeetingTypeRequestDTO": {"properties": {"id": {"type": "string"}}, "required": ["id"], "type": "object"}, "api.UserSummaryDTO": {"properties": {"avatar": {"type": "string"}, "email": {"type": "string"}, "firstName": {"type": "string"}, "fullName": {"type": "string"}, "lastName": {"type": "string"}}, "required": ["fullName", "email"], "type": "object"}, "database.LobbyRow": {"properties": {"About": {"$ref": "#/components/schemas/sql.NullString"}, "AvatarURL": {"$ref": "#/components/schemas/sql.NullString"}, "CreatorUserID": {"format": "int64", "type": "integer"}, "ID": {"format": "int64", "type": "integer"}, "IsActive": {"type": "boolean"}, "Name": {"$ref": "#/components/schemas/sql.NullString"}, "OwnerUserIDs": {"$ref": "#/components/schemas/pq.Int64Array"}, "Slug": {"type": "string"}}, "required": ["ID", "Slug", "OwnerUserIDs", "CreatorUserID", "IsActive", "AvatarURL", "Name", "About"], "type": "object"}, "gorm.DeletedAt": {"properties": {"Time": {"format": "date-time", "type": "string"}, "Valid": {"title": "Valid is true if Time is not NULL\n", "type": "boolean"}}, "required": ["Time", "<PERSON><PERSON>"], "type": "object"}, "livekit.EgressStatus": {"format": "int32", "maximum": 2147483647, "minimum": -2147483648, "type": "integer"}, "mars_api.UserSocial": {"properties": {"avatar": {"type": "string"}, "bio": {"type": "string"}, "firstName": {"type": "string"}, "fullName": {"type": "string"}, "lastName": {"type": "string"}, "platform": {"type": "string"}, "url": {"type": "string"}, "userName": {"type": "string"}}, "required": ["avatar", "bio", "firstName", "fullName", "lastName", "platform", "url", "userName"], "type": "object"}, "models.AuthUser": {"properties": {"CreatedAt": {"format": "date-time", "type": "string"}, "DeletedAt": {"$ref": "#/components/schemas/gorm.DeletedAt"}, "UpdatedAt": {"format": "date-time", "type": "string"}, "appleID": {"type": "string"}, "email": {"type": "string"}, "googleID": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "roleIDs": {"$ref": "#/components/schemas/pq.StringArray"}, "teamID": {"type": "string"}, "teamRoleIDs": {"$ref": "#/components/schemas/pq.StringArray"}}, "required": ["id", "email", "roleIDs", "teamID", "teamRoleIDs", "appleID", "googleID", "CreatedAt", "UpdatedAt", "DeletedAt"], "type": "object"}, "nebula_api.MessageDTO": {"properties": {"content": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "feedback": {"$ref": "#/components/schemas/nebula_api.MessageDTOFeedback"}, "id": {"type": "string"}, "role": {"$ref": "#/components/schemas/nebula_api.MessageDTORole"}, "sources": {"additionalProperties": {"items": {"$ref": "#/components/schemas/nebula_api.MessageItemSourceDTO"}, "type": "array"}, "type": "object"}, "threadId": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["content", "createdAt", "feedback", "id", "role", "sources", "threadId", "updatedAt"], "type": "object"}, "nebula_api.MessageDTOFeedback": {"format": "int64", "type": "integer"}, "nebula_api.MessageDTORole": {"type": "string"}, "nebula_api.MessageItemSourceDTO": {"properties": {"data": {"$ref": "#/components/schemas/nebula_api.MessageItemSourceDataDTO"}, "type": {"$ref": "#/components/schemas/nebula_api.MessageItemSourceDTOType"}}, "required": ["data", "type"], "type": "object"}, "nebula_api.MessageItemSourceDTOType": {"type": "string"}, "nebula_api.MessageItemSourceDataDTO": {"properties": {"about": {"type": "string"}, "endedAt": {"format": "int64", "type": "integer"}, "organizer": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}, "recurrenceId": {"type": "string"}, "sessionId": {"type": "string"}, "startedAt": {"format": "int64", "type": "integer"}, "title": {"type": "string"}}, "required": ["about", "endedAt", "organizer", "participants", "recurrenceId", "sessionId", "startedAt", "title"], "type": "object"}, "nebula_api.ThreadDTO": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "lastMessageAt": {"format": "int64", "type": "integer"}, "title": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["createdAt", "id", "lastMessageAt", "title", "updatedAt"], "type": "object"}, "pq.Int64Array": {"items": {"format": "int64", "type": "integer"}, "type": "array"}, "pq.StringArray": {"items": {"type": "string"}, "type": "array"}, "recallai.RecallConnection": {"properties": {"connected": {"type": "boolean"}, "email": {"type": "string"}, "platform": {"type": "string"}}, "required": ["connected", "platform", "email"], "type": "object"}, "recallai.RecordingPreferences": {"properties": {"bot_name": {"type": "string"}, "record_confirmed": {"type": "boolean"}, "record_external": {"type": "boolean"}, "record_internal": {"type": "boolean"}, "record_non_host": {"type": "boolean"}, "record_only_host": {"type": "boolean"}, "record_recurring": {"type": "boolean"}}, "required": ["record_non_host", "record_recurring", "record_external", "record_internal", "record_confirmed", "record_only_host", "bot_name"], "type": "object"}, "shared.AuthPolicyActions": {"type": "string"}, "shared.BillingCycleDTO": {"properties": {"frequency": {"type": "number"}, "interval": {"type": "string"}}, "type": "object"}, "shared.DataVisibility": {"type": "string"}, "shared.GuestDTO": {"properties": {"email": {"type": "string"}, "fullName": {"type": "string"}, "surrogateID": {"format": "int64", "type": "integer"}}, "required": ["surrogateID"], "type": "object"}, "shared.HttpErrorCode": {"format": "int64", "type": "integer"}, "shared.IsAuthorizedBatchResponseDTO": {"properties": {"act": {"title": "action checked for authorization\n", "type": "string"}, "isAuthorized": {"title": "whether the subject is authorized to perform the action on the object\n", "type": "boolean"}, "obj": {"title": "object checked for authorization\n", "type": "string"}, "sub": {"title": "subject checked for authorization\n", "type": "string"}}, "required": ["sub", "obj", "act", "isAuthorized"], "type": "object"}, "shared.LobbyDTO": {"properties": {"isActive": {"title": "IsActive is true when the lobby is active false when it is not reachable from\nthe URL\n", "type": "boolean"}, "lobbyID": {"title": "LobbyID is the unique identifier of the lobby\n", "type": "string"}, "slug": {"title": "Slug is the unique identifier of the lobby\n", "type": "string"}}, "required": ["lobbyID", "slug", "isActive"], "type": "object"}, "shared.LobbyParticipantDTO": {"properties": {"avatar": {"title": "Avatar the avatar of the user\n", "type": "string"}, "email": {"title": "Email assuming this is shared with the owner of the lobby only!\n", "type": "string"}, "firstName": {"title": "FirstName the first name of the user\n", "type": "string"}, "fullName": {"title": "FullName the full name of the user\n", "type": "string"}, "guestSurrogateID": {"format": "int64", "title": "GuestSurrogate<PERSON> is the id of the guest surrogate\n", "type": "integer"}, "id": {"title": "ID could present either userID or guestSurrogateID\n", "type": "string"}, "isOwner": {"title": "IsOwner is true when the user is the owner of the lobby\n", "type": "boolean"}, "lastName": {"title": "LastName the last name of the use\n", "type": "string"}, "participantID": {"title": "ParticipantID is the id of the participant\n", "type": "string"}, "userID": {"title": "UserID is the id of the user\n", "type": "string"}}, "required": ["participantID", "id", "fullName", "isOwner"], "type": "object"}, "shared.NotificationSettings": {"properties": {"isEmailNotificationEnabledGlobal": {"title": "whether email notifications are enabled globally\n", "type": "boolean"}, "isPushNotificationEnabledGlobal": {"title": "whether push notifications are enabled globally\n", "type": "boolean"}}, "type": "object"}, "shared.OffTheRecord": {"type": "string"}, "shared.OffsetPaginationResponse": {"properties": {"limit": {"format": "int64", "type": "integer"}, "offset": {"format": "int64", "type": "integer"}, "totalCount": {"format": "int64", "type": "integer"}}, "required": ["totalCount", "limit", "offset"], "type": "object"}, "shared.OnboardingFlags": {"properties": {"0": {"type": "boolean"}, "1": {"type": "boolean"}, "2": {"type": "boolean"}, "3": {"type": "boolean"}, "4": {"type": "boolean"}, "5": {"type": "boolean"}, "6": {"type": "string"}}, "type": "object"}, "shared.PaddleCard": {"type": "object"}, "shared.PaddlePaymentMethodDetailsDTO": {"properties": {"card": {"$ref": "#/components/schemas/shared.PaddleCard"}, "type": {"type": "string"}}, "required": ["type", "card"], "type": "object"}, "shared.PaddlePaymentResultDTO": {"properties": {"errorCode": {"$ref": "#/components/schemas/shared.PaddlePaymentResultDTOErrorCode"}, "status": {"$ref": "#/components/schemas/shared.PaddlePaymentResultDTOStatus"}}, "required": ["status", "errorCode"], "type": "object"}, "shared.PaddlePaymentResultDTOErrorCode": {"type": "string"}, "shared.PaddlePaymentResultDTOStatus": {"type": "string"}, "shared.PaddlePriceDTO": {"type": "object"}, "shared.PaddleProductDTO": {"properties": {"description": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "prices": {"items": {"$ref": "#/components/schemas/shared.PriceDTO"}, "type": "array"}, "status": {"type": "string"}}, "required": ["id", "name", "status", "prices"], "type": "object"}, "shared.PaddleQuantityDTO": {"properties": {"maximum": {"type": "number"}, "minimum": {"type": "number"}}, "type": "object"}, "shared.PaddleTransactionDTO": {"properties": {"customData": {"type": "object"}, "items": {"items": {"$ref": "#/components/schemas/shared.PaddleTransactionItemDTO"}, "type": "array"}, "payments": {"items": {"$ref": "#/components/schemas/shared.PaddleTransactionPaymentAttemptDTO"}, "type": "array"}}, "required": ["customData", "items", "payments"], "type": "object"}, "shared.PaddleTransactionItemDTO": {"properties": {"price": {"$ref": "#/components/schemas/shared.PaddlePriceDTO"}}, "required": ["price"], "type": "object"}, "shared.PaddleTransactionPaymentAttemptDTO": {"properties": {"method_details": {"$ref": "#/components/schemas/shared.PaddlePaymentMethodDetailsDTO"}}, "required": ["method_details"], "type": "object"}, "shared.ParticipantMetadata": {"properties": {"IsDiarized": {"type": "boolean"}, "guest": {"$ref": "#/components/schemas/shared.GuestDTO"}, "isLoaded": {"type": "boolean"}, "region": {"type": "string"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user", "guest", "isLoaded", "IsDiarized", "region"], "type": "object"}, "shared.PriceDTO": {"properties": {"billingCycle": {"$ref": "#/components/schemas/shared.BillingCycleDTO"}, "id": {"type": "string"}, "productID": {"type": "string"}, "quantity": {"$ref": "#/components/schemas/shared.PaddleQuantityDTO"}, "status": {"$ref": "#/components/schemas/shared.PriceDTOStatus"}, "taxMode": {"$ref": "#/components/schemas/shared.PriceDTOTaxMode"}, "trialPeriod": {"$ref": "#/components/schemas/shared.PriceDTOTrialPeriod"}, "unitPrice": {"$ref": "#/components/schemas/shared.UnitPriceDTO"}, "unitPriceOverrides": {"items": {"$ref": "#/components/schemas/shared.UnitPriceOverrideDTO"}, "type": "array"}}, "type": "object"}, "shared.PriceDTOStatus": {"type": "string"}, "shared.PriceDTOTaxMode": {"type": "string"}, "shared.PriceDTOTrialPeriod": {"properties": {"frequency": {"type": "number"}, "interval": {"type": "string"}}, "type": "object"}, "shared.RecordingStatus": {"type": "string"}, "shared.SessionAccessRuleDTO": {"properties": {"accessType": {"type": "string"}, "createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "isExternalRequest": {"type": "boolean"}, "requestMessage": {"type": "string"}, "restrictionStatus": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "type": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}, "value": {"type": "string"}}, "type": "object"}, "shared.SessionDTO": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}, "shared.SessionDTOAccessStatus": {"type": "string"}, "shared.SessionDTOType": {"type": "string"}, "shared.SessionIntegrationsDTO": {"properties": {"salesforceBindingId": {"type": "string"}}, "required": ["salesforceBindingId"], "type": "object"}, "shared.SessionState": {"type": "string"}, "shared.SessionSubscriptionPlanDTO": {"properties": {"planConfig": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTO"}, "planConfigOverrides": {"type": "object"}}, "type": "object"}, "shared.Socials": {"items": {"$ref": "#/components/schemas/shared.UserSocialDTO"}, "type": "array"}, "shared.StateUpdatedAt": {"properties": {"state": {"$ref": "#/components/schemas/shared.SessionState"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["state", "updatedAt"], "type": "object"}, "shared.SubscriptionItemDTO": {"properties": {"createdAt": {"type": "string"}, "customData": {"type": "object"}, "nextBilledAt": {"type": "string"}, "previouslyBilledAt": {"type": "string"}, "price": {"$ref": "#/components/schemas/shared.PriceDTO"}, "quantity": {"type": "number"}, "recurring": {"type": "boolean"}, "status": {"$ref": "#/components/schemas/shared.SubscriptionItemDTOStatus"}, "trialDates": {"properties": {"endsAt": {"type": "string"}, "startsAt": {"type": "string"}}, "type": "object"}, "updatedAt": {"type": "string"}}, "type": "object"}, "shared.SubscriptionItemDTOStatus": {"type": "string"}, "shared.SubscriptionPlanConfigDTO": {"properties": {"aiFeed": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "bots": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "createdAt": {"format": "int64", "type": "integer"}, "crm": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "customFeedItems": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "customIntegrations": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "id": {"type": "string"}, "integrations": {"properties": {"apps": {"items": {"type": "string"}, "type": "array"}, "enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingMemory": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingSummary": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingTemplates": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetingWorkflows": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "meetings": {"properties": {"enabled": {"type": "boolean"}, "max": {"type": "number"}}, "required": ["enabled", "max"], "type": "object"}, "modelSegregation": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "offTheRecord": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "paddleProductID": {"type": "string"}, "paddleProductName": {"type": "string"}, "queueMode": {"properties": {"enabled": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "recording": {"properties": {"enabled": {"type": "boolean"}, "local": {"type": "boolean"}}, "required": ["enabled"], "type": "object"}, "stream": {"properties": {"enabled": {"type": "boolean"}, "quality": {"type": "number"}}, "required": ["enabled", "quality"], "type": "object"}, "support": {"properties": {"enabled": {"type": "boolean"}, "type": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTOSupportType"}}, "required": ["enabled", "type"], "type": "object"}, "timeLimit": {"properties": {"enabled": {"type": "boolean"}, "max": {"format": "int64", "type": "integer"}}, "required": ["enabled", "max"], "type": "object"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["aiFeed", "customFeedItems", "customIntegrations", "id", "integrations", "meeting<PERSON><PERSON><PERSON>", "meeting<PERSON>ummary", "meetingTemplates", "meetingWorkflows", "meetings", "modelSegregation", "offTheRecord", "paddleProductID", "paddleProductName", "queueMode", "recording", "stream", "support", "crm", "bots", "timeLimit", "createdAt", "updatedAt"], "type": "object"}, "shared.SubscriptionPlanConfigDTOSupportType": {"type": "string"}, "shared.Team": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "domains": {"items": {"type": "string"}, "type": "array"}, "id": {"type": "string"}, "name": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["id", "name", "domains", "createdAt", "updatedAt"], "type": "object"}, "shared.TeamMemberDTO": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "id": {"type": "string"}, "role": {"type": "string"}, "teamID": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}, "userID": {"type": "string"}}, "required": ["id", "userID", "teamID", "role", "createdAt", "updatedAt"], "type": "object"}, "shared.TeamWithInlineRelationsDTO": {"properties": {"createdAt": {"format": "int64", "type": "integer"}, "domains": {"items": {"type": "string"}, "type": "array"}, "id": {"type": "string"}, "membersCount": {"format": "int64", "type": "integer"}, "name": {"type": "string"}, "role": {"type": "string"}, "updatedAt": {"format": "int64", "type": "integer"}}, "required": ["id", "name", "domains", "createdAt", "updatedAt"], "type": "object"}, "shared.TranscriptionDTO": {"properties": {"batchID": {"type": "string"}, "id": {"type": "string"}, "languageLocale": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "speakerEmail": {"type": "string"}, "speakerFirstName": {"type": "string"}, "speakerFullName": {"type": "string"}, "speakerLastName": {"type": "string"}, "speakerUID": {"type": "string"}, "speakerUserID": {"type": "string"}, "text": {"type": "string"}, "timeUnix": {"format": "int64", "type": "integer"}}, "required": ["id", "batchID", "sessionID", "sessionRecurrenceID", "text", "speakerUID", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "type": "object"}, "shared.UnitPriceDTO": {"properties": {"amount": {"type": "string"}, "currencyCode": {"type": "string"}}, "type": "object"}, "shared.UnitPriceOverrideDTO": {"properties": {"countryCodes": {"items": {"type": "string"}, "type": "array"}, "unitPrice": {"$ref": "#/components/schemas/shared.UnitPriceDTO"}}, "type": "object"}, "shared.UserCryptoToken": {"properties": {"blockchain": {"title": "the blockchain of the token\n", "type": "number"}, "createdAt": {"format": "int64", "title": "the timestamp when the token was created\n", "type": "integer"}, "details": {"title": "the metadata of the token\n", "type": "object"}, "id": {"title": "the ID of the token\n", "type": "string"}, "purposeType": {"title": "the purpose type of the token\n", "type": "string"}, "token": {"title": "the token\n", "type": "string"}, "updatedAt": {"format": "int64", "title": "the timestamp when the token was last updated\n", "type": "integer"}, "url": {"title": "the URL of the NFT\n", "type": "string"}, "userID": {"title": "owner of the token\n", "type": "string"}, "walletID": {"title": "the wallet ID of the token\n", "type": "string"}}, "required": ["blockchain", "createdAt", "details", "id", "purposeType", "token", "updatedAt", "url", "userID", "walletID"], "type": "object"}, "shared.UserDTO": {"properties": {"about": {"title": "the about section of the user\n", "type": "string"}, "appleID": {"title": "the apple ID of the user if they signed up with apple\n", "type": "string"}, "avatar": {"title": "the avatar of the user\n", "type": "string"}, "createdAt": {"format": "int64", "title": "the timestamp when the user was created\n", "type": "integer"}, "customerID": {"title": "the customer ID of the user\n", "type": "string"}, "email": {"title": "the email of the user\n", "type": "string"}, "fingerprint": {"title": "the fingerprint of the user\n", "type": "object"}, "firstName": {"title": "the first name of the user\n", "type": "string"}, "googleID": {"title": "the google ID of the user if they signed up with google\n", "type": "string"}, "id": {"title": "the ID of the user\n", "type": "string"}, "isTestUser": {"title": "whether the user is a test user\n", "type": "boolean"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "lobbies": {"items": {"$ref": "#/components/schemas/shared.LobbyDTO"}, "title": "Lobbies any lobby associated with the user\n", "type": "array"}, "marketingOptIn": {"title": "whether the user opted in to marketing\n", "type": "boolean"}, "nftAvatar": {"$ref": "#/components/schemas/shared.UserCryptoToken"}, "notificationSettings": {"$ref": "#/components/schemas/shared.NotificationSettings"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "roleIDs": {"items": {"type": "string"}, "title": "the role IDs of the user\n", "type": "array"}, "signUpTimestamp": {"format": "int64", "title": "the timestamp when the user signed up\n", "type": "integer"}, "social": {"$ref": "#/components/schemas/shared.Socials"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTO"}, "team": {"$ref": "#/components/schemas/shared.TeamWithInlineRelationsDTO"}, "timezone": {"title": "the timezone of the user\n", "type": "string"}, "updatedAt": {"format": "int64", "title": "the timestamp when the user was last updated\n", "type": "integer"}}, "type": "object"}, "shared.UserMeetingTypeDTO": {"properties": {"createdBy": {"format": "int64", "type": "integer"}, "description": {"type": "string"}, "id": {"type": "string"}, "promptTemplate": {"type": "string"}, "title": {"type": "string"}}, "required": ["description", "id", "promptTemplate", "title"], "type": "object"}, "shared.UserSocialDTO": {"properties": {"avatar": {"title": "the avatar of the user\n", "type": "string"}, "bio": {"title": "the bio of the user\n", "type": "string"}, "firstName": {"title": "the first name of the user\n", "type": "string"}, "fullName": {"title": "the full name of the user\n", "type": "string"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "platform": {"title": "the platform of the user\n", "type": "string"}, "url": {"title": "the URL of the user\n", "type": "string"}, "userName": {"title": "the username of the user\n", "type": "string"}}, "required": ["url"], "type": "object"}, "shared.UserSubscriptionPlanDTO": {"properties": {"addressID": {"type": "string"}, "billingCycle": {"$ref": "#/components/schemas/shared.BillingCycleDTO"}, "billingDetails": {"properties": {"additionalInformation": {"type": "string"}, "enableCheckout": {"type": "boolean"}, "purchaseOrderMinimum": {"type": "string"}}, "type": "object"}, "businessID": {"type": "string"}, "canceledAt": {"type": "string"}, "collectionMode": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTOCollectionMode"}, "createdAt": {"format": "int64", "type": "integer"}, "currencyCode": {"type": "string"}, "currentBillingPeriod": {"properties": {"endsAt": {"type": "string"}, "startsAt": {"type": "string"}}, "type": "object"}, "customData": {"type": "object"}, "customerID": {"type": "string"}, "firstBilledAt": {"type": "string"}, "id": {"type": "string"}, "items": {"items": {"$ref": "#/components/schemas/shared.SubscriptionItemDTO"}, "type": "array"}, "managementUrls": {"properties": {"cancel": {"type": "string"}, "updatePaymentMethod": {"type": "string"}}, "type": "object"}, "nextBilledAt": {"type": "string"}, "paddleSubscriptionID": {"type": "string"}, "pausedAt": {"type": "string"}, "planConfig": {"$ref": "#/components/schemas/shared.SubscriptionPlanConfigDTO"}, "planConfigOverrides": {"type": "object"}, "scheduledChange": {"properties": {"action": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTOScheduledChangeAction"}, "effectiveAt": {"type": "string"}, "resumeAt": {"type": "string"}}, "type": "object"}, "startedAt": {"type": "string"}, "status": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTOStatus"}, "trialEndsAt": {"format": "int64", "type": "integer"}, "updatedAt": {"format": "int64", "type": "integer"}, "userID": {"type": "string"}}, "type": "object"}, "shared.UserSubscriptionPlanDTOCollectionMode": {"type": "string"}, "shared.UserSubscriptionPlanDTOScheduledChangeAction": {"type": "string"}, "shared.UserSubscriptionPlanDTOStatus": {"type": "string"}, "sql.NullString": {"properties": {"String": {"type": "string"}, "Valid": {"title": "Valid is true if String is not NULL\n", "type": "boolean"}}, "required": ["String", "<PERSON><PERSON>"], "type": "object"}}}, "info": {"description": "Generated by encore", "title": "API for elio-bzw2", "version": "1", "x-logo": {"altText": "Encore logo", "backgroundColor": "#EEEEE1", "url": "https://encore.dev/assets/branding/logo/logo-black.png"}}, "openapi": "3.0.0", "paths": {"/billing.FetchAndWriteGetInvoiceByTransactionIDMock": {"get": {"operationId": "GET:billing.FetchAndWriteGetInvoiceByTransactionIDMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:billing.FetchAndWriteGetInvoiceByTransactionIDMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.FetchAndWriteGetSubscriptionUpdatePaymentMethodTransactionMock": {"get": {"operationId": "GET:billing.FetchAndWriteGetSubscriptionUpdatePaymentMethodTransactionMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:billing.FetchAndWriteGetSubscriptionUpdatePaymentMethodTransactionMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.FetchAndWriteListProductsMock": {"get": {"operationId": "GET:billing.FetchAndWriteListProductsMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:billing.FetchAndWriteListProductsMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.FetchAndWriteListTransactionsByCustomerIDMock": {"get": {"operationId": "GET:billing.FetchAndWriteListTransactionsByCustomerIDMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:billing.FetchAndWriteListTransactionsByCustomerIDMock", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.GetInvoiceByTransactionID": {"post": {"operationId": "POST:billing.GetInvoiceByTransactionID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"transactionID": {"type": "string"}}, "required": ["transactionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"url": {"type": "string"}}, "required": ["url"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.GetSubscriptionUpdatePaymentMethodTransaction": {"post": {"operationId": "POST:billing.GetSubscriptionUpdatePaymentMethodTransaction", "requestBody": {"content": {"application/json": {"schema": {"properties": {"subscriptionID": {"type": "string"}}, "required": ["subscriptionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"transaction": {"$ref": "#/components/schemas/shared.PaddleTransactionDTO"}}, "required": ["transaction"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.ListProducts": {"post": {"operationId": "POST:billing.ListProducts", "requestBody": {"content": {"application/json": {"schema": {"properties": {"includePrices": {"type": "boolean"}}, "required": ["includePrices"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"products": {"items": {"$ref": "#/components/schemas/shared.PaddleProductDTO"}, "type": "array"}}, "required": ["products"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/billing.ListTransactionsByCustomerID": {"post": {"operationId": "POST:billing.ListTransactionsByCustomerID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"customerID": {"type": "string"}, "limit": {"format": "int64", "type": "integer"}, "status": {"items": {"type": "string"}, "type": "array"}}, "required": ["customerID", "limit", "status"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"transactions": {"items": {"$ref": "#/components/schemas/shared.PaddleTransactionDTO"}, "type": "array"}}, "required": ["transactions"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/chat.CreateChannel": {"post": {"operationId": "POST:chat.CreateChannel", "requestBody": {"content": {"application/json": {"schema": {"properties": {"creatorUserID": {"type": "string"}, "isPublic": {"type": "boolean"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "isPublic", "creatorUserID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/chat.FreezeRoom": {"post": {"operationId": "POST:chat.FreezeRoom", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/chat.GetChatToken": {"post": {"operationId": "POST:chat.GetChatToken", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"title": "The session ID of the user\n", "type": "string"}, "sessionRecurrenceID": {"title": "The session recurrence ID of the user\n", "type": "string"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["sessionID", "sessionRecurrenceID", "user"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "data": {"properties": {"channelRole": {"$ref": "#/components/schemas/api.ChannelRole"}, "chatRoomId": {"title": "The ID of the chat room\n", "type": "string"}, "token": {"title": "The chat token\n", "type": "string"}}, "required": ["token", "channelRole", "chatRoomId"], "title": "The data returned by the API\n", "type": "object"}, "message": {"title": "A message from the API\n", "type": "string"}, "success": {"title": "Whether the code is a success\n", "type": "boolean"}}, "required": ["message", "success", "code", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/comms.MeetingMetadataReady": {"post": {"description": "<PERSON><PERSON> will then store the metadata title if it's a Recall or Listening Mode session.\n", "operationId": "POST:comms.MeetingMetadataReady", "requestBody": {"content": {"application/json": {"schema": {"properties": {"metadata": {"$ref": "#/components/schemas/api.MeetingMetadata"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "metadata"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "MeetingMetadataReady is a andpoint to notify <PERSON><PERSON> that a specific session's\nmetadata has been generated.\n"}}, "/comms.PostSessionSummaryReady": {"get": {"description": "TODO when tag:internal is merged, is that instead of tag:trixta\n", "operationId": "GET:comms.PostSessionSummaryReady", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"format": "int64", "type": "integer"}, "message": {"type": "string"}, "recipients": {"items": {"type": "string"}, "type": "array"}}, "required": ["message", "code", "recipients"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryReady: Receive a request from nebula letting us know that the\nPSS was generated\n"}}, "/comms.TriggerGuestWaitingInLobbyNotification": {"post": {"operationId": "POST:comms.TriggerGuestWaitingInLobbyNotification", "requestBody": {"content": {"application/json": {"schema": {"properties": {"guestEmail": {"type": "string"}, "guestFullName": {"type": "string"}, "lobbyLink": {"type": "string"}, "toEmail": {"type": "string"}}, "required": ["toEmail", "guestEmail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lobbyLink"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/hubble.PopulateSessionPolicies": {"post": {"description": "populates or removes session policies for users, domains, or guests by access rules and populates session policies for all team members if recurrence is associated with a team.\n", "operationId": "POST:hubble.PopulateSessionPolicies", "requestBody": {"content": {"application/json": {"schema": {"properties": {"recurrenceID": {"type": "string"}, "sessionID": {"type": "string"}}, "required": ["sessionID", "recurrenceID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PopulateSessionPolicies populates session policies for session hosts,\n"}}, "/livekit.EgressStatusChanged": {"post": {"operationId": "POST:livekit.EgressStatusChanged", "requestBody": {"content": {"application/json": {"schema": {"properties": {"egressId": {"type": "string"}, "playlistName": {"type": "string"}, "roomName": {"type": "string"}, "status": {"$ref": "#/components/schemas/livekit.EgressStatus"}}, "required": ["playlistName", "roomName", "status", "egressId"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/livekit.StartAllTrackEgress": {"post": {"operationId": "POST:livekit.StartAllTrackEgress", "requestBody": {"content": {"application/json": {"schema": {"properties": {"roomName": {"type": "string"}}, "required": ["roomName"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/livekit.StartRecording": {"post": {"operationId": "POST:livekit.StartRecording", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}}, "required": ["sessionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"alreadyRecording": {"type": "boolean"}, "playlistFilename": {"type": "string"}}, "required": ["alreadyRecording", "playlistFilename"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/livekit.StartTrackEgress": {"post": {"operationId": "POST:livekit.StartTrackEgress", "requestBody": {"content": {"application/json": {"schema": {"properties": {"identifier": {"type": "string"}, "roomName": {"type": "string"}, "trackId": {"type": "string"}}, "required": ["roomName", "trackId", "identifier"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/livekit.UpdateParticipantMetadata": {"post": {"operationId": "POST:livekit.UpdateParticipantMetadata", "requestBody": {"content": {"application/json": {"schema": {"properties": {"guest": {"$ref": "#/components/schemas/shared.GuestDTO"}, "identity": {"type": "string"}, "isDiarized": {"type": "boolean"}, "isLoaded": {"type": "boolean"}, "region": {"type": "string"}, "rooms": {"items": {"type": "string"}, "type": "array"}, "skipLKSync": {"type": "boolean"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["identity", "user", "guest", "rooms", "isLoaded", "isDiarized", "skipLKSync", "region"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/livekit.UpdateRoomMetadata": {"post": {"operationId": "POST:livekit.UpdateRoomMetadata", "requestBody": {"content": {"application/json": {"schema": {"properties": {"forceRetry": {"type": "boolean"}, "offTheRecordStatus": {"$ref": "#/components/schemas/shared.OffTheRecord"}, "recordingStatus": {"$ref": "#/components/schemas/shared.RecordingStatus"}, "session": {"$ref": "#/components/schemas/shared.SessionDTO"}, "sessionID": {"type": "string"}, "startedAt": {"format": "int64", "type": "integer"}}, "required": ["sessionID", "forceRetry", "offTheRecordStatus", "recordingStatus", "session", "startedAt"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UpdateRoomMetadata updates the metadata of a room if it has changed\n"}}, "/livekit.WebhookEndpoint": {"delete": {"operationId": "DELETE:livekit.WebhookEndpoint", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "get": {"operationId": "GET:livekit.WebhookEndpoint", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "head": {"operationId": "HEAD:livekit.WebhookEndpoint", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "patch": {"operationId": "PATCH:livekit.WebhookEndpoint", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:livekit.WebhookEndpoint", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "put": {"operationId": "PUT:livekit.WebhookEndpoint", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/lobbies.HandleGuestJoinedLobbyEvent": {"post": {"operationId": "POST:lobbies.HandleGuestJoinedLobbyEvent", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the unique ID of the lobby\n", "type": "string"}, "participant": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}}, "required": ["lobbyID", "participant"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/lobbies.HasUserJoinedLobbyMeeting": {"post": {"operationId": "POST:lobbies.HasUserJoinedLobbyMeeting", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"type": "string"}, "unifiedUserID": {"type": "string"}}, "required": ["lobbyID", "unifiedUserID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"hasJoined": {"type": "boolean"}}, "required": ["hasJoined"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/lobbies.StartGuestWaitingInLobbyNotificationsFlow": {"post": {"operationId": "POST:lobbies.StartGuestWaitingInLobbyNotificationsFlow", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the unique ID of the lobby\n", "type": "string"}, "participant": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}}, "required": ["lobbyID", "participant"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/lobbies.StartGuestWaitingInLobbyNotificationsFlowForLobbyOwner": {"post": {"operationId": "POST:lobbies.StartGuestWaitingInLobbyNotificationsFlowForLobbyOwner", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobby": {"$ref": "#/components/schemas/database.LobbyRow"}, "lobbyOwnerID": {"format": "int64", "type": "integer"}, "participant": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}, "userEmail": {"type": "string"}, "userFullName": {"type": "string"}}, "required": ["participant", "lobby", "lobbyOwnerID", "userEmail", "userFullName"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.CreateSessionWithUser": {"post": {"operationId": "POST:meetings.CreateSessionWithUser", "parameters": [{"allowEmptyValue": true, "description": "This should match the old request payload from Mars:\n", "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"type": "string"}, "avatar": {"type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "category": {"type": "string"}, "cover": {"type": "string"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "isPubliclyVisible": {"type": "boolean"}, "isViewerAccessRestricted": {"type": "boolean"}, "lobbyID": {"title": "Optional\n", "type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "sessionSettings": {"type": "object"}, "sessionState": {"type": "string"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "userID": {"title": "Optional, specify the userID to create the session for\n", "type": "string"}, "userMeetingType": {"$ref": "#/components/schemas/api.UserMeetingTypeRequestDTO"}, "viewerAccessRules": {"items": {"$ref": "#/components/schemas/api.SessionAccessRulesDTO"}, "type": "array"}}, "required": ["about", "accessStatus", "avatar", "calendarEventEditURL", "calendarID", "calendarType", "category", "cover", "dataVisibility", "endTimestamp", "meetingType", "ogMetadata", "sessionSettings", "startTimestamp", "sessionState", "sessionTags", "sessionTitle", "userMeetingType", "viewerAccessRules", "isPubliclyVisible", "isViewerAccessRestricted", "userID", "lobbyID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.GetAllAccessRequests": {"get": {"description": "TODO evaluate tag:trixta in this endpoint, if it's used we should make a specific one for external requests\n", "operationId": "GET:meetings.GetAllAccessRequests", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "restrictionStatus", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/api.InReviewAccessRequestDTO"}, "type": "array"}, "cursor": {"type": "string"}, "totalCount": {"format": "int64", "type": "integer"}}, "required": ["accessRules", "cursor", "totalCount"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead\n"}}, "/meetings.GetLatestRecurrenceCompact": {"get": {"operationId": "GET:meetings.GetLatestRecurrenceCompact", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "status": {"type": "string"}}, "required": ["sessionTitle", "sessionID", "sessionRecurrenceID", "status", "startTimestamp", "startedAt"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.GetLobbyGuests": {"post": {"operationId": "POST:meetings.GetLobbyGuests", "parameters": [{"allowEmptyValue": true, "description": "LobbyID is the unique ID of the lobby\n", "explode": true, "in": "query", "name": "lobbyID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"participants": {"items": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}, "type": "array"}}, "required": ["participants"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Get<PERSON><PERSON><PERSON>G<PERSON><PERSON> returns the list of guests in the lobby\n"}}, "/meetings.GetSessionAccessRules": {"post": {"operationId": "POST:meetings.GetSessionAccessRules", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.SessionAccessDTO"}, "type": "array"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionAccessRules returns all access rules for a session\n"}}, "/meetings.GetSessionByID": {"get": {"description": "TODO replace tag:trixta with tag:internal when the tag is merged\n", "operationId": "GET:meetings.GetSessionByID", "parameters": [{"allowEmptyValue": true, "description": "SessionID is the snowflake ID of the session to get\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "RecurrenceID is the snowflake ID of the recurrence to get, when empty the latest recurrence is returned\n", "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionByID Replaces `MarsClient.SessionControllerGetSessionByID`, that used\nto sent a request to: src/modules/sessions/controllers/session.controller.ts in\nmars\n"}}, "/meetings.GetSessionRecurrencesById": {"get": {"description": "states the recurrence should have been in to appear in the response. When no sessions match the sessionID or optional filter the resulting \\`Sessions\\` array in api.GetSessionRecurrencesByIDResponse will be empty.\n", "operationId": "GET:meetings.GetSessionRecurrencesById", "parameters": [{"allowEmptyValue": true, "description": "SessionID is the snowflake ID of the session\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "FilterStates is a comma separated list of states to filter by\n", "explode": true, "in": "query", "name": "filterStates", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/shared.SessionDTO"}, "type": "array"}}, "required": ["sessions"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionRecurrencesById returns a list of recurrences for the passed\nsessionID, can optionally search for specific\n"}}, "/meetings.GetSessionUsersBySessionID": {"get": {"operationId": "GET:meetings.GetSessionUsersBySessionID", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessionUsers": {"items": {"$ref": "#/components/schemas/api.SessionUserDTO"}, "type": "array"}}, "required": ["sessionUsers"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.GetUserByID": {"post": {"operationId": "POST:meetings.GetUserByID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"userID": {"type": "string"}}, "required": ["userID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"title": "the about section of the user\n", "type": "string"}, "appleID": {"title": "the apple ID of the user if they signed up with apple\n", "type": "string"}, "avatar": {"title": "the avatar of the user\n", "type": "string"}, "createdAt": {"format": "int64", "title": "the timestamp when the user was created\n", "type": "integer"}, "customerID": {"title": "the customer ID of the user\n", "type": "string"}, "email": {"title": "the email of the user\n", "type": "string"}, "fingerprint": {"title": "the fingerprint of the user\n", "type": "object"}, "firstName": {"title": "the first name of the user\n", "type": "string"}, "googleID": {"title": "the google ID of the user if they signed up with google\n", "type": "string"}, "id": {"title": "the ID of the user\n", "type": "string"}, "isTestUser": {"title": "whether the user is a test user\n", "type": "boolean"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "lobbies": {"items": {"$ref": "#/components/schemas/shared.LobbyDTO"}, "title": "Lobbies any lobby associated with the user\n", "type": "array"}, "marketingOptIn": {"title": "whether the user opted in to marketing\n", "type": "boolean"}, "nftAvatar": {"$ref": "#/components/schemas/shared.UserCryptoToken"}, "notificationSettings": {"$ref": "#/components/schemas/shared.NotificationSettings"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "roleIDs": {"items": {"type": "string"}, "title": "the role IDs of the user\n", "type": "array"}, "signUpTimestamp": {"format": "int64", "title": "the timestamp when the user signed up\n", "type": "integer"}, "social": {"$ref": "#/components/schemas/shared.Socials"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTO"}, "team": {"$ref": "#/components/schemas/shared.TeamWithInlineRelationsDTO"}, "timezone": {"title": "the timezone of the user\n", "type": "string"}, "updatedAt": {"format": "int64", "title": "the timestamp when the user was last updated\n", "type": "integer"}}, "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetUserByID gets user details by ID\n"}}, "/meetings.GetUserByIDWithRelations": {"post": {"operationId": "POST:meetings.GetUserByIDWithRelations", "requestBody": {"content": {"application/json": {"schema": {"properties": {"userID": {"type": "string"}}, "required": ["userID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.JoinLobbyAsGuest": {"post": {"operationId": "POST:meetings.JoinLobbyAsGuest", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the unique ID of the lobby\n", "type": "string"}, "participant": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}}, "required": ["lobbyID", "participant"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Join<PERSON><PERSON>byAsGuest adds a guest to the lobby and publishes the event to the lobby\nlisteners\n"}}, "/meetings.ListSessionsByLobbyID": {"post": {"operationId": "POST:meetings.ListSessionsByLobbyID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"active": {"type": "boolean"}, "lobbyID": {"type": "string"}}, "required": ["lobbyID", "active"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/shared.SessionDTO"}, "type": "array"}}, "required": ["sessions"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.RemoveGuestFromLobby": {"post": {"operationId": "POST:meetings.RemoveGuestFromLobby", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the unique ID of the lobby\n", "type": "string"}, "participantID": {"title": "ParticipantID is the unique ID of the participant to remove\n", "type": "string"}}, "required": ["lobbyID", "participantID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "RemoveGuestFrom<PERSON><PERSON>by removes a guest from the lobby and publishes the event to\nthe lobby listeners\n"}}, "/meetings.RevokeAccessSessionUser": {"post": {"operationId": "POST:meetings.RevokeAccessSessionUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"accessRuleId": {"type": "string"}}, "required": ["accessRuleId"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/meetings.UpsertSessionUser": {"post": {"operationId": "POST:meetings.UpsertSessionUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"fingerprint": {"items": {"type": "string"}, "type": "array"}, "guestEmail": {"type": "string"}, "guestFullName": {"type": "string"}, "guestSurrogateID": {"format": "int64", "type": "integer"}, "isViewerAccessRevoked": {"type": "boolean"}, "joined": {"type": "boolean"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "userEmail": {"type": "string"}, "userID": {"type": "string"}}, "required": ["userEmail", "userID", "sessionID", "sessionRecurrenceID", "fingerprint", "roleIDs", "isViewerAccessRevoked", "joined", "guestSurrogate<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "guestEmail"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/preview.AssignLivekitRoom": {"post": {"description": "will be sent to the assigned URL.\n", "operationId": "POST:preview.AssignLivekitRoom", "requestBody": {"content": {"application/json": {"schema": {"properties": {"room": {"type": "string"}, "url": {"type": "string"}}, "required": ["room", "url"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AssignLivekitRoom assigns a Livekit room to a specific webhook url, when a\nwebhook is received it\n"}}, "/preview.AssignRecallBot": {"post": {"description": "will be sent to the assigned URL.\n", "operationId": "POST:preview.AssignRecallBot", "requestBody": {"content": {"application/json": {"schema": {"properties": {"botID": {"type": "string"}, "url": {"type": "string"}}, "required": ["botID", "url"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AssignRecallBot assigns a Recall bot to a specific webhook url, when a webhook\nis received it\n"}}, "/preview.HandleLivekitWebhook": {"delete": {"operationId": "DELETE:preview.HandleLivekitWebhook", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "get": {"operationId": "GET:preview.HandleLivekitWebhook", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "head": {"operationId": "HEAD:preview.HandleLivekitWebhook", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "patch": {"operationId": "PATCH:preview.HandleLivekitWebhook", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:preview.HandleLivekitWebhook", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "put": {"operationId": "PUT:preview.HandleLivekitWebhook", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/preview.HandleRecallWebhook": {"post": {"operationId": "POST:preview.HandleRecallWebhook", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-id", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-timestamp", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-signature", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "token", "required": true, "schema": {"type": "string"}, "style": "form"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookData"}, "event": {"type": "string"}}, "required": ["event", "data"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/preview.UnassignLivekitRoom": {"post": {"operationId": "POST:preview.UnassignLivekitRoom", "requestBody": {"content": {"application/json": {"schema": {"properties": {"room": {"type": "string"}}, "required": ["room"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UnassignRecallBot unassigns a Recall bot ID from rerouting webhooks to a\nspecific URL.\n"}}, "/preview.UnassignRecallBot": {"post": {"operationId": "POST:preview.UnassignRecallBot", "requestBody": {"content": {"application/json": {"schema": {"properties": {"botID": {"type": "string"}}, "required": ["botID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UnassignLivekitRoom unassigns a Livekit room from rerouting webhooks to a\nspecific URL.\n"}}, "/pulsar.SpliceRecording": {"post": {"operationId": "POST:pulsar.SpliceRecording", "requestBody": {"content": {"application/json": {"schema": {"properties": {"originalPlaylist": {"type": "string"}, "playlistToSplice": {"type": "string"}}, "required": ["originalPlaylist", "playlistToSplice"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"hasSpliced": {"type": "boolean"}}, "required": ["hasSpliced"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/pulsar.TranscodingJob": {"post": {"operationId": "POST:pulsar.TranscodingJob", "requestBody": {"content": {"application/json": {"schema": {"properties": {"datePrefix": {"type": "string"}, "hasSessionEnded": {"type": "boolean"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionRecurrenceID", "hasSessionEnded", "datePrefix"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/team-members": {"get": {"operationId": "GET:teams.ListTeamMembers", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderBy", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderDirection", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "role", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "teamID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"members": {"items": {"$ref": "#/components/schemas/api.TeamMemberWithRelationsDTO"}, "type": "array"}, "offsetPagination": {"$ref": "#/components/schemas/shared.OffsetPaginationResponse"}}, "required": ["offsetPagination", "members"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:teams.CreateTeamMember", "requestBody": {"content": {"application/json": {"schema": {"properties": {"role": {"type": "string"}, "teamID": {"type": "string"}, "userID": {"type": "string"}}, "required": ["teamID", "userID", "role"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"teamMemberID": {"type": "string"}}, "required": ["teamMemberID"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/team-members/{teamMemberID}": {"delete": {"operationId": "DELETE:teams.DeleteTeamMemberByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "teamMemberID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "get": {"operationId": "GET:teams.GetTeamMemberByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "teamMemberID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"teamMember": {"$ref": "#/components/schemas/shared.TeamMemberDTO"}}, "required": ["teamMember"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "patch": {"operationId": "PATCH:teams.PatchTeamMemberByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "teamMemberID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"role": {"type": "string"}}, "required": ["role"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/teams": {"get": {"operationId": "GET:teams.ListTeams", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderBy", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderDirection", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "include", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"offsetPagination": {"$ref": "#/components/schemas/shared.OffsetPaginationResponse"}, "teams": {"items": {"$ref": "#/components/schemas/api.TeamWithRelations"}, "type": "array"}}, "required": ["offsetPagination", "teams"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:teams.CreateTeam", "requestBody": {"content": {"application/json": {"schema": {"properties": {"domains": {"items": {"type": "string"}, "type": "array"}, "name": {"type": "string"}}, "required": ["name", "domains"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"teamID": {"type": "string"}}, "required": ["teamID"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/teams.CreateTeamInNebula": {"post": {"operationId": "POST:teams.CreateTeamInNebula", "requestBody": {"content": {"application/json": {"schema": {"properties": {"name": {"type": "string"}, "teamID": {"type": "string"}}, "required": ["teamID", "name"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/teams.CreateTeamMemberInNebula": {"post": {"operationId": "POST:teams.CreateTeamMemberInNebula", "requestBody": {"content": {"application/json": {"schema": {"properties": {"teamID": {"type": "string"}, "userID": {"type": "string"}}, "required": ["teamID", "userID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/teams.DeleteTeamMemberInNebula": {"post": {"operationId": "POST:teams.DeleteTeamMemberInNebula", "requestBody": {"content": {"application/json": {"schema": {"properties": {"teamID": {"type": "string"}, "teamMemberID": {"type": "string"}}, "required": ["teamID", "teamMemberID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/teams.GetTeamMembersCountByTeamID": {"post": {"operationId": "POST:teams.GetTeamMembersCountByTeamID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"teamID": {"type": "string"}}, "required": ["teamID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"count": {"format": "int64", "type": "integer"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "count"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/teams/{teamID}": {"delete": {"operationId": "DELETE:teams.DeleteTeamByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "teamID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "get": {"operationId": "GET:teams.GetTeamByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "teamID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "include", "schema": {"items": {"type": "string"}, "type": "array"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"team": {"$ref": "#/components/schemas/api.TeamWithRelations"}}, "required": ["team"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "patch": {"operationId": "PATCH:teams.PatchTeamByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "teamID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"domains": {"items": {"type": "string"}, "type": "array"}, "name": {"type": "string"}}, "required": ["name", "domains"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.CleanupRecallUsers": {"get": {"description": "This prevents us from having tons of users with no connections on the recall side.\n", "operationId": "GET:transcriptions.CleanupRecallUsers", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "CleanupRecallUsers is a cron job that removes users from Recall that have no\nconnections.\n"}, "post": {"description": "This prevents us from having tons of users with no connections on the recall side.\n", "operationId": "POST:transcriptions.CleanupRecallUsers", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "CleanupRecallUsers is a cron job that removes users from Recall that have no\nconnections.\n"}}, "/transcriptions.CreateEndOfSessionTranscriptsBatch": {"get": {"description": "elio/livekit/session-consumer.go#L74\n", "operationId": "GET:transcriptions.CreateEndOfSessionTranscriptsBatch", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "meetingType", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Private endpoint to be called from the session ended event handler in\n"}}, "/transcriptions.GenerateAudioIngressKey": {"post": {"operationId": "POST:transcriptions.GenerateAudioIngressKey", "requestBody": {"content": {"application/json": {"schema": {"properties": {"participant": {"$ref": "#/components/schemas/shared.ParticipantMetadata"}, "session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["participant", "session"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"ingressWebsocketURL": {"type": "string"}, "key": {"title": "Key can be used to authenticate ingress calls\n", "type": "string"}}, "required": ["key", "ingressWebsocketURL"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GenerateAudioIngress<PERSON>ey represents a key used to authenticate audio ingress\n"}}, "/transcriptions.GetBatchIDs": {"get": {"operationId": "GET:transcriptions.GetBatchIDs", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"data": {"items": {"type": "string"}, "type": "array"}}, "required": ["data"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.GetSpeakersAsSessionGuestsBySessionID": {"get": {"operationId": "GET:transcriptions.GetSpeakersAsSessionGuestsBySessionID", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"guests": {"items": {"$ref": "#/components/schemas/shared.GuestDTO"}, "type": "array"}}, "required": ["guests"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.GetTranscriptionBatch": {"get": {"operationId": "GET:transcriptions.GetTranscriptionBatch", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "batchID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"Transcriptions": {"items": {"$ref": "#/components/schemas/shared.TranscriptionDTO"}, "type": "array"}}, "required": ["Transcriptions"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.ProcessBotRecording": {"post": {"operationId": "POST:transcriptions.ProcessBotRecording", "requestBody": {"content": {"application/json": {"schema": {"properties": {"bot_id": {"type": "string"}, "recurrence_id": {"type": "string"}, "session_id": {"type": "string"}, "video_url": {"type": "string"}}, "required": ["video_url", "session_id", "recurrence_id", "bot_id"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/transcriptions.RemoveUsersFromRecallAccount": {"post": {"description": "We need to take into account that the delete user endpoint is rate limited to 10 request per minute.\n", "operationId": "POST:transcriptions.RemoveUsersFromRecallAccount", "requestBody": {"content": {"application/json": {"schema": {"properties": {"exceptions": {"items": {"type": "string"}, "title": "Exceptions List of external user IDs to exclude from deletion\n", "type": "array"}, "recallCustomAPIKey": {"title": "RecallCustomAPIKey valid Recall API key\n", "type": "string"}}, "required": ["recallCustomAPIKey", "exceptions"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "RemoveUsersFromRecallAccount Remove all users from the token passed in the\nparams.\n"}}, "/transcriptions.Update": {"post": {"operationId": "POST:transcriptions.Update", "requestBody": {"content": {"application/json": {"schema": {"properties": {"offTheRecord": {"type": "boolean"}, "recurrenceID": {"type": "string"}, "sessionID": {"type": "string"}}, "required": ["offTheRecord", "sessionID", "recurrenceID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/users": {"get": {"operationId": "GET:meetings.ListUsers", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "offset", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderBy", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "orderDirection", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "email", "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"offsetPagination": {"$ref": "#/components/schemas/shared.OffsetPaginationResponse"}, "users": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}}, "required": ["offsetPagination", "users"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:meetings.CreateUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "avatar": {"type": "string"}, "fingerprint": {"type": "string"}, "marketingOptIn": {"type": "boolean"}, "paddleCustomerID": {"type": "string"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "timezone": {"type": "string"}, "userEmail": {"type": "string"}, "userFirstName": {"type": "string"}, "userLastName": {"type": "string"}}, "required": ["userEmail", "userFirstName", "userLastName", "roleIDs"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"userID": {"type": "string"}}, "required": ["userID"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/ai-feed/event": {"post": {"operationId": "POST:meetings.AIFeedEvent", "requestBody": {"content": {"application/json": {"schema": {"properties": {"aiFeedID": {"format": "int64", "type": "integer"}, "eventType": {"$ref": "#/components/schemas/api.AIFeedEventType"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["eventType", "aiFeedID", "sessionID", "sessionRecurrenceID"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/ai-feed/get": {"get": {"operationId": "GET:meetings.GetAIFeed", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/guest": {"post": {"operationId": "POST:hubble.LoginGuestUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"title": "optional email of the guest user\n", "type": "string"}, "fullName": {"title": "optional full name of the guest user\n", "type": "string"}}, "required": ["email", "fullName"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"authToken": {"title": "the JWT token for the user\n", "type": "string"}, "guest": {"$ref": "#/components/schemas/shared.GuestDTO"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}}, "required": ["authToken", "refreshToken", "guest"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/is-authorized": {"post": {"description": "If the request is related to a session (i.e., the object is a session), and the initial authorization check fails, the function will attempt to populate session-specific policies, invalidate the cache, and then retry the authorization check.\n", "operationId": "POST:hubble.IsAuthorized", "requestBody": {"content": {"application/json": {"schema": {"properties": {"act": {"$ref": "#/components/schemas/shared.AuthPolicyActions"}, "obj": {"title": "object to check authorization on\n", "type": "string"}, "sub": {"items": {"type": "string"}, "title": "subject to check authorization for\n", "type": "array"}}, "required": ["sub", "obj", "act"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "IsAuthorized checks if the user is authorized to perform a specified action.\n"}}, "/v1.0/auth/is-authorized-batch": {"post": {"operationId": "POST:hubble.IsAuthorizedBatch", "requestBody": {"content": {"application/json": {"schema": {"properties": {"act": {"$ref": "#/components/schemas/shared.AuthPolicyActions"}, "objs": {"items": {"type": "string"}, "title": "objects to check authorization on\n", "type": "array"}, "sub": {"title": "subject to check authorization for\n", "type": "string"}}, "required": ["sub", "objs", "act"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/shared.IsAuthorizedBatchResponseDTO"}, "title": "array of authorization checks\n", "type": "array"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/is-authorized-feature": {"get": {"operationId": "GET:hubble.IsAuthorizedToSessionFeature", "parameters": [{"allowEmptyValue": true, "description": "the session ID\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "the feature to check authorization for\n", "explode": true, "in": "query", "name": "feature", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"isAuthorized": {"title": "whether the user is authorized\n", "type": "boolean"}, "subscriptionPlan": {"title": "the subscription plan of the users session\n", "type": "object"}}, "required": ["isAuthorized", "subscriptionPlan"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/is-authorized-integration": {"get": {"operationId": "GET:hubble.IsAuthorizedToIntegration", "parameters": [{"allowEmptyValue": true, "description": "the user ID\n", "explode": true, "in": "query", "name": "user_id", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "the provider name\n", "explode": true, "in": "query", "name": "provider_name", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"isAuthorized": {"title": "whether the user is authorized\n", "type": "boolean"}, "subscriptionPlan": {"title": "the subscription plan of the user\n", "type": "object"}}, "required": ["isAuthorized", "subscriptionPlan"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/logout": {"post": {"operationId": "POST:hubble.Logout", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/refresh-auth-token": {"put": {"operationId": "PUT:hubble.RefreshAuthToken", "requestBody": {"content": {"application/json": {"schema": {"properties": {"refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}}, "required": ["refreshToken"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"authToken": {"title": "the JWT token for the user\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}}, "required": ["authToken", "refreshToken"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/request-otp": {"post": {"operationId": "POST:hubble.RequestOTP", "parameters": [{"allowEmptyValue": true, "description": "the IP address of the user\n", "explode": true, "in": "header", "name": "x-forwarded-for", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "email": {"title": "the email to send the OTP to\n", "type": "string"}, "fingerprint": {"title": "the fingerprint of the device\n", "type": "string"}, "firstName": {"title": "the first name of the user only required if signing up\n", "type": "string"}, "isSignup": {"title": "whether to sign the user up if they don't exist\n", "type": "boolean"}, "joinSession": {"title": "whether to join the user to an existing session\n", "type": "boolean"}, "lastName": {"title": "the last name of the user only required if signing up\n", "type": "string"}, "marketingOptIn": {"title": "whether the user opted in to marketing, promise we don't spam you\n", "type": "boolean"}, "redirect_uri": {"title": "the URL to redirect the user to after they verify their OTP\n", "type": "string"}, "sessionID": {"title": "the session ID to join the user to, required if joinSession is true\n", "type": "string"}, "state": {"title": "used for oAuth2 support, this would be passed on the auth request\n", "type": "string"}, "timezone": {"title": "the timezone of the user only required if signing up\n", "type": "string"}}, "required": ["email", "fingerprint", "firstName", "isSignup", "joinSession", "sessionID", "lastName", "marketingOptIn", "redirect_uri", "timezone", "client_id", "state"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/social-code": {"post": {"operationId": "POST:hubble.SocialCodeLogin", "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "code": {"type": "string"}, "redirect_uri": {"title": "the URL to redirect the user to after they verify their OTP\n", "type": "string"}, "state": {"title": "used by OAuth requester to validate\n", "type": "string"}, "type": {"type": "string"}}, "required": ["code", "type", "client_id", "redirect_uri", "state"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"agent_id": {"title": "the user ID of the user\n", "type": "string"}, "authToken": {"title": "the JWT token for the user\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}, "userID": {"title": "the user ID of the user\n", "type": "string"}}, "required": ["authToken", "refreshToken", "userID", "agent_id"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/social-token": {"post": {"operationId": "POST:hubble.SocialTokenLogin", "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "redirect_uri": {"title": "the URL to redirect the user to after they verify their OTP\n", "type": "string"}, "state": {"title": "used by OAuth requester to validate\n", "type": "string"}, "token": {"title": "the social token\n", "type": "string"}, "type": {"title": "the social type\n", "type": "string"}, "userDetails": {"properties": {"firstName": {"title": "the first name of the user\n", "type": "string"}, "lastName": {"title": "the last name of the user\n", "type": "string"}, "nonce": {"title": "the nonce of the user\n", "type": "string"}}, "required": ["firstName", "lastName", "nonce"], "title": "the user details\n", "type": "object"}}, "required": ["token", "type", "userDetails", "client_id", "redirect_uri", "state"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"agent_id": {"title": "the user ID of the user\n", "type": "string"}, "authToken": {"title": "the JWT token for the user\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}, "userID": {"title": "the user ID of the user\n", "type": "string"}}, "required": ["authToken", "refreshToken", "userID", "agent_id"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/user": {"delete": {"operationId": "DELETE:hubble.DeleteUser", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "email", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "get": {"operationId": "GET:hubble.UserByToken", "parameters": [{"allowEmptyValue": true, "description": "the JWT token\n", "explode": true, "in": "query", "name": "token", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"Data": {"properties": {"authUser": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["authUser"], "type": "object"}}, "required": ["Data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "patch": {"operationId": "PATCH:hubble.UpdateAuthUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"type": "string"}, "id": {"type": "string"}, "roleIDs": {"items": {"type": "string"}, "type": "array"}, "teamID": {"type": "string"}, "teamRoleIDs": {"items": {"type": "string"}, "type": "array"}}, "required": ["id", "email", "teamID", "roleIDs", "teamRoleIDs"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "post": {"operationId": "POST:hubble.UpsertAuthUsers", "requestBody": {"content": {"application/json": {"schema": {"properties": {"users": {"items": {"$ref": "#/components/schemas/api.CreateAuthUserInput"}, "type": "array"}}, "required": ["users"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}, "success": {"type": "boolean"}, "users": {"items": {"$ref": "#/components/schemas/models.AuthUser"}, "type": "array"}}, "required": ["success", "message", "users"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/user/host-opt-in": {"patch": {"operationId": "PATCH:hubble.HostOptIn", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"agent_id": {"title": "the user ID of the user\n", "type": "string"}, "authToken": {"title": "the JWT token for the user\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}, "userID": {"title": "the user ID of the user\n", "type": "string"}}, "required": ["agent_id", "authToken", "refreshToken", "user", "userID"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/auth/verify-otp": {"post": {"operationId": "POST:hubble.VerifyEmailOTP", "parameters": [{"allowEmptyValue": true, "description": "the IP address of the user\n", "explode": true, "in": "header", "name": "x-forwarded-for", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "email": {"title": "the email to verify the OTP for\n", "type": "string"}, "otp": {"title": "the OTP to verify\n", "type": "string"}, "state": {"title": "used for oAuth2 support, the requesting service will verify this value matches\nthe request\n", "type": "string"}}, "required": ["email", "otp", "client_id", "state"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"authToken": {"title": "the JWT token for the user\n", "type": "string"}, "redirect_uri": {"title": "the redirect URI\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}, "userID": {"title": "the user ID of the user\n", "type": "string"}}, "required": ["authToken", "refreshToken", "userID", "redirect_uri"], "title": "the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/billing/plans": {"get": {"operationId": "GET:billing.ListPlans", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.SubscriptionPlanConfigDTOAndPaddleProductDTO"}, "type": "array"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/chat/token": {"get": {"operationId": "GET:meetings.GetChatToken", "parameters": [{"allowEmptyValue": true, "description": "The session ID of the user\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "The session recurrence ID of the user\n", "explode": true, "in": "query", "name": "sessionRecurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "data": {"properties": {"channelRole": {"$ref": "#/components/schemas/api.ChannelRole"}, "chatRoomId": {"title": "The ID of the chat room\n", "type": "string"}, "token": {"title": "The chat token\n", "type": "string"}}, "required": ["token", "channelRole", "chatRoomId"], "title": "The data returned by the API\n", "type": "object"}, "message": {"title": "A message from the API\n", "type": "string"}, "success": {"title": "Whether the code is a success\n", "type": "boolean"}}, "required": ["message", "success", "code", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/integrations/-/get-bindings-by-owner-session-recurrence-per-provider": {"get": {"operationId": "GET:wormhole.GetBindingsByOwnerSessionRecurrencePerProvider", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/integrations/events": {"post": {"operationId": "POST:wormhole.PostIntegrationEvent", "requestBody": {"content": {"application/json": {"schema": {"properties": {"eventType": {"$ref": "#/components/schemas/api.IntegrationEventType"}, "payload": {"type": "object"}}, "required": ["eventType", "payload"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}}, "required": ["message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/lobbies": {"post": {"operationId": "POST:lobbies.CreateLobby", "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"title": "About New about\n", "type": "string"}, "avatarURL": {"title": "AvatarURL New avatar URL\n", "type": "string"}, "isActive": {"title": "IsActive status\n", "type": "boolean"}, "name": {"title": "Name New name\n", "type": "string"}, "slug": {"title": "Slug When the slug is empty use the users first and last name if taken append\nnumber\n", "type": "string"}}, "required": ["slug", "isActive", "avatarURL", "name", "about"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"title": "About Defaults owner bio\n", "type": "string"}, "activeRecurrenceID": {"title": "ActiveRecurrenceID Active recurrence ID will null by default\n", "type": "string"}, "activeSessionID": {"title": "ActiveSessionID Active session ID will null by default\n", "type": "string"}, "avatarURL": {"title": "AvatarURL Defaults owner avatar URL\n", "type": "string"}, "isActive": {"title": "IsActive Is true when the lobby is enabled false otherwise\n", "type": "boolean"}, "isOwnerDetails": {"title": "Extra details for multi owner lobbies\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "name": {"title": "Name Defaults owner first name and last name\n", "type": "string"}, "owners": {"items": {"$ref": "#/components/schemas/shared.UserSocialDTO"}, "type": "array"}, "participants": {"items": {"$ref": "#/components/schemas/shared.LobbyParticipantDTO"}, "type": "array"}, "slug": {"type": "string"}}, "required": ["lobbyID", "slug", "isActive", "activeSessionID", "activeRecurrenceID", "owners", "participants", "isOwnerDetails", "avatarURL", "name", "about"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/lobbies/add-participant": {"post": {"operationId": "POST:lobbies.AddParticipantsToSession", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"type": "string"}, "participantIDs": {"items": {"type": "string"}, "type": "array"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["lobbyID", "sessionID", "sessionRecurrenceID", "participantIDs"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/lobbies/enter": {"get": {"operationId": "GET:lobbies.EnterLobbyBraidable", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/lobbies/slug": {"put": {"operationId": "PUT:lobbies.UpdateL<PERSON>by", "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"title": "About New about\n", "type": "string"}, "avatarURL": {"title": "Multi owner lobbies fields\n", "type": "string"}, "isActive": {"title": "IsActive New enabled status\n", "type": "boolean"}, "lobbyID": {"title": "LobbyID LobbyDTO Lobbies' ID\n", "type": "string"}, "name": {"title": "Name New name\n", "type": "string"}, "ownerUserIDs": {"items": {"type": "string"}, "title": "OwnerUserIDs To add or remove owners from the lobby, cannot remove self\n", "type": "array"}, "slug": {"title": "Slug New slug\n", "type": "string"}}, "required": ["lobbyID", "slug", "isActive", "avatarURL", "name", "about", "ownerUserIDs"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/lobbies/start-session": {"post": {"operationId": "POST:lobbies.StartLobbySession", "requestBody": {"content": {"application/json": {"schema": {"properties": {"lobbyID": {"title": "LobbyID is the ID of the lobby\n", "type": "string"}, "participants": {"items": {"type": "string"}, "title": "Participants is a list of participant IDs to invite to the session\n", "type": "array"}, "session": {"title": "Session is the session to create\n", "type": "object"}}, "required": ["lobbyID", "session", "participants"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/lobbies/summary": {"get": {"operationId": "GET:lobbies.GetSummary", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "lobbyID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "slug", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.LobbySummaryDTO"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/meeting/{id}/token": {"post": {"operationId": "POST:livekit.GenerateToken", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"format": "int64", "title": "2000 for success\n", "type": "integer"}, "data": {"properties": {"token": {"title": "Livekit JWT token\n", "type": "string"}}, "required": ["token"], "type": "object"}, "message": {"title": "Message text if any\n", "type": "string"}, "success": {"title": "true for success\n", "type": "boolean"}}, "required": ["code", "success", "data", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/meeting/{roomName}": {"get": {"operationId": "GET:livekit.FetchRoomMetadata", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "roomName", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"offTheRecordStatus": {"$ref": "#/components/schemas/shared.OffTheRecord"}, "recordingStatus": {"$ref": "#/components/schemas/shared.RecordingStatus"}, "session": {"$ref": "#/components/schemas/shared.SessionDTO"}, "startedAt": {"format": "int64", "type": "integer"}}, "required": ["session", "offTheRecordStatus", "recordingStatus", "startedAt"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "FetchRoomMetadata fetches shared.RoomMetadata from Livekit API\n"}}, "/v1.0/meeting/{roomName}/end": {"post": {"operationId": "POST:livekit.EndRoom", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "roomName", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/meeting/{roomName}/participant/{identity}": {"get": {"operationId": "GET:livekit.FetchParticipantMetadata", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "roomName", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "identity", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"IsDiarized": {"type": "boolean"}, "guest": {"$ref": "#/components/schemas/shared.GuestDTO"}, "isLoaded": {"type": "boolean"}, "region": {"type": "string"}, "user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user", "guest", "isLoaded", "IsDiarized", "region"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/meeting/{roomName}/participants/count": {"get": {"operationId": "GET:livekit.GetParticipantsCount", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "roomName", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"count": {"format": "int64", "type": "integer"}}, "required": ["count"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/meeting/{room}/participant": {"patch": {"operationId": "PATCH:livekit.PatchParticipantState", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "room", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"isDiarized": {"type": "boolean"}, "isLoaded": {"type": "boolean"}}, "required": ["isLoaded", "isDiarized"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/memory/ai-stream": {"post": {"operationId": "POST:wormhole.AskAIV2", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AskAI queries meeting memory\n"}}, "/v1.0/memory/ask-ai-sync": {"post": {"operationId": "POST:wormhole.AskAISync", "requestBody": {"content": {"application/json": {"schema": {"properties": {"messageId": {"type": "string"}, "query": {"type": "string"}, "refresh": {"type": "boolean"}, "requestId": {"type": "string"}, "retry": {"type": "boolean"}, "sessions": {"items": {"properties": {"id": {"type": "string"}, "recurrenceIds": {"items": {"type": "string"}, "type": "array"}}, "required": ["id", "recurrenceIds"], "type": "object"}, "type": "array"}, "threadId": {"type": "string"}, "timeout": {"type": "number"}, "tz": {"type": "string"}}, "required": ["messageId", "query", "refresh", "requestId", "retry", "sessions", "threadId", "timeout", "tz"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"content": {"type": "string"}, "sources": {"type": "object"}, "threadId": {"type": "string"}}, "required": ["content", "sources", "threadId"], "type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AskAISync queries meeting memory in a synchronous, non-streaming way\n"}}, "/v1.0/memory/feedback": {"patch": {"operationId": "PATCH:wormhole.PatchUserFeedback", "requestBody": {"content": {"application/json": {"schema": {"properties": {"feedback": {"format": "int64", "type": "integer"}, "messageId": {"type": "string"}}, "required": ["feedback", "messageId"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.UserFeedbackResponseData"}, "message": {"type": "string"}}, "required": ["data", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PatchUserFeedback posts feedback on behalf of the user (neutral, positive,\nnegative)\n"}}, "/v1.0/memory/stop": {"post": {"operationId": "POST:wormhole.StopGenerationRun", "requestBody": {"content": {"application/json": {"schema": {"properties": {"threadId": {"type": "string"}}, "required": ["threadId"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"providerThreadId": {"type": "string"}, "runId": {"type": "string"}, "threadId": {"type": "string"}}, "required": ["providerThreadId", "runId", "threadId"], "type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["data", "message", "success"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "StopGenerationRun issues a command to nebula to stop a specific thread llm text\ngeneration run\n"}}, "/v1.0/memory/suggestions": {"get": {"operationId": "GET:wormhole.GetSuggestions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "skip", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"suggestions": {"items": {"$ref": "#/components/schemas/api.MMSuggestion"}, "type": "array"}, "total": {"format": "int64", "type": "integer"}}, "required": ["total", "suggestions"], "type": "object"}, "message": {"type": "string"}}, "required": ["data", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Returns meeting memory query suggestions\n"}}, "/v1.0/memory/threads": {"get": {"operationId": "GET:wormhole.GetUserThreads", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "skip", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"hasMore": {"type": "boolean"}, "threads": {"items": {"$ref": "#/components/schemas/nebula_api.ThreadDTO"}, "type": "array"}, "total": {"format": "int64", "type": "integer"}}, "required": ["hasMore", "threads", "total"], "type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["data", "message", "success"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Gets user's meeting memory threads\n"}}, "/v1.0/memory/threads/{threadID}": {"delete": {"operationId": "DELETE:wormhole.DeleteThreadById", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "threadID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"threadId": {"type": "string"}}, "required": ["threadId"], "type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["data", "message", "success"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Deletes a thread\n"}, "get": {"operationId": "GET:wormhole.GetThreadById", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "threadID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"thread": {"$ref": "#/components/schemas/nebula_api.ThreadDTO"}}, "required": ["thread"], "type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["data", "message", "success"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Gets thread by id\n"}}, "/v1.0/memory/threads/{threadID}/messages": {"get": {"operationId": "GET:wormhole.GetThreadMessages", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "threadID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "skip", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"hasMore": {"type": "boolean"}, "messages": {"items": {"$ref": "#/components/schemas/nebula_api.MessageDTO"}, "type": "array"}, "total": {"format": "int64", "type": "integer"}}, "required": ["hasMore", "messages", "total"], "type": "object"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["data", "message", "success"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Gets thread messages\n"}}, "/v1.0/oauth/exchange-code-for-token": {"post": {"operationId": "POST:hubble.OAuthExchangeAuthCodeForAuthToken", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/oauth/refresh-auth-token": {"post": {"operationId": "POST:hubble.OAuthRefreshToken", "requestBody": {"content": {"application/json": {"schema": {"properties": {"GrantType": {"title": "the grant type\n", "type": "string"}, "clientSecret": {"title": "Secret of the client provider, only used for oauth requests\n", "type": "string"}, "client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}}, "required": ["refreshToken", "client_id", "clientSecret", "GrantType"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"access_token": {"type": "string"}, "expires_at_time": {"format": "int64", "title": "ExpiresAtTime the unix timestamp in seconds after which the token expires\n", "type": "integer"}, "expires_in": {"format": "int64", "title": "ExpiresIn the time in seconds until the token expires\n", "type": "integer"}, "refresh_token": {"type": "string"}, "refresh_token_expires_at_time": {"format": "int64", "title": "RefreshTokenExpiresAtTime the unix timestamp for refresh token expiry\n", "type": "integer"}, "refresh_token_expires_in": {"format": "int64", "title": "RefreshTokenExpiresIn the time in seconds until the refresh token expires\n", "type": "integer"}, "token_type": {"type": "string"}}, "required": ["access_token", "expires_in", "expires_at_time", "refresh_token", "refresh_token_expires_in", "refresh_token_expires_at_time", "token_type"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/oauth/request-otp": {"post": {"operationId": "POST:hubble.OAuthRequestOTP", "parameters": [{"allowEmptyValue": true, "description": "the IP address of the user\n", "explode": true, "in": "header", "name": "x-forwarded-for", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "email": {"title": "the email to send the OTP to\n", "type": "string"}, "fingerprint": {"title": "the fingerprint of the device\n", "type": "string"}, "firstName": {"title": "the first name of the user only required if signing up\n", "type": "string"}, "isSignup": {"title": "whether to sign the user up if they don't exist\n", "type": "boolean"}, "joinSession": {"title": "whether to join the user to an existing session\n", "type": "boolean"}, "lastName": {"title": "the last name of the user only required if signing up\n", "type": "string"}, "marketingOptIn": {"title": "whether the user opted in to marketing, promise we don't spam you\n", "type": "boolean"}, "redirect_uri": {"title": "the URL to redirect the user to after they verify their OTP\n", "type": "string"}, "sessionID": {"title": "the session ID to join the user to, required if joinSession is true\n", "type": "string"}, "state": {"title": "used for oAuth2 support, this would be passed on the auth request\n", "type": "string"}, "timezone": {"title": "the timezone of the user only required if signing up\n", "type": "string"}}, "required": ["email", "fingerprint", "firstName", "isSignup", "joinSession", "sessionID", "lastName", "marketingOptIn", "redirect_uri", "timezone", "client_id", "state"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/oauth/social-code": {"post": {"operationId": "POST:hubble.OAuthSocialCodeLogin", "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "code": {"type": "string"}, "redirect_uri": {"title": "the URL to redirect the user to after they verify their OTP\n", "type": "string"}, "state": {"title": "used by OAuth requester to validate\n", "type": "string"}, "type": {"type": "string"}}, "required": ["code", "type", "client_id", "redirect_uri", "state"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"redirect_uri": {"type": "string"}}, "required": ["redirect_uri"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/oauth/verify-otp": {"post": {"operationId": "POST:hubble.OAuthVerifyEmailOTP", "parameters": [{"allowEmptyValue": true, "description": "the IP address of the user\n", "explode": true, "in": "header", "name": "x-forwarded-for", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"client_id": {"title": "ID of the client provider, only used for oauth requests\n", "type": "string"}, "email": {"title": "the email to verify the OTP for\n", "type": "string"}, "otp": {"title": "the OTP to verify\n", "type": "string"}, "state": {"title": "used for oAuth2 support, the requesting service will verify this value matches\nthe request\n", "type": "string"}}, "required": ["email", "otp", "client_id", "state"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"redirect_uri": {"type": "string"}}, "required": ["redirect_uri"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/oauth2/client-credentials": {"post": {"operationId": "POST:hubble.IssueNewOAuth2ClientCredentials", "requestBody": {"content": {"application/json": {"schema": {"properties": {"clientDescription": {"type": "string"}, "clientName": {"type": "string"}, "redirectURI": {"type": "string"}}, "required": ["clientDescription", "clientName", "redirectURI"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"clientDescription": {"type": "string"}, "clientID": {"type": "string"}, "clientName": {"type": "string"}, "clientSecret": {"type": "string"}, "redirectURI": {"type": "string"}}, "required": ["clientDescription", "clientID", "clientName", "clientSecret", "redirectURI"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/post-session-summaries/by-session-ids/{sessionID}": {"delete": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "DELETE:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "get": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "GET:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "head": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "HEAD:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "patch": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "PATCH:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "post": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "POST:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}, "put": {"description": "202 - Accepted: when enabled but pending generation 404 - Not Found: when AI not enabled 200 - Ok: when enabled and done generating 400 - Bad Request: when the sessionID and sessionRecurrenceID is not provided\n", "operationId": "PUT:meetings.PostSessionSummaryOptimistic", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session\nreturns the following status codes\n"}}, "/v1.0/recurrences/past": {"get": {"operationId": "GET:meetings.GetPastSessionRecurrences", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "summaAI", "required": true, "schema": {"type": "string"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/session/transcriptions": {"get": {"operationId": "GET:transcriptions.Download", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions": {"post": {"operationId": "POST:meetings.CreateSession", "parameters": [{"allowEmptyValue": true, "description": "This should match the old request payload from Mars:\n", "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"type": "string"}, "avatar": {"type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "category": {"type": "string"}, "cover": {"type": "string"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "isPubliclyVisible": {"type": "boolean"}, "isViewerAccessRestricted": {"type": "boolean"}, "lobbyID": {"title": "Optional\n", "type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "sessionSettings": {"type": "object"}, "sessionState": {"type": "string"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "userID": {"title": "Optional, specify the userID to create the session for\n", "type": "string"}, "userMeetingType": {"$ref": "#/components/schemas/api.UserMeetingTypeRequestDTO"}, "viewerAccessRules": {"items": {"$ref": "#/components/schemas/api.SessionAccessRulesDTO"}, "type": "array"}}, "required": ["about", "accessStatus", "avatar", "calendarEventEditURL", "calendarID", "calendarType", "category", "cover", "dataVisibility", "endTimestamp", "meetingType", "ogMetadata", "sessionSettings", "startTimestamp", "sessionState", "sessionTags", "sessionTitle", "userMeetingType", "viewerAccessRules", "isPubliclyVisible", "isViewerAccessRestricted", "userID", "lobbyID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access": {"post": {"operationId": "POST:meetings.UpdateSessionAccessControlRules", "requestBody": {"content": {"application/json": {"schema": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/api.UpdateAccessRule"}, "type": "array"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "accessRules"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/add": {"put": {"operationId": "PUT:meetings.AddSessionAccessControlRules", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"domains": {"items": {"type": "string"}, "type": "array"}, "emails": {"items": {"type": "string"}, "type": "array"}, "message": {"type": "string"}, "overrideRules": {"type": "boolean"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "viewerAccessControl": {"items": {"$ref": "#/components/schemas/api.AccessControlItem"}, "type": "array"}}, "required": ["domains", "emails", "sessionID", "sessionRecurrenceID", "message", "overrideRules", "viewerAccessControl"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/shared.SessionAccessRuleDTO"}, "type": "array"}}, "required": ["accessRules"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AddSessionAccessControlRules is now deprecated, please use\nUpdateSessionAccessControlRules (later down in this file)\n"}}, "/v1.0/sessions/access/get-in-review-access-requests-count": {"post": {"operationId": "POST:meetings.CountInReviewRequests", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessions": {"items": {"$ref": "#/components/schemas/api.SessionRequestCount"}, "type": "array"}}, "required": ["sessions"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.SessionRequestCount"}, "type": "array"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/grouped": {"get": {"operationId": "GET:meetings.GetAccessRequestsBraidable", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/in-review": {"get": {"operationId": "GET:meetings.GetInReviewBraidable", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead\n"}}, "/v1.0/sessions/access/remove": {"delete": {"operationId": "DELETE:meetings.RemoveSessionAccessControlRules", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/access/request": {"put": {"operationId": "PUT:meetings.ApproveOrDenyedSessionAccessRequest", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"callTrixtaAction": {"type": "boolean"}, "id": {"type": "string"}, "restrictionStatus": {"type": "string"}}, "required": ["callTrixtaAction", "id", "restrictionStatus"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"message": {"type": "string"}, "sessionId": {"type": "string"}}, "required": ["sessionId", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "ApproveOrDenyedSessionAccessRequest is deprecated and should not be used.\n"}}, "/v1.0/sessions/access/request-access": {"post": {"operationId": "POST:meetings.CreateAccessRequest", "requestBody": {"content": {"application/json": {"schema": {"properties": {"requestMessage": {"type": "string"}, "sessionID": {"type": "string"}}, "required": ["sessionID", "requestMessage"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/cards": {"get": {"operationId": "GET:wormhole.GetIntegrationCards", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/future": {"get": {"operationId": "GET:meetings.GetFutureSessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/guest": {"post": {"operationId": "POST:meetings.LoginGuestUserWithSession", "requestBody": {"content": {"application/json": {"schema": {"properties": {"email": {"title": "optional email of the guest user\n", "type": "string"}, "fullName": {"title": "optional full name of the guest user\n", "type": "string"}, "sessionID": {"title": "session ID\n", "type": "string"}}, "required": ["email", "fullName", "sessionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "data": {"properties": {"authToken": {"title": "the JWT token for the user\n", "type": "string"}, "refreshToken": {"title": "the refresh JWT token for the user\n", "type": "string"}}, "required": ["authToken", "refreshToken"], "title": "the response data\n", "type": "object"}, "message": {"type": "string"}}, "required": ["code", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/library/upcoming": {"get": {"description": "Deprecated: Use GetFutureSessions instead\n", "operationId": "GET:meetings.GetLibraryUpcomingSessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Get future sessions\n"}}, "/v1.0/sessions/meeting-memory": {"get": {"description": "Deprecated: Use GetPastSessionRecurrences instead, with SummaAI=true Remove once the FE is updated to use GetPastSessionRecurrences\n", "operationId": "GET:meetings.GetMeetingMemorySessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetMeetingMemorySessions returns all past sessions that have summaAI enabled\n"}}, "/v1.0/sessions/past": {"get": {"description": "Deprecated: Use GetPastSessionRecurrences instead Remove once the FE is updated to use GetPastSessionRecurrences\n", "operationId": "GET:meetings.GetPastSessions", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.ListSessionsResponseData"}, "nextCursor": {"format": "int64", "type": "integer"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetPastSessions returns all past sessions\n"}}, "/v1.0/sessions/presence/{id}": {"get": {"operationId": "GET:meetings.Presence", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "id", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/users/{sessionID}/{recurrenceID}": {"get": {"operationId": "GET:meetings.GetSessionUsers", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.SessionDefaultUsersDTO"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/{sessionID}/recurrence/{recurrenceID}": {"patch": {"operationId": "PATCH:meetings.UpdateSession", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"type": "string"}, "dataVisibility": {"type": "string"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "isPubliclyVisible": {"type": "boolean"}, "primaryHostUserID": {"type": "string"}, "sessionCategory": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"title": "Title sessionTitle is the field name from the FE\n", "type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"properties": {"id": {"type": "string"}}, "required": ["id"], "type": "object"}}, "required": ["startTimestamp", "endTimestamp", "startedAt", "endedAt", "sessionTitle", "about", "sessionState", "sessionSettings", "sessionCategory", "sessionTags", "primaryHostUserID", "isPubliclyVisible", "accessStatus", "dataVisibility", "userMeetingType"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"Code": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "Message": {"type": "string"}, "data": {"properties": {"session": {"$ref": "#/components/schemas/shared.SessionDTO"}}, "required": ["session"], "type": "object"}}, "required": ["Code", "Message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur": {"patch": {"operationId": "PATCH:meetings.RecurSessionNoRequest", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur-at": {"patch": {"operationId": "PATCH:meetings.RecurSession", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-client-info", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"endTimestamp": {"format": "int64", "type": "integer"}, "startTimestamp": {"format": "int64", "type": "integer"}}, "required": ["startTimestamp", "endTimestamp"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/time": {"get": {"operationId": "GET:wormhole.BraidableTimePinger", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/audio-ingress": {"get": {"operationId": "GET:transcriptions.AudioIngressWebsocket", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "AudioIngressWebsocket handles the websocket connection for raw pcm audio from\nLivekit/other sources\n"}}, "/v1.0/transcriptions/bot/calendar-user": {"get": {"operationId": "GET:transcriptions.GetCalendarUser", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"connections": {"items": {"$ref": "#/components/schemas/recallai.RecallConnection"}, "type": "array"}, "external_id": {"type": "string"}, "id": {"type": "string"}, "preferences": {"$ref": "#/components/schemas/recallai.RecordingPreferences"}}, "required": ["id", "external_id", "connections", "preferences"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/bot/disconnect": {"post": {"operationId": "POST:transcriptions.DisconnectCalendar", "requestBody": {"content": {"application/json": {"schema": {"properties": {"platform": {"title": "Should be the string 'google' or 'microsoft'\n", "type": "string"}}, "required": ["platform"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "Disconnect a user's calendar platform from our Rumi bots recall.ai account\n"}}, "/v1.0/transcriptions/bot/google-auth": {"get": {"operationId": "GET:transcriptions.GenerateBotAuthURL", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"authURL": {"type": "string"}}, "required": ["authURL"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GenerateBotAuthURL Generate a URL for the user to authenticate with Google\nCalendar.\n"}}, "/v1.0/transcriptions/bot/webhook": {"post": {"operationId": "POST:transcriptions.RecallTranscriptionsWebhook", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-id", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-timestamp", "required": true, "schema": {"format": "int64", "type": "integer"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-svix-signature", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "token", "required": true, "schema": {"type": "string"}, "style": "form"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.RecallTranscriptionsWebhookData"}, "event": {"type": "string"}}, "required": ["event", "data"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/transcriptions/speakers": {"get": {"operationId": "GET:transcriptions.FetchHeardSpeakers", "parameters": [{"allowEmptyValue": true, "description": "SessionID is the session for which we want to fetch heard speakers\n", "explode": true, "in": "query", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "RecurrenceID is the recurrence for which we want to fetch heard speakers\n", "explode": true, "in": "query", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "description": "IncludeSnippets determines whether to include speaker snippets in the response, defaults to false\n", "explode": true, "in": "query", "name": "includeSnippets", "required": true, "schema": {"type": "boolean"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"speakers": {"items": {"$ref": "#/components/schemas/api.HeardSpeaker"}, "title": "Speakers is the list of speakers heard in the session\n", "type": "array"}}, "required": ["speakers"], "title": "Data is the response data\n", "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "FetchHeardSpeakers fetches array of speakers heard in a session\n"}, "post": {"operationId": "POST:transcriptions.IdentifyHeardSpeakers", "requestBody": {"content": {"application/json": {"schema": {"properties": {"identifiedSpeakers": {"items": {"$ref": "#/components/schemas/api.IdentifiedSpeaker"}, "title": "IdentifiedSpeakers is the list of speakers to identify\n", "type": "array"}, "recurrenceID": {"title": "RecurrenceID is the recurrence for which we want to identify speakers\n", "type": "string"}, "sessionID": {"title": "SessionID is the session for which we want to identify speakers\n", "type": "string"}}, "required": ["sessionID", "recurrenceID", "identifiedSpeakers"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "IdentifyHeardSpeakers identifies an array of speakers heard in a session\n"}}, "/v1.0/users/id/{userID}": {"put": {"operationId": "PUT:meetings.UpdateUserByID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"oldAvatar": {"type": "string"}, "userData": {"properties": {"about": {"type": "string"}, "avatar": {"type": "string"}, "fingerprint": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "notificationSettings": {"properties": {"isEmailNotificationEnabledGlobal": {"type": "boolean"}, "isPushNotificationEnabledGlobal": {"type": "boolean"}}, "required": ["isEmailNotificationEnabledGlobal", "isPushNotificationEnabledGlobal"], "type": "object"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "social": {"items": {"$ref": "#/components/schemas/mars_api.UserSocial"}, "type": "array"}, "timezone": {"type": "string"}}, "required": ["about", "avatar", "fingerprint", "notificationSettings", "social", "timezone", "firstName", "lastName", "onboarding"], "type": "object"}, "userID": {"type": "string"}}, "required": ["userID", "<PERSON><PERSON><PERSON><PERSON>", "userData"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UpdateUserByID updates user details by their ID\n"}}, "/v1.0/users/id/{userID}/payment-method-details": {"get": {"operationId": "GET:meetings.GetUserPaymentMethodDetails", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/api.GetUserPaymentMethodDetailsResponseData"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/id/{userID}/plan": {"get": {"operationId": "GET:meetings.GetUserPlanByUserID", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/shared.UserSubscriptionPlanDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/id/{userID}/transactions": {"get": {"operationId": "GET:meetings.ListUserTransactions", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "limit", "schema": {"format": "int64", "type": "integer"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "cursor", "schema": {"type": "string"}, "style": "form"}, {"allowEmptyValue": true, "explode": true, "in": "query", "name": "includeInvoices", "schema": {"type": "boolean"}, "style": "form"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"items": {"$ref": "#/components/schemas/api.ListUserTransactionsResponseData"}, "type": "array"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/id/{userID}/update-payment-method-transaction": {"get": {"operationId": "GET:meetings.GetUpdateUserPaymentMethodTransaction", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "userID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"$ref": "#/components/schemas/shared.PaddleTransactionDTO"}, "message": {"type": "string"}, "success": {"type": "boolean"}}, "required": ["success", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/users/me": {"delete": {"operationId": "DELETE:wormhole.DeleteUserByID", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}, "get": {"operationId": "GET:lobbies.GetMyUser", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"user": {"$ref": "#/components/schemas/shared.UserDTO"}}, "required": ["user"], "type": "object"}}, "required": ["data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/ws-health": {"get": {"operationId": "GET:wormhole.ConnectionHealth", "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v1.0/{fallback}": {"delete": {"operationId": "DELETE:wormhole.WormholeRouterv1", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "get": {"operationId": "GET:wormhole.WormholeRouterv1", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "head": {"operationId": "HEAD:wormhole.WormholeRouterv1", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "patch": {"operationId": "PATCH:wormhole.WormholeRouterv1", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "post": {"operationId": "POST:wormhole.WormholeRouterv1", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "put": {"operationId": "PUT:wormhole.WormholeRouterv1", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}}, "/v2.0/session-recordings/off-the-record/disable": {"post": {"operationId": "POST:livekit.DisableOffTheRecord", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}}, "required": ["sessionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"format": "int64", "title": "2000 for success\n", "type": "integer"}, "message": {"title": "Message text if any\n", "type": "string"}, "success": {"title": "true for success\n", "type": "boolean"}}, "required": ["code", "success", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v2.0/session-recordings/off-the-record/enable": {"post": {"operationId": "POST:livekit.EnableOffTheRecord", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}}, "required": ["sessionID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"code": {"format": "int64", "title": "2000 for success\n", "type": "integer"}, "message": {"title": "Message text if any\n", "type": "string"}, "success": {"title": "true for success\n", "type": "boolean"}}, "required": ["code", "success", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/v2.0/{fallback}": {"delete": {"operationId": "DELETE:wormhole.WormholeRouterv2", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "get": {"operationId": "GET:wormhole.WormholeRouterv2", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "head": {"operationId": "HEAD:wormhole.WormholeRouterv2", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "patch": {"operationId": "PATCH:wormhole.WormholeRouterv2", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "post": {"operationId": "POST:wormhole.WormholeRouterv2", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}, "put": {"operationId": "PUT:wormhole.WormholeRouterv2", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "fallback", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "WormholeRouterv1 Route all requests to the existing HTTP router if no other\nendpoint matches.\n"}}, "/v3.0/sessions/{sessionID}/recurrences/{recurrenceID}": {"get": {"description": "It listens for updates on the session and in-review topics to refresh the session and access request data. When recurrenceID is \"latest\", it fetches the latest recurrence ID of the session.\n", "operationId": "GET:meetings.GetSessionRecurrenceByIdBraidableWithAccess", "parameters": [{"allowEmptyValue": true, "explode": false, "in": "path", "name": "sessionID", "required": true, "schema": {"type": "string"}, "style": "simple"}, {"allowEmptyValue": true, "explode": false, "in": "path", "name": "recurrenceID", "required": true, "schema": {"type": "string"}, "style": "simple"}], "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session\nand access DTO if any access rule is associated with the authenticated user.\n"}}, "/wormhole.AddSessionAccessControlRules": {"post": {"operationId": "POST:wormhole.AddSessionAccessControlRules", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"domains": {"items": {"type": "string"}, "type": "array"}, "emails": {"items": {"type": "string"}, "type": "array"}, "message": {"type": "string"}, "overrideRules": {"type": "boolean"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "viewerAccessControl": {"items": {"$ref": "#/components/schemas/api.AccessControlItem"}, "type": "array"}}, "required": ["domains", "emails", "sessionID", "sessionRecurrenceID", "message", "overrideRules", "viewerAccessControl"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"accessRules": {"items": {"$ref": "#/components/schemas/shared.SessionAccessRuleDTO"}, "type": "array"}, "userEmails": {"items": {"type": "string"}, "type": "array"}}, "required": ["accessRules", "userEmails"], "type": "object"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/wormhole.ApproveOrDenySessionAccessRequest": {"post": {"operationId": "POST:wormhole.ApproveOrDenySessionAccessRequest", "parameters": [{"allowEmptyValue": true, "explode": true, "in": "header", "name": "x-fingerprint", "required": true, "schema": {"type": "string"}, "style": "simple"}], "requestBody": {"content": {"application/json": {"schema": {"properties": {"callTrixtaAction": {"type": "boolean"}, "id": {"type": "string"}, "restrictionStatus": {"type": "string"}}, "required": ["callTrixtaAction", "id", "restrictionStatus"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "userEmail": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "userEmail"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/wormhole.CreateSession": {"post": {"operationId": "POST:wormhole.CreateSession", "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "title": {"type": "string"}}, "required": ["title", "about"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "CreateSession\n"}}, "/wormhole.CreateSessionWithUser": {"post": {"operationId": "POST:wormhole.CreateSessionWithUser", "requestBody": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "title": {"type": "string"}, "userID": {"type": "string"}}, "required": ["title", "about", "userID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"about": {"type": "string"}, "accessStatus": {"$ref": "#/components/schemas/shared.SessionDTOAccessStatus"}, "avatar": {"type": "string"}, "branchURL": {"title": "* @deprecated - should be removed if possible\n", "type": "string"}, "calendarEventEditURL": {"type": "string"}, "calendarID": {"type": "string"}, "calendarType": {"type": "string"}, "cover": {"type": "string"}, "createTimestamp": {"format": "int64", "type": "integer"}, "dataVisibility": {"$ref": "#/components/schemas/shared.DataVisibility"}, "endTimestamp": {"format": "int64", "type": "integer"}, "endedAt": {"format": "int64", "type": "integer"}, "featured": {"type": "boolean"}, "hasPastRecordings": {"type": "boolean"}, "hosts": {"items": {"$ref": "#/components/schemas/shared.UserDTO"}, "type": "array"}, "integrations": {"$ref": "#/components/schemas/shared.SessionIntegrationsDTO"}, "isLiveStreamEnabled": {"type": "boolean"}, "isRecordingProcessed": {"type": "boolean"}, "isViewerAccessRestricted": {"description": "SessionCategory          \\*SessionDTO\\_SessionCategory \\`json:\"sessionCategory,omitempty\"\\`\n", "title": "ScreenSharingSettings ScreenSharingSettingsDTO `json:\"screenSharingSettings\"`\n", "type": "boolean"}, "lobbyID": {"type": "string"}, "meetingType": {"$ref": "#/components/schemas/shared.SessionDTOType"}, "ogMetadata": {"type": "object"}, "primaryHostUserID": {"type": "string"}, "publicURL": {"type": "string"}, "rowversion": {"format": "int64", "title": "UserMeetingType *UserMeetingTypeDTO `json:\"userMeetingType,omitempty\"`\n", "type": "integer"}, "sessionCreatorUserID": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}, "sessionSettings": {"type": "object"}, "sessionState": {"$ref": "#/components/schemas/shared.SessionState"}, "sessionTags": {"items": {"type": "string"}, "type": "array"}, "sessionTitle": {"type": "string"}, "startTimestamp": {"format": "int64", "type": "integer"}, "startedAt": {"format": "int64", "type": "integer"}, "stateUpdatedAt": {"items": {"$ref": "#/components/schemas/shared.StateUpdatedAt"}, "type": "array"}, "subscriptionPlan": {"$ref": "#/components/schemas/shared.SessionSubscriptionPlanDTO"}, "updatedAt": {"format": "int64", "type": "integer"}, "userMeetingType": {"$ref": "#/components/schemas/shared.UserMeetingTypeDTO"}}, "required": ["about", "branchURL", "featured", "hosts", "isLiveStreamEnabled", "ogMetadata", "primaryHostUserID", "publicURL", "isViewerAccessRestricted", "dataVisibility", "sessionCreatorUserID", "sessionID", "sessionRecurrenceID", "sessionSettings", "sessionState", "sessionTitle", "updatedAt", "rowversion", "accessStatus", "meetingType"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "CreateSession\n"}}, "/wormhole.GetMeetingMetadataById": {"post": {"operationId": "POST:wormhole.GetMeetingMetadataById", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"metadata": {"properties": {"description": {"type": "string"}, "end_time": {"type": "string"}, "location": {"type": "string"}, "participants": {"items": {"type": "string"}, "type": "array"}, "start_time": {"type": "string"}, "title": {"type": "string"}}, "required": ["description", "end_time", "location", "participants", "start_time", "title"], "type": "object"}}, "required": ["metadata"], "type": "object"}, "message": {"type": "string"}}, "required": ["data", "message"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/wormhole.PostSessionSummary": {"post": {"operationId": "POST:wormhole.PostSessionSummary", "requestBody": {"content": {"application/json": {"schema": {"properties": {"sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"data": {"properties": {"content": {"type": "object"}, "created_at": {"type": "string"}, "html": {"type": "string"}, "id": {"format": "int64", "type": "integer"}, "md": {"type": "string"}, "session_id": {"type": "string"}, "session_recurrence_id": {"type": "string"}, "tldr": {"type": "string"}, "updated_at": {"type": "string"}}, "required": ["id", "session_id", "session_recurrence_id", "content", "html", "md", "tldr", "created_at", "updated_at"], "type": "object"}, "errCode": {"$ref": "#/components/schemas/shared.HttpErrorCode"}, "message": {"type": "string"}}, "required": ["errCode", "message", "data"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "PostSessionSummary Request the PSS payload from nebula.\n"}}, "/wormhole.RemoveSessionAccessControlRules": {"post": {"operationId": "POST:wormhole.RemoveSessionAccessControlRules", "requestBody": {"content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["id", "sessionID", "sessionRecurrenceID"], "type": "object"}}}}, "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"id": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID", "id"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}, "/wormhole.UpdateUserByID": {"post": {"operationId": "POST:wormhole.UpdateUserByID", "requestBody": {"content": {"application/json": {"schema": {"properties": {"oldAvatar": {"type": "string"}, "userData": {"properties": {"about": {"type": "string"}, "avatar": {"type": "string"}, "fingerprint": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "notificationSettings": {"properties": {"isEmailNotificationEnabledGlobal": {"type": "boolean"}, "isPushNotificationEnabledGlobal": {"type": "boolean"}}, "required": ["isEmailNotificationEnabledGlobal", "isPushNotificationEnabledGlobal"], "type": "object"}, "onboarding": {"$ref": "#/components/schemas/shared.OnboardingFlags"}, "social": {"items": {"$ref": "#/components/schemas/mars_api.UserSocial"}, "type": "array"}, "timezone": {"type": "string"}}, "required": ["about", "avatar", "fingerprint", "notificationSettings", "social", "timezone", "firstName", "lastName", "onboarding"], "type": "object"}, "userID": {"type": "string"}}, "required": ["userID", "<PERSON><PERSON><PERSON><PERSON>", "userData"], "type": "object"}}}}, "responses": {"200": {"description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}, "summary": "UpdateUserByID updates user details by their ID\n"}}, "/wormhole.UpsertRecording": {"post": {"operationId": "POST:wormhole.UpsertRecording", "responses": {"200": {"content": {"application/json": {"schema": {"properties": {"playlistLandscape": {"type": "string"}, "playlistPortrait": {"type": "string"}, "sessionID": {"type": "string"}, "sessionRecurrenceID": {"type": "string"}}, "required": ["sessionID", "sessionRecurrenceID"], "type": "object"}}}, "description": "Success response"}, "default": {"$ref": "#/components/responses/APIError"}}}}}, "servers": [{"description": "Encore local dev environment", "url": "http://localhost:4000"}]}