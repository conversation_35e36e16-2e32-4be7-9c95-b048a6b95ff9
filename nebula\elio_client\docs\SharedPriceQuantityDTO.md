# SharedPriceQuantityDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**maximum** | **float** |  | [optional] 
**minimum** | **float** |  | [optional] 

## Example

```python
from elio_client.models.shared_price_quantity_dto import SharedPriceQuantityDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedPriceQuantityDTO from a JSON string
shared_price_quantity_dto_instance = SharedPriceQuantityDTO.from_json(json)
# print the JSON string representation of the object
print(SharedPriceQuantityDTO.to_json())

# convert the object into a dict
shared_price_quantity_dto_dict = shared_price_quantity_dto_instance.to_dict()
# create an instance of SharedPriceQuantityDTO from a dict
shared_price_quantity_dto_from_dict = SharedPriceQuantityDTO.from_dict(shared_price_quantity_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


