# elio-client

Generated by encore

The `elio_client` package is automatically generated by the [OpenAPI Generator](https://openapi-generator.tech) project:

-   API version: 1
-   Package version: 1.0.0
-   Generator version: 7.10.0
-   Build package: org.openapitools.codegen.languages.PythonClientCodegen

## Requirements.

Python 3.8+

## Installation & Usage

This python library package is generated without supporting files like setup.py or requirements files

To be able to use it, you will need these dependencies in your own package that uses this library:

-   urllib3 >= 1.25.3, < 3.0.0
-   python-dateutil >= 2.8.2
-   pydantic >= 2
-   typing-extensions >= 4.7.1

## Getting Started

In your own code, to use this library to connect and interact with elio-client,
you can run the following:

```python

import elio_client
from elio_client.rest import ApiException
from pprint import pprint

# Defining the host is optional and defaults to http://localhost:4000
# See configuration.py for a list of all supported configuration parameters.
configuration = elio_client.Configuration(
    host = "http://localhost:4000"
)



# Enter a context with an instance of the API client
with elio_client.ApiClient(configuration) as api_client:
    # Create an instance of the API class
    api_instance = elio_client.DefaultApi(api_client)
    session_id = 'session_id_example' # str |

    try:
        # PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes
        api_instance.d_elete_meetings_post_session_summary_optimistic(session_id)
    except ApiException as e:
        print("Exception when calling DefaultApi->d_elete_meetings_post_session_summary_optimistic: %s\n" % e)

```

## Documentation for API Endpoints

All URIs are relative to _http://localhost:4000_

| Class        | Method                                                                                                                                                                  | HTTP request                                                            | Description                                                                                                                                                                    |
| ------------ | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| _DefaultApi_ | [**d_elete_meetings_post_session_summary_optimistic**](elio_client/docs/DefaultApi.md#d_elete_meetings_post_session_summary_optimistic)                                 | **DELETE** /v1.0/post-session-summaries/by-session-ids/{sessionID}      | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes                                                               |
| _DefaultApi_ | [**d_elete_meetings_remove_session_access_control_rules**](elio_client/docs/DefaultApi.md#d_elete_meetings_remove_session_access_control_rules)                         | **DELETE** /v1.0/sessions/access/remove                                 |
| _DefaultApi_ | [**d_elete_transcriptions_identity_associated**](elio_client/docs/DefaultApi.md#d_elete_transcriptions_identity_associated)                                             | **DELETE** /.well-known/microsoft-identity-association.json             | Needed to verify domains for Microsoft OAuth2 integration                                                                                                                      |
| _DefaultApi_ | [**d_elete_transcriptions_microsoft_redirect_of_redirect**](elio_client/docs/DefaultApi.md#d_elete_transcriptions_microsoft_redirect_of_redirect)                       | **DELETE** /v1.0/calendar/ms_oauth_callback                             | Needed redirect back to recall                                                                                                                                                 |
| _DefaultApi_ | [**d_elete_xray_delete_x_ray**](elio_client/docs/DefaultApi.md#d_elete_xray_delete_x_ray)                                                                               | **DELETE** /v1.0/xrays/{xrayID}                                         |
| _DefaultApi_ | [**g_et_comms_post_session_summary_ready**](elio_client/docs/DefaultApi.md#g_et_comms_post_session_summary_ready)                                                       | **GET** /comms.PostSessionSummaryReady                                  | PostSessionSummaryReady: Receive a request from nebula letting us know that the PSS was generated                                                                              |
| _DefaultApi_ | [**g_et_meetings_get_access_requests_braidable**](elio_client/docs/DefaultApi.md#g_et_meetings_get_access_requests_braidable)                                           | **GET** /v1.0/sessions/access/grouped                                   |
| _DefaultApi_ | [**g_et_meetings_get_ai_feed**](elio_client/docs/DefaultApi.md#g_et_meetings_get_ai_feed)                                                                               | **GET** /v1.0/ai-feed/get                                               |
| _DefaultApi_ | [**g_et_meetings_get_all_access_requests**](elio_client/docs/DefaultApi.md#g_et_meetings_get_all_access_requests)                                                       | **GET** /meetings.GetAllAccessRequests                                  | GetAllAccessRequests deprecated use meetings.GetSessionAccessRules instead                                                                                                     |
| _DefaultApi_ | [**g_et_meetings_get_chat_token**](elio_client/docs/DefaultApi.md#g_et_meetings_get_chat_token)                                                                         | **GET** /v1.0/chat/token                                                |
| _DefaultApi_ | [**g_et_meetings_get_future_sessions**](elio_client/docs/DefaultApi.md#g_et_meetings_get_future_sessions)                                                               | **GET** /v1.0/sessions/future                                           |
| _DefaultApi_ | [**g_et_meetings_get_in_review_braidable**](elio_client/docs/DefaultApi.md#g_et_meetings_get_in_review_braidable)                                                       | **GET** /v1.0/sessions/access/in-review                                 | GetInReviewBraidable deprecated use GetAccessRequestsBraidable instead                                                                                                         |
| _DefaultApi_ | [**g_et_meetings_get_latest_recurrence_compact**](elio_client/docs/DefaultApi.md#g_et_meetings_get_latest_recurrence_compact)                                           | **GET** /meetings.GetLatestRecurrenceCompact                            |
| _DefaultApi_ | [**g_et_meetings_get_library_upcoming_sessions**](elio_client/docs/DefaultApi.md#g_et_meetings_get_library_upcoming_sessions)                                           | **GET** /v1.0/sessions/library/upcoming                                 | Get future sessions                                                                                                                                                            |
| _DefaultApi_ | [**g_et_meetings_get_meeting_memory_sessions**](elio_client/docs/DefaultApi.md#g_et_meetings_get_meeting_memory_sessions)                                               | **GET** /v1.0/sessions/meeting-memory                                   | GetMeetingMemorySessions returns all past sessions that have summaAI enabled                                                                                                   |
| _DefaultApi_ | [**g_et_meetings_get_meeting_suggestions_by_session**](elio_client/docs/DefaultApi.md#g_et_meetings_get_meeting_suggestions_by_session)                                 | **GET** /meetings.GetMeetingSuggestionsBySession                        | GetMeetingSuggestionsBySession retrieves all meeting suggestions for a session (for future use)                                                                                |
| _DefaultApi_ | [**g_et_meetings_get_meeting_suggestions_by_user**](elio_client/docs/DefaultApi.md#g_et_meetings_get_meeting_suggestions_by_user)                                       | **GET** /v1.0/meetings/{sessionID}/{recurrenceID}/suggestions/user      | GetMeetingSuggestionsByUser retrieves meeting suggestions for a specific user                                                                                                  |
| _DefaultApi_ | [**g_et_meetings_get_meeting_suggestions_by_user_internal**](elio_client/docs/DefaultApi.md#g_et_meetings_get_meeting_suggestions_by_user_internal)                     | **GET** /meetings.GetMeetingSuggestionsByUserInternal                   | GetMeetingSuggestionsByUserInternal retrieves meeting suggestions for a specific user (internal service calls)                                                                 |
| _DefaultApi_ | [**g_et_meetings_get_past_session_recurrences**](elio_client/docs/DefaultApi.md#g_et_meetings_get_past_session_recurrences)                                             | **GET** /v1.0/recurrences/past                                          |
| _DefaultApi_ | [**g_et_meetings_get_past_sessions**](elio_client/docs/DefaultApi.md#g_et_meetings_get_past_sessions)                                                                   | **GET** /v1.0/sessions/past                                             | GetPastSessions returns all past sessions                                                                                                                                      |
| _DefaultApi_ | [**g_et_meetings_get_session_by_id**](elio_client/docs/DefaultApi.md#g_et_meetings_get_session_by_id)                                                                   | **GET** /meetings.GetSessionByID                                        | GetSessionByID Replaces &#x60;MarsClient.SessionControllerGetSessionByID&#x60;, that used to sent a request to: src/modules/sessions/controllers/session.controller.ts in mars |
| _DefaultApi_ | [**g_et_meetings_get_session_recurrence_by_id_braidable_with_access**](elio_client/docs/DefaultApi.md#g_et_meetings_get_session_recurrence_by_id_braidable_with_access) | **GET** /v3.0/sessions/{sessionID}/recurrences/{recurrenceID}           | GetSessionRecurrenceByIdBraidableWithAccess serves a realtime Braidable session and access DTO if any access rule is associated with the authenticated user.                   |
| _DefaultApi_ | [**g_et_meetings_get_session_recurrences_by_id**](elio_client/docs/DefaultApi.md#g_et_meetings_get_session_recurrences_by_id)                                           | **GET** /meetings.GetSessionRecurrencesById                             | GetSessionRecurrencesById returns a list of recurrences for the passed sessionID, can optionally search for specific                                                           |
| _DefaultApi_ | [**g_et_meetings_get_session_users**](elio_client/docs/DefaultApi.md#g_et_meetings_get_session_users)                                                                   | **GET** /v1.0/sessions/users/{sessionID}/{recurrenceID}                 |
| _DefaultApi_ | [**g_et_meetings_get_session_users_by_session_id**](elio_client/docs/DefaultApi.md#g_et_meetings_get_session_users_by_session_id)                                       | **GET** /meetings.GetSessionUsersBySessionID                            |
| _DefaultApi_ | [**g_et_meetings_get_update_user_payment_method_transaction**](elio_client/docs/DefaultApi.md#g_et_meetings_get_update_user_payment_method_transaction)                 | **GET** /v1.0/users/id/{userID}/update-payment-method-transaction       |
| _DefaultApi_ | [**g_et_meetings_get_user_payment_method_details**](elio_client/docs/DefaultApi.md#g_et_meetings_get_user_payment_method_details)                                       | **GET** /v1.0/users/id/{userID}/payment-method-details                  |
| _DefaultApi_ | [**g_et_meetings_get_user_plan_by_user_id**](elio_client/docs/DefaultApi.md#g_et_meetings_get_user_plan_by_user_id)                                                     | **GET** /v1.0/users/id/{userID}/plan                                    |
| _DefaultApi_ | [**g_et_meetings_list_user_transactions**](elio_client/docs/DefaultApi.md#g_et_meetings_list_user_transactions)                                                         | **GET** /v1.0/users/id/{userID}/transactions                            |
| _DefaultApi_ | [**g_et_meetings_list_users**](elio_client/docs/DefaultApi.md#g_et_meetings_list_users)                                                                                 | **GET** /users                                                          |
| _DefaultApi_ | [**g_et_meetings_post_session_summary_optimistic**](elio_client/docs/DefaultApi.md#g_et_meetings_post_session_summary_optimistic)                                       | **GET** /v1.0/post-session-summaries/by-session-ids/{sessionID}         | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes                                                               |
| _DefaultApi_ | [**g_et_meetings_presence**](elio_client/docs/DefaultApi.md#g_et_meetings_presence)                                                                                     | **GET** /v1.0/sessions/presence/{id}                                    |
| _DefaultApi_ | [**g_et_transcriptions_audio_ingress_websocket**](elio_client/docs/DefaultApi.md#g_et_transcriptions_audio_ingress_websocket)                                           | **GET** /v1.0/transcriptions/audio-ingress                              | AudioIngressWebsocket handles the websocket connection for raw pcm audio from Livekit/other sources                                                                            |
| _DefaultApi_ | [**g_et_transcriptions_cleanup_recall_users**](elio_client/docs/DefaultApi.md#g_et_transcriptions_cleanup_recall_users)                                                 | **GET** /transcriptions.CleanupRecallUsers                              | CleanupRecallUsers is a cron job that removes users from Recall that have no connections.                                                                                      |
| _DefaultApi_ | [**g_et_transcriptions_create_end_of_session_transcripts_batch**](elio_client/docs/DefaultApi.md#g_et_transcriptions_create_end_of_session_transcripts_batch)           | **GET** /transcriptions.CreateEndOfSessionTranscriptsBatch              | Private endpoint to be called from the session ended event handler in                                                                                                          |
| _DefaultApi_ | [**g_et_transcriptions_download**](elio_client/docs/DefaultApi.md#g_et_transcriptions_download)                                                                         | **GET** /v1.0/session/transcriptions                                    |
| _DefaultApi_ | [**g_et_transcriptions_fetch_heard_speakers**](elio_client/docs/DefaultApi.md#g_et_transcriptions_fetch_heard_speakers)                                                 | **GET** /v1.0/transcriptions/speakers                                   | FetchHeardSpeakers fetches array of speakers heard in a session                                                                                                                |
| _DefaultApi_ | [**g_et_transcriptions_generate_bot_google_auth_url**](elio_client/docs/DefaultApi.md#g_et_transcriptions_generate_bot_google_auth_url)                                 | **GET** /v1.0/transcriptions/bot/google-auth                            | GenerateBotAuthURL Generate a URL for the user to authenticate with Google Calendar.                                                                                           |
| _DefaultApi_ | [**g_et_transcriptions_generate_bot_microsoft_auth_url**](elio_client/docs/DefaultApi.md#g_et_transcriptions_generate_bot_microsoft_auth_url)                           | **GET** /v1.0/transcriptions/bot/microsoft-auth                         | GenerateBotAuthURL Generate a URL for the user to authenticate with Microsoft Calendar.                                                                                        |
| _DefaultApi_ | [**g_et_transcriptions_get_batch_ids**](elio_client/docs/DefaultApi.md#g_et_transcriptions_get_batch_ids)                                                               | **GET** /transcriptions.GetBatchIDs                                     |
| _DefaultApi_ | [**g_et_transcriptions_get_calendar_user**](elio_client/docs/DefaultApi.md#g_et_transcriptions_get_calendar_user)                                                       | **GET** /v1.0/transcriptions/bot/calendar-user                          |
| _DefaultApi_ | [**g_et_transcriptions_get_speakers_as_session_guests_by_session_id**](elio_client/docs/DefaultApi.md#g_et_transcriptions_get_speakers_as_session_guests_by_session_id) | **GET** /transcriptions.GetSpeakersAsSessionGuestsBySessionID           |
| _DefaultApi_ | [**g_et_transcriptions_get_transcription_batch**](elio_client/docs/DefaultApi.md#g_et_transcriptions_get_transcription_batch)                                           | **GET** /transcriptions.GetTranscriptionBatch                           |
| _DefaultApi_ | [**g_et_transcriptions_identity_associated**](elio_client/docs/DefaultApi.md#g_et_transcriptions_identity_associated)                                                   | **GET** /.well-known/microsoft-identity-association.json                | Needed to verify domains for Microsoft OAuth2 integration                                                                                                                      |
| _DefaultApi_ | [**g_et_transcriptions_microsoft_redirect_of_redirect**](elio_client/docs/DefaultApi.md#g_et_transcriptions_microsoft_redirect_of_redirect)                             | **GET** /v1.0/calendar/ms_oauth_callback                                | Needed redirect back to recall                                                                                                                                                 |
| _DefaultApi_ | [**g_et_xray_get_x_ray**](elio_client/docs/DefaultApi.md#g_et_xray_get_x_ray)                                                                                           | **GET** /v1.0/xrays/id/{xrayID}                                         |
| _DefaultApi_ | [**g_et_xray_get_x_ray_notifications**](elio_client/docs/DefaultApi.md#g_et_xray_get_x_ray_notifications)                                                               | **GET** /v1.0/xrays/id/{xrayID}/notifications                           |
| _DefaultApi_ | [**g_et_xray_get_x_ray_template**](elio_client/docs/DefaultApi.md#g_et_xray_get_x_ray_template)                                                                         | **GET** /v1.0/xray-templates/{templateID}                               |
| _DefaultApi_ | [**g_et_xray_list_x_ray_templates**](elio_client/docs/DefaultApi.md#g_et_xray_list_x_ray_templates)                                                                     | **GET** /v1.0/xray-templates                                            |
| _DefaultApi_ | [**g_et_xray_list_x_rays**](elio_client/docs/DefaultApi.md#g_et_xray_list_x_rays)                                                                                       | **GET** /v1.0/xrays                                                     |
| _DefaultApi_ | [**h_ead_meetings_post_session_summary_optimistic**](elio_client/docs/DefaultApi.md#h_ead_meetings_post_session_summary_optimistic)                                     | **HEAD** /v1.0/post-session-summaries/by-session-ids/{sessionID}        | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes                                                               |
| _DefaultApi_ | [**h_ead_transcriptions_identity_associated**](elio_client/docs/DefaultApi.md#h_ead_transcriptions_identity_associated)                                                 | **HEAD** /.well-known/microsoft-identity-association.json               | Needed to verify domains for Microsoft OAuth2 integration                                                                                                                      |
| _DefaultApi_ | [**h_ead_transcriptions_microsoft_redirect_of_redirect**](elio_client/docs/DefaultApi.md#h_ead_transcriptions_microsoft_redirect_of_redirect)                           | **HEAD** /v1.0/calendar/ms_oauth_callback                               | Needed redirect back to recall                                                                                                                                                 |
| _DefaultApi_ | [**p_atch_meetings_post_session_summary_optimistic**](elio_client/docs/DefaultApi.md#p_atch_meetings_post_session_summary_optimistic)                                   | **PATCH** /v1.0/post-session-summaries/by-session-ids/{sessionID}       | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes                                                               |
| _DefaultApi_ | [**p_atch_meetings_recur_session**](elio_client/docs/DefaultApi.md#p_atch_meetings_recur_session)                                                                       | **PATCH** /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur-at |
| _DefaultApi_ | [**p_atch_meetings_recur_session_no_request**](elio_client/docs/DefaultApi.md#p_atch_meetings_recur_session_no_request)                                                 | **PATCH** /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}/recur    |
| _DefaultApi_ | [**p_atch_meetings_update_session**](elio_client/docs/DefaultApi.md#p_atch_meetings_update_session)                                                                     | **PATCH** /v1.0/sessions/{sessionID}/recurrence/{recurrenceID}          |
| _DefaultApi_ | [**p_atch_transcriptions_identity_associated**](elio_client/docs/DefaultApi.md#p_atch_transcriptions_identity_associated)                                               | **PATCH** /.well-known/microsoft-identity-association.json              | Needed to verify domains for Microsoft OAuth2 integration                                                                                                                      |
| _DefaultApi_ | [**p_atch_transcriptions_microsoft_redirect_of_redirect**](elio_client/docs/DefaultApi.md#p_atch_transcriptions_microsoft_redirect_of_redirect)                         | **PATCH** /v1.0/calendar/ms_oauth_callback                              | Needed redirect back to recall                                                                                                                                                 |
| _DefaultApi_ | [**p_atch_xray_mark_x_ray_notifications_seen**](elio_client/docs/DefaultApi.md#p_atch_xray_mark_x_ray_notifications_seen)                                               | **PATCH** /v1.0/xrays/id/{xrayID}/notifications/mark-seen               |
| _DefaultApi_ | [**p_atch_xray_update_x_ray**](elio_client/docs/DefaultApi.md#p_atch_xray_update_x_ray)                                                                                 | **PATCH** /v1.0/xrays/id/{xrayID}                                       |
| _DefaultApi_ | [**p_ost_comms_meeting_metadata_ready**](elio_client/docs/DefaultApi.md#p_ost_comms_meeting_metadata_ready)                                                             | **POST** /comms.MeetingMetadataReady                                    | MeetingMetadataReady is a endpoint to notify Elio that a specific session&#39;s metadata has been generated.                                                                   |
| _DefaultApi_ | [**p_ost_comms_trigger_guest_waiting_in_lobby_notification**](elio_client/docs/DefaultApi.md#p_ost_comms_trigger_guest_waiting_in_lobby_notification)                   | **POST** /comms.TriggerGuestWaitingInLobbyNotification                  |
| _DefaultApi_ | [**p_ost_meetings_ai_feed_event**](elio_client/docs/DefaultApi.md#p_ost_meetings_ai_feed_event)                                                                         | **POST** /v1.0/ai-feed/event                                            |
| _DefaultApi_ | [**p_ost_meetings_count_in_review_requests**](elio_client/docs/DefaultApi.md#p_ost_meetings_count_in_review_requests)                                                   | **POST** /v1.0/sessions/access/get-in-review-access-requests-count      |
| _DefaultApi_ | [**p_ost_meetings_create_access_request**](elio_client/docs/DefaultApi.md#p_ost_meetings_create_access_request)                                                         | **POST** /v1.0/sessions/access/request-access                           |
| _DefaultApi_ | [**p_ost_meetings_create_session**](elio_client/docs/DefaultApi.md#p_ost_meetings_create_session)                                                                       | **POST** /v1.0/sessions                                                 |
| _DefaultApi_ | [**p_ost_meetings_create_session_with_user**](elio_client/docs/DefaultApi.md#p_ost_meetings_create_session_with_user)                                                   | **POST** /meetings.CreateSessionWithUser                                |
| _DefaultApi_ | [**p_ost_meetings_create_user**](elio_client/docs/DefaultApi.md#p_ost_meetings_create_user)                                                                             | **POST** /users                                                         |
| _DefaultApi_ | [**p_ost_meetings_get_lobby_guests**](elio_client/docs/DefaultApi.md#p_ost_meetings_get_lobby_guests)                                                                   | **POST** /meetings.GetLobbyGuests                                       | GetLobbyGuests returns the list of guests in the lobby                                                                                                                         |
| _DefaultApi_ | [**p_ost_meetings_get_session_access_rules**](elio_client/docs/DefaultApi.md#p_ost_meetings_get_session_access_rules)                                                   | **POST** /meetings.GetSessionAccessRules                                | GetSessionAccessRules returns all access rules for a session                                                                                                                   |
| _DefaultApi_ | [**p_ost_meetings_get_user_by_id**](elio_client/docs/DefaultApi.md#p_ost_meetings_get_user_by_id)                                                                       | **POST** /meetings.GetUserByID                                          | GetUserByID gets user details by ID                                                                                                                                            |
| _DefaultApi_ | [**p_ost_meetings_get_user_by_id_with_relations**](elio_client/docs/DefaultApi.md#p_ost_meetings_get_user_by_id_with_relations)                                         | **POST** /meetings.GetUserByIDWithRelations                             |
| _DefaultApi_ | [**p_ost_meetings_join_lobby_as_guest**](elio_client/docs/DefaultApi.md#p_ost_meetings_join_lobby_as_guest)                                                             | **POST** /meetings.JoinLobbyAsGuest                                     | JoinLobbyAsGuest adds a guest to the lobby and publishes the event to the lobby listeners                                                                                      |
| _DefaultApi_ | [**p_ost_meetings_list_sessions_by_lobby_id**](elio_client/docs/DefaultApi.md#p_ost_meetings_list_sessions_by_lobby_id)                                                 | **POST** /meetings.ListSessionsByLobbyID                                |
| _DefaultApi_ | [**p_ost_meetings_login_guest_user_with_session**](elio_client/docs/DefaultApi.md#p_ost_meetings_login_guest_user_with_session)                                         | **POST** /v1.0/sessions/guest                                           |
| _DefaultApi_ | [**p_ost_meetings_post_session_summary_optimistic**](elio_client/docs/DefaultApi.md#p_ost_meetings_post_session_summary_optimistic)                                     | **POST** /v1.0/post-session-summaries/by-session-ids/{sessionID}        | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes                                                               |
| _DefaultApi_ | [**p_ost_meetings_remove_guest_from_lobby**](elio_client/docs/DefaultApi.md#p_ost_meetings_remove_guest_from_lobby)                                                     | **POST** /meetings.RemoveGuestFromLobby                                 | RemoveGuestFromLobby removes a guest from the lobby and publishes the event to the lobby listeners                                                                             |
| _DefaultApi_ | [**p_ost_meetings_revoke_access_session_user**](elio_client/docs/DefaultApi.md#p_ost_meetings_revoke_access_session_user)                                               | **POST** /meetings.RevokeAccessSessionUser                              |
| _DefaultApi_ | [**p_ost_meetings_update_session_access_control_rules**](elio_client/docs/DefaultApi.md#p_ost_meetings_update_session_access_control_rules)                             | **POST** /v1.0/sessions/access                                          |
| _DefaultApi_ | [**p_ost_meetings_upsert_meeting_suggestions**](elio_client/docs/DefaultApi.md#p_ost_meetings_upsert_meeting_suggestions)                                               | **POST** /meetings.UpsertMeetingSuggestions                             | UpsertMeetingSuggestions stores or updates meeting suggestions for a session (called by Nebula)                                                                                |
| _DefaultApi_ | [**p_ost_meetings_upsert_session_user**](elio_client/docs/DefaultApi.md#p_ost_meetings_upsert_session_user)                                                             | **POST** /meetings.UpsertSessionUser                                    |
| _DefaultApi_ | [**p_ost_transcriptions_cleanup_recall_users**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_cleanup_recall_users)                                               | **POST** /transcriptions.CleanupRecallUsers                             | CleanupRecallUsers is a cron job that removes users from Recall that have no connections.                                                                                      |
| _DefaultApi_ | [**p_ost_transcriptions_create_bot_for_meeting**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_create_bot_for_meeting)                                           | **POST** /v1.0/transcriptions/bot/create-for-meeting                    |
| _DefaultApi_ | [**p_ost_transcriptions_disconnect_calendar**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_disconnect_calendar)                                                 | **POST** /v1.0/transcriptions/bot/disconnect                            | Disconnect a user&#39;s calendar platform from our Rumi bots recall.ai account                                                                                                 |
| _DefaultApi_ | [**p_ost_transcriptions_generate_audio_ingress_key**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_generate_audio_ingress_key)                                   | **POST** /transcriptions.GenerateAudioIngressKey                        | GenerateAudioIngressKey represents a key used to authenticate audio ingress                                                                                                    |
| _DefaultApi_ | [**p_ost_transcriptions_identify_heard_speakers**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_identify_heard_speakers)                                         | **POST** /v1.0/transcriptions/speakers                                  | IdentifyHeardSpeakers identifies an array of speakers heard in a session                                                                                                       |
| _DefaultApi_ | [**p_ost_transcriptions_identity_associated**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_identity_associated)                                                 | **POST** /.well-known/microsoft-identity-association.json               | Needed to verify domains for Microsoft OAuth2 integration                                                                                                                      |
| _DefaultApi_ | [**p_ost_transcriptions_microsoft_redirect_of_redirect**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_microsoft_redirect_of_redirect)                           | **POST** /v1.0/calendar/ms_oauth_callback                               | Needed redirect back to recall                                                                                                                                                 |
| _DefaultApi_ | [**p_ost_transcriptions_process_bot_recording**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_process_bot_recording)                                             | **POST** /transcriptions.ProcessBotRecording                            |
| _DefaultApi_ | [**p_ost_transcriptions_recall_transcriptions_webhook**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_recall_transcriptions_webhook)                             | **POST** /v1.0/transcriptions/bot/webhook                               |
| _DefaultApi_ | [**p_ost_transcriptions_remove_users_from_recall_account**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_remove_users_from_recall_account)                       | **POST** /transcriptions.RemoveUsersFromRecallAccount                   | RemoveUsersFromRecallAccount Remove all users from the token passed in the params.                                                                                             |
| _DefaultApi_ | [**p_ost_transcriptions_update**](elio_client/docs/DefaultApi.md#p_ost_transcriptions_update)                                                                           | **POST** /transcriptions.Update                                         |
| _DefaultApi_ | [**p_ost_xray_create**](elio_client/docs/DefaultApi.md#p_ost_xray_create)                                                                                               | **POST** /v1.0/xrays                                                    |
| _DefaultApi_ | [**p_ost_xray_generate_x_ray_info**](elio_client/docs/DefaultApi.md#p_ost_xray_generate_x_ray_info)                                                                     | **POST** /v1.0/xrays/generate/info                                      |
| _DefaultApi_ | [**p_ost_xray_generate_x_ray_prompt**](elio_client/docs/DefaultApi.md#p_ost_xray_generate_x_ray_prompt)                                                                 | **POST** /v1.0/xrays/generate/prompt                                    |
| _DefaultApi_ | [**p_ost_xray_share_x_ray_as_template**](elio_client/docs/DefaultApi.md#p_ost_xray_share_x_ray_as_template)                                                             | **POST** /v1.0/xrays/id/{xrayID}/share                                  |
| _DefaultApi_ | [**p_ut_meetings_add_session_access_control_rules**](elio_client/docs/DefaultApi.md#p_ut_meetings_add_session_access_control_rules)                                     | **PUT** /v1.0/sessions/access/add                                       | AddSessionAccessControlRules is now deprecated, please use UpdateSessionAccessControlRules (later down in this file)                                                           |
| _DefaultApi_ | [**p_ut_meetings_approve_or_denyed_session_access_request**](elio_client/docs/DefaultApi.md#p_ut_meetings_approve_or_denyed_session_access_request)                     | **PUT** /v1.0/sessions/access/request                                   | ApproveOrDenyedSessionAccessRequest is deprecated and should not be used.                                                                                                      |
| _DefaultApi_ | [**p_ut_meetings_post_session_summary_optimistic**](elio_client/docs/DefaultApi.md#p_ut_meetings_post_session_summary_optimistic)                                       | **PUT** /v1.0/post-session-summaries/by-session-ids/{sessionID}         | PostSessionSummaryOptimistic checks if the AIFeed was enabled for the session returns the following status codes                                                               |
| _DefaultApi_ | [**p_ut_meetings_update_user_by_id**](elio_client/docs/DefaultApi.md#p_ut_meetings_update_user_by_id)                                                                   | **PUT** /v1.0/users/id/{userID}                                         | UpdateUserByID updates user details by their ID                                                                                                                                |
| _DefaultApi_ | [**p_ut_transcriptions_identity_associated**](elio_client/docs/DefaultApi.md#p_ut_transcriptions_identity_associated)                                                   | **PUT** /.well-known/microsoft-identity-association.json                | Needed to verify domains for Microsoft OAuth2 integration                                                                                                                      |
| _DefaultApi_ | [**p_ut_transcriptions_microsoft_redirect_of_redirect**](elio_client/docs/DefaultApi.md#p_ut_transcriptions_microsoft_redirect_of_redirect)                             | **PUT** /v1.0/calendar/ms_oauth_callback                                | Needed redirect back to recall                                                                                                                                                 |

## Documentation For Models

-   [APIError](elio_client/docs/APIError.md)
-   [ApiAccessControlItem](elio_client/docs/ApiAccessControlItem.md)
-   [ApiGenerateXRayInfoResponseData](elio_client/docs/ApiGenerateXRayInfoResponseData.md)
-   [ApiGenerateXRayPromptResponseData](elio_client/docs/ApiGenerateXRayPromptResponseData.md)
-   [ApiGetUserPaymentMethodDetailsResponseData](elio_client/docs/ApiGetUserPaymentMethodDetailsResponseData.md)
-   [ApiGetXRayNotificationsResponseData](elio_client/docs/ApiGetXRayNotificationsResponseData.md)
-   [ApiHeardSpeaker](elio_client/docs/ApiHeardSpeaker.md)
-   [ApiIdentifiedSpeaker](elio_client/docs/ApiIdentifiedSpeaker.md)
-   [ApiInReviewAccessRequestDTO](elio_client/docs/ApiInReviewAccessRequestDTO.md)
-   [ApiInReviewAccessRequestDTOUser](elio_client/docs/ApiInReviewAccessRequestDTOUser.md)
-   [ApiListSessionsResponseData](elio_client/docs/ApiListSessionsResponseData.md)
-   [ApiListUserTransactionsResponseData](elio_client/docs/ApiListUserTransactionsResponseData.md)
-   [ApiListXRayTemplatesResponseData](elio_client/docs/ApiListXRayTemplatesResponseData.md)
-   [ApiListXRaysResponseData](elio_client/docs/ApiListXRaysResponseData.md)
-   [ApiMarkXRayNotificationsSeenResponseData](elio_client/docs/ApiMarkXRayNotificationsSeenResponseData.md)
-   [ApiMeetingMetadata](elio_client/docs/ApiMeetingMetadata.md)
-   [ApiMeetingSuggestionDTO](elio_client/docs/ApiMeetingSuggestionDTO.md)
-   [ApiMeetingSuggestionInput](elio_client/docs/ApiMeetingSuggestionInput.md)
-   [ApiRecallSearchHit](elio_client/docs/ApiRecallSearchHit.md)
-   [ApiRecallTranscriptionsWebhookData](elio_client/docs/ApiRecallTranscriptionsWebhookData.md)
-   [ApiRecallTranscriptionsWebhookLog](elio_client/docs/ApiRecallTranscriptionsWebhookLog.md)
-   [ApiRecallTranscriptionsWebhookSearch](elio_client/docs/ApiRecallTranscriptionsWebhookSearch.md)
-   [ApiRecallTranscriptionsWebhookStatus](elio_client/docs/ApiRecallTranscriptionsWebhookStatus.md)
-   [ApiRecallTranscriptionsWebhookTranscript](elio_client/docs/ApiRecallTranscriptionsWebhookTranscript.md)
-   [ApiRecallWord](elio_client/docs/ApiRecallWord.md)
-   [ApiSessionAccessDTO](elio_client/docs/ApiSessionAccessDTO.md)
-   [ApiSessionAccessRulesDTO](elio_client/docs/ApiSessionAccessRulesDTO.md)
-   [ApiSessionDefaultGuest](elio_client/docs/ApiSessionDefaultGuest.md)
-   [ApiSessionDefaultUser](elio_client/docs/ApiSessionDefaultUser.md)
-   [ApiSessionDefaultUsersDTO](elio_client/docs/ApiSessionDefaultUsersDTO.md)
-   [ApiSessionGuest](elio_client/docs/ApiSessionGuest.md)
-   [ApiSessionRequestCount](elio_client/docs/ApiSessionRequestCount.md)
-   [ApiSessionUser](elio_client/docs/ApiSessionUser.md)
-   [ApiSessionUserDTO](elio_client/docs/ApiSessionUserDTO.md)
-   [ApiSpeakerSnippet](elio_client/docs/ApiSpeakerSnippet.md)
-   [ApiSuggestionResponseDTO](elio_client/docs/ApiSuggestionResponseDTO.md)
-   [ApiUpdateAccessRule](elio_client/docs/ApiUpdateAccessRule.md)
-   [ApiUpdateUserRequestData](elio_client/docs/ApiUpdateUserRequestData.md)
-   [ApiUserMeetingTypeRequestDTO](elio_client/docs/ApiUserMeetingTypeRequestDTO.md)
-   [ApiUserSummaryDTO](elio_client/docs/ApiUserSummaryDTO.md)
-   [ApiXRayDTO](elio_client/docs/ApiXRayDTO.md)
-   [ApiXRayDocCommit](elio_client/docs/ApiXRayDocCommit.md)
-   [ApiXRayNotificationDTO](elio_client/docs/ApiXRayNotificationDTO.md)
-   [ApiXRayTemplateDTO](elio_client/docs/ApiXRayTemplateDTO.md)
-   [DELETEXrayDeleteXRay200Response](elio_client/docs/DELETEXrayDeleteXRay200Response.md)
-   [DataIsTheResponseData](elio_client/docs/DataIsTheResponseData.md)
-   [GETCommsPostSessionSummaryReady200Response](elio_client/docs/GETCommsPostSessionSummaryReady200Response.md)
-   [GETMeetingsGetAllAccessRequests200Response](elio_client/docs/GETMeetingsGetAllAccessRequests200Response.md)
-   [GETMeetingsGetAllAccessRequests200ResponseData](elio_client/docs/GETMeetingsGetAllAccessRequests200ResponseData.md)
-   [GETMeetingsGetChatToken200Response](elio_client/docs/GETMeetingsGetChatToken200Response.md)
-   [GETMeetingsGetLatestRecurrenceCompact200Response](elio_client/docs/GETMeetingsGetLatestRecurrenceCompact200Response.md)
-   [GETMeetingsGetMeetingSuggestionsBySession200Response](elio_client/docs/GETMeetingsGetMeetingSuggestionsBySession200Response.md)
-   [GETMeetingsGetMeetingSuggestionsByUserInternal200Response](elio_client/docs/GETMeetingsGetMeetingSuggestionsByUserInternal200Response.md)
-   [GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData](elio_client/docs/GETMeetingsGetMeetingSuggestionsByUserInternal200ResponseData.md)
-   [GETMeetingsGetPastSessionRecurrences200Response](elio_client/docs/GETMeetingsGetPastSessionRecurrences200Response.md)
-   [GETMeetingsGetSessionRecurrencesById200Response](elio_client/docs/GETMeetingsGetSessionRecurrencesById200Response.md)
-   [GETMeetingsGetSessionUsers200Response](elio_client/docs/GETMeetingsGetSessionUsers200Response.md)
-   [GETMeetingsGetSessionUsersBySessionID200Response](elio_client/docs/GETMeetingsGetSessionUsersBySessionID200Response.md)
-   [GETMeetingsGetUpdateUserPaymentMethodTransaction200Response](elio_client/docs/GETMeetingsGetUpdateUserPaymentMethodTransaction200Response.md)
-   [GETMeetingsGetUserPaymentMethodDetails200Response](elio_client/docs/GETMeetingsGetUserPaymentMethodDetails200Response.md)
-   [GETMeetingsGetUserPlanByUserID200Response](elio_client/docs/GETMeetingsGetUserPlanByUserID200Response.md)
-   [GETMeetingsListUserTransactions200Response](elio_client/docs/GETMeetingsListUserTransactions200Response.md)
-   [GETMeetingsListUsers200Response](elio_client/docs/GETMeetingsListUsers200Response.md)
-   [GETTranscriptionsFetchHeardSpeakers200Response](elio_client/docs/GETTranscriptionsFetchHeardSpeakers200Response.md)
-   [GETTranscriptionsGenerateBotGoogleAuthURL200Response](elio_client/docs/GETTranscriptionsGenerateBotGoogleAuthURL200Response.md)
-   [GETTranscriptionsGetBatchIDs200Response](elio_client/docs/GETTranscriptionsGetBatchIDs200Response.md)
-   [GETTranscriptionsGetBatchIDs200ResponseData](elio_client/docs/GETTranscriptionsGetBatchIDs200ResponseData.md)
-   [GETTranscriptionsGetCalendarUser200Response](elio_client/docs/GETTranscriptionsGetCalendarUser200Response.md)
-   [GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response](elio_client/docs/GETTranscriptionsGetSpeakersAsSessionGuestsBySessionID200Response.md)
-   [GETTranscriptionsGetTranscriptionBatch200Response](elio_client/docs/GETTranscriptionsGetTranscriptionBatch200Response.md)
-   [GETTranscriptionsGetTranscriptionBatch200ResponseData](elio_client/docs/GETTranscriptionsGetTranscriptionBatch200ResponseData.md)
-   [GETXrayGetXRayNotifications200Response](elio_client/docs/GETXrayGetXRayNotifications200Response.md)
-   [GETXrayGetXRayTemplate200Response](elio_client/docs/GETXrayGetXRayTemplate200Response.md)
-   [GETXrayListXRayTemplates200Response](elio_client/docs/GETXrayListXRayTemplates200Response.md)
-   [GETXrayListXRays200Response](elio_client/docs/GETXrayListXRays200Response.md)
-   [MarsApiUserSocial](elio_client/docs/MarsApiUserSocial.md)
-   [PATCHMeetingsRecurSessionNoRequest200Response](elio_client/docs/PATCHMeetingsRecurSessionNoRequest200Response.md)
-   [PATCHMeetingsRecurSessionRequest](elio_client/docs/PATCHMeetingsRecurSessionRequest.md)
-   [PATCHMeetingsUpdateSession200Response](elio_client/docs/PATCHMeetingsUpdateSession200Response.md)
-   [PATCHMeetingsUpdateSessionRequest](elio_client/docs/PATCHMeetingsUpdateSessionRequest.md)
-   [PATCHMeetingsUpdateSessionRequestUserMeetingType](elio_client/docs/PATCHMeetingsUpdateSessionRequestUserMeetingType.md)
-   [PATCHXrayMarkXRayNotificationsSeen200Response](elio_client/docs/PATCHXrayMarkXRayNotificationsSeen200Response.md)
-   [PATCHXrayUpdateXRayRequest](elio_client/docs/PATCHXrayUpdateXRayRequest.md)
-   [POSTCommsMeetingMetadataReadyRequest](elio_client/docs/POSTCommsMeetingMetadataReadyRequest.md)
-   [POSTCommsTriggerGuestWaitingInLobbyNotificationRequest](elio_client/docs/POSTCommsTriggerGuestWaitingInLobbyNotificationRequest.md)
-   [POSTMeetingsAIFeedEventRequest](elio_client/docs/POSTMeetingsAIFeedEventRequest.md)
-   [POSTMeetingsCountInReviewRequests200Response](elio_client/docs/POSTMeetingsCountInReviewRequests200Response.md)
-   [POSTMeetingsCountInReviewRequestsRequest](elio_client/docs/POSTMeetingsCountInReviewRequestsRequest.md)
-   [POSTMeetingsCreateAccessRequestRequest](elio_client/docs/POSTMeetingsCreateAccessRequestRequest.md)
-   [POSTMeetingsCreateSessionWithUser200Response](elio_client/docs/POSTMeetingsCreateSessionWithUser200Response.md)
-   [POSTMeetingsCreateSessionWithUser200ResponseData](elio_client/docs/POSTMeetingsCreateSessionWithUser200ResponseData.md)
-   [POSTMeetingsCreateSessionWithUserRequest](elio_client/docs/POSTMeetingsCreateSessionWithUserRequest.md)
-   [POSTMeetingsCreateUserRequest](elio_client/docs/POSTMeetingsCreateUserRequest.md)
-   [POSTMeetingsGetLobbyGuests200Response](elio_client/docs/POSTMeetingsGetLobbyGuests200Response.md)
-   [POSTMeetingsGetSessionAccessRules200Response](elio_client/docs/POSTMeetingsGetSessionAccessRules200Response.md)
-   [POSTMeetingsGetUserByID200Response](elio_client/docs/POSTMeetingsGetUserByID200Response.md)
-   [POSTMeetingsGetUserByIDRequest](elio_client/docs/POSTMeetingsGetUserByIDRequest.md)
-   [POSTMeetingsGetUserByIDWithRelations200Response](elio_client/docs/POSTMeetingsGetUserByIDWithRelations200Response.md)
-   [POSTMeetingsGetUserByIDWithRelations200ResponseData](elio_client/docs/POSTMeetingsGetUserByIDWithRelations200ResponseData.md)
-   [POSTMeetingsJoinLobbyAsGuestRequest](elio_client/docs/POSTMeetingsJoinLobbyAsGuestRequest.md)
-   [POSTMeetingsListSessionsByLobbyIDRequest](elio_client/docs/POSTMeetingsListSessionsByLobbyIDRequest.md)
-   [POSTMeetingsLoginGuestUserWithSession200Response](elio_client/docs/POSTMeetingsLoginGuestUserWithSession200Response.md)
-   [POSTMeetingsLoginGuestUserWithSessionRequest](elio_client/docs/POSTMeetingsLoginGuestUserWithSessionRequest.md)
-   [POSTMeetingsRemoveGuestFromLobbyRequest](elio_client/docs/POSTMeetingsRemoveGuestFromLobbyRequest.md)
-   [POSTMeetingsRevokeAccessSessionUserRequest](elio_client/docs/POSTMeetingsRevokeAccessSessionUserRequest.md)
-   [POSTMeetingsUpdateSessionAccessControlRulesRequest](elio_client/docs/POSTMeetingsUpdateSessionAccessControlRulesRequest.md)
-   [POSTMeetingsUpsertMeetingSuggestions200Response](elio_client/docs/POSTMeetingsUpsertMeetingSuggestions200Response.md)
-   [POSTMeetingsUpsertMeetingSuggestionsRequest](elio_client/docs/POSTMeetingsUpsertMeetingSuggestionsRequest.md)
-   [POSTMeetingsUpsertSessionUserRequest](elio_client/docs/POSTMeetingsUpsertSessionUserRequest.md)
-   [POSTTranscriptionsCreateBotForMeeting200Response](elio_client/docs/POSTTranscriptionsCreateBotForMeeting200Response.md)
-   [POSTTranscriptionsCreateBotForMeetingRequest](elio_client/docs/POSTTranscriptionsCreateBotForMeetingRequest.md)
-   [POSTTranscriptionsDisconnectCalendarRequest](elio_client/docs/POSTTranscriptionsDisconnectCalendarRequest.md)
-   [POSTTranscriptionsGenerateAudioIngressKey200Response](elio_client/docs/POSTTranscriptionsGenerateAudioIngressKey200Response.md)
-   [POSTTranscriptionsGenerateAudioIngressKeyRequest](elio_client/docs/POSTTranscriptionsGenerateAudioIngressKeyRequest.md)
-   [POSTTranscriptionsIdentifyHeardSpeakersRequest](elio_client/docs/POSTTranscriptionsIdentifyHeardSpeakersRequest.md)
-   [POSTTranscriptionsProcessBotRecordingRequest](elio_client/docs/POSTTranscriptionsProcessBotRecordingRequest.md)
-   [POSTTranscriptionsRecallTranscriptionsWebhookRequest](elio_client/docs/POSTTranscriptionsRecallTranscriptionsWebhookRequest.md)
-   [POSTTranscriptionsRemoveUsersFromRecallAccountRequest](elio_client/docs/POSTTranscriptionsRemoveUsersFromRecallAccountRequest.md)
-   [POSTTranscriptionsUpdateRequest](elio_client/docs/POSTTranscriptionsUpdateRequest.md)
-   [POSTXrayCreate200Response](elio_client/docs/POSTXrayCreate200Response.md)
-   [POSTXrayCreateRequest](elio_client/docs/POSTXrayCreateRequest.md)
-   [POSTXrayGenerateXRayInfo200Response](elio_client/docs/POSTXrayGenerateXRayInfo200Response.md)
-   [POSTXrayGenerateXRayInfoRequest](elio_client/docs/POSTXrayGenerateXRayInfoRequest.md)
-   [POSTXrayGenerateXRayPrompt200Response](elio_client/docs/POSTXrayGenerateXRayPrompt200Response.md)
-   [POSTXrayGenerateXRayPromptRequest](elio_client/docs/POSTXrayGenerateXRayPromptRequest.md)
-   [PUTMeetingsAddSessionAccessControlRules200Response](elio_client/docs/PUTMeetingsAddSessionAccessControlRules200Response.md)
-   [PUTMeetingsAddSessionAccessControlRules200ResponseData](elio_client/docs/PUTMeetingsAddSessionAccessControlRules200ResponseData.md)
-   [PUTMeetingsAddSessionAccessControlRulesRequest](elio_client/docs/PUTMeetingsAddSessionAccessControlRulesRequest.md)
-   [PUTMeetingsApproveOrDenyedSessionAccessRequest200Response](elio_client/docs/PUTMeetingsApproveOrDenyedSessionAccessRequest200Response.md)
-   [PUTMeetingsApproveOrDenyedSessionAccessRequestRequest](elio_client/docs/PUTMeetingsApproveOrDenyedSessionAccessRequestRequest.md)
-   [PUTMeetingsUpdateUserByIDRequest](elio_client/docs/PUTMeetingsUpdateUserByIDRequest.md)
-   [RecallaiBotMeetingLink](elio_client/docs/RecallaiBotMeetingLink.md)
-   [RecallaiCalendarMeeting](elio_client/docs/RecallaiCalendarMeeting.md)
-   [RecallaiCalendarUser](elio_client/docs/RecallaiCalendarUser.md)
-   [RecallaiMeetingMetadata](elio_client/docs/RecallaiMeetingMetadata.md)
-   [RecallaiMeetingParticipant](elio_client/docs/RecallaiMeetingParticipant.md)
-   [RecallaiRecallConnection](elio_client/docs/RecallaiRecallConnection.md)
-   [RecallaiRecordingPreferences](elio_client/docs/RecallaiRecordingPreferences.md)
-   [SharedBillingCycleDTO](elio_client/docs/SharedBillingCycleDTO.md)
-   [SharedBillingPaymentMethodDetailsDTO](elio_client/docs/SharedBillingPaymentMethodDetailsDTO.md)
-   [SharedBillingPaymentResultDTO](elio_client/docs/SharedBillingPaymentResultDTO.md)
-   [SharedBillingTransactionDTO](elio_client/docs/SharedBillingTransactionDTO.md)
-   [SharedBillingTransactionItemDTO](elio_client/docs/SharedBillingTransactionItemDTO.md)
-   [SharedBillingTransactionPaymentAttemptDTO](elio_client/docs/SharedBillingTransactionPaymentAttemptDTO.md)
-   [SharedGuestDTO](elio_client/docs/SharedGuestDTO.md)
-   [SharedLobbyDTO](elio_client/docs/SharedLobbyDTO.md)
-   [SharedLobbyParticipantDTO](elio_client/docs/SharedLobbyParticipantDTO.md)
-   [SharedNotificationSettings](elio_client/docs/SharedNotificationSettings.md)
-   [SharedOffsetPaginationResponse](elio_client/docs/SharedOffsetPaginationResponse.md)
-   [SharedOnboardingFlags](elio_client/docs/SharedOnboardingFlags.md)
-   [SharedParticipantMetadata](elio_client/docs/SharedParticipantMetadata.md)
-   [SharedPriceDTO](elio_client/docs/SharedPriceDTO.md)
-   [SharedPriceDTOTrialPeriod](elio_client/docs/SharedPriceDTOTrialPeriod.md)
-   [SharedPriceQuantityDTO](elio_client/docs/SharedPriceQuantityDTO.md)
-   [SharedSessionAccessRuleDTO](elio_client/docs/SharedSessionAccessRuleDTO.md)
-   [SharedSessionDTO](elio_client/docs/SharedSessionDTO.md)
-   [SharedSessionIntegrationsDTO](elio_client/docs/SharedSessionIntegrationsDTO.md)
-   [SharedSessionSubscriptionPlanDTO](elio_client/docs/SharedSessionSubscriptionPlanDTO.md)
-   [SharedStateUpdatedAt](elio_client/docs/SharedStateUpdatedAt.md)
-   [SharedSubscriptionItemDTO](elio_client/docs/SharedSubscriptionItemDTO.md)
-   [SharedSubscriptionItemDTOTrialDates](elio_client/docs/SharedSubscriptionItemDTOTrialDates.md)
-   [SharedSubscriptionPlanConfigDTO](elio_client/docs/SharedSubscriptionPlanConfigDTO.md)
-   [SharedSubscriptionPlanConfigDTOAiFeed](elio_client/docs/SharedSubscriptionPlanConfigDTOAiFeed.md)
-   [SharedSubscriptionPlanConfigDTOIntegrations](elio_client/docs/SharedSubscriptionPlanConfigDTOIntegrations.md)
-   [SharedSubscriptionPlanConfigDTOMeetings](elio_client/docs/SharedSubscriptionPlanConfigDTOMeetings.md)
-   [SharedSubscriptionPlanConfigDTORecording](elio_client/docs/SharedSubscriptionPlanConfigDTORecording.md)
-   [SharedSubscriptionPlanConfigDTOStream](elio_client/docs/SharedSubscriptionPlanConfigDTOStream.md)
-   [SharedSubscriptionPlanConfigDTOSupport](elio_client/docs/SharedSubscriptionPlanConfigDTOSupport.md)
-   [SharedSubscriptionPlanConfigDTOTimeLimit](elio_client/docs/SharedSubscriptionPlanConfigDTOTimeLimit.md)
-   [SharedTeamWithInlineRelationsDTO](elio_client/docs/SharedTeamWithInlineRelationsDTO.md)
-   [SharedTranscriptionDTO](elio_client/docs/SharedTranscriptionDTO.md)
-   [SharedUnitPriceDTO](elio_client/docs/SharedUnitPriceDTO.md)
-   [SharedUnitPriceOverrideDTO](elio_client/docs/SharedUnitPriceOverrideDTO.md)
-   [SharedUserCryptoToken](elio_client/docs/SharedUserCryptoToken.md)
-   [SharedUserDTO](elio_client/docs/SharedUserDTO.md)
-   [SharedUserMeetingTypeDTO](elio_client/docs/SharedUserMeetingTypeDTO.md)
-   [SharedUserSocialDTO](elio_client/docs/SharedUserSocialDTO.md)
-   [SharedUserSubscriptionPlanDTO](elio_client/docs/SharedUserSubscriptionPlanDTO.md)
-   [SharedUserSubscriptionPlanDTOBillingDetails](elio_client/docs/SharedUserSubscriptionPlanDTOBillingDetails.md)
-   [SharedUserSubscriptionPlanDTOManagementUrls](elio_client/docs/SharedUserSubscriptionPlanDTOManagementUrls.md)
-   [SharedUserSubscriptionPlanDTOScheduledChange](elio_client/docs/SharedUserSubscriptionPlanDTOScheduledChange.md)
-   [TheDataReturnedByTheAPI](elio_client/docs/TheDataReturnedByTheAPI.md)
-   [TheResponseData](elio_client/docs/TheResponseData.md)

<a id="documentation-for-authorization"></a>

## Documentation For Authorization

Endpoints do not require authorization.

## Author
