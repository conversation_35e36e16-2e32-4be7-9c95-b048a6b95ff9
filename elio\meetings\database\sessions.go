package database

import (
	"context"
	"encore.app/meetings/api"
	"encore.app/pkg/database"
	"encore.app/shared"
	"github.com/jmoiron/sqlx"
)

var sessionColumns = `
		session_cols.id,
		"recurrenceID",
		session_cols.title,
		"startTimestamp",
		"endTimestamp",
		"startedAt",
		"endedAt",
		url,
		"branchURL",
		state,
		"hostUserIDs",
		"hostSessionUserIDs",
		"primaryHostUserID",
		"creatorUserID",
		settings,
		avatar,
		cover,
		about,
		"ogMetadata",
		category,
		tags,
		"isFeatured",
		"createdAt",
		"updatedAt",
		"isLiveStreamEnabled",
		"stateUpdatedAt",
		"producerUserIDs",
		"producerSessionUserIDs",
		"isRecordingProcessed",
		"isPubliclyVisible",
		"isViewerAccessRestricted",
		"calendarType",
		"calendarID",
		"calendarEventEditURL",
		"hasPastRecordings",
		"enableBots",
		"meetingType",
		"user_meeting_type",
		"rowversion",
		"accessStatus",
		"dataVisibility",
		"lobbyID",
    meeting_types.created_by AS umt_created_by,
    meeting_types.description AS umt_description,
    meeting_types.title AS umt_title,
    meeting_types.prompt_template AS umt_prompt_template,
		"dataVisibility"
`

/** NOTE: only for testing */
var getSessions = database.NewStatement(`
	SELECT ` + sessionColumns + `
	FROM sessions session_cols
	LEFT JOIN meeting_types
		ON session_cols."user_meeting_type" = meeting_types.id
`)

var GetSessionBySessionId = database.NewStatement(`
	SELECT ` + sessionColumns + `
	FROM sessions session_cols
	LEFT JOIN meeting_types
		ON session_cols."user_meeting_type" = meeting_types.id
	WHERE session_cols.id = :sessionID
`)

// GetRecurringEndedSessions selects all recurring, ended sessions.
var getRecurringEndedSessions = database.NewStatement(`
	SELECT ` + sessionColumns + `
	FROM sessions session_cols
	LEFT JOIN meeting_types
	ON session_cols."user_meeting_type" = meeting_types.id
	WHERE CAST(settings->>'recurring' as INTEGER) > 0
		AND state = 'ended'
		AND "startTimestamp" < EXTRACT(EPOCH FROM NOW())
	ORDER BY "createdAt" DESC
	LIMIT 100
`)

var getFutureSessions = database.NewStatement(`
	WITH session_cols AS (
	  SELECT sessions.*, COALESCE(sessions."startTimestamp", sessions."createdAt") AS sort_timestamp
	  FROM sessions
	  LEFT JOIN session_access_rules
			ON sessions.id = session_access_rules."sessionID"
			AND sessions."recurrenceID" = session_access_rules."sessionRecurrenceID"
			AND session_access_rules."accessType" = 'viewer'
			AND session_access_rules."restrictionStatus" = 'granted'
			AND session_access_rules."type" = 'email'
			AND session_access_rules.value = :email
	  LEFT JOIN session_users
			ON session_users."userID" = :userId
			AND sessions.id = session_users."sessionID"
			AND sessions."recurrenceID" = session_users."sessionRecurrenceID"
	  WHERE
	    sessions.state IN ('scheduled', 'active', 'ready')
	    AND sessions."endTimestamp" >= :unixBeforeStale
	    AND (
					(session_access_rules.id IS NOT NULL OR session_users.id IS NOT NULL)
					OR (sessions."hostUserIDs" @> :userIdArray OR sessions."producerUserIDs" @> :userIdArray)
			)
	  ORDER BY sort_timestamp ASC
	  LIMIT :limit + 1
	  OFFSET :offset
	)
	SELECT ` + sessionColumns + `
	FROM session_cols
	LEFT JOIN meeting_types
		ON session_cols."user_meeting_type" = meeting_types.id
`)

var getActiveSessions = database.NewStatement(`
	SELECT
		` + sessionColumns + `
	FROM sessions session_cols
	LEFT JOIN meeting_types
		ON session_cols."user_meeting_type" = meeting_types.id
	WHERE state = 'active'
	ORDER BY "createdAt" ASC
	LIMIT 100
`)

var GetSessionByRecurrenceId = database.NewStatement(`
	SELECT ` + sessionColumns + `
	FROM sessions_recurrences session_cols
	LEFT JOIN meeting_types
	ON session_cols."user_meeting_type" = meeting_types.id
	WHERE
		session_cols.id = :sessionID
		AND "recurrenceID" = :recurrenceID
`)

var getRecurrenceID = database.NewStatement(`
	SELECT "recurrenceID"
	FROM sessions
	WHERE sessions.id = :sessionID
`)

var CreateSessionWithIDs = database.NewStatement(`
	INSERT INTO sessions (
		id,
		"recurrenceID",
		title,
		"startTimestamp",
		"endTimestamp",
		url,
		"branchURL",
		state,
		"hostUserIDs",
		"hostSessionUserIDs",
		"primaryHostUserID",
		"creatorUserID",
		settings,
		avatar,
		cover,
		about,
		"ogMetadata",
		category,
		tags,
		"isFeatured",
		"createdAt",
		"updatedAt",
		"isLiveStreamEnabled",
		"stateUpdatedAt",
		"producerUserIDs",
		"producerSessionUserIDs",
		"isRecordingProcessed",
		"isPubliclyVisible",
		"isViewerAccessRestricted",
		"dataVisibility",
		"calendarType",
		"calendarID",
		"calendarEventEditURL",
		"hasPastRecordings",
		"enableBots",
		"meetingType",
		user_meeting_type,
		rowversion,
		"accessStatus",
		"lobbyID"
	) VALUES (
		:id,
		:recurrenceID,
		:title,
		:startTimestamp,
		:endTimestamp,
		:url,
		:branchURL,
		:state,
		:hostUserIDs,
		:hostSessionUserIDs,
		:primaryHostUserID,
		:creatorUserID,
		:settings,
		:avatar,
		:cover,
		:about,
		:ogMetadata,
		:category,
		:tags,
		:isFeatured,
		:createdAt,
		:updatedAt,
		:isLiveStreamEnabled,
		:stateUpdatedAt,
		:producerUserIDs,
		:producerSessionUserIDs,
		:isRecordingProcessed,
		:isPubliclyVisible,
		:isViewerAccessRestricted,
		:dataVisibility,
		:calendarType,
		:calendarID,
		:calendarEventEditURL,
		:hasPastRecordings,
		:enableBots,
		:meetingType,
		:user_meeting_type,
		:rowversion,
		:accessStatus,
		:lobbyID
	)
`)

var UpdateSession = database.NewStatement(`
	UPDATE sessions SET
		"startTimestamp" = :startTimestamp,
		"endTimestamp" = :endTimestamp,
		"startedAt" = :startedAt,
		"endedAt" = :endedAt,
		title = :title,
		about = :about,
		state = :state,
		settings = :settings,
		category = :category,
		tags = :tags,
		"primaryHostUserID" = :primaryHostUserID,
		"isPubliclyVisible" = :isPubliclyVisible,
		"accessStatus" = :accessStatus,
		"dataVisibility" = :dataVisibility,
		"user_meeting_type" = :user_meeting_type,
		"updatedAt" = :updatedAt,
		"stateUpdatedAt" = :stateUpdatedAt,
		rowversion = :rowversion + 1
	WHERE id = :id AND "recurrenceID" = :recurrenceID
`)

var UpdateScheduledSession = database.NewStatement(`
	UPDATE sessions SET
		"startTimestamp" = :startTimestamp,
		"endTimestamp" = :endTimestamp,
		"startedAt" = :startedAt,
		"endedAt" = :endedAt,
		state = 'scheduled',
		"recurrenceID" = :recurrenceID,
		"hostUserIDs" = :hostUserIDs,
		"hostSessionUserIDs" = :hostSessionUserIDs,
		"producerSessionUserIDs" = :producerSessionUserIDs,
		"isLiveStreamEnabled" = false,
		"updatedAt" = :updatedAt,
		"stateUpdatedAt" = :stateUpdatedAt,
		"isRecordingProcessed" = :isRecordingProcessed,
		settings = :settings
	WHERE id = :id
`)

var NewSnowflakeID = database.NewStatement(`SELECT CAST(id_generator() AS bigint) AS "id"`)

var FetchRowVersion = database.NewStatement(`SELECT rowversion FROM sessions WHERE id = :sessionID`)

var GetCategoryIsEnabled = database.NewStatement(
	`SELECT "isEnabled" FROM sessions_categories WHERE "id" = :id`,
)

var listSessionsByLobbyID = database.NewStatement(`
	SELECT
		` + sessionColumns + `
	FROM sessions session_cols
	LEFT JOIN meeting_types
	ON session_cols."user_meeting_type" = meeting_types.id
	WHERE "lobbyID" = :lobbyID
`)

var listActiveSessionsByLobbyID = database.NewStatement(`
	SELECT
		` + sessionColumns + `
	FROM sessions session_cols
	LEFT JOIN meeting_types
	ON session_cols."user_meeting_type" = meeting_types.id
	WHERE "lobbyID" = :lobbyID
	AND state = 'active'
`)

func init() {
	Statements = append(Statements, database.Statements{
		getSessions, GetSessionByRecurrenceId, getRecurrenceID, CreateSessionWithIDs,
		UpdateSession, UpdateScheduledSession, NewSnowflakeID, GetSessionBySessionId, GetCategoryIsEnabled,
		getRecurringEndedSessions, getActiveSessions, getFutureSessions, listSessionsByLobbyID, listActiveSessionsByLobbyID, FetchRowVersion,
	}...)
}

/** NOTE: only for testing */
func GetSessions(ctx context.Context) (*[]SessionRow, error) {
	var err error
	sessions := []SessionRow{}

	getSessions.Queryx(ctx, database.Args{}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var sessionRow SessionRow
		if scanErr := r.StructScan(&sessionRow); scanErr != nil {
			err = scanErr
			return false
		}

		sessions = append(sessions, sessionRow)
		return true
	})

	return &sessions, err
}

func GetSessionByID(ctx context.Context, sessionID string) (*SessionRow, error) {
	row := SessionRow{}
	if err := GetSessionBySessionId.GetStruct(ctx, database.Args{
		"sessionID": sessionID,
	}, &row); err != nil {
		return nil, err
	}

	return &row, nil
}

func GetSessionByRecurrenceID(ctx context.Context, sessionID string, RecurrenceID string) (*SessionRow, error) {
	row := SessionRow{}
	if err := GetSessionByRecurrenceId.GetStruct(ctx, database.Args{
		"sessionID": sessionID, "recurrenceID": RecurrenceID,
	}, &row); err != nil {
		return nil, err
	}

	return &row, nil
}

func GetRecurrenceID(ctx context.Context, sessionID string) (string, error) {
	var recurrenceID int64
	err := getRecurrenceID.GetValues(ctx, database.Args{"sessionID": sessionID}, &recurrenceID)
	if err != nil {
		return "", err
	}
	if recurrenceID == 0 {
		return "", nil
	}
	return shared.FormatInt(recurrenceID), nil
}

func GetFutureSessions(ctx context.Context, userID string, userEmail string, req *api.ListSessionsRequest, unixBeforeStale int64) ([]SessionRow, *int, error) {
	var err error
	sessions := []SessionRow{}

	getFutureSessions.Queryx(ctx, database.Args{
		"userId":          userID,
		"userIdArray":     []string{userID},
		"unixBeforeStale": unixBeforeStale,
		"email":           userEmail,
		"limit":           req.Limit,
		"offset":          req.Offset,
	}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var sessionRow SessionRow
		if scanErr := r.StructScan(&sessionRow); scanErr != nil {
			err = scanErr
			return false
		}

		sessions = append(sessions, sessionRow)
		return true
	})

	// there's more
	if len(sessions) > req.Limit {
		// pop last session (there's limit + 1 in the query to make sure we have the next cursor)
		sessions = sessions[:len(sessions)-1]
		nextCursor := req.Offset + req.Limit
		return sessions, &nextCursor, err
	}

	// no more sessions to fetch
	return sessions, nil, err
}

func GetRecurringEndedSessions(ctx context.Context) ([]SessionRow, error) {
	var err error
	sessions := []SessionRow{}

	getRecurringEndedSessions.Queryx(ctx, map[string]interface{}{}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var sessionRow SessionRow
		if scanErr := r.StructScan(&sessionRow); scanErr != nil {
			err = scanErr
			return false
		}

		sessions = append(sessions, sessionRow)
		return true
	})

	return sessions, err
}

func GetActiveSessions(ctx context.Context) ([]SessionRow, error) {
	var err error
	sessions := []SessionRow{}

	getActiveSessions.Queryx(ctx, map[string]interface{}{}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var sessionRow SessionRow
		if scanErr := r.StructScan(&sessionRow); scanErr != nil {
			err = scanErr
			return false
		}

		sessions = append(sessions, sessionRow)
		return true
	})

	return sessions, err
}

func ListSessionsByLobbyID(ctx context.Context, lobbyID string) ([]SessionRow, error) {
	var err error
	sessions := []SessionRow{}

	listSessionsByLobbyID.Queryx(ctx, database.Args{
		"lobbyID": lobbyID,
	}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var sessionRow SessionRow
		if scanErr := r.StructScan(&sessionRow); scanErr != nil {
			err = scanErr
			return false
		}

		sessions = append(sessions, sessionRow)
		return true
	})

	return sessions, err
}

func ListActiveSessionsByLobbyID(ctx context.Context, lobbyID string) ([]SessionRow, error) {
	var err error
	sessions := []SessionRow{}

	listActiveSessionsByLobbyID.Queryx(ctx, database.Args{
		"lobbyID": lobbyID,
	}, func(r *sqlx.Rows, dbErr error) bool {
		if dbErr != nil {
			err = dbErr
			return false
		}

		var sessionRow SessionRow
		if scanErr := r.StructScan(&sessionRow); scanErr != nil {
			err = scanErr
			return false
		}

		sessions = append(sessions, sessionRow)
		return true
	})

	return sessions, err
}
