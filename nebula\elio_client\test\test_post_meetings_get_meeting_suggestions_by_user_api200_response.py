# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_meetings_get_meeting_suggestions_by_user_api200_response import POSTMeetingsGetMeetingSuggestionsByUserAPI200Response

class TestPOSTMeetingsGetMeetingSuggestionsByUserAPI200Response(unittest.TestCase):
    """POSTMeetingsGetMeetingSuggestionsByUserAPI200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTMeetingsGetMeetingSuggestionsByUserAPI200Response:
        """Test POSTMeetingsGetMeetingSuggestionsByUserAPI200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTMeetingsGetMeetingSuggestionsByUserAPI200Response`
        """
        model = POSTMeetingsGetMeetingSuggestionsByUserAPI200Response()
        if include_optional:
            return POSTMeetingsGetMeetingSuggestionsByUserAPI200Response(
                message = '',
                success = True,
                suggestions = [
                    ''
                    ]
            )
        else:
            return POSTMeetingsGetMeetingSuggestionsByUserAPI200Response(
                message = '',
                success = True,
                suggestions = [
                    ''
                    ],
        )
        """

    def testPOSTMeetingsGetMeetingSuggestionsByUserAPI200Response(self):
        """Test POSTMeetingsGetMeetingSuggestionsByUserAPI200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
