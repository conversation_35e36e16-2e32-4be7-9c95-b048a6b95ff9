# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.get_meetings_get_meeting_suggestions_by_user200_response import GETMeetingsGetMeetingSuggestionsByUser200Response

class TestGETMeetingsGetMeetingSuggestionsByUser200Response(unittest.TestCase):
    """GETMeetingsGetMeetingSuggestionsByUser200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> GETMeetingsGetMeetingSuggestionsByUser200Response:
        """Test GETMeetingsGetMeetingSuggestionsByUser200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `GETMeetingsGetMeetingSuggestionsByUser200Response`
        """
        model = GETMeetingsGetMeetingSuggestionsByUser200Response()
        if include_optional:
            return GETMeetingsGetMeetingSuggestionsByUser200Response(
                message = '',
                success = True,
                suggestions = [
                    ''
                    ]
            )
        else:
            return GETMeetingsGetMeetingSuggestionsByUser200Response(
                message = '',
                success = True,
                suggestions = [
                    ''
                    ],
        )
        """

    def testGETMeetingsGetMeetingSuggestionsByUser200Response(self):
        """Test GETMeetingsGetMeetingSuggestionsByUser200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
