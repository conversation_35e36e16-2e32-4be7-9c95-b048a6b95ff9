package api

// MeetingSuggestionDTO represents a single meeting suggestion
type MeetingSuggestionDTO struct {
	Id                  string `json:"id"`
	SessionId          string `json:"sessionId"`
	SessionRecurrenceId string `json:"sessionRecurrenceId"`
	UserId             string `json:"userId"`
	Category           string `json:"category"`
	Prompt             string `json:"prompt"`
	CreatedAt          int64  `json:"createdAt"`
	UpdatedAt          int64  `json:"updatedAt"`
}

// SuggestionResponseDTO represents a suggestion response optimized for analytics
type SuggestionResponseDTO struct {
	Id         string `json:"id"`
	Content    string `json:"content"`
	Category   string `json:"category"`
	IsPersonal bool   `json:"isPersonal"`
	CreatedAt  int64  `json:"createdAt"`
}

// MeetingSuggestionInput represents input for creating/updating suggestions
type MeetingSuggestionInput struct {
	UserId   string `json:"userId"`
	Category string `json:"category"`
	Prompt   string `json:"prompt"`
}

// UpsertMeetingSuggestionsRequest represents the request to upsert meeting suggestions
type UpsertMeetingSuggestionsRequest struct {
	SessionID    string                    `json:"sessionID"`
	RecurrenceID string                    `json:"recurrenceID"`
	Suggestions  []MeetingSuggestionInput  `json:"suggestions"`
}

// UpsertMeetingSuggestionsResponse represents the response from upserting suggestions
type UpsertMeetingSuggestionsResponse struct {
	Count   int    `json:"count"`
}

// GetMeetingSuggestionsRequest represents the request to get meeting suggestions for a user
type GetMeetingSuggestionsRequest struct {
}

// GetMeetingSuggestionsInternalRequest represents the internal request to get meeting suggestions for a user
type GetMeetingSuggestionsInternalRequest struct {
	SessionID    string `json:"sessionID"`
	RecurrenceID string `json:"recurrenceID"`
	UserID       string `json:"userID"`
}

// GetMeetingSuggestionsResponse represents the response with meeting suggestions
type GetMeetingSuggestionsResponse struct {
	Data struct {
		Suggestions []SuggestionResponseDTO `json:"suggestions"`
		Total       int                     `json:"total"`
	} `json:"data"`
	Message string `json:"message"`
}

// GetSessionSuggestionsRequest represents the request to get all suggestions for a session
type GetSessionSuggestionsRequest struct {
	SessionID    string `json:"sessionID"`
	RecurrenceID string `json:"recurrenceID"`
}

// GetSessionSuggestionsResponse represents the response with all session suggestions
type GetSessionSuggestionsResponse struct {
	Suggestions []MeetingSuggestionDTO `json:"suggestions"`
} 