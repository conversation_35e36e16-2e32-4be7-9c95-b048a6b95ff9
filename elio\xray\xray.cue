package xray

Nebula: {
    HostUrl: string | *"http://localhost:3006"
    BasicAuthUsername: string | *"nebula"
    BasicAuthPassword: string | *"nebula_pass"
}

if #Meta.Environment.Type == "ephemeral" {
    Nebula: {
        HostUrl: "https://nebula-pr-:prid.feature.rumi.ai"
    }
}

if #Meta.Environment.Name == "develop" || #Meta.Environment.Name == "staging" {
    Nebula: {
        HostUrl: "https://nebula."+#Meta.Environment.Name+".waitroom.com"
    }	
}

if #Meta.Environment.Name == "main" {
    Nebula: {
        HostUrl: "https://nebula.prod.waitroom.com"
    }
}