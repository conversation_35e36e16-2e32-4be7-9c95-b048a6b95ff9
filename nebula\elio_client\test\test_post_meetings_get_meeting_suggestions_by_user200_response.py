# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_meetings_get_meeting_suggestions_by_user200_response import POSTMeetingsGetMeetingSuggestionsByUser200Response

class TestPOSTMeetingsGetMeetingSuggestionsByUser200Response(unittest.TestCase):
    """POSTMeetingsGetMeetingSuggestionsByUser200Response unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTMeetingsGetMeetingSuggestionsByUser200Response:
        """Test POSTMeetingsGetMeetingSuggestionsByUser200Response
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTMeetingsGetMeetingSuggestionsByUser200Response`
        """
        model = POSTMeetingsGetMeetingSuggestionsByUser200Response()
        if include_optional:
            return POSTMeetingsGetMeetingSuggestionsByUser200Response(
                message = '',
                success = True,
                suggestions = [
                    ''
                    ]
            )
        else:
            return POSTMeetingsGetMeetingSuggestionsByUser200Response(
                message = '',
                success = True,
                suggestions = [
                    ''
                    ],
        )
        """

    def testPOSTMeetingsGetMeetingSuggestionsByUser200Response(self):
        """Test POSTMeetingsGetMeetingSuggestionsByUser200Response"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
