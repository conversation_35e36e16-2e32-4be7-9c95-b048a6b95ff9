# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_list_x_rays_response_data import ApiListXRaysResponseData

class TestApiListXRaysResponseData(unittest.TestCase):
    """ApiListXRaysResponseData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiListXRaysResponseData:
        """Test ApiListXRaysResponseData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiListXRaysResponseData`
        """
        model = ApiListXRaysResponseData()
        if include_optional:
            return ApiListXRaysResponseData(
                has_more = True,
                total_count = 56,
                xrays = [
                    elio_client.models.api/x_ray_dto.api.XRayDTO(
                        alert_channels = {
                            'key' : True
                            }, 
                        created_at = 56, 
                        current_commit = elio_client.models.api/x_ray_doc_commit.api.XRayDocCommit(
                            author_id = 56, 
                            content = '', 
                            created_at = 56, 
                            id = 56, 
                            updated_at = 56, 
                            xray_id = 56, ), 
                        current_commit_id = 56, 
                        description = '', 
                        frequency = '', 
                        icon = '', 
                        id = 56, 
                        is_active = True, 
                        last_digest_at = 56, 
                        owner_id = 56, 
                        prompt = '', 
                        scope = '', 
                        short_summary = '', 
                        title = '', 
                        type = '', 
                        unread_notifications_count = 56, 
                        updated_at = 56, 
                        visibility = '', )
                    ]
            )
        else:
            return ApiListXRaysResponseData(
                has_more = True,
                total_count = 56,
                xrays = [
                    elio_client.models.api/x_ray_dto.api.XRayDTO(
                        alert_channels = {
                            'key' : True
                            }, 
                        created_at = 56, 
                        current_commit = elio_client.models.api/x_ray_doc_commit.api.XRayDocCommit(
                            author_id = 56, 
                            content = '', 
                            created_at = 56, 
                            id = 56, 
                            updated_at = 56, 
                            xray_id = 56, ), 
                        current_commit_id = 56, 
                        description = '', 
                        frequency = '', 
                        icon = '', 
                        id = 56, 
                        is_active = True, 
                        last_digest_at = 56, 
                        owner_id = 56, 
                        prompt = '', 
                        scope = '', 
                        short_summary = '', 
                        title = '', 
                        type = '', 
                        unread_notifications_count = 56, 
                        updated_at = 56, 
                        visibility = '', )
                    ],
        )
        """

    def testApiListXRaysResponseData(self):
        """Test ApiListXRaysResponseData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
