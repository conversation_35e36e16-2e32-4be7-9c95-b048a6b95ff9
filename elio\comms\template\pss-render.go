package template

import (
	_ "embed"
	"encoding/json"
	"strings"

	"encore.dev/rlog"
	"github.com/osteele/liquid"
	"github.com/tidwall/sjson"
)

//go:embed blocks/interview_introductions.liquid
var interviewIntroductions string

//go:embed blocks/company_update.liquid
var companyUpdate string

//go:embed blocks/department_update.liquid
var departmentUpdate string

//go:embed blocks/meeting_summary_participants.liquid
var meetingSummaryParticipants string

//go:embed blocks/meeting_summary_agenda_items.liquid
var meetingSummaryAgendaItems string

//go:embed blocks/personal_updates.liquid
var personalUpdates string

//go:embed blocks/employee_recognition.liquid
var employeeRecognition string

//go:embed blocks/meeting_summary_discussions.liquid
var meetingSummaryDiscussions string

//go:embed blocks/professional_development_details.liquid
var professionalDevelopmentDetails string

//go:embed blocks/strategic_initiatives.liquid
var strategicInitiatives string

//go:embed blocks/projects_initiatives.liquid
var projectsInitiatives string

//go:embed blocks/collaboration-coordination.liquid
var collaborationCoordination string

//go:embed blocks/competitor_analysis.liquid
var competitorAnalysis string

//go:embed blocks/candidate_background.liquid
var candidateBackground string

//go:embed blocks/candidate_skills.liquid
var candidateSkills string

//go:embed blocks/client_background.liquid
var clientBackground string

//go:embed blocks/sales_opportunities.liquid
var salesOpportunities string

//go:embed blocks/progress_updates.liquid
var progressUpdates string

//go:embed blocks/key_outcomes.liquid
var keyOutcomes string

//go:embed blocks/concerns_challenges.liquid
var concernsChallenges string

//go:embed blocks/challenges_issues.liquid
var challengesIssues string

//go:embed blocks/objections_concerns.liquid
var objectionsConcerns string

//go:embed blocks/additional_issues_points.liquid
var additionalIssuesPoints string

//go:embed blocks/open_discussion_qa.liquid
var openDiscussionQA string

//go:embed blocks/qa.liquid
var qa string

//go:embed blocks/upcoming_priorities.liquid
var upcomingPriorities string

//go:embed blocks/conclusion_next_steps.liquid
var conclusionNextSteps string

//go:embed blocks/meeting_summary_decisions.liquid
var meetingSummaryDecisions string

//go:embed blocks/suggestions.liquid
var suggestions string

func RenderPostSessionSummaryHTML(content json.RawMessage) (strings.Builder, error) {
	bindingsJson, _ := sjson.SetBytes([]byte("{}"), "response.data.content", content)
	contentMap := make(map[string]any)
	if err := json.Unmarshal(bindingsJson, &contentMap); err != nil {
		return strings.Builder{}, err
	}

	bldr := &strings.Builder{}
	engine := liquid.NewEngine()
	// Introductions
	render(engine, bldr, interviewIntroductions, contentMap)
	// Company and Department Updates
	render(engine, bldr, companyUpdate, contentMap)
	render(engine, bldr, departmentUpdate, contentMap)
	// Meeting Participants
	render(engine, bldr, meetingSummaryParticipants, contentMap)
	// Agenda Items
	render(engine, bldr, meetingSummaryAgendaItems, contentMap)
	// Personal Updates
	render(engine, bldr, personalUpdates, contentMap)
	// Employee Recognition
	render(engine, bldr, employeeRecognition, contentMap)
	// Discussions
	render(engine, bldr, meetingSummaryDiscussions, contentMap)
	// Professional Development Details
	render(engine, bldr, professionalDevelopmentDetails, contentMap)
	// Strategic Initiatives
	render(engine, bldr, strategicInitiatives, contentMap)
	// Projects and Initiatives
	render(engine, bldr, projectsInitiatives, contentMap)
	// Collaboration and Coordination
	render(engine, bldr, collaborationCoordination, contentMap)
	// Competitor Analysis
	render(engine, bldr, competitorAnalysis, contentMap)
	// Candidate, Client, and Sales Details
	render(engine, bldr, candidateBackground, contentMap)
	render(engine, bldr, candidateSkills, contentMap)
	render(engine, bldr, clientBackground, contentMap)
	render(engine, bldr, salesOpportunities, contentMap)
	// Progress and Key Outcomes
	render(engine, bldr, progressUpdates, contentMap)
	render(engine, bldr, keyOutcomes, contentMap)
	// Challenges and Issues
	render(engine, bldr, concernsChallenges, contentMap)
	render(engine, bldr, challengesIssues, contentMap)
	render(engine, bldr, objectionsConcerns, contentMap)
	// Additional Points and Open Discussion
	render(engine, bldr, additionalIssuesPoints, contentMap)
	render(engine, bldr, openDiscussionQA, contentMap)
	render(engine, bldr, qa, contentMap)
	// Decisions Made and Action Items
	render(engine, bldr, meetingSummaryDecisions, contentMap)
	// Upcoming Priorities
	render(engine, bldr, upcomingPriorities, contentMap)
	// Follow Up and Next Steps
	render(engine, bldr, conclusionNextSteps, contentMap)
	return *bldr, nil
}

// RenderPostSessionSummaryHTMLWithSuggestions renders the post-session summary HTML with personalized suggestions
func RenderPostSessionSummaryHTMLWithSuggestions(content json.RawMessage, personalizedSuggestions []string, recordingURL string) (strings.Builder, error) {
	bindingsJson, _ := sjson.SetBytes([]byte("{}"), "response.data.content", content)
	contentMap := make(map[string]any)
	if err := json.Unmarshal(bindingsJson, &contentMap); err != nil {
		return strings.Builder{}, err
	}

	// Add suggestions and recording URL to the context
	contentMap["suggestions"] = personalizedSuggestions
	contentMap["recording_url"] = recordingURL

	bldr := &strings.Builder{}
	engine := liquid.NewEngine()
	// Introductions
	render(engine, bldr, interviewIntroductions, contentMap)
	// Company and Department Updates
	render(engine, bldr, companyUpdate, contentMap)
	render(engine, bldr, departmentUpdate, contentMap)
	// Meeting Participants
	render(engine, bldr, meetingSummaryParticipants, contentMap)
	// Agenda Items
	render(engine, bldr, meetingSummaryAgendaItems, contentMap)
	// Personal Updates
	render(engine, bldr, personalUpdates, contentMap)
	// Employee Recognition
	render(engine, bldr, employeeRecognition, contentMap)
	// Discussions
	render(engine, bldr, meetingSummaryDiscussions, contentMap)
	// Professional Development Details
	render(engine, bldr, professionalDevelopmentDetails, contentMap)
	// Strategic Initiatives
	render(engine, bldr, strategicInitiatives, contentMap)
	// Projects and Initiatives
	render(engine, bldr, projectsInitiatives, contentMap)
	// Collaboration and Coordination
	render(engine, bldr, collaborationCoordination, contentMap)
	// Competitor Analysis
	render(engine, bldr, competitorAnalysis, contentMap)
	// Candidate, Client, and Sales Details
	render(engine, bldr, candidateBackground, contentMap)
	render(engine, bldr, candidateSkills, contentMap)
	render(engine, bldr, clientBackground, contentMap)
	render(engine, bldr, salesOpportunities, contentMap)
	// Progress and Key Outcomes
	render(engine, bldr, progressUpdates, contentMap)
	render(engine, bldr, keyOutcomes, contentMap)
	// Challenges and Issues
	render(engine, bldr, concernsChallenges, contentMap)
	render(engine, bldr, challengesIssues, contentMap)
	render(engine, bldr, objectionsConcerns, contentMap)
	// Additional Points and Open Discussion
	render(engine, bldr, additionalIssuesPoints, contentMap)
	render(engine, bldr, openDiscussionQA, contentMap)
	render(engine, bldr, qa, contentMap)
	// Decisions Made and Action Items
	render(engine, bldr, meetingSummaryDecisions, contentMap)
	// Upcoming Priorities
	render(engine, bldr, upcomingPriorities, contentMap)
	// Follow Up and Next Steps
	render(engine, bldr, conclusionNextSteps, contentMap)
	// Personalized Suggestions (rendered after all content)
	render(engine, bldr, suggestions, contentMap)
	return *bldr, nil
}

func render(engine *liquid.Engine, bldr *strings.Builder, source string, b map[string]any) {
	html, err := engine.ParseAndRenderString(source, b)
	if err != nil {
		rlog.Error("error rendering template", "err", err, "template", source)
	}
	bldr.WriteString(html)
}
