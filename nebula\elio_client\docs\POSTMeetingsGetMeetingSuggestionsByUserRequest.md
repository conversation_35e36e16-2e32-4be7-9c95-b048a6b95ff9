# POSTMeetingsGetMeetingSuggestionsByUserRequest


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**recurrence_id** | **str** |  | 
**session_id** | **str** |  | 
**user_id** | **str** |  | 

## Example

```python
from elio_client.models.post_meetings_get_meeting_suggestions_by_user_request import POSTMeetingsGetMeetingSuggestionsByUserRequest

# TODO update the JSON string below
json = "{}"
# create an instance of POSTMeetingsGetMeetingSuggestionsByUserRequest from a JSON string
post_meetings_get_meeting_suggestions_by_user_request_instance = POSTMeetingsGetMeetingSuggestionsByUserRequest.from_json(json)
# print the JSON string representation of the object
print(POSTMeetingsGetMeetingSuggestionsByUserRequest.to_json())

# convert the object into a dict
post_meetings_get_meeting_suggestions_by_user_request_dict = post_meetings_get_meeting_suggestions_by_user_request_instance.to_dict()
# create an instance of POSTMeetingsGetMeetingSuggestionsByUserRequest from a dict
post_meetings_get_meeting_suggestions_by_user_request_from_dict = POSTMeetingsGetMeetingSuggestionsByUserRequest.from_dict(post_meetings_get_meeting_suggestions_by_user_request_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


