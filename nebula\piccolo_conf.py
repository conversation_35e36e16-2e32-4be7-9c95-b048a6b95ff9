import ssl
from piccolo.conf.apps import AppRegistry
from piccolo.engine.postgres import PostgresEngine
from ssl import SSLContext

from nebula.settings import settings

DB = PostgresEngine(
    config={
        "database": settings.postgres_db,
        "user": settings.postgres_user,
        "password": settings.postgres_pass,
        "host": settings.postgres_host,
        "port": settings.postgres_port,
        # Neon DBs do not respect search_path coming from server_settings at all, and this is the only way to set it.
        # We're stuck with public schemas until <PERSON><PERSON><PERSON> starts supporting passing dsn string as replacement to the config dict
        "server_settings": {"search_path": "nebula_schema"},
        "ssl": SSLContext(verify_mode=ssl.CERT_REQUIRED)
        if settings.db_require_ssl
        else None,
    },
)

APP_REGISTRY = AppRegistry(
    apps=["nebula.db.app_conf"],
)
