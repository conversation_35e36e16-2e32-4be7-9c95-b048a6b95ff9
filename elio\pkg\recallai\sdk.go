package recallai

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"encore.dev/rlog"
)

type RecallSDKInterface interface {
	GetCalendarAuthToken(params CalendarAuthTokenParams) (*CalendarAuthTokenResponse, error)
	GoogleCallbackURL() string
	MicrosoftCallbackURL() string
	DisconnectCalendarPlatform(params DisconnectCalendarPlatformParams, userToken string) error
	UpdateRecordingPreferences(userToken string, preferences *RecordingPreferences) error
	RetrieveBot(ctx context.Context, BotID string, userToken string) (*BotResponse, error)
	GetCalendarUser(userToken string) (*CalendarUser, error)
	GetDefaultPreferences() *RecordingPreferences
	GetMeetingURL(botResponse *BotResponse) string
	ListUsers() ([]CalendarUser, error)
	DeleteUser(userToken string) error
	CreateBot(ctx context.Context, userToken string, params CreateBotParams) (*BotResponse, error)
}

// NewUSEastRecallSDK creates a new RecallSDK instance
func NewUSEastRecallSDK(token string) RecallSDK {
	return RecallSDK{
		Token:          token,
		RegionEndpoint: "https://us-east-1.recall.ai/api",
	}
}

func (sdk RecallSDK) payloadToReader(payload any) *strings.Reader {
	if payload == nil {
		return nil
	}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		return nil
	}
	return strings.NewReader(string(jsonData))
}

func (sdk RecallSDK) doRequest(dest any, payload any, path string, method string, userToken string) error {
	// Path argument shouldn't start with a "/"
	endpoint := fmt.Sprintf("%v/%v", sdk.RegionEndpoint, path)

	// http.NewRequest requires a reader as body or a untyped nil to signal that there is no body
	var request *http.Request
	var err error
	if payload != nil {
		reader := sdk.payloadToReader(payload)
		request, err = http.NewRequest(method, endpoint, reader)
	} else {
		request, err = http.NewRequest(method, endpoint, nil)
	}

	if err != nil {
		return err
	}

	request.Header.Add("Authorization", fmt.Sprintf("Token %v", sdk.Token))
	request.Header.Add("Accept", "application/json")
	request.Header.Add("Content-Type", "application/json")
	if userToken != "" {
		request.Header.Add("x-recallcalendarauthtoken", userToken)
	}

	response, err := http.DefaultClient.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	responseBody, err := io.ReadAll(response.Body)
	if err != nil {
		return err
	}

	if response.StatusCode >= 300 {
		return fmt.Errorf("RecallSDK request failed with status code %v", response.StatusCode)
	}

	if dest != nil && len(responseBody) > 0 {
		err = json.Unmarshal(responseBody, &dest)
		if err != nil {
			rlog.Error("Error unmarshalling response body", "responseBody", string(responseBody))
			return err
		}
	}

	return nil
}

// GetCalendarAuthToken Calendar API:
func (sdk RecallSDK) GetCalendarAuthToken(params CalendarAuthTokenParams) (*CalendarAuthTokenResponse, error) {
	endpoint := "v1/calendar/authenticate/"
	var response CalendarAuthTokenResponse
	err := sdk.doRequest(&response, params, endpoint, "POST", "")
	if err != nil {
		return nil, err
	}

	return &response, nil
}

// GoogleCallbackURL Return the URL to redirect the user to for Google OAuth
func (sdk RecallSDK) GoogleCallbackURL() string {
	return fmt.Sprintf("%s/v1/calendar/google_oauth_callback/", sdk.RegionEndpoint)
}

func (sdk RecallSDK) MicrosoftCallbackURL() string {
	return fmt.Sprintf("%s/v1/calendar/ms_oauth_callback", sdk.RegionEndpoint)
}

func (sdk RecallSDK) DisconnectCalendarPlatform(params DisconnectCalendarPlatformParams, userToken string) error {
	err := sdk.doRequest(nil, params, "v1/calendar/user/disconnect/", "POST", userToken)
	if err != nil {
		return err
	}
	return nil
}

func (sdk RecallSDK) UpdateRecordingPreferences(userToken string, preferences *RecordingPreferences) error {
	if preferences == nil {
		preferences = sdk.GetDefaultPreferences()
	}

	params := RecordingPreferencesParams{
		Preferences: *preferences,
	}

	err := sdk.doRequest(nil, params, "v1/calendar/user/", "PUT", userToken)
	if err != nil {
		return err
	}

	return nil
}

func (sdk RecallSDK) RetrieveBot(ctx context.Context, BotID string, userToken string) (*BotResponse, error) {
	var response BotResponse
	endpoint := fmt.Sprintf("v1/bot/%v/", BotID)
	err := sdk.doRequest(&response, nil, endpoint, "GET", userToken)
	if err != nil {
		return nil, err
	}

	return &response, nil
}

func (sdk RecallSDK) GetCalendarUser(userToken string) (*CalendarUser, error) {
	var response CalendarUser
	err := sdk.doRequest(&response, nil, "v1/calendar/user/", "GET", userToken)
	if err != nil {
		return nil, err
	}
	return &response, nil
}

func (sdk RecallSDK) GetDefaultPreferences() *RecordingPreferences {
	return &RecordingPreferences{
		BotName:         "Rumi.ai Notetaker",
		RecordNonHost:   true,
		RecordRecurring: true,
		RecordExternal:  true,
		RecordInternal:  true,
		RecordConfirmed: false,
		RecordOnlyHost:  false,
	}
}

func (sdk RecallSDK) GetMeetingURL(botResponse *BotResponse) string {
	meetingId := botResponse.MeetingUrl.MeetingId
	platform := botResponse.MeetingUrl.Platform

	switch platform {
	case "google_meet":
		return fmt.Sprintf("https://meet.google.com/%s", meetingId)
	case "zoom":
		return fmt.Sprintf("https://zoom.us/j/%s", meetingId)
	case "microsoft_teams":
		return fmt.Sprintf("https://teams.microsoft.com/meet/%s", meetingId)
	case "microsoft_teams_live":
		return fmt.Sprintf("https://teams.live.com/meet/%s", meetingId)
	}

	return ""
}

func (sdk RecallSDK) ListUsers() ([]CalendarUser, error) {
	var response []CalendarUser
	err := sdk.doRequest(&response, nil, "v1/calendar/users/", "GET", "")
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (sdk RecallSDK) DeleteUser(userToken string) error {
	err := sdk.doRequest(nil, nil, "v1/calendar/user/", "DELETE", userToken)
	if err != nil {
		return err
	}
	return nil
}

func (sdk RecallSDK) CreateBot(ctx context.Context, userToken string, params CreateBotParams) (*BotResponse, error) {
	var response BotResponse
	endpoint := "v1/bot/"
	err := sdk.doRequest(&response, params, endpoint, "POST", userToken)
	if err != nil {
		return nil, err
	}
	return &response, nil
}
