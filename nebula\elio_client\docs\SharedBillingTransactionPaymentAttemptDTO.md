# SharedBillingTransactionPaymentAttemptDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**method_details** | [**SharedBillingPaymentMethodDetailsDTO**](SharedBillingPaymentMethodDetailsDTO.md) |  | 

## Example

```python
from elio_client.models.shared_billing_transaction_payment_attempt_dto import SharedBillingTransactionPaymentAttemptDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedBillingTransactionPaymentAttemptDTO from a JSON string
shared_billing_transaction_payment_attempt_dto_instance = SharedBillingTransactionPaymentAttemptDTO.from_json(json)
# print the JSON string representation of the object
print(SharedBillingTransactionPaymentAttemptDTO.to_json())

# convert the object into a dict
shared_billing_transaction_payment_attempt_dto_dict = shared_billing_transaction_payment_attempt_dto_instance.to_dict()
# create an instance of SharedBillingTransactionPaymentAttemptDTO from a dict
shared_billing_transaction_payment_attempt_dto_from_dict = SharedBillingTransactionPaymentAttemptDTO.from_dict(shared_billing_transaction_payment_attempt_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


