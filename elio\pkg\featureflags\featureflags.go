package featureflags

import (
	"log"
	"os"
	"sync"

	"encore.dev/rlog"
	"github.com/posthog/posthog-go"
)

var secrets struct {
	PosthogAP<PERSON>K<PERSON> string
}

var (
	client     posthog.Client
	once       sync.Once
	closeOnce  sync.Once
)

// Initialize sets up the PostHog client (called once)
func Initialize() {
	once.Do(func() {
		if secrets.PosthogAPIKey == "" {
			rlog.Warn("FeatureFlags: PostHog API key not set")
			return
		}

		var err error
		client, err = posthog.NewWithConfig(
			secrets.PosthogAPIKey,
			posthog.Config{
				Logger:     posthog.StdLogger(log.New(os.Stdout, "posthog ", 0), false),
			},
		)

		if err != nil {
			rlog.Error("Failed to initialize PostHog client", "error", err)
			client = nil
		}
	})
}

// IsEnabled checks if a feature flag is enabled for a user ID
func IsEnabled(flagKey string, userID string) bool {
	Initialize() // Ensure client is initialized
	
	if client == nil {
		rlog.Warn("FeatureFlags client not initialized, returning false", "flag", flagKey, "user", userID)
		return false
	}

	enabled, err := client.IsFeatureEnabled(posthog.FeatureFlagPayload{
		Key:        flagKey,
		DistinctId: userID,
	})

	if err != nil {
		rlog.Error("Failed to check feature flag, defaulting to false", "flag", flagKey, "user", userID, "error", err)
		return false
	}

	// Type assert the result to bool
	if enabledBool, ok := enabled.(bool); ok {
		return enabledBool
	}

	// If it's not a bool, check if it's truthy
	return enabled != nil && enabled != false
}

// GetValue gets the value of a feature flag for a user ID
func GetValue(flagKey string, userID string) interface{} {
	Initialize() // Ensure client is initialized
	
	if client == nil {
		rlog.Warn("FeatureFlags client not initialized, returning nil", "flag", flagKey, "user", userID)
		return nil
	}

	value, err := client.GetFeatureFlag(posthog.FeatureFlagPayload{
		Key:        flagKey,
		DistinctId: userID,
	})

	if err != nil {
		rlog.Error("Failed to get feature flag value, returning nil", "flag", flagKey, "user", userID, "error", err)
		return nil
	}

	return value
}

// GetStringValue gets a string value from a feature flag, with a default fallback
func GetStringValue(flagKey string, userID string, defaultValue string) string {
	value := GetValue(flagKey, userID)
	if strValue, ok := value.(string); ok {
		return strValue
	}
	return defaultValue
}

// GetIntValue gets an int value from a feature flag, with a default fallback
func GetIntValue(flagKey string, userID string, defaultValue int) int {
	value := GetValue(flagKey, userID)
	if intValue, ok := value.(int); ok {
		return intValue
	}
	if floatValue, ok := value.(float64); ok {
		return int(floatValue)
	}
	return defaultValue
}

// GetBoolValue gets a bool value from a feature flag, with a default fallback
func GetBoolValue(flagKey string, userID string, defaultValue bool) bool {
	value := GetValue(flagKey, userID)
	if boolValue, ok := value.(bool); ok {
		return boolValue
	}
	return defaultValue
}

// GetAllFlags gets all feature flags for a given user
func GetAllFlags(userID string) map[string]interface{} {
	Initialize() // Ensure client is initialized
	
	if client == nil {
		rlog.Warn("FeatureFlags client not initialized, returning empty map")
		return make(map[string]interface{})
	}

	flags, err := client.GetAllFlags(posthog.FeatureFlagPayloadNoKey{
		DistinctId: userID,
	})

	if err != nil {
		rlog.Error("Failed to get all feature flags", "user", userID, "error", err)
		return make(map[string]interface{})
	}

	return flags
}

// Close gracefully shuts down the PostHog client, flushing any buffered events
// Safe to call multiple times and safe to call before initialization.
func Close() {
	closeOnce.Do(func() {
		if client != nil {
			rlog.Info("Closing PostHog client and flushing events")
			client.Close()
		} else {
			rlog.Debug("PostHog client was never initialized, nothing to close")
		}
	})
}

// Common feature flag keys
const (
	MeetingSuggestions = "meeting-suggestions"
) 