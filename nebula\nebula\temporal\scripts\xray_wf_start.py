import uuid
import asyncio
from nebula.temporal.types import XRayScanWorkflowParams
from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
from temporalio.client import Client
from nebula.settings import settings
from nebula.temporal.client import create_temporal_client


async def start_xray_scan_wf(client: Client):
    """Start X-Ray scan workflow for testing / once off run."""
    # Example parameters - adjust these for your testing
    recurrence_id = 1308724707679274372  # Replace with actual recurrence ID
    user_id = 1053583008122013541  # Replace with actual user ID

    result = await client.execute_workflow(
        workflow=XRayScanWorkflow.run,
        arg=XRayScanWorkflowParams(
            recurrence_id=recurrence_id,
            user_id=user_id,
            team_id=None,  # Optional team ID
        ),
        id=str(uuid.uuid4()),
        task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
    )

    print(f"X-Ray scan workflow result -> {result}")


async def main():
    """Main function to execute the X-Ray workflow."""
    try:
        client = await create_temporal_client()
        await start_xray_scan_wf(client)
    except Exception as e:
        print(f"Error: {e}")
        raise e


if __name__ == "__main__":
    asyncio.run(main())
