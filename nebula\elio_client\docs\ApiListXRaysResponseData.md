# ApiListXRaysResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**has_more** | **bool** |  | 
**total_count** | **int** |  | 
**xrays** | [**List[ApiXRayDTO]**](ApiXRayDTO.md) |  | 

## Example

```python
from elio_client.models.api_list_x_rays_response_data import ApiListXRaysResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiListXRaysResponseData from a JSON string
api_list_x_rays_response_data_instance = ApiListXRaysResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiListXRaysResponseData.to_json())

# convert the object into a dict
api_list_x_rays_response_data_dict = api_list_x_rays_response_data_instance.to_dict()
# create an instance of ApiListXRaysResponseData from a dict
api_list_x_rays_response_data_from_dict = ApiListXRaysResponseData.from_dict(api_list_x_rays_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


