deployment:
  image:
    pullPolicy: Always
    repository: 177319056053.dkr.ecr.us-east-1.amazonaws.com/nebula
    tag: staging-1
  initialDelaySeconds: 120
  minReadySeconds: 60
  nodeSelector:
    karpenter.sh/nodepool: staging-cluster-nodepool
env:
  ### APM Related and System Required Variables
  - name: "DD_LOGS_INJECTION"
    value: "true"
  - name: "DD_ENV"
    value: "staging"
  - name: "DD_PROFILING_ENABLED"
    value: "true"
  - name: "DD_TRACE_DEBUG"
    value: "false"
  - name: "DD_SERVICE"
    value: "nebula"

hpa:
  cpuUtilization:
    targetPercentage: 90
  maxReplicas: 1
  minReplicas: 1
replicaCount: 1
resources:
  requests:
    memory: "750Mi"
    cpu: "3000m"
  limits:
    memory: "1256Mi"
    cpu: "4024m"
service:
  externalPort: 80
  internalPort: 8000
  name: nebula-staging
  type: ""

ingress:
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
  hosts:
    - name: waitroom.com
      rules:
        - subdomain: nebula.staging.
          path: /
