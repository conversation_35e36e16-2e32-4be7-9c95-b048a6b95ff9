# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, StrictStr
from typing import Any, ClassVar, Dict, List
from elio_client.models.recallai_bot_meeting_link import Recal<PERSON>BotMeetingLink
from elio_client.models.recallai_calendar_meeting import RecallaiCalendarMeeting
from elio_client.models.recallai_meeting_metadata import RecallaiMeetingMetadata
from elio_client.models.recallai_meeting_participant import Recal<PERSON>MeetingParticipant
from typing import Optional, Set
from typing_extensions import Self

class POSTTranscriptionsCreateBotForMeeting200Response(BaseModel):
    """
    POSTTranscriptionsCreateBotForMeeting200Response
    """ # noqa: E501
    calendar_meetings: List[RecallaiCalendarMeeting]
    id: StrictStr
    meeting_metadata: RecallaiMeetingMetadata
    meeting_participants: List[RecallaiMeetingParticipant]
    meeting_url: RecallaiBotMeetingLink
    video_url: StrictStr
    __properties: ClassVar[List[str]] = ["calendar_meetings", "id", "meeting_metadata", "meeting_participants", "meeting_url", "video_url"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of POSTTranscriptionsCreateBotForMeeting200Response from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in calendar_meetings (list)
        _items = []
        if self.calendar_meetings:
            for _item_calendar_meetings in self.calendar_meetings:
                if _item_calendar_meetings:
                    _items.append(_item_calendar_meetings.to_dict())
            _dict['calendar_meetings'] = _items
        # override the default output from pydantic by calling `to_dict()` of meeting_metadata
        if self.meeting_metadata:
            _dict['meeting_metadata'] = self.meeting_metadata.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in meeting_participants (list)
        _items = []
        if self.meeting_participants:
            for _item_meeting_participants in self.meeting_participants:
                if _item_meeting_participants:
                    _items.append(_item_meeting_participants.to_dict())
            _dict['meeting_participants'] = _items
        # override the default output from pydantic by calling `to_dict()` of meeting_url
        if self.meeting_url:
            _dict['meeting_url'] = self.meeting_url.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of POSTTranscriptionsCreateBotForMeeting200Response from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "calendar_meetings": [RecallaiCalendarMeeting.from_dict(_item) for _item in obj["calendar_meetings"]] if obj.get("calendar_meetings") is not None else None,
            "id": obj.get("id"),
            "meeting_metadata": RecallaiMeetingMetadata.from_dict(obj["meeting_metadata"]) if obj.get("meeting_metadata") is not None else None,
            "meeting_participants": [RecallaiMeetingParticipant.from_dict(_item) for _item in obj["meeting_participants"]] if obj.get("meeting_participants") is not None else None,
            "meeting_url": RecallaiBotMeetingLink.from_dict(obj["meeting_url"]) if obj.get("meeting_url") is not None else None,
            "video_url": obj.get("video_url")
        })
        return _obj


