# ApiListUserTransactionsResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**invoice_url** | **str** |  | 
**transaction** | [**SharedBillingTransactionDTO**](SharedBillingTransactionDTO.md) |  | 

## Example

```python
from elio_client.models.api_list_user_transactions_response_data import ApiListUserTransactionsResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of ApiListUserTransactionsResponseData from a JSON string
api_list_user_transactions_response_data_instance = ApiListUserTransactionsResponseData.from_json(json)
# print the JSON string representation of the object
print(ApiListUserTransactionsResponseData.to_json())

# convert the object into a dict
api_list_user_transactions_response_data_dict = api_list_user_transactions_response_data_instance.to_dict()
# create an instance of ApiListUserTransactionsResponseData from a dict
api_list_user_transactions_response_data_from_dict = ApiListUserTransactionsResponseData.from_dict(api_list_user_transactions_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


