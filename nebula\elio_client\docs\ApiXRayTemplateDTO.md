# ApiXRayTemplateDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**created_at** | **int** |  | 
**description** | **str** |  | 
**icon** | **str** |  | 
**id** | **int** |  | 
**owner** | [**SharedUserDTO**](SharedUserDTO.md) |  | 
**owner_id** | **int** |  | 
**prompt** | **str** |  | 
**short_summary** | **str** |  | 
**title** | **str** |  | 
**type** | **str** |  | 
**updated_at** | **int** |  | 

## Example

```python
from elio_client.models.api_x_ray_template_dto import ApiXRayTemplateDTO

# TODO update the JSON string below
json = "{}"
# create an instance of ApiXRayTemplateDTO from a JSON string
api_x_ray_template_dto_instance = ApiXRayTemplateDTO.from_json(json)
# print the JSON string representation of the object
print(ApiXRayTemplateDTO.to_json())

# convert the object into a dict
api_x_ray_template_dto_dict = api_x_ray_template_dto_instance.to_dict()
# create an instance of ApiXRayTemplateDTO from a dict
api_x_ray_template_dto_from_dict = ApiXRayTemplateDTO.from_dict(api_x_ray_template_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


