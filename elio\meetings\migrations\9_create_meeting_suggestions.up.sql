create table if not exists meeting_suggestions (
    id BIGSERIAL PRIMARY KEY,
    session_id TEXT NOT NULL,
    session_recurrence_id TEXT NOT NULL,
    user_id TEXT NOT NULL,
    category TEXT NOT NULL, -- 'self_review', 'action_items', 'content_feedback'
    prompt TEXT NOT NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NOT NULL,
    UNIQUE(session_id, session_recurrence_id, user_id, category)
);

create index if not exists idx_meeting_suggestions_session 
    on meeting_suggestions(session_id, session_recurrence_id);
create index if not exists idx_meeting_suggestions_user 
    on meeting_suggestions(user_id); 