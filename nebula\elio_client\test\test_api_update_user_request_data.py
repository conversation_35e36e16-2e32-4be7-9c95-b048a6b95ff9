# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.api_update_user_request_data import ApiUpdateUserRequestData

class TestApiUpdateUserRequestData(unittest.TestCase):
    """ApiUpdateUserRequestData unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> ApiUpdateUserRequestData:
        """Test ApiUpdateUserRequestData
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `ApiUpdateUserRequestData`
        """
        model = ApiUpdateUserRequestData()
        if include_optional:
            return ApiUpdateUserRequestData(
                about = '',
                avatar = '',
                first_name = '',
                last_name = '',
                onboarding = elio_client.models.shared/onboarding_flags.shared.OnboardingFlags(
                    0 = True, 
                    1 = True, 
                    2 = True, 
                    3 = True, 
                    4 = True, 
                    5 = True, 
                    6 = '', ),
                social = [
                    elio_client.models.shared/user_social_dto.shared.UserSocialDTO(
                        avatar = '', 
                        bio = '', 
                        first_name = '', 
                        full_name = '', 
                        last_name = '', 
                        platform = '', 
                        url = '', 
                        user_name = '', )
                    ]
            )
        else:
            return ApiUpdateUserRequestData(
        )
        """

    def testApiUpdateUserRequestData(self):
        """Test ApiUpdateUserRequestData"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
