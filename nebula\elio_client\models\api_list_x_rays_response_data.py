# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt
from typing import Any, ClassVar, Dict, List
from elio_client.models.api_x_ray_dto import ApiXRayDTO
from typing import Optional, Set
from typing_extensions import Self

class ApiListXRaysResponseData(BaseModel):
    """
    ApiListXRaysResponseData
    """ # noqa: E501
    has_more: StrictBool = Field(alias="hasMore")
    total_count: StrictInt = Field(alias="totalCount")
    xrays: List[ApiXRayDTO]
    __properties: ClassVar[List[str]] = ["hasMore", "totalCount", "xrays"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of ApiListXRaysResponseData from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in xrays (list)
        _items = []
        if self.xrays:
            for _item_xrays in self.xrays:
                if _item_xrays:
                    _items.append(_item_xrays.to_dict())
            _dict['xrays'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of ApiListXRaysResponseData from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "hasMore": obj.get("hasMore"),
            "totalCount": obj.get("totalCount"),
            "xrays": [ApiXRayDTO.from_dict(_item) for _item in obj["xrays"]] if obj.get("xrays") is not None else None
        })
        return _obj


