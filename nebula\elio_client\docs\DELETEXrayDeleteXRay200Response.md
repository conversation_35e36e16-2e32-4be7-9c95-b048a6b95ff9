# DELETEXrayDeleteXRay200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**message** | **str** |  | 
**success** | **bool** |  | 

## Example

```python
from elio_client.models.delete_xray_delete_x_ray200_response import DELETEXrayDeleteXRay200Response

# TODO update the JSON string below
json = "{}"
# create an instance of DELETEXrayDeleteXRay200Response from a JSON string
delete_xray_delete_x_ray200_response_instance = DELETEXrayDeleteXRay200Response.from_json(json)
# print the JSON string representation of the object
print(DELETEXrayDeleteXRay200Response.to_json())

# convert the object into a dict
delete_xray_delete_x_ray200_response_dict = delete_xray_delete_x_ray200_response_instance.to_dict()
# create an instance of DELETEXrayDeleteXRay200Response from a dict
delete_xray_delete_x_ray200_response_from_dict = DELETEXrayDeleteXRay200Response.from_dict(delete_xray_delete_x_ray200_response_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


