# ApiXRayDocCommit


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**author_id** | **int** |  | 
**content** | **str** |  | 
**created_at** | **int** |  | 
**id** | **int** |  | 
**updated_at** | **int** |  | 
**xray_id** | **int** |  | 

## Example

```python
from elio_client.models.api_x_ray_doc_commit import ApiXRayDocCommit

# TODO update the JSON string below
json = "{}"
# create an instance of ApiXRayDocCommit from a JSON string
api_x_ray_doc_commit_instance = ApiXRayDocCommit.from_json(json)
# print the JSON string representation of the object
print(ApiXRayDocCommit.to_json())

# convert the object into a dict
api_x_ray_doc_commit_dict = api_x_ray_doc_commit_instance.to_dict()
# create an instance of ApiXRayDocCommit from a dict
api_x_ray_doc_commit_from_dict = ApiXRayDocCommit.from_dict(api_x_ray_doc_commit_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


