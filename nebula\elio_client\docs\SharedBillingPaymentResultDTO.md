# SharedBillingPaymentResultDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**error_code** | **str** |  | 
**status** | **str** |  | 

## Example

```python
from elio_client.models.shared_billing_payment_result_dto import SharedBillingPaymentResultDTO

# TODO update the JSON string below
json = "{}"
# create an instance of SharedBillingPaymentResultDTO from a JSON string
shared_billing_payment_result_dto_instance = SharedBillingPaymentResultDTO.from_json(json)
# print the JSON string representation of the object
print(SharedBillingPaymentResultDTO.to_json())

# convert the object into a dict
shared_billing_payment_result_dto_dict = shared_billing_payment_result_dto_instance.to_dict()
# create an instance of SharedBillingPaymentResultDTO from a dict
shared_billing_payment_result_dto_from_dict = SharedBillingPaymentResultDTO.from_dict(shared_billing_payment_result_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


