package transcriptions

if #Meta.Environment.Name == "staging" {
	Kafka: {
		TLS: true
		SASL: false
		TopicLiveTranscripts: "sessions-live-transcriptions-batch-staging"
	}
	RecallAiWebhookToken: "mgyQy7bhglInaMA2TYXiLMtJ73eobgJh"
	FrontendURL: "https://staging.rumi.ai"
	AWSBucketName: "waitroom-staging-recordings"
}

if #Meta.Environment.Name == "main" {
	Kafka: {
		TLS: true
		SASL: true

		TopicLiveTranscripts: "sessions-live-transcriptions-batch"
	}
	RecallAiWebhookToken: "8kYHF9PZgoZpBanXr3wDNjMMRVjIAFvq"
	FrontendURL: "https://rumi.ai"
	AWSBucketName: "waitroom-prod-recordings"
	MicrosoftOAuth2ClientID: "834d8a70-5299-43d6-b57c-cd002f6a28fe"
	GoogleOAuth2ClientID: "732264686334-tcvc2siq6s4ctt2orhhhf1r6dhfm1pc0.apps.googleusercontent.com"
	RecallWebhookURL: "https://api.rumi.ai/preview.HandleRecallWebhook?token=8kYHF9PZgoZpBanXr3wDNjMMRVjIAFvq"
	GoogleLoginGroupId: "861a9def-3c2f-45dc-85ff-8444be3d9987"
}

if #Meta.Environment.Cloud == "encore" && #Meta.Environment.Type == "ephemeral" {
	Kafka: {
		TLS: false
		SASL: true
		TopicLiveTranscripts: "pr-:prid-sessions-live-transcriptions-batch"
	}
	RecallAiWebhookToken: "mgyQy7bhglInaMA2TYXiLMtJ73eobgJh"
	FrontendURL: "https://staging.rumi.ai"
	AWSBucketName: "waitroom-staging-recordings"
}
