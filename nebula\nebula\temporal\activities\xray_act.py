import json
from typing import List, Tu<PERSON>, Optional, Dict, Any
from datetime import datetime
import time

from nebula.web.api.x_ray.schema import XRayResponse
from temporalio import activity

from nebula.clients.llms import xray_openai_client
from nebula.db.models.xray import XRay
from nebula.db.models.xray_doc_commit import XRayDocumentCommit
from nebula.db.models.xray_doc_commit_notif import XRayDocumentCommitNotification
from nebula.services.search.common import (
    Participant,
    SessionRecurrence,
    User,
    get_participant_and_organizer_names,
    get_past_recurrence_timestamps,
    get_past_team_visible_session_recurrences_by_team_id,
    get_past_team_visible_session_recurrences_since_unix_ts_by_team_id,
    get_past_user_session_recurrences_by_user_ids,
    get_past_user_session_recurrences_since_unix_ts_by_user_ids,
    get_recurrence,
    get_recurrence_transcript_paragraphs,
    get_recurrences_participants_by_recurrence_ids,
    get_user_by_id,
    user_or_guest_name,
)
from nebula.services.x_ray.helpers import xray_to_xray_model
from nebula.services.x_ray.constants import AI_AUTHOR_ID
from nebula.temporal.activities.base import BaseActivity
from nebula.temporal.types import (
    GenerateScheduledDigestContentInput,
    XRayDeepScanActivityResult,
    XRayDeepScanLLMResult,
    XRayDeepScanParams,
    XRayQuickScanActivityParams,
    XRayQuickScanResult,
    XRayNotificationsResult,
    CreateDigestCommitInput,
)
from nebula.services.x_ray.types import XRayModel
from nebula.utils import duration_str_from_secs, relative_tz_dt_from_unix
from nebula.services.x_ray.prompts import XRayPrompts
from nebula.settings import settings


class XRayActivities(BaseActivity):
    def _get_quick_scan_user_prompt(
        self, transcript: str, xrays: List[XRayResponse.XRay]
    ):
        xrays_json = json.dumps(
            [xray_doc.model_dump() for xray_doc in xrays],
            indent=4,
            default=str,
        )
        return f"""Do your thing.

        Current user: Dynamic User (will be updated)

        Meeting transcript: {transcript}

        List of XRay documents: {xrays_json}

        {self.get_current_time_prompt()}
        """

    def _get_deep_scan_user_prompt(
        self,
        recurrence: SessionRecurrence,
        participants: List[Participant],
        transcript: str,
        xray_doc: XRayModel,
        document_content: str = "",
        blame_info: Optional[Dict[int, Dict[str, Any]]] = None,
    ):
        # TODO(branko): accept through params
        tz = "Europe/Belgrade"

        timestamps = get_past_recurrence_timestamps(recurrence)
        blame_info = blame_info or {}

        recurrence_user_names = get_participant_and_organizer_names(
            participants, recurrence["creatorID"]
        )

        # Format document with blame information if available
        document_with_blame = ""
        if document_content and blame_info:
            lines = document_content.splitlines()
            formatted_lines: List[str] = []
            for i, line in enumerate(lines):
                blame_data = blame_info.get(i)
                if blame_data:
                    author_name = blame_data.get("author_name", "Unknown")
                    author_type = blame_data.get("author_type", "human")
                    time_ago = blame_data.get("time_ago", "")

                    attribution = f"[{author_name} ({author_type}) • {time_ago}"
                    attribution += "]"

                    formatted_lines.append(f"{line} {attribution}")
                else:
                    formatted_lines.append(line)
            document_with_blame = "\n".join(formatted_lines)
        else:
            document_with_blame = document_content

        # Build document type specific context
        document_type_context = f"Document type: {xray_doc.xray_type}\n"
        document_type_context += f"Document scope: {xray_doc.scope}\n"

        # Add specific instructions for each document type
        if xray_doc.xray_type == XRay.XRayType.Monitor.value:
            document_type_context += """
            This is a Monitor document: append new facts without modifying existing content.
            
            When analyzing this document:
            - Never edit existing entries, regardless of who created them
            - Only append new entries when you find relevant facts in the transcript
            - Use the attribution information to understand the timeline of when facts were recorded
            - If you see contradictions between older and newer entries, add a new entry that notes the update
            - Pay attention to who recorded each fact - human entries may contain specialized knowledge
            - When producing response, keep the attribution out of the response. Attribution is there only to help you understand the timeline of when facts were recorded.
            
            Remember that your role is to maintain the chronological integrity of this audit trail while adding new relevant information.
            """
        elif xray_doc.xray_type == XRay.XRayType.Digest.value:
            document_type_context += """
            This is a Digest document: provide a summary of information for a specific time period.
            
            When creating a new digest:
            - Review the attribution information to understand when each part was written and by whom
            - Note which topics were emphasized by human authors vs. AI in previous digests
            - Follow similar organization and depth as human-authored sections
            - Use the timeline information to ensure your digest represents the most current understanding
            - Match the tone and style of human contributions when possible
            - When producing response, keep the attribution out of the response. Attribution is there only to help you understand the timeline of when facts were recorded.
            
            Your goal is to create a comprehensive summary that maintains continuity with previous digests while incorporating new information from the meeting transcript.
            """
        else:  # Build
            document_type_context += """
            This is a Build document: update and maintain a living document by adding, editing, or removing content.
            
            This document has line-by-line attribution showing who edited each part and when. Use this information to:
            - Preserve human-authored content unless there's a clear reason to update it
            - Feel free to modify or remove AI-generated content if it's outdated or incorrect
            - Pay attention to recent changes (within the last few days) as they likely contain the most current information
            - If humans and AI have edited the same section multiple times, prioritize the human intent
            - When you modify content, be mindful of the attribution history
            - When producing response, keep the attribution out of the response. Attribution is there only to help you understand the timeline of when facts were recorded.
            
            For example, if you see "Project deadline is June 30 [Sarah (human) • 1 day ago]", that's likely authoritative information from a recent human update that should be preserved.
            """

        out = f"""Do your thing.

        Current user: Dynamic User (will be updated)

        Meeting metadata: {json.dumps(recurrence, indent=4, default=str)}
        Meeting started at: {relative_tz_dt_from_unix(timestamps["started_at"], tz)}
        Meeting ended at: {relative_tz_dt_from_unix(timestamps["ended_at"], tz)}
        Meeting duration: {duration_str_from_secs(timestamps["ended_at"] - timestamps["started_at"])}
        Meeting organizer/host: {recurrence_user_names["organizer_name"]}
        Meeting participants: {recurrence_user_names["participant_names"]}
        Meeting transcript: {transcript}

        {document_type_context}
        Current document content with attribution:
        {document_with_blame}

        {self.get_current_time_prompt()}
        """
        return out

    @activity.defn
    async def get_active_xrays_no_digests(
        self, owner_id: int
    ) -> List[XRayResponse.XRay]:
        """Get all X-Rays for a user."""
        xrays, _, _ = await self.xray_service.get_active_xrays_no_digests_paginated(
            user_id=owner_id,
            limit=1000,  # Get all for now
            skip=0,
        )
        return xrays

    @activity.defn
    async def get_xrays_with_scope(
        self, owner_id: int, scope: Optional[str] = None
    ) -> List[XRayResponse.XRay]:
        """Get X-Rays with specific scope filter."""
        xrays, _, _ = await self.xray_service.get_xrays_paginated(
            user_id=owner_id, limit=1000, skip=0
        )

        if scope:
            filtered_xrays = [xray for xray in xrays if xray.scope == scope]
        else:
            filtered_xrays = xrays

        return filtered_xrays

    @activity.defn
    async def create_commit_and_update_blame(
        self,
        doc_id: int,
        content: str,
        author_id: int,
        recurrence_id: Optional[int] = None,
        is_ai_generated: bool = False,
    ) -> Tuple[XRay, XRayDocumentCommit]:
        """
        Create a new commit for a document using the git-blame system.

        This replaces the old revision-based system with a more granular tracking system.

        Parameters:
        - doc_id: The ID of the document
        - content: The new content of the document
        - author_id: The ID of the user/owner creating the commit
        - recurrence_id: Optional ID of the meeting this commit is associated with
        - is_ai_generated: Whether this content was generated by AI
        """
        # Get document to verify it exists
        doc = await self.get_xray_by_id(doc_id)
        if not doc:
            raise ValueError(f"Document with ID {doc_id} not found")

        # Use AI_AUTHOR_ID if the content was AI-generated
        effective_author_id = AI_AUTHOR_ID if is_ai_generated else author_id

        # Create commit and update X-Ray
        commit = await self.xray_service.create_commit_and_update_xray(
            xray_id=doc_id,
            content=content,
            author_id=effective_author_id,
            recurrence_id=recurrence_id,
        )

        # Get the updated X-Ray
        result = await self.xray_service.get_xray_with_current_commit(doc_id)
        if not result:
            raise ValueError(f"X-Ray with ID {doc_id} not found, after commit creation")

        return result[0], commit

    @activity.defn
    async def quick_scan(
        self, params: XRayQuickScanActivityParams
    ) -> XRayQuickScanResult:
        response = await xray_openai_client.beta.chat.completions.parse(
            model=settings.openai_xray_model,
            messages=[
                {
                    "role": "system",
                    "content": XRayPrompts.get_quick_scan_system_prompt(),
                },
                {
                    "role": "user",
                    "content": self._get_quick_scan_user_prompt(
                        transcript=params.transcript, xrays=params.xrays
                    ),
                },
            ],
            response_format=XRayQuickScanResult,
        )

        return XRayQuickScanResult(
            **json.loads(response.choices[0].message.content or "{}")
        )

    @activity.defn
    async def deep_scan(self, params: XRayDeepScanParams) -> XRayDeepScanActivityResult:
        print("deep scan called", params)
        recurrence = await self.get_recurrence_by_id(params.recurrence_id)
        print(f"recurrence: {recurrence}")

        # Abort if no recurrence is found
        if not recurrence:
            return XRayDeepScanActivityResult(
                success=False,
                changed=False,
                message="recurrence not found",
                prev_commit_content="",
                new_commit_content="",
            )

        # Get xray
        retrieved_xray = await self.get_xray_with_commit(params.xray.id)
        print(f"doc_result: {retrieved_xray}")

        # Xray to model
        doc_model = (
            xray_to_xray_model(retrieved_xray[0])
            if retrieved_xray and retrieved_xray[0]
            else None
        )

        if not doc_model:
            return XRayDeepScanActivityResult(
                success=False,
                changed=False,
                message="xray not found",
                prev_commit_content="",
                new_commit_content="",
            )

        # Get xray doc with blame information for enhanced context
        document_content = ""
        blame_info = {}

        # Get document content and blame info if available
        if retrieved_xray and retrieved_xray[0]:
            content, blame_data = await self.xray_service.get_xray_content_with_blame(
                retrieved_xray[0].id,
            )
            if content:
                document_content = content
                blame_info = blame_data

        participants, _ = await get_recurrences_participants_by_recurrence_ids(
            recurrence_ids=[params.recurrence_id], mars_pool=self.mars_db
        )

        # Process document based on its type
        if doc_model and doc_model.xray_type:
            if doc_model.xray_type == XRay.XRayType.Build.value:
                return await self._process_build_document(
                    params,
                    recurrence,
                    participants,
                    doc_model,
                    document_content,
                    blame_info,
                )
            elif doc_model.xray_type == XRay.XRayType.Monitor.value:
                return await self._process_monitor_document(
                    params,
                    recurrence,
                    participants,
                    doc_model,
                    document_content,
                    blame_info,
                )
            elif doc_model.xray_type == XRay.XRayType.Digest.value:
                return await self._process_digest_document(
                    params,
                    recurrence,
                    participants,
                    doc_model,
                    document_content,
                    blame_info,
                )

        # Default to build behavior
        return await self._process_build_document(
            params, recurrence, participants, doc_model, document_content, blame_info
        )

    async def _process_build_document(
        self,
        params: XRayDeepScanParams,
        recurrence: SessionRecurrence,
        participants: List[Participant],
        doc_model: XRayModel,
        document_content: str,
        blame_info: Dict[int, Dict[str, Any]],
    ) -> XRayDeepScanActivityResult:
        st = time.perf_counter()
        """Process Build document type - update existing content intelligently."""

        # Run deep scan / new doc content
        deep_scan_oai_resp = await xray_openai_client.beta.chat.completions.parse(
            model=settings.openai_xray_model,
            messages=[
                {
                    "role": "system",
                    "content": XRayPrompts.get_build_doc_system_prompt(),
                },
                {
                    "role": "user",
                    "content": self._get_deep_scan_user_prompt(
                        transcript=params.transcript,
                        recurrence=recurrence,
                        participants=participants,
                        xray_doc=doc_model,
                        document_content=document_content,
                        blame_info=blame_info,
                    ),
                },
            ],
            response_format=XRayDeepScanLLMResult,
        )

        # Parse deep scan result
        parsed_result = XRayDeepScanLLMResult(
            **json.loads(deep_scan_oai_resp.choices[0].message.content or "{}")
        )

        # If we didn't get a new doc content, abort
        prev_content = document_content or ""
        if not parsed_result or not parsed_result.new_doc_commit_content:
            msg = "no relevant information found"
            return XRayDeepScanActivityResult(
                changed=False,
                success=False,
                message=msg,
                prev_commit_content=prev_content,
                new_commit_content=prev_content,
            )

        # Create commit and blame lines
        _, commit = await self.create_commit_and_update_blame(
            doc_id=params.xray.id,
            content=parsed_result.new_doc_commit_content,
            author_id=doc_model.owner_id,
            recurrence_id=params.recurrence_id,
            is_ai_generated=True,
        )

        # Generate smart notifications
        timestamps = get_past_recurrence_timestamps(recurrence)
        meeting_duration = duration_str_from_secs(
            timestamps["ended_at"] - timestamps["started_at"]
        )
        participant_names = [user_or_guest_name(p) for p in participants]

        notifications = await self.create_document_notifications(
            commit_id=commit.id,
            doc_title=doc_model.title,
            prev_content=prev_content,
            new_content=parsed_result.new_doc_commit_content,
            transcript=params.transcript,
            participants=participant_names,
            meeting_title=recurrence.get("title", "Meeting"),
            meeting_duration=meeting_duration,
            user_id=doc_model.owner_id,
            recurrence_id=params.recurrence_id,
        )

        activity.logger.info(
            f"Generated {len(notifications)} notifications for doc {params.xray.id}"
        )

        msg = f"document successfully updated. commit successfully created. doc_id: {params.xray.id}, commit_id: {commit.id}"
        activity.logger.info(f"deep scan build doc time: {time.perf_counter() - st}")
        return XRayDeepScanActivityResult(
            success=True,
            changed=True,
            message=msg,
            prev_commit_content=prev_content,
            new_commit_content=parsed_result.new_doc_commit_content,
        )

    async def _process_monitor_document(
        self,
        params: XRayDeepScanParams,
        recurrence: SessionRecurrence,
        participants: List[Participant],
        doc_model: XRayModel,
        document_content: str,
        blame_info: Dict[int, Dict[str, Any]],
    ) -> XRayDeepScanActivityResult:
        """Process Monitor document type - append new information without modifying existing content."""
        deep_scan_oai_resp = await xray_openai_client.beta.chat.completions.parse(
            model=settings.openai_xray_model,
            messages=[
                {
                    "role": "system",
                    "content": XRayPrompts.get_monitor_doc_system_prompt(),
                },
                {
                    "role": "user",
                    "content": self._get_deep_scan_user_prompt(
                        transcript=params.transcript,
                        recurrence=recurrence,
                        participants=participants,
                        xray_doc=doc_model,
                        document_content=document_content,
                        blame_info=blame_info,
                    ),
                },
            ],
            response_format=XRayDeepScanLLMResult,
        )

        parsed_result = XRayDeepScanLLMResult(
            **json.loads(deep_scan_oai_resp.choices[0].message.content or "{}")
        )

        prev_content = document_content or ""

        if not parsed_result or not parsed_result.new_doc_commit_content:
            msg = "no relevant information found"
            return XRayDeepScanActivityResult(
                changed=False,
                success=False,
                message=msg,
                prev_commit_content=prev_content,
                new_commit_content=prev_content,
            )

        # For Monitor type, the new content should be appended to existing content
        if prev_content:
            # Only add a separator if there's existing content
            new_content = f"{prev_content}\n\n--- Update from meeting {params.recurrence_id} on {datetime.now().strftime('%Y-%m-%d')} ---\n\n{parsed_result.new_doc_commit_content}"
        else:
            new_content = parsed_result.new_doc_commit_content

        _, commit = await self.create_commit_and_update_blame(
            doc_id=params.xray.id,
            content=new_content,
            author_id=doc_model.owner_id,
            recurrence_id=params.recurrence_id,
            is_ai_generated=True,
        )

        # Generate smart notifications
        timestamps = get_past_recurrence_timestamps(recurrence)
        meeting_duration = duration_str_from_secs(
            timestamps["ended_at"] - timestamps["started_at"]
        )
        participant_names = [user_or_guest_name(p) for p in participants]

        notifications = await self.create_document_notifications(
            commit_id=commit.id,
            doc_title=doc_model.title,
            prev_content=prev_content,
            new_content=new_content,
            transcript=params.transcript,
            participants=participant_names,
            meeting_title=recurrence.get("title", "Meeting"),
            meeting_duration=meeting_duration,
            user_id=doc_model.owner_id,
            recurrence_id=params.recurrence_id,
        )

        activity.logger.info(
            f"Generated {len(notifications)} notifications for doc {params.xray.id}"
        )

        msg = f"monitor document successfully updated. commit successfully created. doc_id: {params.xray.id}, commit_id: {commit.id}"
        return XRayDeepScanActivityResult(
            success=True,
            changed=True,
            message=msg,
            prev_commit_content=prev_content,
            new_commit_content=new_content,
        )

    async def _process_digest_document(
        self,
        params: XRayDeepScanParams,
        recurrence: SessionRecurrence,
        participants: List[Participant],
        doc_model: XRayModel,
        document_content: str,
        blame_info: Dict[int, Dict[str, Any]],
    ) -> XRayDeepScanActivityResult:
        """Process Digest document type - create periodic summaries."""
        deep_scan_oai_resp = await xray_openai_client.beta.chat.completions.parse(
            model=settings.openai_xray_model,
            messages=[
                {
                    "role": "system",
                    "content": XRayPrompts.get_digest_doc_system_prompt(),
                },
                {
                    "role": "user",
                    "content": self._get_deep_scan_user_prompt(
                        transcript=params.transcript,
                        recurrence=recurrence,
                        participants=participants,
                        xray_doc=doc_model,
                        document_content=document_content,
                        blame_info=blame_info,
                    ),
                },
            ],
            response_format=XRayDeepScanLLMResult,
        )

        parsed_result = XRayDeepScanLLMResult(
            **json.loads(deep_scan_oai_resp.choices[0].message.content or "{}")
        )

        prev_content = document_content or ""

        if not parsed_result or not parsed_result.new_doc_commit_content:
            msg = "no relevant information found for digest"
            return XRayDeepScanActivityResult(
                changed=False,
                success=False,
                message=msg,
                prev_commit_content=prev_content,
                new_commit_content=prev_content,
            )

        # For Digest type, the content is completely replaced with the new digest
        new_content = parsed_result.new_doc_commit_content

        _, commit = await self.create_commit_and_update_blame(
            doc_id=params.xray.id,
            content=new_content,
            author_id=doc_model.owner_id,
            recurrence_id=params.recurrence_id,
            is_ai_generated=True,
        )

        # Generate smart notifications
        timestamps = get_past_recurrence_timestamps(recurrence)
        meeting_duration = duration_str_from_secs(
            timestamps["ended_at"] - timestamps["started_at"]
        )
        participant_names = [user_or_guest_name(p) for p in participants]

        notifications = await self.create_document_notifications(
            commit_id=commit.id,
            doc_title=doc_model.title,
            prev_content=prev_content,
            new_content=new_content,
            transcript=params.transcript,
            participants=participant_names,
            meeting_title=recurrence.get("title", "Meeting"),
            meeting_duration=meeting_duration,
            user_id=doc_model.owner_id,
            recurrence_id=params.recurrence_id,
        )

        activity.logger.info(
            f"Generated {len(notifications)} notifications for doc {params.xray.id}"
        )

        msg = f"digest document successfully updated. commit successfully created. doc_id: {params.xray.id}, commit_id: {commit.id}"
        return XRayDeepScanActivityResult(
            success=True,
            changed=True,
            message=msg,
            prev_commit_content=prev_content,
            new_commit_content=new_content,
        )

    @activity.defn
    async def get_transcript_by_recurrence_id(
        self, recurrence_id: int
    ) -> Optional[str]:
        transcript = await get_recurrence_transcript_paragraphs(
            recurrence_id=recurrence_id, luxor_pool=self.luxor_db
        )
        if not transcript or not transcript.get("full_text"):
            return ""

        return transcript["full_text"]

    @activity.defn
    async def get_recurrence_by_id(
        self, recurrence_id: int
    ) -> Optional[SessionRecurrence]:
        return await get_recurrence(recurrence_id, self.mars_db)

    @activity.defn
    async def get_xray_by_id(self, doc_id: int) -> XRayModel | None:
        result = await self.xray_service.get_xray_with_current_commit(doc_id)
        return xray_to_xray_model(result[0]) if result and result[0] else None

    @activity.defn
    async def get_past_team_visible_recurrences_by_team_id(
        self, team_id: int, limit: int = 5, offset: int = 0
    ) -> List[SessionRecurrence]:
        team_recurrences = await get_past_team_visible_session_recurrences_by_team_id(
            team_id=team_id, limit=limit, offset=offset, mars_pool=self.mars_db
        )
        return team_recurrences

    @activity.defn
    async def get_past_user_session_recurrences_by_user_id(
        self, user_id: int, limit: int = 5, offset: int = 0
    ) -> List[SessionRecurrence]:
        user_recurrences = await get_past_user_session_recurrences_by_user_ids(
            user_ids=[user_id], limit=limit, offset=offset, mars_pool=self.mars_db
        )
        return user_recurrences

    @activity.defn
    async def get_xray_with_commit(
        self, doc_id: int
    ) -> Tuple[Optional[XRay], Optional[XRayDocumentCommit]]:
        result = await self.xray_service.get_xray_with_current_commit(doc_id)
        return result if result else (None, None)

    @activity.defn
    async def get_doc_commits(
        self, doc_id: int, limit: int = 50, offset: int = 0
    ) -> List[XRayDocumentCommit]:
        """Get all commits for a document, ordered by creation date (newest first)."""
        try:
            commits = (
                await XRayDocumentCommit.objects()
                .where(XRayDocumentCommit.xray_id == doc_id)
                .order_by(XRayDocumentCommit.created_at, ascending=False)
                .limit(limit)
                .offset(offset)
            )
            return commits
        except Exception as e:
            activity.logger.error(f"Error getting commits for doc {doc_id}: {e}")
            return []

    @activity.defn
    async def get_doc_commit_by_id(
        self, commit_id: int
    ) -> Optional[XRayDocumentCommit]:
        """Get a specific commit by ID."""
        try:
            return (
                await XRayDocumentCommit.objects()
                .where(XRayDocumentCommit.id == commit_id)
                .first()
            )
        except Exception as e:
            activity.logger.error(f"Error getting commit {commit_id}: {e}")
            return None

    @activity.defn
    async def get_doc_notifications(
        self, user_id: int, limit: int = 30, offset: int = 0
    ) -> Tuple[List[XRayDocumentCommitNotification], int]:
        """Get notifications for a user with pagination."""
        try:
            notifications = await (
                XRayDocumentCommitNotification.objects()
                .where(XRayDocumentCommitNotification.user_id == user_id)
                .limit(limit)
                .offset(offset)
            )

            total_count = await XRayDocumentCommitNotification.count().where(
                XRayDocumentCommitNotification.user_id == user_id
            )
            return notifications, total_count

        except Exception as e:
            activity.logger.error(
                f"Error getting notifications for user {user_id}: {e}"
            )
            return [], 0

    @activity.defn
    async def generate_scheduled_digest_content(
        self, params: GenerateScheduledDigestContentInput
    ) -> str | None:
        """Generate digest content from multiple recent meetings for scheduled digest X-Rays"""
        try:
            # Get the X-Ray
            xray = await self.get_xray_by_id(params.xray_id)
            if not xray:
                raise ValueError(f"X-Ray {params.xray_id} not found")

            if xray.xray_type != "digest":
                raise ValueError(f"X-Ray {params.xray_id} is not a digest type")

            unix_ts = xray.last_digest_at or int(datetime.now().timestamp())

            # Get past meetings for the time period (default to last 7 days for now)
            # TODO: Calculate proper time range based on cron expression frequency
            past_meetings = (
                await get_past_user_session_recurrences_since_unix_ts_by_user_ids(
                    user_ids=[xray.owner_id],
                    unix_ts=unix_ts,
                    limit=100,  # This probably needs to be handled better
                    offset=0,
                    mars_pool=self.mars_db,
                )
            )

            if params.team_id:
                past_meetings.extend(
                    await get_past_team_visible_session_recurrences_since_unix_ts_by_team_id(
                        team_id=params.team_id,
                        unix_ts=unix_ts,
                        limit=100,  # This probably needs to be handled better
                        offset=0,
                        mars_pool=self.mars_db,
                    )
                )

            if not past_meetings:
                return "No recent meetings found for digest generation."

            # Sort meetings by recency (most recent first) for better temporal context
            def get_meeting_timestamp(meeting: SessionRecurrence):
                timestamps = get_past_recurrence_timestamps(meeting)
                # Use actual start time, fall back to planned start time, then created time
                return (
                    timestamps.get("ended_at", 0)
                    or meeting.get("started_at", 0)
                    or meeting.get("startTimestamp")
                    or meeting.get("createdAt")
                )

            sorted_meetings = sorted(
                past_meetings, key=get_meeting_timestamp, reverse=True
            )

            # Aggregate ALL meetings from the time period with timestamps
            all_transcripts: List[str] = []
            all_participants: List[Participant] = []
            meeting_titles: List[str] = []

            current_time = datetime.now().timestamp()

            for meeting in sorted_meetings:
                recurrence_id = meeting.get("recurrenceID", 0)

                # Get transcript for this meeting
                transcript = await self.get_transcript_by_recurrence_id(recurrence_id)
                if transcript:
                    meeting_title = meeting.get("title", "Unknown Meeting")
                    meeting_titles.append(meeting_title)

                    # Calculate relative time for this meeting
                    meeting_timestamp = get_meeting_timestamp(meeting)
                    time_diff_seconds = current_time - meeting_timestamp

                    # Format relative time
                    if time_diff_seconds < 3600:  # Less than 1 hour
                        minutes = int(time_diff_seconds // 60)
                        relative_time = (
                            f"{minutes} minute{'s' if minutes != 1 else ''} ago"
                        )
                    elif time_diff_seconds < 86400:  # Less than 1 day
                        hours = int(time_diff_seconds // 3600)
                        relative_time = f"{hours} hour{'s' if hours != 1 else ''} ago"
                    else:  # 1 day or more
                        days = int(time_diff_seconds // 86400)
                        relative_time = f"{days} day{'s' if days != 1 else ''} ago"

                    # Add timestamp to section header
                    section_header = f"=== {meeting_title} ({relative_time}) ==="
                    all_transcripts.append(f"{section_header}\n{transcript}")

                # Collect participants (keep original Participant objects)
                participants, _ = await get_recurrences_participants_by_recurrence_ids(
                    recurrence_ids=[recurrence_id], mars_pool=self.mars_db
                )
                all_participants.extend(participants)

            if not all_transcripts:
                return "No transcripts available for recent meetings."

            # Create aggregated input for digest generation
            aggregated_transcript = "\n\n".join(all_transcripts)

            # Deduplicate participants by displayName while preserving type
            seen_names = set()
            unique_participants: List[Participant] = []
            for p in all_participants:
                name = user_or_guest_name(p)
                if name not in seen_names:
                    seen_names.add(name)
                    unique_participants.append(p)

            # Use the most recent meeting for metadata (fallback)
            recent_meeting = sorted_meetings[0]
            recent_recurrence_id = recent_meeting.get("recurrenceID", 0)

            # Get current document content and blame info
            current_xray_with_commit = await self.get_xray_with_commit(params.xray_id)
            document_content = ""
            blame_info = {}

            if current_xray_with_commit and current_xray_with_commit[0]:
                (
                    content,
                    blame_data,
                ) = await self.xray_service.get_xray_content_with_blame(params.xray_id)
                if content:
                    document_content = content
                    blame_info = blame_data

            # Create XRayDeepScanParams with aggregated data
            from nebula.temporal.types import XRayDeepScanParams, XRayQuickScanResult

            params2 = XRayDeepScanParams(
                recurrence_id=recent_recurrence_id,
                transcript=aggregated_transcript,  # Use aggregated transcript from ALL meetings
                xray=xray,
                quick_result_scan=XRayQuickScanResult(
                    xray_ids=[params.xray_id]
                ),  # Dummy quick scan result
            )

            # Reuse existing digest processing logic with aggregated data
            digest_result = await self._process_digest_document(
                params=params2,
                recurrence=recent_meeting,
                participants=unique_participants,  # Use aggregated participants
                doc_model=xray,
                document_content=document_content,
                blame_info=blame_info,
            )

            if digest_result.success and digest_result.new_commit_content:
                return digest_result.new_commit_content
            else:
                return None

        except Exception as e:
            activity.logger.error(
                f"Error generating scheduled digest for X-Ray {params.xray_id}: {e}"
            )
            return None

    @activity.defn
    async def update_xray_last_digest_at(self, xray_id: int) -> bool:
        """Update last_digest_at timestamp for an X-Ray"""
        try:
            from datetime import datetime
            from nebula.db.models.xray import XRay

            await XRay.update({XRay.last_digest_at: datetime.now()}).where(
                XRay.id == xray_id
            )
            activity.logger.info(f"Updated last_digest_at for X-Ray {xray_id}")
            return True

        except Exception as e:
            activity.logger.error(
                f"Error updating last_digest_at for X-Ray {xray_id}: {e}"
            )
            return False

    @activity.defn
    async def create_digest_commit(
        self, params: CreateDigestCommitInput
    ) -> Optional[int]:
        """Create a commit for digest content with simplified parameter passing"""
        try:
            # Create the commit using existing service method
            _, commit = await self.create_commit_and_update_blame(
                doc_id=params.xray_id,
                content=params.digest_content,
                author_id=params.author_id,
                recurrence_id=None,  # No specific recurrence for scheduled digests
                is_ai_generated=True,
            )

            activity.logger.info(
                f"Created digest commit {commit.id} for X-Ray {params.xray_id}"
            )
            return commit.id

        except Exception as e:
            activity.logger.error(f"Error creating digest commit: {e}")
            return None

    def _get_notification_prompt(
        self,
        prev_content: str,
        new_content: str,
        doc_name: str,
        recent_notifications: List[XRayDocumentCommitNotification],
    ) -> str:
        recent_notifications_text = "\n".join(
            [f"- {notif.content}" for notif in recent_notifications]
        )

        return f"""Create a clear, straightforward notification about updates to an X-Ray document.
        Write in a professional but natural tone - like sending a quick update to a colleague.

        Document name: {doc_name}

        Previous document content:
        {prev_content}

        New document content:
        {new_content}

        Recent notifications (DO NOT duplicate these messages or create very similar ones):
        {recent_notifications_text}

        Write a brief message (max 200 characters) that clearly explains what's new in the document.
        Be direct and specific, but use natural language.
        IMPORTANT: Your message should be meaningfully different from the recent notifications shown above.

        Examples of good notifications:
        - Added key points from today's backend planning discussion
        - Updated project timeline based on engineering team's feedback
        - Included new requirements from the client meeting
        - Added notes about the API changes discussed in standup
        - Updated success metrics based on today's strategy meeting
        """

    @activity.defn
    async def create_document_notifications(
        self,
        commit_id: int,
        doc_title: str,
        prev_content: str,
        new_content: str,
        transcript: str,
        participants: List[str],
        meeting_title: str,
        meeting_duration: str,
        user_id: int,
        recurrence_id: int,
    ) -> List[XRayDocumentCommitNotification]:
        """Generate and persist 0-3 smart notifications for document changes"""
        try:
            # Get recent notifications for context
            recent_notifications, _ = await self.get_doc_notifications(
                user_id=user_id, limit=20, offset=0
            )

            # Generate notifications using structured output
            response = await xray_openai_client.beta.chat.completions.parse(
                model=settings.openai_xray_model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_multi_notification_system_prompt(),
                    },
                    {
                        "role": "user",
                        "content": self._get_multi_notification_user_prompt(
                            doc_title,
                            prev_content,
                            new_content,
                            transcript,
                            participants,
                            meeting_title,
                            recent_notifications,
                        ),
                    },
                ],
                response_format=XRayNotificationsResult,
                max_tokens=500,  # Allow for multiple notifications
                temperature=0.7,
            )

            # Parse the structured result
            result = XRayNotificationsResult(
                **json.loads(response.choices[0].message.content or "{}")
            )

            # Create and save notifications with Python attribution
            notifications: List[XRayDocumentCommitNotification] = []
            for notif_data in result.notifications:
                # Add meeting attribution in Python
                content_with_attribution = (
                    f"{notif_data.content}\n\n{meeting_title} ({meeting_duration})"
                )

                notification = XRayDocumentCommitNotification(
                    user_id=user_id,
                    xray_doc_commit_id=commit_id,
                    title=notif_data.title,
                    content=content_with_attribution,
                    seen=False,
                    source={"type": "meeting", "id": recurrence_id},
                )
                await notification.save()
                notifications.append(notification)

            activity.logger.info(
                f"Generated {len(notifications)} notifications for commit {commit_id}"
            )
            return notifications

        except Exception as e:
            activity.logger.error(
                f"Error creating document notifications for commit {commit_id}: {e}"
            )
            return []

    def _get_multi_notification_system_prompt(self) -> str:
        return """You are a notification system for X-Ray documents - living documents that track important information from meetings.

Your job is to analyze meeting transcripts and document changes to generate 0-3 focused notifications for users.

NOTIFICATIONS ARE USER-FACING: These appear in the user's dashboard and should be meaningful, actionable, and not spammy.

DECISION CRITERIA:
- Generate notifications for SIGNIFICANT events only
- Consider both importance AND timing since last notification
- Important events (deals, decisions, partnerships) should generally get notifications
- Minor updates may not need notifications if recent ones exist
- Maximum 3 notifications per document update (usually 0-1)

OUTPUT FORMAT: JSON object with "notifications" array and optional "reasoning"

Each notification has:
- title: "[Category] — [Key contributors (max 3 people)]"  
- content: "• Key points\n• **Impact:** if relevant\n• **Next steps:** if applicable"

CONTRIBUTOR RULES:
- Only include people who DROVE the specific outcome
- Look for: who proposed, decided, took ownership, provided critical input
- Don't include passive participants
- Max 3 contributors per notification

EXAMPLES:
{
  "notifications": [
    {
      "title": "Deal closed — Dennis Drake, Abe Roder",
      "content": "• SmartTax will onboard two users to Rumi\\n• **Impact:** Won new business and onboarding process is underway\\n• **Next steps:** Prepare and send detailed quote"
    }
  ]
}"""

    def _get_multi_notification_user_prompt(
        self,
        doc_title: str,
        prev_content: str,
        new_content: str,
        transcript: str,
        participants: List[str],
        meeting_title: str,
        recent_notifications: List[XRayDocumentCommitNotification],
    ) -> str:
        # Format recent notifications with timestamps for context
        recent_context = ""
        if recent_notifications:
            recent_context = "RECENT NOTIFICATIONS (consider timing and content):\n"
            for notif in recent_notifications[:10]:  # Last 10 notifications
                days_ago = (datetime.now() - notif.created_at).days
                recent_context += f"- {days_ago} days ago: {notif.title}\n  {notif.content[:100]}...\n"

        return f"""ANALYZE AND GENERATE NOTIFICATIONS

DOCUMENT: {doc_title}
MEETING: {meeting_title}
PARTICIPANTS: {", ".join(participants)}

TRANSCRIPT:
{transcript}

DOCUMENT CHANGES:
Previous content:
{prev_content}

New content:
{new_content}

{recent_context}

TASK: Generate 0-3 notifications for significant events. Consider:
1. What meaningful events happened in this meeting?
2. Are they important enough to notify the user?
3. Has anything similar been notified recently?
4. Who were the key contributors to each event?

Return JSON with "notifications" array. Can be empty if no significant events."""
