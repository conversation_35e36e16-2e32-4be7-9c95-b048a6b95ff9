# RecallaiMeetingMetadata


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**title** | **str** |  | 
**zoom_meeting_uuid** | **str** |  | 

## Example

```python
from elio_client.models.recallai_meeting_metadata import RecallaiMeetingMetadata

# TODO update the JSON string below
json = "{}"
# create an instance of RecallaiMeetingMetadata from a JSON string
recallai_meeting_metadata_instance = RecallaiMeetingMetadata.from_json(json)
# print the JSON string representation of the object
print(RecallaiMeetingMetadata.to_json())

# convert the object into a dict
recallai_meeting_metadata_dict = recallai_meeting_metadata_instance.to_dict()
# create an instance of RecallaiMeetingMetadata from a dict
recallai_meeting_metadata_from_dict = RecallaiMeetingMetadata.from_dict(recallai_meeting_metadata_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


