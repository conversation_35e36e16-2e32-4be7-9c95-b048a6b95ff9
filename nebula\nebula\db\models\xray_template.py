from enum import Enum

from piccolo.columns import BigSerial, Text, Timestamp, BigInt
from piccolo.columns.defaults.timestamp import TimestampNow
from piccolo.columns.indexes import IndexMethod
from piccolo.table import Table


class XRayTemplate(Table, tablename="xray_templates"):
    id = BigSerial(primary_key=True)
    owner_id = BigInt(
        null=True
    )  # Null when it's a Rumi suggested template, populated when shared by the user. Owner is the user who shared the template.

    class XRayType(str, Enum):
        Build = "build"  # Original xray type - edits and updates content - "fusion"
        Monitor = "monitor"  # Tracks specific facts/events, only appends updates to the document. The document itself is not shown on the frontend, only notifications
        Digest = "digest"  # Provides periodic summaries

    xray_type = Text(
        choices=XRayType,
        null=False,
        default=XRayType.Build,
        index=True,
        index_method=IndexMethod.btree,
    )

    # Copied means that column is copied from the X-Ray at the moment of sharing.
    icon = Text(null=False)  # Copied
    title = Text(null=False)  # Copied
    description = Text(null=False)  # Copied
    prompt = Text(null=False)  # Copied
    short_summary = Text(null=False)  # Copied

    created_at = Timestamp(default=TimestampNow())
    updated_at = Timestamp(default=TimestampNow())
