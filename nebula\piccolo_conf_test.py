import os
import uuid
from piccolo.conf.apps import AppRegistry
from piccolo.engine.postgres import PostgresEngine

from nebula.constants import NEBULA_TEST_SCHEMA, NEBULA_TEST_DB_NAME
from nebula.settings import settings

# Get unique schema for this test run to ensure isolation from other tests
TEST_SCHEMA_NAME = os.environ.get("POSTGRES_SCHEMA", "test_schema")

print("piccolo_conf_test.py: Loading test configuration")
print(f"Test schema name: {TEST_SCHEMA_NAME}")
print(f"Host: {settings.postgres_host}")
print(f"Database: {settings.postgres_db}")

# Strategy: Use schema isolation instead of separate database
# This works with both local PostgreSQL and remote Neon databases
DB = PostgresEngine(
    config={
        "database": settings.postgres_db,  # Use the main database
        "user": settings.postgres_user,
        "password": settings.postgres_pass,
        "host": settings.postgres_host,
        "port": settings.postgres_port,
        "server_settings": {
            "search_path": TEST_SCHEMA_NAME
        },  # Use the dynamic schema name
    }
)

print(f"piccolo_conf_test.py: DB engine created with schema: {TEST_SCHEMA_NAME}")

# Export for use in fixtures
TEST_SCHEMA = TEST_SCHEMA_NAME

APP_REGISTRY = AppRegistry(
    apps=["nebula.db.app_conf"],
)
