# coding: utf-8

"""
    API for elio-bzw2

    Generated by encore

    The version of the OpenAPI document: 1
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


import unittest

from elio_client.models.post_meetings_upsert_meeting_suggestions_api_request import POSTMeetingsUpsertMeetingSuggestionsAPIRequest

class TestPOSTMeetingsUpsertMeetingSuggestionsAPIRequest(unittest.TestCase):
    """POSTMeetingsUpsertMeetingSuggestionsAPIRequest unit test stubs"""

    def setUp(self):
        pass

    def tearDown(self):
        pass

    def make_instance(self, include_optional) -> POSTMeetingsUpsertMeetingSuggestionsAPIRequest:
        """Test POSTMeetingsUpsertMeetingSuggestionsAPIRequest
            include_optional is a boolean, when False only required
            params are included, when True both required and
            optional params are included """
        # uncomment below to create an instance of `POSTMeetingsUpsertMeetingSuggestionsAPIRequest`
        """
        model = POSTMeetingsUpsertMeetingSuggestionsAPIRequest()
        if include_optional:
            return POSTMeetingsUpsertMeetingSuggestionsAPIRequest(
                recurrence_id = '',
                session_id = '',
                suggestions = [
                    elio_client.models.api/meeting_suggestion_input.api.MeetingSuggestionInput(
                        category = '', 
                        prompt = '', 
                        user_id = '', )
                    ]
            )
        else:
            return POSTMeetingsUpsertMeetingSuggestionsAPIRequest(
                recurrence_id = '',
                session_id = '',
                suggestions = [
                    elio_client.models.api/meeting_suggestion_input.api.MeetingSuggestionInput(
                        category = '', 
                        prompt = '', 
                        user_id = '', )
                    ],
        )
        """

    def testPOSTMeetingsUpsertMeetingSuggestionsAPIRequest(self):
        """Test POSTMeetingsUpsertMeetingSuggestionsAPIRequest"""
        # inst_req_only = self.make_instance(include_optional=False)
        # inst_req_and_optional = self.make_instance(include_optional=True)

if __name__ == '__main__':
    unittest.main()
