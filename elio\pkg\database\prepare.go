package database

import (
	"context"
	"database/sql"
	"strings"
	"testing"

	"encore.app/pkg/metrics"
	"encore.dev"

	"encoding/json"
	"fmt"

	"encore.dev/rlog"
	"github.com/jmoiron/sqlx"
	"github.com/stretchr/testify/require"
)

type CanValidate interface {
	Validate(ctx context.Context, db *sqlx.DB, name string) error
	GetQueryString() string
}

type Statements []CanValidate

// cleanQuery makes the query a bit more readable in logs
func cleanQuery(query string) string {
	qry := strings.ReplaceAll(query, "\n", " ")
	qry = strings.ReplaceAll(qry, "\t", "")
	qry = strings.ReplaceAll(qry, "  ", " ")
	qry = strings.ReplaceAll(qry, "\"", "'")
	qry = strings.Trim(qry, " ")
	return qry
}

func (receiver Statements) ValidateAll(ctx context.Context, db *sqlx.DB, name string) error {
	for _, p := range receiver {
		if err := p.Val<PERSON>te(ctx, db, name); err != nil {
			rlog.Error("Failed to validate statement", "error", err, "query", cleanQuery(p.Get<PERSON>ueryString()), "name", name)
			return err
		}
		// This is very noisy during tests:
		if encore.Meta().Environment.Type != encore.EnvTest {
			rlog.Debug("Validating statement", "query", cleanQuery(p.GetQueryString()), "name", name)
		}
	}
	return nil
}

// ValidatedStmt Syntactic sugar for preparing a statements and wrapping query strings
type ValidatedStmt struct {
	db    *sqlx.DB
	tx    *sqlx.Tx
	query string
	name  string
}

func NewStatement(query string) *ValidatedStmt {
	return &ValidatedStmt{query: query}
}

func (receiver *ValidatedStmt) prepare(ctx context.Context) (*sqlx.NamedStmt, error) {
	return receiver.db.PrepareNamedContext(ctx, receiver.query)
}

func (receiver *ValidatedStmt) InTx(tx *sqlx.Tx) *ValidatedStmt {
	if tx == nil {
		return receiver
	}
	return &ValidatedStmt{tx: tx, query: receiver.query}
}

func (receiver *ValidatedStmt) Validate(ctx context.Context, db *sqlx.DB, name string) error {
	receiver.db = db
	receiver.name = name
	stmt, err := receiver.prepare(ctx)
	if err != nil {
		return err
	}
	return stmt.Close()
}

func (receiver *ValidatedStmt) GetQueryString() string {
	return receiver.query
}

func (receiver *ValidatedStmt) ExecuteNamed(ctx context.Context, arg interface{}) (sql.Result, error) {
	if receiver.tx != nil {
		return receiver.tx.NamedExecContext(ctx, receiver.query, arg)
	}
	return receiver.db.NamedExecContext(ctx, receiver.query, arg)
}

func (receiver *ValidatedStmt) GetValues(ctx context.Context, arg Args, values ...any) error {
	err := sql.ErrNoRows // default to no rows will be overwritten if there are rows
	receiver.Queryx(ctx, arg, func(rows *sqlx.Rows, e error) bool {
		err = e
		if err == nil {
			err = rows.Scan(values...)
		}
		return false
	})
	return err
}

func (receiver *ValidatedStmt) UnsafeGetValues(ctx context.Context, arg Args, values ...any) error {
	err := sql.ErrNoRows // default to no rows will be overwritten if there are rows
	receiver.UnsafeQueryx(ctx, arg, func(rows *sqlx.Rows, e error) bool {
		err = e
		if err == nil {
			err = rows.Scan(values...)
		}
		return false // only one row
	})
	return err
}

func (receiver *ValidatedStmt) GetStruct(ctx context.Context, arg Args, dest interface{}) error {
	err := sql.ErrNoRows // default to no rows will be overwritten if there are rows
	receiver.Queryx(ctx, arg, func(rows *sqlx.Rows, e error) bool {
		err = e
		if err == nil {
			err = rows.StructScan(dest)
		}
		return false // only one row
	})
	return err
}

func (receiver *ValidatedStmt) UnsafeGetStruct(ctx context.Context, arg Args, dest interface{}) error {
	err := sql.ErrNoRows // default to no rows will be overwritten if there are rows
	receiver.UnsafeQueryx(ctx, arg, func(rows *sqlx.Rows, e error) bool {
		err = e
		if err == nil {
			err = rows.StructScan(dest)
		}
		return false // only one row
	})
	return err
}

func (receiver *ValidatedStmt) Execx(ctx context.Context, arg interface{}) ([]string, error) {
	var rows *sqlx.Rows
	var err error
	if receiver.tx != nil {
		rows, err = receiver.tx.NamedQuery(receiver.query, arg)
	} else {
		rows, err = receiver.db.NamedQueryContext(ctx, receiver.query, arg)
	}
	if err != nil {
		return []string{}, err
	}
	defer rows.Close()
	cols, err := rows.Columns()
	if err != nil {
		return []string{}, err
	}
	return cols, nil
}

func (receiver *ValidatedStmt) UnsafeExecx(ctx context.Context, arg interface{}) ([]string, error) {
	var rows *sqlx.Rows
	var err error
	if receiver.tx != nil {
		rows, err = receiver.tx.Unsafe().NamedQuery(receiver.query, arg)
	} else {
		rows, err = receiver.db.Unsafe().NamedQueryContext(ctx, receiver.query, arg)
	}
	if err != nil {
		return []string{}, err
	}
	defer rows.Close()
	cols, err := rows.Columns()
	if err != nil {
		return []string{}, err
	}
	return cols, nil
}

// TODO replace with yield in next go ver
func (receiver *ValidatedStmt) Queryx(ctx context.Context, arg interface{}, yield func(rows *sqlx.Rows, err error) bool) {
	timer := metrics.TimeQuery(receiver.name, receiver.query)
	defer timer.Done()
	var rows *sqlx.Rows
	var err error
	if receiver.tx != nil {
		// Drop context for now
		rows, err = receiver.tx.NamedQuery(receiver.query, arg)
	} else {
		rows, err = receiver.db.NamedQueryContext(ctx, receiver.query, arg)
	}
	if err != nil {
		yield(nil, err)
		return
	}
	defer rows.Close()
	for rows.Next() {
		if !yield(rows, nil) {
			break
		}
	}
}

// TODO replace with yield in next go ver
func (receiver *ValidatedStmt) UnsafeQueryx(ctx context.Context, arg interface{}, yield func(rows *sqlx.Rows, err error) bool) {
	timer := metrics.TimeQuery(receiver.name, receiver.query)
	defer timer.Done()
	var rows *sqlx.Rows
	var err error
	if receiver.tx != nil {
		// Drop context for now
		rows, err = receiver.tx.Unsafe().NamedQuery(receiver.query, arg)
	} else {
		rows, err = receiver.db.Unsafe().NamedQueryContext(ctx, receiver.query, arg)
	}
	if err != nil {
		yield(nil, err)
		return
	}
	defer rows.Close()
	for rows.Next() {
		if !yield(rows, nil) {
			break
		}
	}
}

func AssertXNoOpenConns(t *testing.T, db *sqlx.DB) {
	stats := db.Stats()
	require.Equal(t, 0, stats.InUse, "expected no connections in use")
	// require.Equal(t, 0, stats.MaxOpenConnections, "expected no max open connection")
}

type Args map[string]interface{}

type JSONBRaw []byte

func (j *JSONBRaw) Scan(src interface{}) error {
	if src == nil {
		return nil
	}
	// Check if src is []uint8 (byte slice)
	if data, ok := src.([]byte); ok {
		*j = data
		return nil
	}
	return fmt.Errorf("unsupported type: %T", src)
}

// TODO is there not a build in type for this?
type JSONB map[string]interface{}

func JSONScan(dest any, src interface{}) error {
	if src == nil {
		return nil
	}
	// Check if src is []uint8 (byte slice)
	if data, ok := src.([]byte); ok {
		if len(data) == 0 {
			return nil
		}
		return json.Unmarshal(data, dest)
	}
	return fmt.Errorf("unsupported type: %T", src)
}

func (j *JSONB) Scan(src interface{}) error {
	return JSONScan(j, src)
}

type JSONBArray []JSONB

func (j *JSONBArray) Scan(src interface{}) error {
	return JSONScan(j, src)
}
