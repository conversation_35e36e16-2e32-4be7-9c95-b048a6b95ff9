import json
import time
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

from nebula.web.api.search.mm_sse_collector import MMRagEventCollector
import pytest
from fastapi import Request, Header
from fastapi.encoders import jsonable_encoder
from starlette import status

from nebula.services.search.common import AskAIBody
from nebula.web.api.search.views import (
    mm_non_streaming,
    ask_ai_non_streaming,
)


@pytest.mark.asyncio
async def test_response_collector():
    # Initialize collector
    collector = MMRagEventCollector()

    # Test initial state
    assert collector.content == ""
    assert collector.sources == []
    assert not collector.complete
    assert collector.error is None
    assert collector.code == 200

    # Test text_generation_delta message
    data_message = {
        "type": "http.response.body",
        "body": b'data: {"type":"text_generation_delta","data":{"content":"Hello, world!"}}',
    }
    await collector.collect(data_message)
    assert collector.content == "Hello, world!"
    assert not collector.complete

    # Test sources_updated message
    sources_message = {
        "type": "http.response.body",
        "body": b'data: {"type":"sources_updated","data":{"sources":[{"recurrenceId":123}]}}',
    }
    await collector.collect(sources_message)
    assert collector.content == "Hello, world!"
    assert collector.sources == [{"recurrenceId": 123}]
    assert not collector.complete

    # Test stream_ended message
    end_message = {
        "type": "http.response.body",
        "body": b'data: {"type":"stream_ended"}',
    }
    await collector.collect(end_message)
    assert collector.content == "Hello, world!"
    assert collector.sources == [{"recurrenceId": 123}]
    assert collector.complete

    # Test handling invalid message
    invalid_message = {
        "type": "http.response.body",
        "body": b"invalid json",
    }
    await collector.collect(invalid_message)
    # Should not affect the existing state
    assert collector.content == "Hello, world!"
    assert collector.sources == [{"recurrenceId": 123}]
    assert collector.complete


@pytest.mark.asyncio
async def test_mm_non_streaming_success():
    test_body = AskAIBody(
        requestId="test-request",
        query="test query",
        threadId="thread-123",
    )
    user_id = "123"

    async def mock_rag(body, send, user_id):
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"text_generation_delta","data":{"content":"Hello"}}',
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"text_generation_delta","data":{"content":" world!"}}',
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"sources_updated","data":{"sources":[{"recurrenceId":123}]}}',
            }
        )
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"stream_ended"}',
            }
        )

    with patch("nebula.web.api.search.sync.rag", new=mock_rag):
        result, status_code = await mm_non_streaming(test_body, user_id)

        assert status_code == 200
        assert result["success"] is True
        assert result["data"]["content"] == "Hello world!"
        assert result["data"]["sources"] == [{"recurrenceId": 123}]
        assert result["message"] == "Success"


@pytest.mark.asyncio
async def test_mm_non_streaming_error():
    test_body = AskAIBody(
        requestId="test-request",
        query="test query",
        threadId="thread-123",
    )
    user_id = "123"

    async def mock_rag_error(body, send, user_id):
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"stream_failed","code":400,"data":{"content":"Bad request"}}',
            }
        )

    with patch("nebula.web.api.search.sync.rag", new=mock_rag_error):
        result, status_code = await mm_non_streaming(test_body, user_id)

        assert status_code == 400
        assert result["success"] is False
        assert result["message"] == "Bad request"
        assert "sources" in result


@pytest.mark.asyncio
async def test_mm_non_streaming_timeout():
    test_body = AskAIBody(
        requestId="test-request",
        query="test query",
        threadId="thread-123",
    )
    user_id = "123"

    async def mock_rag_timeout(body, send, user_id):
        await send(
            {
                "type": "http.response.body",
                "body": b'data: {"type":"text_generation_delta","data":{"content":"Hello"}}',
            }
        )
        await asyncio.sleep(10.0)

    with patch("nebula.web.api.search.sync.rag", new=mock_rag_timeout):
        result, status_code = await mm_non_streaming(test_body, user_id, timeout=0.1)

        assert status_code == 504
        assert result["success"] is False
        assert "timed out" in result["message"].lower()


@pytest.mark.asyncio
async def test_ask_ai_non_streaming_endpoint():
    # Test data
    test_body = AskAIBody(
        requestId="test-request",
        query="test query",
        threadId="thread-123",
    )
    user_id = "user-123"

    # Mock Request
    mock_request = MagicMock(spec=Request)

    # Mock mm_non_streaming function
    mock_result = {
        "success": True,
        "data": {"content": "Hello world!", "sources": [{"recurrenceId": 123}]},
        "message": "Success",
    }

    # Patch the mm_non_streaming function
    with patch(
        "nebula.web.api.search.views.mm_non_streaming",
        new=AsyncMock(return_value=(mock_result, 200)),
    ):
        # Call the endpoint function
        response = await ask_ai_non_streaming(
            req=mock_request, body=test_body, x_user_id=user_id, timeout=60.0
        )

        # Verify the response
        assert response.status_code == 200
        assert json.loads(response.body) == jsonable_encoder(mock_result)


@pytest.mark.asyncio
async def test_ask_ai_non_streaming_endpoint_missing_user_id():
    # Test data
    test_body = AskAIBody(
        requestId="test-request",
        query="test query",
        threadId="thread-123",
    )

    # Mock Request
    mock_request = MagicMock(spec=Request)

    # Call the endpoint function without user_id
    response = await ask_ai_non_streaming(
        req=mock_request, body=test_body, x_user_id=None, timeout=60.0
    )

    # Verify the response
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    response_data = json.loads(response.body)
    assert response_data["message"] == "User id is missing"
    assert response_data["data"] is None


@pytest.mark.asyncio
async def test_ask_ai_non_streaming_endpoint_exception():
    # Test data
    test_body = AskAIBody(
        requestId="test-request",
        query="test query",
        threadId="thread-123",
    )
    user_id = "user-123"

    # Mock Request
    mock_request = MagicMock(spec=Request)

    # Patch mm_non_streaming to raise exception
    with patch(
        "nebula.web.api.search.views.mm_non_streaming",
        new=AsyncMock(side_effect=Exception("Test exception")),
    ):
        # Call the endpoint function
        response = await ask_ai_non_streaming(
            req=mock_request, body=test_body, x_user_id=user_id, timeout=60.0
        )

        # Verify the response
        assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR
        response_data = json.loads(response.body)
        assert "Internal server error" in response_data["message"]
        assert response_data["data"] is None
