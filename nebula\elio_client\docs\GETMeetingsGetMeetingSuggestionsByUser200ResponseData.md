# GETMeetingsGetMeetingSuggestionsByUser200ResponseData


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**suggestions** | [**List[ApiSuggestionResponseDTO]**](ApiSuggestionResponseDTO.md) |  | 
**total** | **int** |  | 

## Example

```python
from elio_client.models.get_meetings_get_meeting_suggestions_by_user200_response_data import GETMeetingsGetMeetingSuggestionsByUser200ResponseData

# TODO update the JSON string below
json = "{}"
# create an instance of GETMeetingsGetMeetingSuggestionsByUser200ResponseData from a JSON string
get_meetings_get_meeting_suggestions_by_user200_response_data_instance = GETMeetingsGetMeetingSuggestionsByUser200ResponseData.from_json(json)
# print the JSON string representation of the object
print(GETMeetingsGetMeetingSuggestionsByUser200ResponseData.to_json())

# convert the object into a dict
get_meetings_get_meeting_suggestions_by_user200_response_data_dict = get_meetings_get_meeting_suggestions_by_user200_response_data_instance.to_dict()
# create an instance of GETMeetingsGetMeetingSuggestionsByUser200ResponseData from a dict
get_meetings_get_meeting_suggestions_by_user200_response_data_from_dict = GETMeetingsGetMeetingSuggestionsByUser200ResponseData.from_dict(get_meetings_get_meeting_suggestions_by_user200_response_data_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


