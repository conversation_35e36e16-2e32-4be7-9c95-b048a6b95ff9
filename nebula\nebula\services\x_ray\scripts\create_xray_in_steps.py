import requests

# These are some scripts to help with testing and development.
# Nebula FastAPI server has to be up and running first.


NEBULA_BASE_URL = "http://localhost:3006"


def create_xray_in_steps():
    user_id = "1053583008122013541"  # <PERSON><PERSON><PERSON>'s user id

    # Step 1 inputs
    xray_description = "I want to know everything about the X-Ray project. Deadlines, UX concerns, decisions made, how customers are reacting. Everything."

    # Step 1
    url = f"{NEBULA_BASE_URL}/v1.0/x-ray/create/step1"
    print("Step 1 URL", url)

    resp = requests.post(
        url,
        headers={
            "x-user-id": user_id,
        },
        json={
            "description": xray_description,
        },
    )

    resp_body = resp.json()
    data = resp_body.get("data", None)
    if not data:
        print("Step 1 response body:", resp_body)
        raise Exception("No data in step1 response")

    # Step 2 inputs
    xray_type = data.get("xrayType", None)
    prompt = data.get("prompt", None)

    if not xray_type or not prompt:
        raise Exception("No xray_type or prompt in response")

    print("Step 1 X-Ray type:", xray_type)
    print("Step 1 X-Ray prompt:", prompt)
    print("Proceeding to step 2")

    # Step 2
    url = f"{NEBULA_BASE_URL}/v1.0/x-ray/create/step2"
    print("Step 2 URL", url)

    resp = requests.post(
        url,
        headers={
            "x-user-id": user_id,
        },
        json={"xrayType": xray_type, "prompt": prompt},
    )

    resp_body = resp.json()
    data = resp_body.get("data", None)
    if not data:
        raise Exception("No data in step2 response")

    title = data.get("title", None)
    emoji = data.get("emoji", None)
    short_summary = data.get("shortSummary", None)

    if not title or not emoji or not short_summary:
        print("Step 2 response", resp_body)
        raise Exception("No title, emoji, or short_summary in step2 response")

    print("Step 2 X-Ray title:", title)
    print("Step 2 X-Ray emoji:", emoji)
    print("Step 2 X-Ray short summary:", short_summary)
    print("Proceeding to step 3")

    # Step 3
    url = f"{NEBULA_BASE_URL}/v1.0/x-ray/create/step3"
    print("Step 3 URL", url)

    resp = requests.post(
        url,
        headers={
            "x-user-id": user_id,
        },
        json={
            "description": xray_description,
            "xrayType": xray_type,
            "prompt": prompt,
            "title": title,
            "emoji": emoji,
            "shortSummary": short_summary,
        },
    )

    resp_body = resp.json()
    print("Step 3 resp body:", resp_body)
    data = resp_body.get("data", None)
    if not data:
        print("Step 3 response body:", resp_body)
        raise Exception("No data in step3 response")

    print("Step 3 X-Ray created:", data)


if __name__ == "__main__":
    create_xray_in_steps()
