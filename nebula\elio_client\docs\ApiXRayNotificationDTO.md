# ApiXRayNotificationDTO


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**content** | **str** |  | 
**created_at** | **int** |  | 
**id** | **int** |  | 
**seen** | **bool** |  | 
**source** | **object** |  | 
**updated_at** | **int** |  | 
**user_id** | **int** |  | 
**xray_doc_commit_id** | **int** |  | 

## Example

```python
from elio_client.models.api_x_ray_notification_dto import ApiXRayNotificationDTO

# TODO update the JSON string below
json = "{}"
# create an instance of ApiXRayNotificationDTO from a JSON string
api_x_ray_notification_dto_instance = ApiXRayNotificationDTO.from_json(json)
# print the JSON string representation of the object
print(ApiXRayNotificationDTO.to_json())

# convert the object into a dict
api_x_ray_notification_dto_dict = api_x_ray_notification_dto_instance.to_dict()
# create an instance of ApiXRayNotificationDTO from a dict
api_x_ray_notification_dto_from_dict = ApiXRayNotificationDTO.from_dict(api_x_ray_notification_dto_dict)
```
[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)


