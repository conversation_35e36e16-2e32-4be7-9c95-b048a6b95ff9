from temporalio import workflow

with workflow.unsafe.imports_passed_through():
    from typing import List
    from datetime import timedelta
    from nebula.temporal.activities.user_act import UserActivities
    from nebula.temporal.types import XRayBackfillScanWorkflowResult
    from nebula.services.search.common import SessionRecurrence
    from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
    from nebula.temporal.types import XRayBackfillScanWorkflowParams
    from nebula.temporal.activities.xray_act import XRayActivities
    from nebula.temporal.types import XRayScanWorkflowParams


@workflow.defn(name="XRayBackfillScan")
class XRayBackfillScanWorkflow:
    """
    This workflow is used to backfill a single X-Ray for a given user and is mosly for testing purposes.
    It will scan past recurrences (personal + team visible) for the user and process them.
    """

    @workflow.run
    async def run(
        self, params: XRayBackfillScanWorkflowParams
    ) -> XRayBackfillScanWorkflowResult:
        limit = 5
        st = workflow.info().start_time
        user = await workflow.execute_activity_method(
            UserActivities.get_user_by_id,
            arg=params.user_id,
            start_to_close_timeout=timedelta(seconds=10),
        )
        if not user:
            raise Exception("User does not exist")

        xray = await workflow.execute_activity_method(
            XRayActivities.get_xray_by_id,
            arg=int(params.xray_id),
            start_to_close_timeout=timedelta(seconds=10),
        )
        if not xray:
            raise Exception("XRay does not exist")

        # Personal + team visible recurrences
        all_user_recurrences: List[SessionRecurrence] = []
        user_team = user.get("team") if user else None
        if xray.visibility == "team" and user_team:
            team_id = user_team.get("id")
            if team_id:
                _team_visible_past_recurrences = await workflow.execute_activity_method(
                    XRayActivities.get_past_team_visible_recurrences_by_team_id,
                    args=(int(team_id), limit, 0),
                    start_to_close_timeout=timedelta(seconds=10),
                )
                _team_visible_past_recurrences.reverse()
                #  TODO: Final list must be unique
                all_user_recurrences += _team_visible_past_recurrences

        personal_past_recurrences = await workflow.execute_activity_method(
            XRayActivities.get_past_user_session_recurrences_by_user_id,
            args=(params.user_id, 5, 0),
            start_to_close_timeout=timedelta(seconds=10),
        )
        personal_past_recurrences.reverse()
        all_user_recurrences += personal_past_recurrences
        initial_len = len(all_user_recurrences)
        processed_len = len(all_user_recurrences)

        for recurrence in all_user_recurrences:
            await workflow.execute_child_workflow(
                XRayScanWorkflow.run,
                arg=XRayScanWorkflowParams(
                    user_id=params.user_id,
                    recurrence_id=recurrence["recurrenceID"],
                ),
                # task_queue=settings.temporal_xray_task_queue,
            )

        return XRayBackfillScanWorkflowResult(
            success=True,
            duration=(workflow.now() - st).total_seconds(),
            initial_len=initial_len,
            processed_len=processed_len,
        )
