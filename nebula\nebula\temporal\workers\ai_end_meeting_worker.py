import asyncio

import logging
import sys
from loguru import logger

from nebula.temporal.activities.recurrence_act import RecurrenceActivities
from nebula.temporal.activities.xray_act import XRayActivities
from nebula.temporal.runner import new_sandbox_runner
from nebula.settings import settings
from nebula.db.mars.prep_stmts import MARS_PREPARED_STMTS
from nebula.db.pool import init_luxor_db_pool, init_mars_db_pool
from nebula.temporal.workflows.ai_end_meeting_wf import AiEndMeetingWorkflow
from nebula.temporal.workflows.metadata_backfill_wf import BackfillMetadataWorkflow
from nebula.temporal.workflows.metadata_wf import MetadataWorkflow
from nebula.temporal.workflows.post_session_summary_wf import PostSessionSummaryWorkflow
from nebula.temporal.workflows.typesense_indexing_wf import TypesenseIndexingWorkflow
from nebula.temporal.workflows.tldr_generation_wf import TLDRGenerationWorkflow
from nebula.temporal.workflows.meeting_suggestions_wf import MeetingSuggestionsWorkflow
from nebula.temporal.workflows.xray_backfill_wf import XRayBackfillScanWorkflow
from nebula.temporal.workflows.xray_digest_wf import XRayDigestWorkflow
from nebula.temporal.workflows.xray_wf import XRayScanWorkflow
from temporalio.worker import Worker, WorkerTuner, PollerBehaviorAutoscaling
from nebula.temporal.client import create_temporal_client
from nebula.temporal.activities.metadata_act import MetadataActivities
from nebula.temporal.activities.post_session_summary_act import (
    PostSessionSummaryActivities,
)
from nebula.temporal.activities.typesense_act import TypesenseActivities
from nebula.temporal.activities.tldr_act import TLDRActivities
from nebula.temporal.activities.meeting_suggestions_act import (
    MeetingSuggestionsActivities,
)
from nebula.temporal.activities.user_act import UserActivities
from nebula.rlogging import InterceptHandler


interrupt_event = asyncio.Event()


async def main():
    logging.basicConfig(handlers=[InterceptHandler()], level=0, force=True)

    # set logs output, level and format
    logger.remove()
    logger.add(
        sys.stderr,
        level=settings.log_level.value,
    )

    logging.info(f"Starting [ai-end-meeting-worker] with temporal queue prefix: [{settings.temporal_ai_end_meeting_queue_with_prefix}]")

    # Connect client
    client = await create_temporal_client()

    mars_db_pool = await init_mars_db_pool(prepared_statements=MARS_PREPARED_STMTS)
    luxor_db_pool = await init_luxor_db_pool()

    # Initialize activities
    metadata_activities = MetadataActivities()
    xray_activities = XRayActivities(mars_db=mars_db_pool, luxor_db=luxor_db_pool)
    post_session_summary_activities = PostSessionSummaryActivities()
    typesense_activities = TypesenseActivities()
    tldr_activities = TLDRActivities()
    meeting_suggestions_activities = MeetingSuggestionsActivities()
    recurrence_activities = RecurrenceActivities(mars_db=mars_db_pool)
    user_activities = UserActivities(mars_db=mars_db_pool)

    tuner = WorkerTuner.create_resource_based(
        target_memory_usage=0.7,  # Use up to 70% of available memory
        target_cpu_usage=0.8,  # Use up to 80% of available CPU
    )

    worker = Worker(
        client,
        task_queue=settings.temporal_ai_end_meeting_queue_with_prefix,
        workflows=[
            AiEndMeetingWorkflow,
            BackfillMetadataWorkflow,
            MetadataWorkflow,
            PostSessionSummaryWorkflow,
            TypesenseIndexingWorkflow,
            TLDRGenerationWorkflow,
            MeetingSuggestionsWorkflow,
            XRayScanWorkflow,
            XRayDigestWorkflow,
            XRayBackfillScanWorkflow,
        ],
        activities=[
            # User activities
            user_activities.get_user_ids_for_xray_scans,
            user_activities.get_user_by_id,
            # Recurrence activities
            recurrence_activities.get_past_ai_enabled_recurrences,
            # Metadata activities
            metadata_activities.get_transcription_batches,
            metadata_activities.get_session_data,
            metadata_activities.create_metadata_record,
            metadata_activities.process_transcript,
            metadata_activities.extract_metadata,
            metadata_activities.get_metadata_record,
            metadata_activities.update_elio_about_metadata_creation,
            # Post session summary activities
            post_session_summary_activities.get_transcription_batches_for_pss,
            post_session_summary_activities.get_session_data_for_pss,
            post_session_summary_activities.process_transcript_for_pss,
            post_session_summary_activities.create_or_update_post_session_summary,
            post_session_summary_activities.generate_post_meeting_summary,
            post_session_summary_activities.get_action_items_for_pss,
            post_session_summary_activities.notify_draconids_summary_ready,
            # Typesense activities
            typesense_activities.get_recurrence_data,
            typesense_activities.process_transcripts_for_typesense,
            typesense_activities.index_meeting_in_typesense,
            # TLDR activities
            tldr_activities.generate_tldr_summary,
            tldr_activities.update_post_session_summary_with_tldr,
            tldr_activities.notify_luxor_tldr_ready,
            # Meeting suggestions activities
            meeting_suggestions_activities.get_all_participants,
            meeting_suggestions_activities.process_transcript_with_user_ids,
            meeting_suggestions_activities.extract_all_users_meeting_suggestions,
            meeting_suggestions_activities.create_all_users_meeting_suggestions_record,
            # XRay activities
            xray_activities.get_active_xrays_no_digests,
            xray_activities.get_xrays_with_scope,
            xray_activities.get_xray_by_id,
            xray_activities.get_xray_with_commit,
            xray_activities.get_doc_commits,
            xray_activities.get_doc_commit_by_id,
            xray_activities.get_doc_notifications,
            xray_activities.get_recurrence_by_id,  # TODO: should go inside recurrence activities 👆
            xray_activities.get_transcript_by_recurrence_id,
            xray_activities.get_past_user_session_recurrences_by_user_id,
            xray_activities.get_past_team_visible_recurrences_by_team_id,
            xray_activities.quick_scan,
            xray_activities.deep_scan,
            xray_activities.create_document_notifications,
            xray_activities.generate_scheduled_digest_content,
            xray_activities.create_digest_commit,
            xray_activities.update_xray_last_digest_at,
        ],
        workflow_runner=new_sandbox_runner(),
        tuner=tuner,
        workflow_task_poller_behavior=PollerBehaviorAutoscaling(),
        activity_task_poller_behavior=PollerBehaviorAutoscaling(),
    )

    # Run the worker
    async with worker:
        # Wait until interrupted
        logging.info("Worker started, ctrl+c to exit")
        await interrupt_event.wait()
        logging.info("Shutting down")

        # Clean up DB pools
        await mars_db_pool.wait_closed()


if __name__ == "__main__":
    loop = asyncio.new_event_loop()
    try:
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        interrupt_event.set()
        loop.run_until_complete(loop.shutdown_asyncgens())
