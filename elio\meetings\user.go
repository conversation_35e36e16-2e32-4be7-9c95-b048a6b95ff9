package meetings

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"net/http"

	"encore.app/billing"
	billing_api "encore.app/billing/api"
	"encore.app/hubble"
	hubble_api "encore.app/hubble/api"
	"encore.app/meetings/api"
	"encore.app/meetings/database"
	shared "encore.app/shared"
	"encore.dev/beta/auth"
	"encore.dev/beta/errs"
	"encore.dev/rlog"
	"github.com/samber/lo"
)

// GetUserByID gets user details by ID
//
//encore:api private tag:trixta
func (meetings *Meetings) GetUserByID(ctx context.Context, request *api.GetUserByIDRequest) (*shared.UserDTO, error) {
	user, err := database.GetUserByID(ctx, request.UserID)
	if err != nil {
		return nil, err
	}

	return user.ToUserDTO(), nil
}

//encore:api private
func (c *Meetings) GetUserByIDWithRelations(ctx context.Context, request *api.GetUserByIDRequest) (*api.GetMyUserResponse, error) {
	user, err := database.GetUserByID(ctx, request.UserID)
	if err != nil {
		rlog.Error("Failed to get user", "error", err)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}
	resp := &api.GetMyUserResponse{}
	resp.Data.User = *user.ToUserDTO()

	if err = user.PopulateRelations(ctx, &resp.Data.User); err != nil {
		rlog.Error("failed to populate user relations", "error", err, "userID", request.UserID)
		return nil, shared.B().WithCode(shared.InternalServerError)
	}

	return resp, err
}

//encore:api auth method=POST path=/users tag:internal
func (c *Meetings) CreateUser(ctx context.Context, request *api.CreateUserRequest) (*api.CreateUserResponse, error) {
	// Create user in DB
	newUserID, err := database.InsertUser(ctx, database.CreateUserWithIDsRequest{
		UserEmail:        request.UserEmail,
		UserFirstName:    request.UserFirstName,
		UserLastName:     request.UserLastName,
		Fingerprint:      request.Fingerprint,
		Timezone:         request.Timezone,
		RoleIDs:          request.RoleIDs,
		Avatar:           request.Avatar,
		About:            request.About,
		PaddleCustomerID: request.PaddleCustomerID,
		MarketingOptIn:   request.MarketingOptIn,
	})
	if err != nil {
		err = errs.Wrap(err, "failed to insert user")
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	// Call hubble to upsert auth user
	_, err = hubble.UpsertAuthUsers(ctx, &hubble_api.CreateAuthUsersRequest{
		Users: []hubble_api.CreateAuthUserInput{
			{
				ID:      *newUserID,
				Email:   request.UserEmail,
				RoleIDs: request.RoleIDs,
			},
		},
	})
	if err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	return &api.CreateUserResponse{UserID: *newUserID}, nil
}

//encore:api auth method=GET path=/users tag:internal
func (c *Meetings) ListUsers(ctx context.Context, request *api.ListUsersRequest) (*api.ListUsersResponse, error) {
	paginationParams, err := request.ParsePaginationParams()
	if err != nil {
		return nil, err
	}

	var usersRows []database.UserRow
	var userCount int

	// List users by email
	if request.Email != "" {
		var orderBy string
		if request.OrderBy == "" {
			orderBy = "email"
		} else {
			orderBy = request.OrderBy
		}

		var orderDirection string
		if request.OrderDirection == "" {
			orderDirection = "ASC"
		} else {
			orderDirection = request.OrderDirection
		}

		usersRows, err = database.ListUsersByEmail(ctx, &database.ListUsersByEmailRequest{
			Limit:          paginationParams.Limit,
			Offset:         paginationParams.Offset,
			OrderBy:        orderBy,
			OrderDirection: orderDirection,
			Email:          request.Email,
		})
		if err != nil {
			return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
		}

		userCount, err = database.CountUsersByEmail(ctx, &database.CountUsersByEmailRequest{
			Email: request.Email,
		})
		if err != nil {
			return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
		}
	}

	// List all users if no filter is provided
	if request.Email == "" {
		var orderBy string
		if request.OrderBy == "" {
			orderBy = "id"
		} else {
			orderBy = request.OrderBy
		}

		var orderDirection string
		if request.OrderDirection == "" {
			orderDirection = "ASC"
		} else {
			orderDirection = request.OrderDirection
		}

		usersRows, err = database.ListUsers(ctx, &database.ListUsersRequest{
			Limit:          paginationParams.Limit,
			Offset:         paginationParams.Offset,
			OrderBy:        orderBy,
			OrderDirection: orderDirection,
		})
		if err != nil {
			return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
		}

		userCount, err = database.CountUsers(ctx)
		if err != nil {
			return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
		}
	}

	userDTOs := lo.Map(usersRows, func(item database.UserRow, _ int) shared.UserDTO {
		return *item.ToUserDTO()
	})

	return &api.ListUsersResponse{
		OffsetPaginationResponse: shared.OffsetPaginationResponse{
			TotalCount: userCount,
			Limit:      paginationParams.Limit,
			Offset:     paginationParams.Offset,
		},
		Users: userDTOs,
	}, nil
}

//encore:api auth method=GET path=/v1.0/users/id/:userID/plan
func (meetings *Meetings) GetUserPlanByUserID(ctx context.Context, userID string) (*api.GetUserPlanByUserIDResponse,
	error) {
	// Check that userID is the same as the userID in the JWT
	userIDFromJWT, _ := auth.UserID()
	if userID != string(userIDFromJWT) {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.Unauthorized], shared.Unauthorized, http.StatusUnauthorized)
	}

	userRow, err := database.GetUserByID(ctx, userID)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserNotFound], shared.UserNotFound, http.StatusNotFound)
		}
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	userDTO := userRow.ToUserDTO()
	err = userRow.PopulatePlan(ctx, userDTO)
	if err != nil {
		if errors.Is(err, database.ErrGetUserSubscriptionPlanConfig) || errors.Is(err, database.ErrGetUserSubscriptionPlan) {
			return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserSubscriptionPlanNotFound], shared.UserSubscriptionPlanNotFound, http.StatusNotFound)
		}

		err = errs.Wrap(err, "failed to populate user plan")
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	if userDTO.SubscriptionPlan == nil {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserSubscriptionPlanNotFound], shared.UserSubscriptionPlanNotFound, http.StatusNotFound)
	}

	subscriptionPlan := userDTO.SubscriptionPlan

	return &api.GetUserPlanByUserIDResponse{
		Success: true,
		Message: "Data fetched successfully",
		Data:    *subscriptionPlan,
	}, nil
}

// UpdateUserByID updates user details by their ID
//
//encore:api auth method=PUT path=/v1.0/users/id/:userID
func (c *Meetings) UpdateUserByID(ctx context.Context, userID string, req *api.UpdateUserRequest) (*api.UpdateUserResponse,
	error) {
	_, authed := auth.UserID()
	if authed {
		err := hubble.IsAuthorized(ctx, &hubble_api.IsAuthorizedRequest{
			Sub: shared.GetUserSubjectWithRole(userID), Obj: shared.GetUserObjectByID(userID), Act: shared.AuthPolicyActionUpdate,
		})
		if err != nil {
			return nil, err
		}
	}

	// Get user
	user, err := GetUserByID(ctx, &api.GetUserByIDRequest{UserID: userID})
	if err != nil || user == nil {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserNotFound], shared.UserNotFound, http.StatusNotFound)
	}

	// Prepare user's next state by merging the user's current state with the request
	nextUser := database.UpdateUserRequest{
		UserID:    userID,
		Avatar:    user.Avatar,
		FirstName: user.FirstName,
		LastName:  user.LastName,
		About:     user.About,
		Social:    user.Social,
	}

	if req.UserData.Avatar != nil {
		if *req.UserData.Avatar == "" {
			nextUser.Avatar = nil
		} else {
			nextUser.Avatar = req.UserData.Avatar
		}
	}

	if req.UserData.FirstName != nil {
		nextUser.FirstName = req.UserData.FirstName
	}

	if req.UserData.LastName != nil {
		nextUser.LastName = req.UserData.LastName
	}

	if req.UserData.About != nil {
		nextUser.About = req.UserData.About
	}

	if req.UserData.Social != nil {
		nextUser.Social = req.UserData.Social
	}

	// Update user
	err = database.UpdateUser(ctx, nextUser)
	if err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	// Update onboarding flags
	if req.UserData.Onboarding != nil {
		if err := database.UpdateUserOnboardingFlag(ctx, userID, req.UserData.Onboarding); err != nil {
			return nil, errs.WrapCode(err, errs.Internal, "failed to update onboarding flag")
		}
	}

	// Get user again and return it
	user, err = GetUserByID(ctx, &api.GetUserByIDRequest{UserID: userID})
	if err != nil {
		return nil, errs.WrapCode(err, errs.Internal, "user not found after update")
	}

	res := api.UpdateUserResponse{}
	res.Data.User = *user

	return &res, nil
}

//encore:api auth method=GET path=/v1.0/users/id/:userID/payment-method-details
func (meetings *Meetings) GetUserPaymentMethodDetails(ctx context.Context, userID string) (*api.GetUserPaymentMethodDetailsResponse, error) {
	// Check that userID is the same as the userID in the JWT
	userIDFromJWT, _ := auth.UserID()
	if userID != string(userIDFromJWT) {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.Unauthorized], shared.Unauthorized, http.StatusUnauthorized)
	}

	user, err := GetUserByID(ctx, &api.GetUserByIDRequest{UserID: userID})
	if err != nil {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserNotFound], shared.UserNotFound, http.StatusNotFound)
	}

	if user.CustomerID == nil || *user.CustomerID == "" {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.PaddleCustomerDoesNotExist], shared.PaddleCustomerDoesNotExist, http.StatusInternalServerError)
	}

	listTransactionsRes, err := billing.ListTransactionsByCustomerID(ctx, billing_api.ListTransactionsByCustomerIDRequest{
		CustomerID: *user.CustomerID,
		Limit:      lo.ToPtr(5),
		Status:     lo.ToPtr([]string{"paid", "completed", "billed", "past_due", "canceled"}),
	})
	if err != nil {
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	transactions := listTransactionsRes.Transactions
	if len(transactions) == 0 {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserPaymentDetailsNotFound], shared.UserPaymentDetailsNotFound, http.StatusNotFound)
	}

	transactionsWithPayments := lo.Filter(transactions, func(t shared.BillingTransactionDTO, _ int) bool {
		if len(t.Payments) == 0 {
			return false
		}
		return true
	})

	// Iterate over transactions and find the first payment with a non-empty payment status
	// This assumes that the transactions, and payments, are sorted by descending createdAt
	lastPayment := (func() *shared.BillingPaymentResultDTO {
		for _, t := range transactionsWithPayments {
			lastTransactionPaymentAttemptWithNonEmptyStatus, _, ok := lo.FindLastIndexOf(t.Payments, func(p shared.BillingTransactionPaymentAttemptDTO) bool {
				return p.Status != ""
			})
			if !ok {
				continue
			}

			lastPayment := &shared.BillingPaymentResultDTO{
				Status: shared.BillingPaymentResultDTOStatus(lastTransactionPaymentAttemptWithNonEmptyStatus.Status),
			}

			if lastTransactionPaymentAttemptWithNonEmptyStatus.ErrorCode != nil {
				lastPayment.ErrorCode = shared.BillingPaymentResultDTOErrorCode(*lastTransactionPaymentAttemptWithNonEmptyStatus.ErrorCode)
			}

			return lastPayment
		}

		return nil
	})()

	paymentMethodDetails := (func() *shared.BillingPaymentMethodDetailsDTO {
		for _, t := range transactionsWithPayments {
			firstTransactionPaymentAttemptWithPaymentMethodDetails, ok := lo.Find(t.Payments, func(p shared.BillingTransactionPaymentAttemptDTO) bool {
				return p.MethodDetails.Type != ""
			})
			if !ok {
				continue
			}

			return lo.ToPtr(firstTransactionPaymentAttemptWithPaymentMethodDetails.MethodDetails)
		}

		return nil
	})()
	_ = paymentMethodDetails

	return &api.GetUserPaymentMethodDetailsResponse{
		Success: true,
		Message: "Data fetched successfully",
		Data: api.GetUserPaymentMethodDetailsResponseData{
			LastPayment:             lastPayment,
			PaymentMethodDetailsDTO: *paymentMethodDetails,
		},
	}, nil
}

//encore:api auth method=GET path=/v1.0/users/id/:userID/transactions
func (meetings *Meetings) ListUserTransactions(ctx context.Context, userID string, request *api.ListUserTransactionsRequest) (*api.ListUserTransactionsResponse,
	error) {
	// Check that userID is the same as the userID in the JWT
	userIDFromJWT, _ := auth.UserID()
	if userID != string(userIDFromJWT) {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.Unauthorized], shared.Unauthorized, http.StatusUnauthorized)
	}

	user, err := GetUserByID(ctx, &api.GetUserByIDRequest{UserID: userID})
	if err != nil {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserNotFound], shared.UserNotFound, http.StatusNotFound)
	}

	if user.CustomerID == nil || *user.CustomerID == "" {
		return &api.ListUserTransactionsResponse{
			Data: []api.ListUserTransactionsResponseData{},
		}, nil
	}

	listTransactionsRes, err := billing.ListTransactionsByCustomerID(ctx, billing_api.ListTransactionsByCustomerIDRequest{
		CustomerID: *user.CustomerID,
		Limit:      lo.ToPtr(5),
		Status:     lo.ToPtr([]string{"paid", "completed", "billed", "past_due", "canceled"}),
	})
	if err != nil {
		err = fmt.Errorf("error listing transactions by customer id: %w", err)
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	transactions := listTransactionsRes.Transactions

	transactionsWithInvoiceURL := lo.Map(transactions, func(t shared.BillingTransactionDTO, _ int) api.ListUserTransactionsResponseData {
		getInvoiceByTransactionIDRes, err := billing.GetInvoiceByTransactionID(ctx, billing_api.GetInvoiceByTransactionIDRequest{
			TransactionID: t.ID,
		})
		if err != nil {
			err = fmt.Errorf("error getting invoice by transaction id: %w", err)
			return api.ListUserTransactionsResponseData{
				Transaction: t,
			}
		}
		invoiceURL := getInvoiceByTransactionIDRes.URL

		td := api.ListUserTransactionsResponseData{
			Transaction: t,
			InvoiceURL:  invoiceURL,
		}

		return td
	})

	return &api.ListUserTransactionsResponse{
		Success: true,
		Message: "Data fetched successfully",
		Data:    transactionsWithInvoiceURL,
	}, nil
}

//encore:api auth method=GET path=/v1.0/users/id/:userID/update-payment-method-transaction
func (meetings *Meetings) GetUpdateUserPaymentMethodTransaction(ctx context.Context, userID string) (*api.GetUpdateUserPaymentMethodTransactionResponse,
	error) {
	// Check that userID is the same as the userID in the JWT
	userIDFromJWT, _ := auth.UserID()
	if userID != string(userIDFromJWT) {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.Unauthorized], shared.Unauthorized, http.StatusUnauthorized)
	}

	getUserSubscriptionPlanRes, err := meetings.GetUserPlanByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	userSubscriptionPlan := getUserSubscriptionPlanRes.Data
	paddleSubscriptionID := userSubscriptionPlan.PaddleSubscriptionID

	if paddleSubscriptionID == nil {
		return nil, shared.HttpResponseError(shared.ErrorMessages[shared.UserSubscriptionPlanNotFound], shared.UserSubscriptionPlanNotFound, http.StatusNotFound)
	}

	getSubscriptionUpdatePaymentMethodTransactionRes, err := billing.GetSubscriptionUpdatePaymentMethodTransaction(ctx,
		billing_api.GetSubscriptionUpdatePaymentMethodTransactionRequest{
			SubscriptionID: *paddleSubscriptionID,
		})
	if err != nil {
		err = fmt.Errorf("error getting subscription update payment method transaction: %w", err)
		return nil, shared.HttpResponseError(err.Error(), shared.InternalServerError, http.StatusInternalServerError)
	}

	readyTransaction := getSubscriptionUpdatePaymentMethodTransactionRes.Transaction

	return &api.GetUpdateUserPaymentMethodTransactionResponse{
		Success: true,
		Message: "Data fetched successfully",
		Data:    readyTransaction,
	}, nil
}
